{"name": "common-components", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0", "@tanstack/react-query": "^4.0.10"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-syntax-flow": "^7.16.7", "@babel/plugin-transform-react-jsx": "^7.17.3", "@tanstack/react-query": "^4.0.10", "@types/node": "17.0.21", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "babel-loader": "^8.2.5", "eslint": "8.10.0", "eslint-config-prettier": "^8.4.0", "eslint-config-react-app": "^7.0.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-test-id": "^2.1.0", "eslint-webpack-plugin": "^3.2.0", "material-ui-phone-number": "^3.0.0", "prettier": "^2.5.1", "react": "18.2.0", "react-dom": "18.2.0", "require-from-string": "^2.0.2", "tsconfig-paths-webpack-plugin": "^4.0.0", "types": "1.0.0", "typescript": "^5.3.3", "utils": "1.0.0", "webpack": "^5.74.0", "zustand": "^3.7.1"}, "keywords": [], "author": "", "license": "ISC"}