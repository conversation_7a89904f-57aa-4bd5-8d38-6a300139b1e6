import type { UseQueryResult } from '@tanstack/react-query';

export type IQueryManagerProps<
  T extends Record<string, UseQueryResult<any, any>>,
> = {
  queries: T;
  children: (data: DataFromQueries<T>) => JSX.Element | null;
  whenLoading: JSX.Element;
  whenError: (errors: ErrorsFromQueries<T>) => JSX.Element;
};

export type DataFromQueries<
  T extends Record<string, UseQueryResult<any, any>>,
> = {
  [K in keyof T]: T[K] extends UseQueryResult<infer D, any> ? D : never;
};

export type ErrorsFromQueries<
  T extends Record<string, UseQueryResult<any, any>>,
> = {
  [K in keyof T]: T[K] extends UseQueryResult<any, infer E> ? E : never;
};

export const QueryManager = <
  T extends Record<string, UseQueryResult<any, any>>,
>({
  queries,
  children,
  whenError,
  whenLoading,
}: IQueryManagerProps<T>) => {
  if (Object.keys(queries).every(k => queries[k]?.isSuccess)) {
    const dataObj = {} as DataFromQueries<T>;
    for (const k in queries) {
      dataObj[k] = queries[k]?.data;
    }
    return children(dataObj as DataFromQueries<T>);
  }

  if (Object.keys(queries).some(k => queries[k]?.isError)) {
    const errorObj = {} as ErrorsFromQueries<T>;
    for (const k in queries) {
      errorObj[k] = queries[k]?.error;
    }
    return whenError(errorObj as ErrorsFromQueries<T>);
  }

  return whenLoading;
};
