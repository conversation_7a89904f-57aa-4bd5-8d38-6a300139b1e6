import { isNil } from 'lodash';

type NonNullableValues<T extends Record<string, any>> = {
  // eslint-disable-next-line @typescript-eslint/ban-types
  [K in keyof T]-?: NonNullable<T[K]> & {};
};

export interface IAssertProps<T extends Record<string, any>> {
  values: T;
  whenError: (errors: (keyof T)[]) => JSX.Element;
  children: (values: NonNullableValues<T>) => JSX.Element | null;
}

export const Assert = <T extends Record<string, any>>({
  values,
  children,
  whenError,
}: IAssertProps<T>) => {
  const nilValuesKeys = Object.keys(values).filter(k => isNil(values[k]));

  if (nilValuesKeys.length === 0) {
    return children(values as NonNullableValues<T>);
  }
  return whenError(nilValuesKeys as (keyof T)[]);
};
