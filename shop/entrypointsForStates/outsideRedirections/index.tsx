import { useFsFlag } from '@flagship.io/react-sdk';
import { useEffect } from 'react';
import { GlobalLoader } from 'shared-components';

import { useLocale } from '@/hooks/locale/useLocale';
import { useGetStateContextValue } from '@/hooks/store/useStateValue';
import { NEW_FUNNEL_URL } from '@/utils/constants';

const OutsideRedirectionsEntryPoint = () => {
  const isRedirectionToNewFunnelsEnabled = useFsFlag(
    'shop_to_funnel',
    false,
  ).getValue();
  const offerPeriodicity = useGetStateContextValue('offerPeriodicity');
  const productId = useGetStateContextValue('productId');
  const productFamilyId = useGetStateContextValue('productFamilyId');
  const { locale } = useLocale();

  useEffect(() => {
    if (isRedirectionToNewFunnelsEnabled && productId) {
      window.location.href = `${NEW_FUNNEL_URL}${locale}/buy/${productFamilyId}/${productId}?periodicity=${offerPeriodicity}`;
    }
  }, [
    productFamilyId,
    productId,
    offerPeriodicity,
    isRedirectionToNewFunnelsEnabled,
    locale,
  ]);

  return <GlobalLoader fullHeight />;
};

export default OutsideRedirectionsEntryPoint;
