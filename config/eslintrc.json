{"env": {"browser": true, "es2021": true, "amd": true, "node": true, "jest": true}, "root": true, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2021, "sourceType": "module"}, "plugins": ["@typescript-eslint", "import", "prettier", "react", "react-hooks"], "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}], "indent": [0, 2, {"SwitchCase": 1}], "linebreak-style": "off", "space-before-blocks": ["error", "always"], "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "semi": [2, "always"], "semi-spacing": "error", "comma-spacing": ["error"], "comma-dangle": ["error", {"arrays": "always-multiline", "objects": "always-multiline", "imports": "always-multiline", "exports": "always-multiline", "functions": "always-multiline"}], "prefer-const": "error", "prefer-rest-params": "error", "arrow-spacing": ["error", {"before": true, "after": true}], "func-call-spacing": ["error", "never"], "class-methods-use-this": ["off", {"exceptMethods": ["componentDidMount"]}], "eqeqeq": "error", "no-this-before-super": "error", "space-before-function-paren": 0, "prefer-template": "warn", "template-curly-spacing": "warn", "object-property-newline": ["warn", {"allowMultiplePropertiesPerLine": true}], "block-spacing": ["warn", "always"], "array-bracket-spacing": ["warn", "never"], "arrow-parens": ["warn", "as-needed"], "space-in-parens": ["warn", "never"], "no-undef": 0, "key-spacing": ["warn", {"mode": "minimum"}], "keyword-spacing": "warn", "object-curly-newline": "off", "object-curly-spacing": ["error", "always"], "curly": ["error", "all"], "prefer-promise-reject-errors": "warn", "prefer-arrow-callback": "warn", "accessor-pairs": "error", "no-console": "error", "no-fallthrough": "off", "camelcase": "off", "react/jsx-max-props-per-line": [0, {"maximum": 4, "when": "always"}], "react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "react/jsx-key": 0, "react/prefer-stateless-function": "warn", "react/no-this-in-sfc": "error", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/display-name": "off", "react/sort-comp": ["warn", {"order": ["static-methods", "constructor", "getter", "setter", "lifecycle", "everything-else", "/^on.+$/", "rendering"], "groups": {"rendering": ["/^render.+$/", "render"]}}], "import/no-unresolved": "off", "import/order": ["error", {"pathGroups": [{"pattern": "@/**", "group": "parent", "position": "after"}], "groups": [["external", "builtin"], ["internal"], ["index", "sibling", "parent"]], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "no-unexpected-multiline": "off", "function-paren-newline": "off", "no-return-await": "error", "no-mixed-operators": "off", "no-shadow": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-shadow": ["error"], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/ban-ts-comment": ["warn"], "@typescript-eslint/no-unused-vars": ["error", {"varsIgnorePattern": "^_+"}], "@typescript-eslint/camelcase": "off", "@typescript-eslint/no-use-before-define": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "overrides": [{"files": ["*.test.ts", "*.test.tsx"], "rules": {"import/first": "off"}}], "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"typescript": {"alwaysTryTypes": true}}, "import/external-module-folders": ["node_modules", "node_modules/@types"], "react": {"version": "detect"}}}