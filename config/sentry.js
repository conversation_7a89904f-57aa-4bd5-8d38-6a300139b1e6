export const initializeSentry = (
  sentry,
  captureConsoleIntegration,
  SENTRY_DSN,
  errorsToIgnore,
) => {
  sentry.init({
    dsn: SENTRY_DSN,
    integrations: [
      new captureConsoleIntegration({
        levels: ['error'],
      }),
    ],
    maxBreadcrumbs: 50,
    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: 0.2,
    beforeSend(event, hint) {
      const { environment, breadcrumbs } = event;
      const errorOrErrorMessage = hint.originalException;
      const isDev = environment === 'development';
      const { errorsStringsToIgnore, errorsApisToIgnore } = errorsToIgnore;

      const hasStringErrorToIgnore =
        !!errorOrErrorMessage &&
        !!errorsStringsToIgnore.find(errorString => {
          const regex = new RegExp(errorString, 'g');
          return typeof errorOrErrorMessage === 'string'
            ? errorOrErrorMessage.match(regex)
            : errorOrErrorMessage.message?.match(regex);
        });

      const hasApiErrorToIgnore = errorsApisToIgnore?.find(apiErrorToIgnore => {
        const regex = new RegExp(apiErrorToIgnore, 'g');
        return !!breadcrumbs?.find(
          breadcrumb =>
            breadcrumb.level === 'error' && breadcrumb?.data?.url?.match(regex),
        );
      });

      if (isDev || hasStringErrorToIgnore || hasApiErrorToIgnore) {
        return null;
      }
      return event;
    },
    // ...
    // Note: if you want to override the automatic release value, do not set a
    // `release` value here - use the environment variable `SENTRY_RELEASE`, so
    // that it will also get attached to your source maps
  });
};
