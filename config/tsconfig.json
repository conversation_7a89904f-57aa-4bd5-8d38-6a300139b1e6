{"compilerOptions": {"target": "esnext", "lib": ["es6", "dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "noImplicitAny": true, "sourceMap": true, "noUncheckedIndexedAccess": true}}