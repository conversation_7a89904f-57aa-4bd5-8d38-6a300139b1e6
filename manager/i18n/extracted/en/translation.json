{"account": {"billing": {"contactSupport": "In order to change your country, <0>contact the support</0>.", "title": "Billing information", "update": {"error": {"message": "An error occurred when updating your billing information:"}, "submit": {"label": "Save billing information"}, "success": {"message": "Your billing information has been updated."}}}, "company": {"update": {"success": {"message": "Your company information has been updated.", "title": "Company infos updated"}, "error": {"message": "An error occurred when updating your company information:"}}}, "payment": {"add": {"link": "Add a new payment method"}, "edit": {"link": "Edit"}, "type": {"none": "No payment method", "card": "Credit card", "bancontact": "Bancontact", "ideal": "Ideal", "paypal_express_checkout": "PayPal", "sepa": "SEPA", "sofort": "Sofort"}}, "privacy": {"cta": {"label": "Change my privacy preferences"}, "title": "Manage your privacy preferences"}, "user": {"downloadData": {"label": "Download my data"}, "editEmail": {"label": "Edit my email"}, "form": {"submit": {"label": "Save account information"}}, "paymentMethod": {"title": "Payment method"}, "resendVerificationEmail": {"label": "Resend the verification email"}, "security": {"buttons": {"hub": "Manage my security settings", "password": "Edit my password"}, "title": "Security", "text": "Strengthen the security of your account by activating two-factor authentication (2FA). You can use an authentication app or security keys. Feel free to generate backup codes as an alternative to 2FA authentication.<2>Learn more about two-factor authentication.</2>"}, "socials": {"buttons": {"text": "Manage my social logins"}, "text": "<0>To log in to your Shadow account, you can set up your Shadow password, or link an external account. Email communications from Shadow will continue to be sent to the email address of your Shadow account.</0>", "title": "Socials Login"}, "title": "Account information", "update": {"error": {"message": "An error occurred when updating your personal information:"}, "success": {"message": "Your personal information has been updated."}}}}, "company": {"button": "Save company information", "title": "Your company"}, "consentBanner": {"title": "Shadow consent banner"}, "footer": {"cgu": "Terms of Use", "companyName": "© Shadow", "cookies": "Cookies", "legal": "Legal", "privacy": "Privacy"}, "header": {"login": {"welcome": {"label": "Welcome"}}, "logoAlt": "Shadow", "logout": {"label": "Logout"}}, "infoBanner": {"unpaid": {"error": {"content": "Your payment has been declined. Please try again later, or update your payment method by clicking <0>here</0>", "title": "Payment failed"}, "infos": {"productName": {"cloudpc": "Shadow PC", "shadow-drive": "Shadow Drive"}, "content": "Your {{ productName }} subscription is unpaid. Your {{ productName }} subscription will be terminated and data erased $t(infoBanner.unpaid.infos.daysBeforeTermination, {\"count\": {{daysBeforeTermination}} }) if your subscription remains unpaid. Please <0>retry your payment</0> or <1>update your payment method</1> to avoid losing your subscription and data.", "contentBusiness": "One or more invoices remain unpaid to this day. Please regularize your subscription as soon as possible. Please <0>retry your payment</0> or <1>update your payment method</1> to avoid losing your subscription and data.", "daysBeforeTermination_zero": "in less than a day", "daysBeforeTermination_one": "within 1 day", "daysBeforeTermination_other": "within {{count}} days"}, "success": {"content": "Your payment has been successful. For more information and details, please check your invoice", "title": "Payment succeeded"}}}, "invoices": {"list": {"body": {"link": {"csv": "Download CSV", "pdf": "Download PDF"}, "status": {"not_paid": "Unpaid", "paid": "Paid", "payment_due": "Payment due", "pending": "Pending", "posted": "Posted", "voided": "Voided"}, "statusTooltip": {"not_paid": "Your invoice hasn't been paid, please update your payment method or retry a payment.", "payment_due": "Your invoice's payement hasn't been fulfilled, please update your payment method or retry a payment.", "pending": "Your invoice is waiting to be fulfilled.", "posted": "Your invoice has been posted.", "voided": "Your invoice has been voided due to various reasons. Please contact support if you do not know why.", "notPaid": "Your invoice hasn't been paid, please update your payment method or retry a payment."}}, "heading": {"date": "Date", "status": "Status", "total": "Amount"}, "download": {"error": "An error occurred while downloading your invoice, please try again later"}}, "noInvoice": "No invoices", "title": "Invoices", "total": "Total: {{total}} invoices"}, "subscription": {"details": {"hasStartingPrice": "Starting", "status": {"active": "active since ", "inactive": "inactive since ", "pending": "end on ", "future": "waiting for activation", "non_renewing": "Cancellation in progress"}, "scheduledChangesName": {"change_plan": "Change your plan", "default": "Update in your subscription", "reduce_extra_storage": "Reduce your extra storage quantity", "remove_power_upgrade": "Remove Power Upgrade", "update_ram": "Update your RAM quantity"}, "scheduledChangesProductFamily": {"shadow-drive": "Shadow Drive", "cloudpc": "Shadow PC"}, "name": {"unknown": "Shadow PC", "cloudpc-b2b-edu-plan-a-2022": "Spark Edu", "cloudpc-b2b-edu-plan-b-2022": "Aurora Edu", "cloudpc-b2b-edu-plan-c-2022": "Lightning Edu", "cloudpc-b2b-edu-plan-d-2022": "<PERSON><PERSON>", "cloudpc-b2b-nogpu-plan-a-2023": "Shadow PC Enterprise - Essential", "cloudpc-b2b-plan-a-2022": "Shadow PC Enterprise - Standard - GPU P5000/1080", "cloudpc-b2b-plan-b-2022": "Shadow PC Enterprise - Extended - GPU Quadro RTX5000", "cloudpc-b2b-plan-c-2022": "Shadow PC Enterprise - Expert - GPU Quadro RTX6000", "cloudpc-b2b-plan-d-2022": "Shadow PC Enterprise - Advanced - GPU A4500", "cloudpc-b2b-premium2022": "Shadow PC Pro", "cloudpc-b2b-standard2022": "Shadow PC Pro", "cloudpc-b2c-A4000power-2022": "Shadow Power", "cloudpc-b2c-power2022-c1": "Power Upgrade", "cloudpc-b2c-power2022-c12": "Power Upgrade", "cloudpc-b2c-power2023": "Shadow PC - Power", "cloudpc-b2c-standard2021-c1": "Shadow PC", "cloudpc-b2c-standard2021-c12": "Shadow PC", "cloudpc-b2c-standard2023": "Shadow PC - <PERSON><PERSON>", "cloudpc-b2p-nogpu-plan-a-2023": "Shadow PC Pro - Essential", "cloudpc-b2p-plan-a-2022": "Shadow PC Pro - Standard", "cloudpc-b2p-plan-b-2022": "Shadow PC Pro - Extended", "cloudpc-b2p-plan-c-2022": "Shadow PC Pro - Expert", "cloudpc-b2p-plan-d-2022": "Shadow PC Pro - Advanced", "cloudpc-b2p-plan-e-2022": "Shadow PC Pro - Advanced Lite", "cloudpc-old-b2c-boost2019-c1": "Shadow PC - <PERSON><PERSON>", "cloudpc-old-b2c-boost2019-c12": "Shadow PC - <PERSON><PERSON>", "cloudpc-old-b2c-infinite2019-c1": "Shadow Infinite", "cloudpc-old-b2c-infinite2019-c12": "Shadow Infinite", "cloudpc-old-b2c-infinite2021-c1": "Shadow Infinite", "cloudpc-old-b2c-ultra2019-c1": "Shadow Ultra", "cloudpc-old-b2c-ultra2019-c12": "Shadow Ultra", "cloudpc-old-b2c-ultra2021-c1": "Shadow Ultra", "shadow-drive-b2c-free": "Free plan: 20 GB", "shadow-drive-b2c-premium": "Shadow Drive Premium: 2 TB", "cloudpc-b2c-discovery2024": "Shadow PC - Discovery", "shadow-drive-b2c-premium_b": "Shadow Drive Premium: 200 GB", "cloudpc-b2c-newboost2025": "Shadow PC - Neo", "cloudpc-b2c-newboost2025-EUR-Every": "Shadow PC - Neo", "cloudpc-b2c-standard2025": "Shadow PC - <PERSON><PERSON>", "cloudpc-b2c-standard2025-OS": "Shadow PC - <PERSON><PERSON>", "cloudpc-b2c-standard2025-summer-EUR-Every": "Shadow PC - Boost Classic", "cloudpc-b2c-standard2025-summer": "Shadow PC - Boost Classic", "cloudpc-b2p-newstandard2025": "Shadow PC - Neo Pro", "cloudpc-b2p-newstandard2025-EUR-Every": "Shadow PC - Neo Pro"}, "scheduledChange": {"periodicity": "Starting on <bold>{{scheduledChangesApplicationDate}}</bold>, your billing cycle will switch to a <bold>{{period}}</bold> frequency.", "plan": "From <bold>{{scheduledChangesApplicationDate}}</bold>, your subscription plan will be updated to <bold>{{planOfferName}}</bold>."}}, "cancelDrive": {"confirmation": {"subscription": {"changedYourMind": "We are always improving Shadow Drive so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime.", "item-1": "You will still have access to your Shadow Drive until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>", "item-2": "We will send you a confirmation email to <bold>{{ email }}</bold> shortly.", "item-3": "You won't be charged anymore."}, "subtitle": "We are sad to see you leave.", "title": "Your subscription has been canceled"}, "information": {"subtitle": "If you cancel your subscription, your access to Shadow Drive will end on <bold>{{ lastBillingDate }}</bold>", "title": "Here's some information before you cancel", "whenSubscriptionActive": {"access": "You can still access your Shadow Drive until the end of your subscription.", "backupTip1": "Don't forget to back up your files", "backupTip2": "Make sure to transfer important files from Shadow Drive to your local PC. Your data will be deleted after your subscription ends.", "enjoy": "Enjoy the most out of Shadow Drive", "title": "While your subscription is still active"}, "whenSubscriptionEnds": {"dataAccess": "You'll no longer be able to access your files and they will be permanently deleted.", "description": "If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!", "reSubscribe": {"title": "Restart your subscription anytime!"}, "shadowAccess": "You'll lose access to your Shadow Drive", "title": "When your subscription ends"}}, "notification": {"error": "There was an error when trying to cancel your Drive subscription, please try again later or contact support."}, "reason": {"placeholder": "Please, describe why you want to cancel your Drive subscription"}, "survey": {"title": "We're always improving our service and your feedback matters"}}, "cancellationReasons": {"category": {"financialReason": "Financial reasons", "productAndUsage": "Product and usage", "technicalIssue": "Technical Issue"}, "reason": {"incompatibilitySoftwareOrGame": "Incompatibility of the software or the game for which I chose Shadow", "itWasJustATest": "I don't plan to keep my <PERSON> (it was just a test)", "latencyIssue": "I felt latency and/or delay in my actions", "noNeedAnymore": "I don't need it anymore", "personalFinancialIssue": "I would like to keep my Shadow but I prefer to stop my subscription for financial issues", "priceTooHigh": "The price is too high for what I do with my Shadow", "shadowSpecTooWeak": "The technical specifications are too weak for what I want to do", "stabilityIssue": "I felt instability (bugs, updates, …)", "startIssue": "I'm having trouble launching Shadow", "storageNotBigEnough": "The storage is not big enough for my use", "tooManyConstraints": "Too many constraints? (autoshutdown…)", "weakInternetConnection": "My internet connection is too weak"}}, "cancelVm": {"confirmation": {"subscription": {"changedYourMind": "We are always improving <PERSON> so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime.", "item-1": "You will still have access to your Shadow until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>", "item-2": "We will send you a confirmation email to <bold>{{ email }}</bold>", "item-3": "You won't be charged anymore."}, "subtitle": "We are sad to see you leave.", "title": "Your subscription has been canceled"}, "discount": {"acceptDiscountButton": {"label": "Yes, I want to keep Shadow PC for another month at {{ discountedPrice }}", "upgradeLabel": "Yes, I want to try {{ offerName }} for a month at {{ discountedPrice }}"}, "declineDiscountButton": {"label": "No, I definitely want to cancel"}, "description": "That’s why we want to offer you your Shadow PC for <bold>only {{ discountedPrice }}</bold> next month instead of {{ regularPrice }}.", "notification": {"error": "There was an error, please try again later or contact support."}, "title": "We are sad to let you go!", "upgradeDescription": "Enjoy {{ offerName }} for <bold>just {{ discountedPrice }}</bold> for the first month instead of {{ regularPrice }}: <bold>{{ discountValue }} off</bold> and adjusted based on what you’ve already paid!"}, "discountAccepted": {"confirmationText": "You can now continue to play, create, work, or do anything you want on your Shadow PC", "confirmationText2": "Good news! We will deduct <bold>{{ amount }}</bold> from your next bill.", "title": "Delighted to keep you with us!"}, "information": {"subtitle": "If you cancel your subscription, your access to Shadow will end on <bold>{{ lastBillingDate }}</bold>", "whenSubscriptionActive": {"access": "You can still access your Shadow until the end of your subscription. You still have time to make it to the top of the leaderboard.", "backupTip1": "Don't forget to back up your files", "backupTip2": "Make sure to transfer important files from <PERSON> to your local PC. Your data will be deleted after your subscription ends.", "enjoy": "Enjoy the most out of your games", "title": "While your subscription is still active"}, "whenSubscriptionEnds": {"dataAccess": "You'll no longer be able to access games, data, or software on your Shadow PC.", "description": "If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!", "reSubscribe": {"title": "Restart your subscription anytime!"}, "shadowAccess": "You'll lose access to your Shadow", "title": "When your subscription ends"}, "title": "Here's some information before you cancel"}, "notification": {"error": "There was an error when trying to cancel your subscription, please try again later or contact support."}, "otherReason": {"placeholder": "Please, describe the issue you had", "title": "Other"}, "survey": {"subtitle": "Please select your main reason for canceling. It will only take a minute.", "title": "We're always improving our service and your feedback matters"}}, "plan": {"addon": {"drive": {"get": {"link": "Get your Shadow Drive"}, "upgrade": {"link": "Upgrade to Premium (2 TB)"}}}, "details": "{{planName}} ({{status}}{{startDate}})", "drive": {"action": {"cancel": "Cancel my subscription", "edit": "Edit", "reactivate": "Reactivate", "resubscribe": "Resubscribe", "upgrade": "Switch to premium plan (2 TB)"}, "application": {"link": {"label": "Access my Shadow Drive", "title": "Go to Shadow Drive application"}}, "description": "A simple cloud storage solution to back up your data and access it anywhere, fully secured via traffic encryption. The perfect companion to Shadow.", "title": "My subscription"}, "storage": {"ctaLabel": "Manage extra storage", "alert": {"title": "Warning", "description": "This change requires a restart. Depending on the data center you are on, your Shadow PC may shut down immediately after confirmation. Please save what you’re doing before proceeding."}}, "vm": {"action": {"changePlan": {"alert": {"description": "This change requires a restart. Depending on the data center you are on, your Shadow PC may shut down immediately after confirmation. Please save what you’re doing before proceeding.", "title": "Warning"}, "title": "Change plan"}, "delete": "Cancel subscription", "reactivate": "Reactivate", "changeVmPeriodicity": "Change your billing frequency"}}}, "reactivateVm": {"notification": {"error": "We could not reactivate your subscription, please try again later.", "success": "You have successfully reactivated your subscription."}, "title": "Reactivate my Shadow"}, "renameVm": {"title": "Rename this Shadow PC"}, "resetVm": {"alert": {"description": "You will lose all your data (except D: Disk) in the process.", "title": "Warning"}, "notification": {"error": "There was an error when trying to reset your PC, please try again later or contact support.", "success": "You have successfully triggered a reset of your PC. This process will take around 1 hour."}, "resetLabel": "Reset my Shadow PC", "selectConfiguration": "Please select your new configuration", "subtitle": "Resetting your Shadow will bring a new clean install of your Windows, and reset all your settings.", "title": "Reset my Shadow"}, "vmName": {"notification": {"error": "There was an error changing this <PERSON>'s name. Please try again later.", "success": "PC's name changed successfully"}}, "driveGroups": {"manageGroupMembers": {"status": {"pending": "Pending", "active": "Active", "disabled": "Disabled", "expired": "Expired"}}}, "periodicity": {"adjective": {"day_one": "Daily", "day_other": "For {{count}} days", "week_one": "Weekly", "week_other": "For {{count}} weeks", "month_one": "Monthly", "month_other": "For {{count}} months", "year_one": "Yearly", "year_other": "For {{count}} years"}, "relative": {"day_one": "Daily", "day_other": "{{count}} days", "week_one": "Weekly", "week_other": "{{count}} weeks", "month_one": "Monthly", "month_other": "{{count}} months", "year_one": "Yearly", "year_other": "{{count}} years"}, "save": "(save {{percent}}%)"}, "gamePurchased": {"success": {"content": "Check your emails to see the next steps.", "title": "You just bought a game !"}}, "update": {"success": {"content": "It may take a few moments before your Shadow PC is fully ready. Please reboot your Shadow PC the next time you access it.", "title": "Your subscription has been updated"}}, "subscribeAlwaysOn": {"title": "Subscribe to Always On", "info": {"description": "Enabling the “Always On” feature will cost {{ alwaysOnPrice }}/month.", "title": "Notice"}, "alert": {"title": "Warning", "description": "This change requires a restart. Depending on the data center you are on, your Shadow PC may shut down immediately after confirmation. Please save what you’re doing before proceeding."}}, "unsubscribeAlwaysOn": {"alert": {"description": "To deactivate this feature, please contact support.", "title": "Notice"}, "title": "Unubscribe to Always On"}, "cancelScheduledChange": {"description": "A modification to your subscription was scheduled. If you proceed with the cancellation, your subscription will remain unchanged as it is today. Are you sure you want to proceed with this cancellation?", "notification": {"error": "An error occurred when updating your scheduled change. Please try again later.", "success": "Your change was canceled successfully"}, "title": "Cancel scheduled change"}, "promoBanner": {"newBoostOffer": {"button": "Upgrade", "text": "Upgrade to Neo Early Access, the future of your Boost offer.<br/>Skip the line for only {{price}} and play in RTX today."}}, "promoAlert": {"newBoostOffer": {"title": "Upgrade to Neo Early Access, the future of your Boost offer.", "button": "Upgrade", "text": "Skip the line for only {{price}} and play in RTX today.<br/>Or wait for the official migration and get upgraded for free later this year."}}, "changeVmPeriodicity": {"estimateApiError": "An error occurred while getting an estimation for updating your subscription, please try again later or contact support.", "success": {"content": "Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment."}, "modal": {"bullet-1": {"title": "Your change will be effective at your next billing", "description": "Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment."}, "bullet-2": {"title": "Cancel your change anytime", "description": "If you cancel your change before <bold>{{ nextBillingDate }}</bold>, you'll be able to keep your current subscription!"}, "subtitle": "Customize billing cycles for better control over your finances.", "title": "Change your periodicity"}, "updateApiError": "An error occurred while updating your subscription, please try again later or contact support."}, "vmDetails": {"vm": {"periodicity": {"label": "Subscription periodicity"}}}}, "support": {"form": {"placeholder": {"selectTopic": "Select a topic", "message": "Describe here your question/issue for our Support Team", "selectSystem": "Select a system", "selectIssue": "Select an issue", "selectVm": "Optional - Select one of your Shadow PC"}, "os": {"windows": "I'm using the Windows app", "mac": "I'm using the MacOS app", "ios": "I'm using the iOS or iPadOS app", "android": "I'm using the Android app", "browser": "I'm using <PERSON> in Browser", "linux": "I'm using the Linux app", "appleTv": "I'm using the Apple TV app", "androidTv": "I'm using the Android TV app", "raspberry": "I'm using a Raspberry PI", "other": "It’s something else"}, "m3": {"soundIssue": "I have sound issues", "latencyIssue": "I have latency issues", "inputIssue": "I have input issues", "longLoading": "I have long loading times", "artefact": "I have artifacts on my image", "display": "I have display issues", "deviceCompatibility": "I have an incompatible device", "appCrashes": "The app crashes at launch", "errorMessage": "I have an error message", "other": "It's something else"}, "m1": {"other": "It's something else", "accessShadow": "After being logged, I can't access my Shadow", "shadowAwesome": "I just wanted to say that <PERSON> is awesome", "improveShadow": "I have an idea to make <PERSON> even better", "shadowPCIssue": "I have an issue while using Shadow PC", "shadowDriveIssue": "I have an issue while using Shadow Drive", "subscriptionQuestion": "I have a question about my subscription"}, "m2": {"billRegul": "I can't regularize my last invoice", "billQuestion": "I have a question about my last invoice", "promo": "I have a question about a promo code", "command": "I have a question about my command tracking", "cancel": "I want to cancel my subscription", "deleteAccount": "I want to delete my account", "2FA": "I have a question about the 2FA", "personalInfo": "I want to update my personal information", "other": "Other", "emailHacked": "My email was changed without my consent"}, "description": "Need help or have questions about Shadow services? Our support team is here to help, and will get back to you as soon as possible.<br/>\n<br/>\nOur Customer Support is available in English and French from <bold>Monday</bold> to <bold>Friday</bold> and during the following opening hours:<br/>\n<bold>English: 10am to 6pm (EST & CET)<br/>\nFrench: 10am to 6pm (CET)</bold><br/>\n<br/>\nTo get started, please select a topic below:", "title": "Contact the support team", "notification": {"error": "An error occurred when sending your message to our support team. Please try again later.", "success": "Your message has been send to our support team!", "uploading": "Uploading files and sending your ticket..."}, "attachments": {"dragText": "Click or drag files to this area to upload", "hint": "You can attach screenshots or other files (20MB maximum total size) to help us understand your issue better.", "sizeError": "File size must be less than 10MB", "totalSizeError": "Total files size cannot exceed 20MB"}}, "ressources": {"description": "Everything you need to know when getting started with <PERSON>. Check out these guides first to get the most from your Shadow experience.", "faq": {"description": "This page provides answers to frequently asked questions.", "title": "FAQ"}, "helpcenter": {"description": "Learn how to use <PERSON>, fix a problem, and get answers to your questions.", "title": "Help center"}, "title": "Get Started", "userId": "For information, your id is: {{uid}}"}, "unknownEmail": "unknown email", "title": "Support"}, "emailing": {"shadow": {"title": "Shadow PC", "description": "Sign Up to stay up to date about our last news and exclusive offers. Unsubscribe easily whenever you want."}, "storage": {"title": "Shadow Storage", "description": "Sign Up to stay up to date about our last news and exclusive offers. Unsubscribe easily whenever you want."}, "drive": {"title": "Shadow Drive", "description": "Sign Up to stay up to date about our last news and exclusive offers. Unsubscribe easily whenever you want."}}, "billingDetails": {"title": "Manage billing details"}, "notFound": {"description": "The page your are looking for does not exist.", "title": "404 not found"}, "paymentMethod": {"title": "Manage payment method"}, "userManager": {"title": "Manage my Users", "notAdminError": "You don't have permission to access to this page", "inviteError": {"description": "An error occurred while processing your invitation. Please try again or contact support for assistance. (Error: {{errorMessage}})", "goToManager": "Continue to Manager", "title": "Invitation failed"}, "inviteSuccess": {"description": "You can now access your account and start using <PERSON>. Please also take a moment to review and complete your profile information below.", "title": "Invitation confirmed"}}, "vmManager": {"title": "Manage my Shadow PCs"}, "navigation": {"account": {"personal": "My Account", "security": "Security", "title": "Account"}, "billing": {"details": "Billing details", "invoices": "Invoices", "paymentMethod": "Payment method", "title": "Billing"}, "download": "Download", "gameStore": "Game Store", "shadowDrive": "Shadow Drive", "support": "Support", "user": "User", "vms": "PCs", "shadowPC": {"title": "Shadow PC", "users": "Users", "vms": "PCs"}}, "meta": {"account": {"personal": {"title": "Shadow - My account"}, "security": {"title": "Shadow - Security settings"}}, "billing": {"details": {"title": "Shadow - Billing details"}, "invoices": {"title": "Shadow - Invoices"}, "paymentMethod": {"title": "Shadow - Payment method"}}, "default": {"description": "Manage your Shadow account profile, password, security options, product subscriptions, users and payment method", "title": "Shadow Manager"}, "support": {"title": "Shadow - Support"}, "vm": {"manager": {"title": "Shadow - My PCs"}, "user": {"title": "Shadow - Users"}}}, "downloadData": {"modal": {"title": "Download my personal data"}}, "api": {"error": {"description": "An error has occurred fetching your data. Reload the page or contact support", "title": "Something went wrong..."}}, "form": {"address1": {"error": {"required": "Address is required"}, "label": "Address"}, "birthdate": {"error": {"required": "Birthdate is required", "invalid": "You must be 15 or older"}, "label": "Birthdate"}, "city": {"error": {"required": "City is required"}, "label": "City"}, "companyName": {"label": "Company name"}, "country": {"error": {"required": "Country is required"}, "label": "Country"}, "email": {"error": {"email": "Field must be a valid email", "required": "Email is required"}}, "firstname": {"error": {"required": "firstname is required"}, "label": "Firstname"}, "firstName": {"error": {"required": "First name is required"}, "label": "First name"}, "language": {"label": "Language"}, "lastname": {"error": {"required": "lastname is required"}, "label": "Lastname"}, "lastName": {"error": {"required": "Last name is required"}, "label": "Last name"}, "phone": {"label": "Phone number", "error": {"required": "Phone is required"}}, "vatNumber": {"error": {"invalid": "Your VAT number is invalid"}, "label": "VAT number"}, "vmName": {"error": {"maxLength": "PC name must be at most {{ maxLength }} characters", "minLength": "PC name must be at least {{ minLength }} characters", "specialChar": "PC name must not use special characters", "required": "Shadow PC name is required"}, "label": "Name"}, "word": {"info": "Please write <bold>{{ wordToCompare }}</bold> below in order to continue:", "error": {"mismatch": "Sorry the word you entered is different from what we ask you to type. \nPlease verify your input and correct it."}}, "zipcode": {"error": {"required": "Zipcode is required"}, "label": "Zipcode"}, "select": {"automatic": {"label": "Automatic"}, "manual": {"label": "Manual"}, "error": {"default": "Please select an option"}}, "configuration": {"label": "Configuration", "information": "In manual configuration, you will need to set up the language, keyboard, and your Windows PC yourself."}, "keyboard": {"label": "Keyboard"}, "default": {"error": {"maxLength": "Maximum allowed: 64 characters", "required": "Field is required"}}, "periodicity": {"placeholder": "Please select a periodicity", "label": "Periodicity"}}, "global": {"cancel": "Cancel", "confirm": "Confirm", "continue": "Continue", "email": "Email", "invite": "Invite", "loading": "Loading...", "next": "Next", "noResults": "No results", "ok": "OK", "revoke": "Revoke", "send": "Send", "unknown": "Unknown", "skipReason": "I don't want to answer", "contactSupport": "Contact support", "moreInfo": "More info", "new": "New"}, "login": {"error": {"description": "An error occurred when attempting to logged you in. Please try again later or contact support if the problem persists.", "title": "Oops, something is broken"}}, "list": {"user": {"activateMember": {"notification": {"error": "Error reactivating member {{email}}: {{error}}", "success": "Successfully reactivated member {{email}}"}, "label": "Activate this account"}, "deleteMember": {"modal": {"content": "This operation cannot be undone.", "title": "Delete user"}, "notification": {"alreadyAssigned": "Cannot delete, member is currently assigned to a Shadow PC.", "error": "Error deleting member {{email}}: {{error}}", "success": "Successfully deleted member {{email}}"}, "label": "Delete user"}, "resendInvitation": {"notification": {"error": "Error resending invitation to {{email}}: {{error}}", "success": "Successfully resend invitation to {{email}}"}}, "invite": {"error": {"message": "You have already sent an invitation to this email", "title": "An error has occured when sending an invitation"}, "success": {"title": "Invitation sent", "message": "{{ email }} will receive an email shortly inviting him to create an account in your team and get access to his Shadow PC."}}, "updateUserRole": {"notification": {"success": "Successfully updated member {{email}} to {{role}}", "error": "Error updating member {{email}} to {{role}}: {{error}}"}, "demote": {"label": "Demote admin to member"}, "promote": {"label": "Set member as admin"}}, "status": {"pending": "Invitation sent", "expired": "Expired", "disabled": "Deactivated"}, "role": {"owner": "owner", "member": "member", "admin": "admin", "manager": "manager"}, "total": "Total: {{total}} users", "showConfiguration": "Show configuration about this user", "table": {"action": "See more information about this user"}, "searchPlaceholder": "Search for User name / email / tags"}, "vm": {"noVmFound": "You don't have any Shadow PC", "createFirstVmButtonLabel": "Buy your first Shadow PC", "options": {"addVmButtonLabel": "New Shadow PC"}, "vmStatus": {"maintenance": "maintenance", "on_hold": "unpaid", "running": "running", "stopped": "Ready to Launch"}, "subscriptionStatus": {"active": "Active", "cancelled": "Cancelled", "future": "Creating...", "in_trial": "In trial", "non_renewing": "Cancellation in progress", "not_paid": "Unpaid", "paused": "Paused"}, "internalStorage": {"label": "Internal Storage", "value": "{{totalStorageQty, storageUnit}}"}, "user": {"title": "User"}, "alwaysOn": {"tooltip": "With the “Always On” feature, your virtual machines can operate continuously without shutting down automatically after 4 hours of inactivity.", "label": "Shadow PC Always on"}, "created": {"notification": {"success": {"title": "Your Shadow PC creation has started", "message": "The Shadow PC is being setup, it can last up to 1 hour. You will receive an email as soon as your Shadow PC will be available."}}}, "resetVm": "Reset this Shadow PC", "stopVm": {"notification": {"error": "There was an error when trying to stop your Shadow PC, please try again later or contact support.", "success": "You have successfully triggered a stop of your Shadow PC. This process will take around a few minutes."}, "label": "Stop this Shadow PC"}, "renameVm": "Rename this Shadow PC", "deleteVm": "Cancel subscription", "reset": "Reset VM", "assignOrRevokeMember": {"revoke": {"modal": {"content": "The user will not have access to this Shadow PC anymore, but will still remains in your users list.<br/>\n<br/>\nThe Shadow PC will not be deleted.", "title": "Revoke access"}, "notification": {"error": "Error revoking member {{email}}: {{error}}", "success": "Successfully revoke {{email}}"}, "label": "Revoke access"}, "assign": {"placeholder": "Search a user...", "noUser": "No user is assigned.", "label": "Assign", "notification": {"error": "Error assigning member {{email}}: {{error}}", "success": "Successfully assigned {{email}}"}}, "vmCreating": "Cannot assign a user while this PC is being created.", "error": {"vmNotStopped": "Cannot assign or revoke new users until this PC is stopped.", "userAlreadyAssigned": "User already assigned to another Shadow PC."}, "title": "User"}, "creationDate": "Creation Date", "subscriptionRenewal": "Subscription renewal", "datacenter": "Datacenter", "offer": "Offer", "subscriptionEnd": "Next Payment", "status": "Status", "clone": {"noStock": "There is no stock available in this datacenter. Cloning is not possible.", "notCloneable": "Cloning is only available for Shadow PC Pro running Windows Enterprise.", "disabled": "Cannot clone this Shadow PC", "enabled": "Clone this Shadow PC", "notStopped": "This Shadow PC is currently running. Please turn it off to proceed with cloning."}, "total": "Total: {{total}} Shadow PC", "showConfiguration": "Show configuration about this Shadow PC", "table": {"action": "See more information about this Shadow PC"}, "noSearchResults": "No results found", "searchPlaceholder": "Search for VM name / user / tags"}, "heading": {"role": "Role", "created": "Created", "user": "User", "name": "Name", "tags": "Tags", "datacenter": "Datacenter", "status": "Status"}}, "tagManager": {"notification": {"success": "You have successfully set tags.", "error": "There was an error when trying to set tags, please try again later or contact support.", "errorLength": "Tag must be at least {{minLength}}-{{maxLength}} characters long and contain no special characters"}, "noTags": "No existing tags", "placeholder": "Add a tag", "title": "Tags"}}