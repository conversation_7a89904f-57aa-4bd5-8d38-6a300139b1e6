{"name": "manager", "version": "1.0.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "test": "echo \"No test specified\"", "tsc": "tsc --noEmit", "i18n": "node ../scripts/prepare-i18next.js && i18next --silent --config ./i18next-parser-config.cjs && node ../scripts/i18next-parser-postprocess.js", "prebuild": "node ../scripts/copy-i18n-files-to-public.js"}, "dependencies": {"@ant-design/icons": "5.1.4", "@blade-group/shade": "0.1.7", "@didomi/react": "^1.8.1", "@emotion/styled": "^11.8.1", "@flagship.io/react-sdk": "^3.3.0", "@hookform/resolvers": "^3.3.4", "@mui/material": "5.10.2", "@remix-run/dev": "^2.8.1", "@remix-run/node": "^2.8.1", "@remix-run/react": "^2.8.1", "@remix-run/serve": "^2.8.1", "@sentry/vite-plugin": "^3.1.0", "@tanstack/react-query": "^4.0.10", "@tanstack/react-query-devtools": "^4.0.10", "@xstate/react": "3.2.2", "accept-language-parser": "^1.5.0", "antd": "^5.21.0", "antd-style": "^3.6.2", "country-flag-icons": "^1.5.11", "date-fns": "3.3.1", "dayjs": "1.11.13", "i18next": "^23.11.3", "i18next-browser-languagedetector": "^7.2.1", "i18next-fs-backend": "^2.3.1", "i18next-http-backend": "^2.5.1", "immer": "^9.0.12", "isbot": "^4.1.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "match-sorter": "^6.3.1", "notistack": "^2.0.4", "oidc-client-ts": "^2.0.1", "prettier": "^3.2.5", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.28.0", "react-i18next": "^14.1.1", "react-international-phone": "^4.3.0", "react-oidc-context": "^2.1.0", "remix-flat-routes": "^0.6.4", "remix-i18next": "^7.0.2", "remix-utils": "^8.7.0", "sort-by": "^1.2.0", "styled-components": "^6.1.8", "tiny-invariant": "^1.3.1", "types": "1.0.0", "utils": "1.0.0", "xstate": "4.38.2", "yup": "^0.32.11", "zustand": "^3.7.1"}, "devDependencies": {"@types/accept-language-parser": "^1.5.6", "@types/react": "18.2.25", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "8.10.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.3.3", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=18.0.0"}}