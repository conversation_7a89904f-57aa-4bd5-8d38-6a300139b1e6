import { WarningOutlined } from '@ant-design/icons';
import { Alert } from 'antd';
import { useState } from 'react';
import { IMemberDetails, NotificationState } from 'types';

import RetryPayment from '../retryPayment/retryPayment';

import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { useIsSubscriptionOnHold } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { useHasUnpaidInvoice } from '@/app/hooks/reactQuery/user/useUser';

const InfoBanner = () => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const paymentDueInfo = useHasUnpaidInvoice();
  const { isCloudPcSubscriptionOnHold, isDriveSubscriptionOnHold } =
    useIsSubscriptionOnHold();
  const [title, setTitle] = useState('');

  const [retryPaymentState, setRetryPaymentState] =
    useState<NotificationState>();

  if (
    ((isCloudPcSubscriptionOnHold || isDriveSubscriptionOnHold) &&
      paymentDueInfo.hasUnpaidInvoice) ||
    retryPaymentState === 'success'
  ) {
    return (
      <Alert
        icon={
          retryPaymentState === NotificationState.ERROR && <WarningOutlined />
        }
        message={title}
        description={
          <RetryPayment
            paymentDueInfo={paymentDueInfo}
            setRetryPaymentState={setRetryPaymentState}
            setTitle={setTitle}
            isB2b={isB2b}
          />
        }
        type={
          retryPaymentState === NotificationState.SUCCESS
            ? NotificationState.SUCCESS
            : NotificationState.ERROR
        }
        showIcon
      />
    );
  }

  return <></>;
};

export default InfoBanner;
