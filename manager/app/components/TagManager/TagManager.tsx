import { message, Typography } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ITag } from 'types';
import { logError } from 'utils';

import { DrawerSection } from '../ui/Drawer/DrawerSection/DrawerSection';
import LocalLoader from '../ui/Loader/LocalLoader';
import { TagsInput } from '../ui/TagsInput/TagsInput';

import {
  useCreateTag,
  useCurrentMember,
  useUpdateMemberTags,
} from '@/app/hooks/reactQuery/member/useMember';
import { useVmTags } from '@/app/hooks/reactQuery/subscriptions/useSubscription';

interface Props {
  entity: 'vm' | 'user';
  id: string;
  initialTags: ITag[];
}

export const TagManager = ({ entity, id, initialTags }: Props) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();

  const vmTags = useVmTags();
  const userTags = useUpdateMemberTags();
  const createTag = useCreateTag();

  const availableTags = currentMemberQuery.data?.available_tags as ITag[];
  const [appliedTags, setAppliedTags] = useState(initialTags);

  const onSuccess = () => {
    message.success(
      t('tagManager.notification.success', 'You have successfully set tags.'),
    );
  };

  const onError = () => {
    message.error(
      t(
        'tagManager.notification.error',
        'There was an error when trying to set tags, please try again later or contact support.',
      ),
    );
  };

  const saveAllTagsToEntity = async (tags: ITag[], shouldRefetch: boolean) => {
    try {
      if (entity === 'vm') {
        await vmTags.mutateAsync({
          subscriptionId: id,
          tags,
        });
      } else {
        await userTags.mutateAsync({
          memberId: id,
          tags,
        });
      }

      if (shouldRefetch) {
        currentMemberQuery.refetch();
      }

      setAppliedTags(tags);
      onSuccess();
    } catch (e) {
      onError();
      logError('saveAllAppliedTagsToVm', e);
    }
  };

  const createNewTag = async (newTag: string) => {
    try {
      const updatedAvailableTags = await createTag.mutateAsync(newTag);
      if (updatedAvailableTags && updatedAvailableTags.length) {
        const newlyCreatedTag = updatedAvailableTags[0];
        return newlyCreatedTag;
      }
    } catch (e) {
      onError();
      logError('createNewTag', e);
    }
  };

  // Event triggered when the user selects an existing or new option.
  const onSaveConfirm = async (newTagNameValue: string) => {
    const isTagAlreadyAppliedToVm = appliedTags.some(
      tag => tag.name === newTagNameValue,
    );

    if (!newTagNameValue || isTagAlreadyAppliedToVm) {
      return;
    }

    const availableAndUnappliedTag = availableTags
      .filter(availableTag => !appliedTags.includes(availableTag))
      .find(tag => tag.name === newTagNameValue);
    const shouldCreateTag = !availableAndUnappliedTag;

    let newTagToSaveToVm: ITag | undefined;

    if (shouldCreateTag) {
      const newlyCreatedTag = await createNewTag(newTagNameValue);
      if (newlyCreatedTag) {
        newTagToSaveToVm = newlyCreatedTag;
      }
    } else {
      newTagToSaveToVm = availableAndUnappliedTag;
    }

    if (newTagToSaveToVm) {
      await saveAllTagsToEntity([...appliedTags, newTagToSaveToVm], false);
    } else {
      onError();
      logError('onConfirm: newTagToSaveToVm is undefined');
    }
  };

  // Event triggered when the user delete a single tag
  const onDeleteSingleTag = async (tagToRemove: ITag) => {
    const newAppliedTags = appliedTags.filter(
      appliedTag => appliedTag !== tagToRemove,
    );

    saveAllTagsToEntity(newAppliedTags, true);
  };

  // Event triggered when the user deletes all tags
  const onDeleteAllTags = async () => {
    saveAllTagsToEntity([], true);
  };

  if (currentMemberQuery.isLoading) {
    return <LocalLoader />;
  }

  return (
    <DrawerSection>
      <Typography.Title level={4}>
        {t('tagManager.title', 'Tags')}
      </Typography.Title>
      <TagsInput
        isSubmittingTags={vmTags.isLoading}
        availableTags={availableTags}
        appliedTags={appliedTags}
        onSaveConfirm={onSaveConfirm}
        onDeleteSingleTag={onDeleteSingleTag}
        onDeleteAllTags={onDeleteAllTags}
      />
    </DrawerSection>
  );
};
