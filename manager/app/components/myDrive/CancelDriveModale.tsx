import { Flex, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { CancelDriveSteps, ISubscription } from 'types';
import { DATE_FORMAT_BY_MARKET } from 'utils';

import CancelDriveForm from './CancelDrive/CancelDriveForm';
import CancelDriveModalConfirmation from './CancelDrive/CancelDriveModaleConfirmation';
import CancelDriveModalInformation from './CancelDrive/CancelDriveModaleInformation';

import Modal from '@/app/components/ui/Modal/Modal';
import { useDate } from '@/app/hooks/useDate';
interface ICancelDriveProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  subscription: ISubscription;
  email: string | undefined;
}

const CANCEL_DRIVE_STEP: Record<CancelDriveSteps, string> = {
  survey: 'cancel-survey',
  information: 'cancel-information',
  confirmation: 'cancel-confirmation',
};

const CancelDriveModal = ({
  isOpen,
  setIsOpen,
  subscription,
  email,
}: ICancelDriveProps) => {
  const { t } = useTranslation();
  const { getSubscriptionNextBillingDate } = useDate();

  const initialStep = CANCEL_DRIVE_STEP.survey;
  const [step, setStep] = useState<string>(initialStep);
  const [lastBillingDate, setLastBillingDate] = useState<string | null>(null);
  const [cancellationReason, setCancellationReason] = useState<string>('');

  const closeAndResetInitialStep = () => {
    closeModal();
    // Timeout that prevents a flicker by letting the fade out animation end before reseting state.
    setTimeout(() => {
      setStep(initialStep);
    }, 200);
  };

  // We need this useEffect because when the subscription is
  // cancelled from the modal, the lastBillingDate is null.
  useEffect(() => {
    const subscriptionNextBillingDate = getSubscriptionNextBillingDate(
      subscription,
      DATE_FORMAT_BY_MARKET,
    );
    if (subscriptionNextBillingDate) {
      setLastBillingDate(subscriptionNextBillingDate);
    }
  }, [getSubscriptionNextBillingDate, subscription]);

  const getTitle = () => {
    switch (step) {
      case CANCEL_DRIVE_STEP.survey:
        return t(
          'subscription.cancelDrive.survey.title',
          "We're always improving our service and your feedback matters",
        );
      case CANCEL_DRIVE_STEP.information:
        return t(
          'subscription.cancelDrive.information.title',
          "Here's some information before you cancel",
        );
      case CANCEL_DRIVE_STEP.confirmation:
        return t(
          'subscription.cancelDrive.confirmation.title',
          'Your subscription has been canceled',
        );
    }
  };

  const computeSubtitle = () => {
    switch (step) {
      case CANCEL_DRIVE_STEP.information:
        return (
          <Typography.Text>
            <Trans
              i18nKey="subscription.cancelDrive.information.subtitle"
              defaults="If you cancel your subscription, your access to Shadow Drive will end on <bold>{{ lastBillingDate }}</bold>"
              values={{ lastBillingDate }}
              components={{ bold: <strong /> }}
            />
          </Typography.Text>
        );
      case CANCEL_DRIVE_STEP.confirmation:
        return t(
          'subscription.cancelDrive.confirmation.subtitle',
          'We are sad to see you leave.',
        );
    }
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onCancel={closeAndResetInitialStep}
      title={getTitle()}
    >
      <Flex vertical gap={24}>
        {computeSubtitle()}

        {step === CANCEL_DRIVE_STEP.survey && (
          <CancelDriveForm
            onSuccess={({ reason }) => {
              setCancellationReason(reason);
              setStep(CANCEL_DRIVE_STEP.information);
            }}
            onCancel={closeAndResetInitialStep}
          />
        )}
        {step === CANCEL_DRIVE_STEP.information && (
          <CancelDriveModalInformation
            cancellationReason={cancellationReason}
            lastBillingDate={lastBillingDate}
            onCancel={closeAndResetInitialStep}
            onSuccess={() => {
              setStep(CANCEL_DRIVE_STEP.confirmation);
            }}
            subscription={subscription}
          />
        )}
        {step === CANCEL_DRIVE_STEP.confirmation && (
          <CancelDriveModalConfirmation
            lastBillingDate={lastBillingDate}
            email={email}
            onCancel={closeAndResetInitialStep}
          />
        )}
      </Flex>
    </Modal>
  );
};

export default CancelDriveModal;
