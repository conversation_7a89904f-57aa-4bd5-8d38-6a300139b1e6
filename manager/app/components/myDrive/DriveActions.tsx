import { CloseOutlined, EditOutlined, StarOutlined } from '@ant-design/icons';
import { Dropdown, Flex, MenuProps, message, Typography } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  DriveProduct,
  ISubscription,
  ProductId,
  SubscriptionStatus,
} from 'types';
import { logError } from 'utils';

import CancelDriveModal from './CancelDriveModale';

import Button from '@/app/components/ui/Button/Button';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import {
  useIsSubscriptionOnHold,
  useReactivateSubscription,
} from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { SHOP_URL } from '@/app/utils/constants';

interface IDriveActionsProps {
  productId?: string;
  subscription?: ISubscription;
}

const DriveActions = ({ productId, subscription }: IDriveActionsProps) => {
  const { t } = useTranslation();

  const { isDriveSubscriptionOnHold } = useIsSubscriptionOnHold();
  // const shouldDisableCancelButton = isDriveSubscriptionOnHold;

  const shouldDisplayDriveUpgrade =
    !isDriveSubscriptionOnHold && productId === ProductId.DRIVE_B2C_FREE;

  const [isCancelModalOpen, setIsCancelModalOpen] = useState<boolean>(false);
  const currentMemberQuery = useCurrentMember();
  const userDetails = currentMemberQuery.data?.user;
  const [isSubmittingReactivateDrive, setIsSubmittingReactivateDrive] =
    useState<boolean>(false);

  const reactivateSubscription = useReactivateSubscription();

  const handleReactivateDrive = async () => {
    setIsSubmittingReactivateDrive(true);

    try {
      if (!subscription) {
        throw new Error('No subscription provided!');
      }

      await reactivateSubscription.mutateAsync(subscription.id);

      message.success(
        t(
          'subscription.reactivateVm.notification.success',
          'You have successfully reactivated your subscription.',
        ),
      );
    } catch (e) {
      message.error(
        t(
          'subscription.reactivateVm.notification.error',
          'We could not reactivate your subscription, please try again later.',
        ),
      );
      logError('handleReactivateDrive', e);
    }

    setIsSubmittingReactivateDrive(false);
  };

  const items: MenuProps['items'] = [
    {
      key: 'drive-upgrade',
      icon: <StarOutlined />,
      label: (
        // We need to keep the productType query param in order to work properly
        // TODO : remove it after drive upgrade funnel refactor
        <Typography.Link
          href={`${SHOP_URL}?funnel=drive_upgrade&productType=${DriveProduct.DRIVE_PREMIUM}`}
        >
          {t(
            'subscription.plan.drive.action.upgrade',
            'Switch to premium plan (2 TB)',
          )}
        </Typography.Link>
      ),
    },
    {
      key: 'drive-cancel',
      icon: <CloseOutlined />,
      label: (
        <Typography.Link onClick={() => setIsCancelModalOpen(true)}>
          {t('subscription.plan.drive.action.cancel', 'Cancel my subscription')}
        </Typography.Link>
      ),
      danger: true,
      // TODO:
      // disabled: { shouldDisableCancelButton },
      // onClick={() => setIsCancelModalOpen(true)}
    },
  ];

  return (
    <>
      {subscription?.status === SubscriptionStatus.ACTIVE && (
        <Flex align="left">
          {shouldDisplayDriveUpgrade ? (
            <Dropdown
              data-testId="subscription-drive-dropdown"
              menu={{
                items,
              }}
            >
              <Button
                data-testid="edit_drive-button"
                size="middle"
                secondary
                icon={<EditOutlined />}
              >
                {t('subscription.plan.drive.action.edit', 'Edit')}
              </Button>
            </Dropdown>
          ) : (
            <Button
              data-testid="cancel_drive-button"
              danger
              onClick={() => setIsCancelModalOpen(true)}
            >
              {t(
                'subscription.plan.drive.action.cancel',
                'Cancel my subscription',
              )}
            </Button>
          )}
        </Flex>
      )}
      {/* TODO: Mehdi: Button can be clickable a second time after reactivate, just before display 'Cancel my subscription' */}
      {subscription?.status === SubscriptionStatus.NON_RENEWING && (
        <Button
          data-testid="reactivate_drive-button"
          key="drive-reactivate"
          loading={isSubmittingReactivateDrive}
          onClick={handleReactivateDrive}
        >
          {t('subscription.plan.drive.action.reactivate', 'Reactivate')}
        </Button>
      )}
      {subscription?.status === SubscriptionStatus.CANCELLED && (
        <Button
          data-testid="resubscribe_drive-button"
          key="drive-resubscribe"
          href={SHOP_URL}
        >
          {t('subscription.plan.drive.action.resubscribe', 'Resubscribe')}
        </Button>
      )}
      <CancelDriveModal
        isOpen={isCancelModalOpen}
        setIsOpen={setIsCancelModalOpen}
        subscription={subscription as ISubscription}
        email={userDetails?.email}
      />
    </>
  );
};

export default DriveActions;
