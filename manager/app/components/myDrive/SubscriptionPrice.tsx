import { Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

import { usePrice } from '@/app/hooks/usePrice';
import { IPrice } from '@/app/types/subscriptions';

const SubscriptionPrice = ({
  amount,
  period,
  periodUnit,
  isAddon = false,
  hasStartingPrice = false,
}: IPrice) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();

  return (
    <Flex align="baseline" vertical style={{ flexShrink: 0 }}>
      <Typography.Text
        color="primary"
        strong
        style={{
          color: 'var(--ant-color-primary)',
          fontSize: 'var(--ant-font-size-xl)',
        }}
      >
        {hasStartingPrice && (
          <>{t('subscription.details.hasStartingPrice', 'Starting')} </>
        )}
        {isAddon && '+'}
        {formatPrice(amount)}
      </Typography.Text>
      <Typography.Text
        style={{
          color: 'var(--ant-color-primary)',
          fontSize: 'var(--ant-font-size-sm)',
        }}
      >
        {t(`subscription.periodicity.adjective.${periodUnit}`, {
          count: period,
        })}
      </Typography.Text>
    </Flex>
  );
};

export default SubscriptionPrice;
