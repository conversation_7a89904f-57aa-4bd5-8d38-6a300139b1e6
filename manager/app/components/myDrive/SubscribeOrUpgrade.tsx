import { ArrowRightOutlined } from '@ant-design/icons';
import { Flex, Grid, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DriveProduct,
  IAnyOffer,
  ISubscription,
  OfferPeriodUnit,
  ProductFamilyId,
  ProductId,
  SubscriptionStatus,
} from 'types';

import SubscriptionPrice from './SubscriptionPrice';

import { useIsSubscriptionOnHold } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { useCatalog } from '@/app/hooks/useCatalog';
import { SHOP_URL } from '@/app/utils/constants';

interface ISubscribeOrUpgrade {
  subscriptionStatus: SubscriptionStatus;
  driveOffer: IAnyOffer | undefined;
  currentSubscription?: ISubscription;
}

const SubscribeOrUpgrade = ({
  subscriptionStatus,
  currentSubscription,
  driveOffer,
}: ISubscribeOrUpgrade) => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;

  const screens = useBreakpoint();
  const catalogQuery = useCatalog(currentSubscription?.id);
  const catalog = catalogQuery.data;

  const { isDriveSubscriptionOnHold } = useIsSubscriptionOnHold();

  const shouldDisplayDriveUpgrade =
    !isDriveSubscriptionOnHold &&
    driveOffer?.product_id === ProductId.DRIVE_B2C_FREE;

  if (!catalog || driveOffer?.product_id === ProductId.DRIVE_B2C_PREMIUM) {
    return <></>;
  }
  const premiumDriveOffer = Object.values(catalog.offers.byId).find(offer => {
    return (
      offer.product_id === ProductId.DRIVE_B2C_PREMIUM &&
      offer.periodicity === driveOffer?.periodicity
    );
  });

  const hasDriveFree =
    shouldDisplayDriveUpgrade &&
    premiumDriveOffer &&
    subscriptionStatus === SubscriptionStatus.ACTIVE;

  return (
    <>
      {!driveOffer && (
        <Typography.Link
          href={`${SHOP_URL}b2c?familyId=${ProductFamilyId.SHADOWDRIVE}`}
          style={{
            color: 'var(--ant-color-primary)',
            fontSize: 'var(--ant-font-size-lg)',
          }}
        >
          <Flex
            justify="space-between"
            align="center"
            style={{
              padding: screens.md ? '24px' : '12px',
              borderRadius: '8px',
              backgroundColor: '#ebeefa',
            }}
            gap={screens.md ? 0 : 12}
          >
            <Flex
              gap={screens.md ? '16px' : '8px'}
              style={{ textAlign: 'center' }}
            >
              {t(
                'subscription.plan.addon.drive.get.link',
                'Get your Shadow Drive',
              )}
              <ArrowRightOutlined />
            </Flex>
            <SubscriptionPrice
              amount={0}
              period={1}
              periodUnit={OfferPeriodUnit.MONTH}
            />
          </Flex>
        </Typography.Link>
      )}
      {/* User has already a Drive Free */}
      {hasDriveFree && (
        // We need to keep the productType query param in order to work properly
        // TODO : remove it after drive upgrade funnel refactor
        <Typography.Link
          href={`${SHOP_URL}?funnel=drive_upgrade&productType=${DriveProduct.DRIVE_PREMIUM}`}
          style={{
            color: 'var(--ant-color-primary)',
            fontSize: 'var(--ant-font-size-lg)',
          }}
        >
          <Flex
            justify="space-between"
            align="center"
            style={{
              padding: screens.md ? '24px' : '12px',
              borderRadius: '8px',
              backgroundColor: '#ebeefa',
            }}
            gap={screens.md ? 0 : 12}
          >
            <Flex gap={16}>
              {t(
                'subscription.plan.addon.drive.upgrade.link',
                'Upgrade to Premium (2 TB)',
              )}

              <ArrowRightOutlined />
            </Flex>
            <SubscriptionPrice
              amount={premiumDriveOffer.price * 100}
              period={premiumDriveOffer.period}
              periodUnit={premiumDriveOffer.period_unit}
            />
          </Flex>
        </Typography.Link>
      )}
    </>
  );
};

export default SubscribeOrUpgrade;
