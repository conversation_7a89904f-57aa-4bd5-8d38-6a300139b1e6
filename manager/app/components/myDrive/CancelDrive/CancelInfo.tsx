import { Badge, Flex, Typography } from 'antd';
import { ReactNode } from 'react';
import '../../../styles/global.css';

interface ICancelInfosProps {
  title: string;
  items: Array<{
    label: string;
    description: ReactNode;
  }>;
}

const CancelInfo = ({ title, items }: ICancelInfosProps) => {
  return (
    <Flex vertical>
      <Typography.Title level={5} style={{ color: '#3653CC' }}>
        {title}
      </Typography.Title>
      <Flex vertical gap={8}>
        {items.map(({ label, description }, index) => (
          <Flex vertical key={index}>
            <Badge
              color="black"
              text={<Typography.Text strong>{label}</Typography.Text>}
            />
            <Typography.Text>{description}</Typography.Text>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};

export default CancelInfo;
