import { Flex, Form, Input } from 'antd';
import { useCallback } from 'react';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import useCancelDriveForm from '@/app/hooks/form/useCancelDriveForm';
import { IFormProps } from '@/app/types/forms';

const CancelDriveForm = ({
  onSuccess = () => null,
  onError = () => null,
  onCancel = () => null,
}: IFormProps) => {
  const { t } = useTranslation();
  const { onSubmit, control, isSubmitting } = useCancelDriveForm(
    onSuccess,
    onError,
  );

  const handleSubmit = useCallback(async () => {
    await onSubmit();
  }, [onSubmit]);

  return (
    <Flex gap={16} vertical>
      <Form
        id="cancel_drive_form"
        onFinish={handleSubmit}
        style={{ marginTop: '24px' }}
        layout="vertical"
      >
        <Flex vertical gap={16}>
          <Controller
            name="reason"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Form.Item
                label={t(
                  'subscription.cancelDrive.reason.placeholder',
                  'Please, describe why you want to cancel your Drive subscription',
                )}
                help={error && error.message}
                hasFeedback={!!error}
              >
                <Input.TextArea
                  {...field}
                  id="coupon-input"
                  data-testid="coupon-input"
                  rows={5}
                  style={{ resize: 'none' }}
                />
              </Form.Item>
            )}
          />
          <Button
            data-testid="cancel_drive-continue-button"
            block
            htmlType="submit"
            loading={isSubmitting}
          >
            {t('global.next', 'Next')}
          </Button>
          <Button
            data-testid="cancel_drive-cancel-button"
            type="default"
            onClick={onCancel}
            disabled={isSubmitting}
            block
          >
            {t('global.cancel', 'Cancel')}
          </Button>
        </Flex>
      </Form>
    </Flex>
  );
};

export default CancelDriveForm;
