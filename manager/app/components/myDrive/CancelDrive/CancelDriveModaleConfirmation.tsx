import { Flex, Typography } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';

interface ICancelDriveModalConfirmationProps {
  lastBillingDate: string | null;
  email: string | undefined;
  onCancel: () => void;
}

const CancelDriveModalConfirmation = ({
  lastBillingDate,
  email,
  onCancel,
}: ICancelDriveModalConfirmationProps) => {
  const { t } = useTranslation();

  return (
    <>
      <Flex vertical gap={16}>
        <Typography.Text>
          <Trans
            i18nKey="subscription.cancelDrive.confirmation.subscription.item-1"
            defaults="You will still have access to your Shadow Drive until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>"
            values={{ lastBillingDate }}
            components={{ bold: <strong /> }}
          />
        </Typography.Text>

        <Typography.Text>
          <Trans
            i18nKey="subscription.cancelDrive.confirmation.subscription.item-2"
            defaults="We will send you a confirmation email to <bold>{{ email }}</bold> shortly."
            values={{ email }}
            components={{ bold: <strong /> }}
          />
        </Typography.Text>

        <Typography.Text>
          {t(
            'subscription.cancelDrive.confirmation.subscription.item-3',
            "You won't be charged anymore.",
          )}
        </Typography.Text>

        <Typography.Text>
          <Trans
            i18nKey="subscription.cancelDrive.confirmation.subscription.changedYourMind"
            defaults="We are always improving Shadow Drive so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime."
            components={{ bold: <strong /> }}
          />
        </Typography.Text>
      </Flex>
      <Button
        data-testid="cancel_drive-confirm-button"
        onClick={onCancel}
        block
      >
        {t('global.ok', 'OK')}
      </Button>
    </>
  );
};

export default CancelDriveModalConfirmation;
