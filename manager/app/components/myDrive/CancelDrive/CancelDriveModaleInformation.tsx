import { Flex, message } from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ISubscription } from 'types';
import { logError } from 'utils';

import CancelInfo from './CancelInfo';

import Button from '@/app/components/ui/Button/Button';
import { useCancelSubscription } from '@/app/hooks/reactQuery/subscriptions/useSubscription';

interface ICancelDriveModalInformationProps {
  cancellationReason: string;
  lastBillingDate: string | null;
  onCancel: () => void;
  onSuccess: () => void;
  subscription: ISubscription;
}

const CancelDriveModalInformation = ({
  cancellationReason,
  lastBillingDate,
  onCancel,
  onSuccess,
  subscription,
}: ICancelDriveModalInformationProps) => {
  const { t } = useTranslation();
  const cancelSubscription = useCancelSubscription();
  const [isCancellingDrive, setIsCancellingDrive] = useState<boolean>(false);

  const handleCancelDrive = async () => {
    setIsCancellingDrive(true);

    try {
      await cancelSubscription.mutateAsync({
        cancellationReasons: { comment: cancellationReason },
        subscriptionId: subscription.id,
      });

      onSuccess();
    } catch (e) {
      onCancel();
      message.error(
        t(
          'subscription.cancelDrive.notification.error',
          'There was an error when trying to cancel your Drive subscription, please try again later or contact support.',
        ),
      );
      logError('handleCancelDrive', e);
    } finally {
      setIsCancellingDrive(false);
    }
  };

  return (
    <Flex vertical gap={24}>
      <CancelInfo
        title={t(
          'subscription.cancelDrive.information.whenSubscriptionEnds.title',
          'When your subscription ends',
        )}
        items={[
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionEnds.shadowAccess',
              "You'll lose access to your Shadow Drive",
            ),
            description: t(
              'subscription.cancelDrive.information.whenSubscriptionEnds.dataAccess',
              "You'll no longer be able to access your files and they will be permanently deleted.",
            ),
          },
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionEnds.reSubscribe.title',
              'Restart your subscription anytime!',
            ),
            description: (
              <Trans
                i18nKey="subscription.cancelDrive.information.whenSubscriptionEnds.description"
                defaults="If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!"
                values={{ lastBillingDate }}
                components={{ bold: <strong /> }}
              />
            ),
          },
        ]}
      />
      <CancelInfo
        title={t(
          'subscription.cancelDrive.information.whenSubscriptionActive.title',
          'While your subscription is still active',
        )}
        items={[
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.enjoy',
              'Enjoy the most out of Shadow Drive',
            ),
            description: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.access',
              'You can still access your Shadow Drive until the end of your subscription.',
            ),
          },
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.backupTip1',
              "Don't forget to back up your files",
            ),
            description: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.backupTip2',
              'Make sure to transfer important files from Shadow Drive to your local PC. Your data will be deleted after your subscription ends.',
            ),
          },
        ]}
      />

      <Flex vertical gap={16}>
        <Button
          data-testid="cancel_drive-confirm-button"
          loading={isCancellingDrive}
          onClick={handleCancelDrive}
          block
        >
          {t('global.confirm', 'Confirm')}
        </Button>
        {!isCancellingDrive && (
          <Button
            data-testid="cancel_drive-cancel-button"
            type="default"
            onClick={onCancel}
            block
          >
            {t('global.cancel', 'Cancel')}
          </Button>
        )}
      </Flex>
    </Flex>
  );
};

export default CancelDriveModalInformation;
