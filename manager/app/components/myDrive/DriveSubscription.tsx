import { Badge, Flex, Grid, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  IAnyOffer,
  ISubscription,
  ProductType,
  SubscriptionStatus,
} from 'types';
import { findSubscriptionItemByType } from 'utils';

import DriveActions from './DriveActions';
import SubscriptionPrice from './SubscriptionPrice';

import Button from '@/app/components/ui/Button/Button';
import { useDate } from '@/app/hooks/useDate';
import { DRIVE_URL, STATUS_DRIVE } from '@/app/utils/constants';

interface IDriveSubscription {
  driveOffer: IAnyOffer | undefined;
  currentSubscription?: ISubscription;
}

const DriveSubscription = ({
  currentSubscription,
  driveOffer,
}: IDriveSubscription) => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;

  const screens = useBreakpoint();
  const { getSubscriptionStartDate } = useDate();

  const currentSubscriptionItem = findSubscriptionItemByType(
    currentSubscription,
    ProductType.PLAN,
  );
  const amount = currentSubscriptionItem?.price;
  const subscription = currentSubscription;

  const status =
    (currentSubscription?.status as SubscriptionStatus) ||
    SubscriptionStatus.ACTIVE;

  const isSubscriptionActive = status === SubscriptionStatus.ACTIVE;
  const isSubscriptionNonRenewing = status === SubscriptionStatus.NON_RENEWING;

  return (
    <>
      <Flex vertical gap={24}>
        <Flex
          justify="space-between"
          align={'center'}
          vertical={screens.md ? false : true}
        >
          <Typography.Title
            level={4}
            style={{ color: 'var(--ant-color-primary)' }}
          >
            {t('subscription.plan.drive.title', 'My subscription')}
          </Typography.Title>
          {!!amount && !!driveOffer && (
            <Flex justify={'center'}>
              <SubscriptionPrice
                id={subscription?.id}
                amount={amount}
                period={driveOffer?.period}
                periodUnit={driveOffer?.period_unit}
              />
            </Flex>
          )}
        </Flex>
        {subscription && (
          <Flex
            justify="space-between"
            align={'center'}
            vertical={screens.md ? false : true}
            gap={screens.md ? 16 : 24}
          >
            <Typography.Text style={{ textAlign: 'center' }}>
              <Badge
                status={STATUS_DRIVE[status]}
                style={{ marginRight: '8px' }}
              />
              {t('subscription.plan.details', {
                defaultValue: '{{planName}} ({{status}}{{startDate}})',
                planName: t(
                  `subscription.details.name.${driveOffer?.product_id}`,
                ),
                status: t(`subscription.details.status.${subscription.status}`),
                startDate: getSubscriptionStartDate(
                  subscription as ISubscription,
                ),
              })}
            </Typography.Text>
            <DriveActions
              productId={driveOffer?.product_id}
              subscription={subscription}
            />
          </Flex>
        )}
        {/* Display dropdown menu with actions if active and can upgrade */}
        {(isSubscriptionActive || isSubscriptionNonRenewing) && (
          <Flex justify={screens.md ? 'flex-end' : 'center'}>
            <Button
              data-testid="drive_app-button"
              href={DRIVE_URL}
              target="_blank"
              title={t(
                'subscription.plan.drive.application.link.title',
                'Go to Shadow Drive application',
              )}
            >
              {t(
                'subscription.plan.drive.application.link.label',
                'Access my Shadow Drive',
              )}
            </Button>
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default DriveSubscription;
