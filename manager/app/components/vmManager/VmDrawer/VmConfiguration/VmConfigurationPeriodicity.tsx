import { useTranslation } from 'react-i18next';
import { ICatalog } from 'types';
import { getOfferFromId } from 'utils';

import VmConfigurationCard from './VmConfigurationCard/VmConfigurationCard';
import { VmActionType } from '../../VmActions/VmActionModal';

import { ClockIcon, EditIcon } from '@/app/components/Icon';
import Button from '@/app/components/ui/Button/Button';
import { useCanUpdateBillingPeriodicity } from '@/app/hooks/useCanUpdateBillingPeriodicity';
import { IVmManager } from '@/app/types/adminManagers';

interface IVmConfigurationPeriodicity {
  showAction: boolean;
  isCatalogLoading: boolean;
  setActionModal: (action: VmActionType | undefined) => void;
  vm: IVmManager;
  catalog: ICatalog | undefined;
}

const VmConfigurationPeriodicity = ({
  catalog,
  isCatalogLoading,
  showAction,
  setActionModal,
  vm,
}: IVmConfigurationPeriodicity) => {
  const { t } = useTranslation();

  const currentOffer = getOfferFromId(catalog, vm.subscription.plan_id ?? '');

  const { canUpdateBillingPeriodicity, isLoading } =
    useCanUpdateBillingPeriodicity(vm.subscription);

  return (
    <VmConfigurationCard
      isLoading={isCatalogLoading}
      icon={<ClockIcon style={{ fontSize: '16px' }} />}
      title={t(
        'subscription.vmDetails.vm.periodicity.label',
        'Subscription periodicity',
      )}
      value={t(
        `subscription.periodicity.relative.${currentOffer?.period_unit}`,
        {
          count: currentOffer?.period,
        },
      )}
      actionNode={
        showAction &&
        canUpdateBillingPeriodicity && (
          <Button
            data-testid="change_vm_periodicity-button"
            onClick={() => {
              setActionModal('changePeriodicity');
            }}
            size="small"
            type="primary"
            shape="circle"
            color="primary"
            variant="link"
            loading={isLoading}
            icon={<EditIcon style={{ fontSize: 16 }} />}
            title={t(
              'subscription.plan.vm.action.changeVmPeriodicity',
              'Change periodicity',
            )}
          />
        )
      }
    />
  );
};

export default VmConfigurationPeriodicity;
