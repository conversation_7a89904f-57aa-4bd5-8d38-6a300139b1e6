import { CrownOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import VmConfigurationCard from './VmConfigurationCard/VmConfigurationCard';
import { VmActionType } from '../../VmActions/VmActionModal';

import { EditIcon } from '@/app/components/Icon';
import Button from '@/app/components/ui/Button/Button';
import { IVmManager } from '@/app/types/adminManagers';

interface IVmConfigurationPlan {
  showAction: boolean;
  isCatalogLoading: boolean;
  vm: IVmManager;
  setActionModal: (action: VmActionType) => void;
}
const VmConfigurationPlan = ({
  showAction,
  isCatalogLoading,
  vm,
  setActionModal,
}: IVmConfigurationPlan) => {
  const { t } = useTranslation();

  const handleChangePlan = () => {
    setActionModal('changePlan');
  };

  return (
    <VmConfigurationCard
      isLoading={isCatalogLoading}
      icon={<CrownOutlined style={{ fontSize: '16px' }} />}
      title={t('list.vm.offer', 'Offer')}
      value={t(`subscription.details.name.${vm.type}`)}
      actionNode={
        showAction && (
          <Button
            data-testid="change_vm_plan-button"
            onClick={() => {
              handleChangePlan();
            }}
            size="small"
            type="primary"
            shape="circle"
            color="primary"
            variant="link"
            icon={<EditIcon style={{ fontSize: 16 }} />}
            title={t(
              'subscription.plan.vm.action.changePlan.title',
              'Change plan',
            )}
          />
        )
      }
    />
  );
};

export default VmConfigurationPlan;
