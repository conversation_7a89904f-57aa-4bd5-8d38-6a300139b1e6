import { Input } from 'antd';
import { useTranslation } from 'react-i18next';

import useStyles from './VmSearch.styles';

export const VmSearch = ({
  searchQuery,
  setSearchQuery,
  isLoading = false,
}: {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  isLoading?: boolean;
}) => {
  const { styles } = useStyles();
  const { t } = useTranslation();

  // TODO: check state when doing multiple searches
  // TODO: yarn i18n + add on crowdin

  return (
    <Input.Search
      loading={isLoading}
      className={styles.search}
      placeholder={t(
        'list.vm.searchPlaceholder',
        'Search for VM name / user / tags',
      )}
      size="large"
      value={searchQuery}
      onChange={e => setSearchQuery(e.target.value)}
      onSearch={value => setSearchQuery(value)}
      onClear={() => setSearchQuery('')}
      allowClear
    />
  );
};
