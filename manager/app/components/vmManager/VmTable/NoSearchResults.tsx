import { Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

const NoSearchResults = () => {
  const { t } = useTranslation();

  // TODO: yarn i18n + add on crowdin
  return (
    <Flex gap="middle" vertical align="center" style={{ padding: '40px 0' }}>
      <Typography.Text>
        {t('list.vm.noSearchResults', 'No results found')}
      </Typography.Text>
    </Flex>
  );
};

export default NoSearchResults;
