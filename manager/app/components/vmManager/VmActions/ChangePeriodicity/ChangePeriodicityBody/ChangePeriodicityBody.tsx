import { Divider, Flex, Typography } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from './ChangePeriodicityBody.style';

interface IChangeVmPeriodicityModalBodyProps {
  nextBillingDate: string;
  newPrice?: string;
  relativeDuration: string;
}

const ChangeVmPeriodicityModalBody = ({
  nextBillingDate,
  newPrice,
  relativeDuration,
}: IChangeVmPeriodicityModalBodyProps) => {
  const { t } = useTranslation();
  const { styles } = useStyles();

  return (
    <Flex vertical gap="middle" className={styles.changePeriodicity__wrapper}>
      <Flex vertical>
        <Typography.Text className={styles.changePeriodicity__subtitle}>
          {t(
            'subscription.changeVmPeriodicity.modal.bullet-1.title',
            'Your change will be effective at your next billing date',
          )}
        </Typography.Text>
        <Typography.Text>
          <Trans
            i18nKey="subscription.changeVmPeriodicity.modal.bullet-1.description"
            defaults="Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment."
            values={{
              nextBillingDate,
              newPrice,
              relativeDuration,
            }}
            components={{ bold: <strong /> }}
          />
        </Typography.Text>
      </Flex>
      <Divider />
      <Flex vertical>
        <Typography.Text className={styles.changePeriodicity__subtitle}>
          {t(
            'subscription.changeVmPeriodicity.modal.bullet-2.title',
            'Cancel your change anytime',
          )}
        </Typography.Text>
        <Typography.Text>
          <Trans
            i18nKey="subscription.changeVmPeriodicity.modal.bullet-2.description"
            defaults="If you cancel your change before <bold>{{ nextBillingDate }}</bold>, you'll be able to keep your current subscription!"
            values={{
              nextBillingDate,
            }}
            components={{ bold: <strong /> }}
          />
        </Typography.Text>
      </Flex>
    </Flex>
  );
};

export default ChangeVmPeriodicityModalBody;
