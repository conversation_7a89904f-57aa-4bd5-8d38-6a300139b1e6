import { DownOutlined } from '@ant-design/icons';
import { Flex, Form, message, Select } from 'antd';
import { useModifySubscription } from 'hooks';
import { compact, isEmpty } from 'lodash';
import { useEffect } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ISubscription, OfferPeriodicity, OfferPeriodUnit } from 'types';
import {
  computePercentGain,
  computeSubscriptionItemPricesWithTargetPeriodicity,
  formatRelativePeriodicity,
  getMaximalOffer,
  getMinimalOffer,
  getOfferFromId,
  getOffers,
  trackEvent,
} from 'utils';

import ChangeVmPeriodicityModalBody from './ChangePeriodicityBody/ChangePeriodicityBody';

import Button from '@/app/components/ui/Button/Button';
import ShadowLoader from '@/app/components/ui/Loader/ShadowLoader';
import { useCanUpdateBillingPeriodicity } from '@/app/hooks/useCanUpdateBillingPeriodicity';
import { useCatalog } from '@/app/hooks/useCatalog';
import { useChangePeriodicityEstimate } from '@/app/hooks/useChangePeriodicityEstimate';
import { IFormProps, SelectOption } from '@/app/types/forms';

interface IChangePeriodicityForm extends IFormProps {
  subscription: ISubscription;
  isOpen: boolean;
}

interface FormValues {
  periodicity: OfferPeriodicity;
}

const ChangePeriodicityForm = ({
  onCancel,
  subscription,
}: IChangePeriodicityForm) => {
  const { t } = useTranslation();
  const { data: catalog } = useCatalog();
  const [form] = Form.useForm();
  const { mutateAsync, isLoading } = useModifySubscription(subscription.id);
  const { validPeriodicities } = useCanUpdateBillingPeriodicity(subscription);

  const getPeriodicitySelectOptions = () => {
    const currentOffer = getOfferFromId(catalog, subscription?.plan_id ?? '');

    if (!currentOffer) {
      return [];
    }

    const allOffersForProduct = getOffers(
      catalog,
      currentOffer?.product_id,
      offer =>
        validPeriodicities.includes(offer.periodicity) &&
        offer.periodicity !== currentOffer?.periodicity,
    );

    const periodicitySelectOptions: SelectOption[] = compact(
      allOffersForProduct.map(offerForProduct => {
        const minimalOffer = getMinimalOffer([currentOffer, offerForProduct]);
        const maximalOffer = getMaximalOffer([currentOffer, offerForProduct]);

        const isOfferForProductTheCheapest =
          minimalOffer.id === offerForProduct.id;

        const percentGain = isOfferForProductTheCheapest
          ? computePercentGain(minimalOffer, maximalOffer)
          : 0;

        const label = `${t(
          `subscription.periodicity.relative.${offerForProduct.period_unit}`,
          {
            count: offerForProduct.period,
          },
        )} ${
          percentGain > 0
            ? t('subscription.periodicity.save', {
                defaultValue: '(save {{percent}}%)',
                percent: percentGain || 0,
              })
            : ''
        }`;

        return {
          label,
          value: offerForProduct.periodicity,
        };
      }),
    );

    return periodicitySelectOptions;
  };

  const periodicitySelectOptions = getPeriodicitySelectOptions();

  const selectedPeriodicity: OfferPeriodicity | undefined = Form.useWatch(
    'periodicity',
    form,
  );

  const splitedSelectedPeriodicity = selectedPeriodicity?.split('-');
  const selectedPeriod = Number(splitedSelectedPeriodicity?.[0]);
  const selectedPeriodUnit = splitedSelectedPeriodicity?.[1] as OfferPeriodUnit;

  const {
    isEstimationLoading,
    formattedNewPrice,
    formattedSubscriptionNextBillingDate,
    hasEstimationError,
  } = useChangePeriodicityEstimate({
    subscription,
    selectedPeriodicity,
  });

  // Reset the form if there is an error
  useEffect(() => {
    if (hasEstimationError) {
      form.resetFields(['periodicity']);
    }
  }, [form, hasEstimationError]);

  const relativeDuration = formatRelativePeriodicity(
    selectedPeriod,
    selectedPeriodUnit,
  );

  const handleFormSubmit = (values: FormValues) => {
    const itemPricesPayload =
      computeSubscriptionItemPricesWithTargetPeriodicity(
        catalog,
        subscription,
        values.periodicity,
      );

    if (isEmpty(itemPricesPayload)) {
      message.error(
        `Empty item_prices payload return for subscription with id '${subscription.id}'`, // TODO more gently message
      );
    }

    mutateAsync({
      item_prices: itemPricesPayload,
      zipcode: null,
    })
      .then(() => {
        message.success(
          <Trans
            i18nKey="subscription.changeVmPeriodicity.success.content"
            defaults="Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment."
            values={{
              nextBillingDate: formattedSubscriptionNextBillingDate,
              newPrice: formattedNewPrice,
              relativeDuration,
            }}
            components={{ bold: <strong /> }}
          />,
        );
        trackEvent('click', {
          event_category: 'change_offer_periodicity',
          event_label: 'change_offer_periodicity_confirmed',
        });
      })
      .catch(() => {
        message.error(
          `${t(
            'subscription.changeVmPeriodicity.updateApiError',
            'An error occurred while updating your subscription, please try again later or contact support.',
          )}`,
        );
      })
      .finally(onCancel);
  };

  const initialValues = {
    periodicity: null,
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFormSubmit}
      requiredMark={false}
      initialValues={initialValues}
    >
      <Form.Item
        name="periodicity"
        required
        label={t('form.periodicity.label', 'Periodicity')}
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Select
          id="periodicity"
          disabled={isEstimationLoading}
          placeholder={t(
            'form.periodicity.placeholder',
            'Please select a periodicity',
          )}
          data-testid="change_vm_periodicity-select"
          suffixIcon={
            isEstimationLoading ? <ShadowLoader size={24} /> : <DownOutlined />
          }
          options={periodicitySelectOptions.map(option => ({
            ...option,
            'data-testid': `change_vm_periodicity-${option.value}-option`,
          }))}
        />
      </Form.Item>
      {!isEstimationLoading && formattedNewPrice && selectedPeriodicity && (
        <ChangeVmPeriodicityModalBody
          newPrice={formattedNewPrice}
          nextBillingDate={formattedSubscriptionNextBillingDate}
          relativeDuration={formatRelativePeriodicity(
            selectedPeriod,
            selectedPeriodUnit,
          )}
        />
      )}
      <Flex vertical gap="middle">
        <Button
          data-testid="change_vm_periodicity-confirm-button"
          disabled={isEstimationLoading || !selectedPeriodicity}
          loading={isLoading}
          block
          htmlType="submit"
        >
          {t('global.confirm', 'Confirm')}
        </Button>
        <Button
          data-testid="change_vm_periodicity-cancel-button"
          type="default"
          onClick={onCancel}
          block
          disabled={isLoading}
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Form>
  );
};

export default ChangePeriodicityForm;
