import { Space, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { ISubscription } from 'types';

import ChangePeriodicityForm from './ChangePeriodicityForm';

import Modal from '@/app/components/ui/Modal/Modal';

interface IChangePeriodicity {
  closeModal: () => void;
  isOpen: boolean;
  subscription: ISubscription;
}

const ChangePeriodicity = ({
  closeModal,
  isOpen,
  subscription,
}: IChangePeriodicity) => {
  const { t } = useTranslation();

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      onCancel={closeModal}
      open={isOpen}
      title={t(
        'subscription.changeVmPeriodicity.modal.title',
        'Change your periodicity',
      )}
    >
      <Space align="center" direction="vertical">
        <Typography.Text>
          {t(
            'subscription.changeVmPeriodicity.modal.subtitle',
            'Customize billing cycles for better control over your finances.',
          )}
        </Typography.Text>
      </Space>
      <ChangePeriodicityForm
        subscription={subscription}
        onCancel={closeModal}
        isOpen={isOpen}
      />
    </Modal>
  );
};

export default ChangePeriodicity;
