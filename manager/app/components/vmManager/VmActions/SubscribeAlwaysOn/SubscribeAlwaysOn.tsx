import { Alert, Flex } from 'antd';
import { Trans, useTranslation } from 'react-i18next';
import { AddonType, ICatalog, NotificationState, ProductType } from 'types';
import {
  getAddonOfferToDisplay,
  getOfferFromId,
  getOffers,
  getProductAddonsOrChargesDetails,
} from 'utils';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { usePrice } from '@/app/hooks/usePrice';
import { IVmManager } from '@/app/types/adminManagers';
import { SHOP_URL } from '@/app/utils/constants';

interface ISubscribeAlwaysOn {
  closeModal: () => void;
  isOpen: boolean;
  subscription: IVmManager;
  catalog?: ICatalog;
}

const SubscribeAlwaysOn = ({
  catalog,
  closeModal,
  isOpen,
  subscription,
}: ISubscribeAlwaysOn) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();

  if (!catalog || !subscription.type) {
    return null;
  }

  // get periodicity of current subscription
  const offerPeriodicity = getOfferFromId(
    catalog,
    subscription.subscription.plan_id,
  )?.periodicity;

  // get if subscription is eligible to alwaysOn addon
  const alwaysOnAddon = getProductAddonsOrChargesDetails(
    subscription.type,
    ProductType.ADDON,
    catalog,
  ).find(
    item =>
      catalog.products.byId[item.id]?.meta_data?.type === AddonType.ALWAYS_ON,
  );

  // get all alwaysOn addon with matching periodicity
  const alwaysOnAddonOffers = getOffers(
    catalog,
    alwaysOnAddon?.id || '',
    offer => offer.periodicity === offerPeriodicity,
  );

  // get addon data
  const addonOffer = getAddonOfferToDisplay(
    subscription.subscription.plan_id,
    alwaysOnAddonOffers[0]?.id || '',
    alwaysOnAddonOffers[0]?.product_id || '',
    catalog,
  );

  const addonOfferPrice = formatPrice(
    (addonOffer?.price || 0) * 100,
    undefined,
    0,
  );

  const handleSubscribeAlwaysOn = () => {
    window.location.href = `${SHOP_URL}?funnel=update_always_on&subscription=${subscription?.id}`;
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onOk={closeModal}
      onCancel={closeModal}
      title={t(
        'subscription.subscribeAlwaysOn.title',
        'Subscribe to Always On',
      )}
    >
      <Flex vertical gap={24}>
        <Alert
          message={t('subscription.subscribeAlwaysOn.info.title', 'Notice')}
          description={
            <Trans
              i18nKey="subscription.subscribeAlwaysOn.info.description"
              defaults="Enabling the “Always On” feature will cost {{ alwaysOnPrice }}/month."
              values={{ alwaysOnPrice: `${addonOfferPrice}` }}
            />
          }
          type={NotificationState.INFO}
          showIcon
        />
        <Alert
          message={t('subscription.subscribeAlwaysOn.alert.title', 'Warning')}
          description={
            <Trans
              i18nKey="subscription.subscribeAlwaysOn.alert.description"
              defaults="This change requires a restart: your Shadow PC will shut down immediately after confirmation."
            />
          }
          type={NotificationState.WARNING}
          showIcon
        />
        <Button
          data-testid="subscribe_always_on-confirm-button"
          block
          htmlType="submit"
          onClick={() => {
            handleSubscribeAlwaysOn();
          }}
        >
          {t('global.confirm', 'Confirm')}
        </Button>
        <Button
          data-testid="subscribe_always_on-cancel-button"
          type="default"
          onClick={closeModal}
          block
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default SubscribeAlwaysOn;
