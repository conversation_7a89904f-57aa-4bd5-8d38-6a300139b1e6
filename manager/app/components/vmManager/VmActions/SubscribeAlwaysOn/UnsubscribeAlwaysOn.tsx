import { useNavigate } from '@remix-run/react';
import { Alert, Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { NotificationState } from 'types';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { ROUTES_PATH } from '@/app/utils/constants';

interface ISubscribeAlwaysOn {
  closeModal: () => void;
  isOpen: boolean;
}

const UnubscribeAlwaysOn = ({ closeModal, isOpen }: ISubscribeAlwaysOn) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onOk={closeModal}
      onCancel={closeModal}
      title={t(
        'subscription.unsubscribeAlwaysOn.title',
        'Unubscribe to Always On',
      )}
    >
      <Flex vertical gap={24}>
        <Alert
          message={t('subscription.unsubscribeAlwaysOn.alert.title', 'Notice')}
          description={t(
            'subscription.unsubscribeAlwaysOn.alert.description',
            'To deactivate this feature, please contact support.',
          )}
          type={NotificationState.INFO}
          showIcon
        />
        <Button
          data-testid="contact_support-button"
          block
          htmlType="submit"
          onClick={() => {
            navigate(ROUTES_PATH.SUPPORT);
          }}
        >
          {t('global.contactSupport', 'Contact support')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default UnubscribeAlwaysOn;
