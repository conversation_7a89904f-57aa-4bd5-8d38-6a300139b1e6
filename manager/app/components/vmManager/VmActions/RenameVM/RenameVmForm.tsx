import { Flex, Form, Input, InputRef, message } from 'antd';
import { Ref, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FORM_VALIDATION_NO_SPECIAL_CHAR,
  FORM_VALIDATION_VM_NAME_MAX_LENGTH,
  FORM_VALIDATION_VM_NAME_MIN_LENGTH,
} from 'utils';

import Button from '@/app/components/ui/Button/Button';
import { useVmName } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { IVmManager } from '@/app/types/adminManagers';
import { IUpdateVmNamePayload } from '@/app/types/api';
import { IFormProps } from '@/app/types/forms';

interface IVmNameProps extends IFormProps {
  autoFocusRef: Ref<InputRef>;
  subscription: IVmManager;
}

const RenameVmForm = ({
  autoFocusRef,
  onCancel,
  subscription,
}: IVmNameProps) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { mutateAsync, isLoading } = useVmName();
  const [disabledSubmit, setDisabledSubmit] = useState(true);

  const handleFormChange = () => {
    const hasErrors = form.getFieldsError().some(({ errors }) => errors.length);
    const hasSameValue = form.getFieldValue('name') === subscription.name;
    setDisabledSubmit(hasErrors || hasSameValue);
  };

  const handleSubmit = (values: IUpdateVmNamePayload) => {
    mutateAsync({
      subscriptionId: subscription.id,
      name: values.name,
    })
      .then(() => {
        message.success(
          t(
            'subscription.vmName.notification.success',
            "PC's name changed successfully",
          ),
        );
      })
      .catch(() => {
        message.error(
          t(
            'subscription.vmName.notification.error',
            "There was an error changing this PC's name. Please try again later.",
          ),
        );
      })
      .finally(onCancel);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFieldsChange={handleFormChange}
      onFinish={handleSubmit}
      initialValues={{
        name: subscription.name,
      }}
      requiredMark={false}
    >
      <Form.Item
        name="name"
        // validateStatus={(isLoading && 'validating') || ''}
        label={t('form.vmName.label', 'Name')}
        rules={[
          {
            required: true,
            message: t('form.vmName.error.required', 'Vm name is required'),
          },
          {
            min: FORM_VALIDATION_VM_NAME_MIN_LENGTH,
            message: t(
              'form.vmName.error.minLength',
              'PC name must be at least {{ minLength }} characters',
              {
                minLength: FORM_VALIDATION_VM_NAME_MIN_LENGTH,
              },
            ),
          },
          {
            max: FORM_VALIDATION_VM_NAME_MAX_LENGTH,
            message: t(
              'form.vmName.error.maxLength',
              'PC name must be at most {{ maxLength }} characters',
              {
                maxLength: FORM_VALIDATION_VM_NAME_MAX_LENGTH,
              },
            ),
          },
          {
            pattern: FORM_VALIDATION_NO_SPECIAL_CHAR,
            message: t(
              'form.vmName.error.specialChar',
              'PC name must not use special characters',
            ),
          },
        ]}
      >
        <Input
          ref={autoFocusRef}
          id="name"
          size="large"
          data-testid="rename_vm-input"
          placeholder={
            !subscription.name ? `VM #${subscription.id}` : undefined
          }
        />
      </Form.Item>
      <Flex vertical gap="middle">
        <Button
          data-testid="rename_vm-confirm-button"
          disabled={disabledSubmit || isLoading}
          block
          loading={isLoading}
          htmlType="submit"
        >
          {t('global.confirm', 'Confirm')}
        </Button>
        <Button
          data-testid="rename_vm-cancel-button"
          type="default"
          onClick={onCancel}
          disabled={isLoading}
          block
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Form>
  );
};

export default RenameVmForm;
