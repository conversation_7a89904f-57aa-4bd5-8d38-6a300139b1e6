import { InputRef } from 'antd';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import RenameVmForm from './RenameVmForm';

import Modal from '@/app/components/ui/Modal/Modal';
import { IVmManager } from '@/app/types/adminManagers';

interface RenameVm {
  closeModal: () => void;
  isOpen: boolean;
  subscription: IVmManager;
}

const RenameVm = ({ closeModal, isOpen, subscription }: RenameVm) => {
  const { t } = useTranslation();
  const autoFocusRef = useRef<InputRef>(null);

  return (
    <Modal
      afterOpenChange={open => open && autoFocusRef.current?.focus()}
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onCancel={closeModal}
      title={t('subscription.renameVm.title', 'Rename my Shadow')}
    >
      <RenameVmForm
        autoFocusRef={autoFocusRef}
        onCancel={closeModal}
        subscription={subscription}
      />
    </Modal>
  );
};

export default RenameVm;
