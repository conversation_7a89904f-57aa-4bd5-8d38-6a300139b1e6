import { Alert, Flex, message } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  IMemberDetails,
  ISubscription,
  NotificationState,
  ResetVmSteps,
} from 'types';

import ResetVmForm from './ResetVmForm';

import WordConfirmationForm from '@/app/components/account/WordConfirmationForm';
import Modal from '@/app/components/ui/Modal/Modal';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { useConfig } from '@/app/hooks/store/useConfig';
import { FORM_VALIDATION_WORD_COMPARE_RESET_VM } from '@/app/utils/constants';

interface ResetVm {
  closeModal: () => void;
  isOpen: boolean;
  subscriptionId: ISubscription['id'];
}

const RESET_VM_STEP: Record<ResetVmSteps, string> = {
  password: 'password-confirmation',
  configuration: 'vm-configuration',
};

const ResetVm = ({ closeModal, isOpen, subscriptionId }: ResetVm) => {
  const { t } = useTranslation();

  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];
  const { language } = useConfig();

  const initialStep = RESET_VM_STEP.password;
  const [step, setStep] = useState<string>(initialStep);

  const closeAndResetInitialStep = () => {
    closeModal();
    // Timeout that prevents a flicker by letting the fade out animation end before reseting state.
    setTimeout(() => {
      setStep(initialStep);
    }, 200);
  };

  const onResetVmSuccess = () => {
    closeModal();
    message.success(
      t(
        'subscription.resetVm.notification.success',
        'You have successfully triggered a reset of your PC. This process will take around 1 hour.',
      ),
    );
  };

  const onResetVmError = () => {
    closeModal();
    closeAndResetInitialStep();
    message.error(
      t(
        'subscription.resetVm.notification.error',
        'There was an error when trying to reset your PC, please try again later or contact support.',
      ),
    );
  };

  const renderErrorAlert = () => (
    <Alert
      message={t('subscription.resetVm.alert.title', 'Warning')}
      description={t(
        'subscription.resetVm.alert.description',
        'You will lose all your data (except D: Disk) in the process.',
      )}
      type={NotificationState.ERROR}
      showIcon
    />
  );

  const computeSubtitle = () => {
    switch (step) {
      case RESET_VM_STEP.password:
        return t(
          'subscription.resetVm.subtitle',
          'Resetting your Shadow will bring a new clean install of your Windows, and reset all your settings.',
        );
      case RESET_VM_STEP.configuration:
        return t(
          'subscription.resetVm.selectConfiguration',
          'Please select your new configuration',
        );
    }
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onOk={closeModal}
      onCancel={closeAndResetInitialStep}
      title={t('subscription.resetVm.title', 'Reset my Shadow')}
    >
      <Flex vertical gap={24}>
        <Flex justify="center" style={{ textAlign: 'center' }}>
          {computeSubtitle()}
        </Flex>
        {renderErrorAlert()}
        {step === RESET_VM_STEP.password && (
          <WordConfirmationForm
            onSuccess={() => setStep(RESET_VM_STEP.configuration)}
            wordToCompare={FORM_VALIDATION_WORD_COMPARE_RESET_VM[language]}
            onCancel={closeAndResetInitialStep}
          />
        )}
        {step === RESET_VM_STEP.configuration && (
          <ResetVmForm
            subscriptionId={subscriptionId}
            onSuccess={onResetVmSuccess}
            onError={onResetVmError}
            isB2b={isB2b}
            onCancel={closeAndResetInitialStep}
          />
        )}
      </Flex>
    </Modal>
  );
};

export default ResetVm;
