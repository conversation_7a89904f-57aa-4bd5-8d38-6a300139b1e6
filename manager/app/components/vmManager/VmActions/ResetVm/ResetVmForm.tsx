import { Flex, Form, Select } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardISO } from 'types';
import { getTimezoneName, logError } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import Flag from '@/app/components/ui/Flag';
import {
  useUpdateVmReadyToPlay,
  useDeleteVmReadyToPlay,
  useVmDiskReset,
  useVmStatus,
  useVmStop,
} from '@/app/hooks/reactQuery/vm/useVdi';
import { useConfig } from '@/app/hooks/store/useConfig';
import { SelectOption } from '@/app/types/forms';
import type { IFormProps } from '@/app/types/forms';
import { VmConfiguration } from '@/app/types/vm';
import {
  CONFIGURATION_OPTIONS,
  KEYBOARD_OPTIONS,
  KEYBOARD_PER_LANGUAGE,
  LOCALE_KEYBOARD_PER_LANGUAGE,
  VM_LANGUAGES_WITH_FLAG_OPTIONS,
} from '@/app/utils/constants';

interface IResetVmFormProps extends IFormProps {
  subscriptionId: string;
  isB2b: boolean;
}

const ResetVmForm = ({
  onSubmitting = () => null,
  onSuccess = () => null,
  onError = () => null,
  subscriptionId,
  onCancel = () => null,
}: IResetVmFormProps) => {
  const { t } = useTranslation();
  const { language } = useConfig();
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // VM related hooks
  const { refetch: refetchVmStatus } = useVmStatus(subscriptionId);
  const vmStop = useVmStop();
  const updateReadyToPlay = useUpdateVmReadyToPlay();
  const deleteReadyToPlay = useDeleteVmReadyToPlay();
  const vmDiskReset = useVmDiskReset();

  const defaultVmConfiguration = VmConfiguration.AUTOMATIC;

  const initialValues = {
    configuration: defaultVmConfiguration,
    language,
    keyboard: KEYBOARD_PER_LANGUAGE[language] as KeyboardISO,
  };

  const watchedConfiguration = Form.useWatch('configuration', form);

  const optionWithFlag = (
    array: SelectOption[],
    type: 'language' | 'keyboard',
  ) => {
    return array.map(option => ({
      ...option,
      label: option.flag ? (
        <Flag
          strong
          flex={true}
          value={option.label}
          flag={option.flag.toUpperCase() as keyof typeof Flags}
        />
      ) : (
        option.label
      ),
      ...{ 'data-testid': `reset_vm-${type}-${option.value}-option` },
    }));
  };

  const handleSubmit = async (formValues: typeof initialValues) => {
    try {
      setIsSubmitting(true);
      onSubmitting(true);

      if (!subscriptionId) {
        throw new Error('No subscription ID found');
      }

      const { configuration, ...readyToPlayConfig } = formValues;
      const { data } = await refetchVmStatus();

      if (data) {
        if (data.running) {
          await vmStop.mutateAsync(subscriptionId);
        }

        if (configuration === VmConfiguration.AUTOMATIC) {
          await updateReadyToPlay.mutateAsync({
            readyToPlayConfig: {
              ...readyToPlayConfig,
              language:
                LOCALE_KEYBOARD_PER_LANGUAGE[readyToPlayConfig.language],
            },
            timezone: getTimezoneName(),
            subscriptionId: subscriptionId,
          });
        } else if (configuration === VmConfiguration.MANUAL) {
          await deleteReadyToPlay.mutateAsync(subscriptionId);
        }

        await vmDiskReset.mutateAsync(subscriptionId);

        onSuccess();
        onCancel();
      } else {
        throw new Error('Error fetching VM status');
      }
    } catch (e) {
      onError();
      logError('ResetVmForm handleSubmit', e);
    } finally {
      setIsSubmitting(false);
      onSubmitting(false);
    }
  };

  useEffect(() => {
    onSubmitting(isSubmitting);
  }, [isSubmitting, onSubmitting]);

  return (
    <Flex gap={16} vertical>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={initialValues}
      >
        <Form.Item
          name="configuration"
          required
          label={t('form.configuration.label', 'Configuration')}
        >
          <Select
            id="configuration"
            data-testid="reset_vm-configuration-select"
            options={CONFIGURATION_OPTIONS.map(option => ({
              ...option,
              'data-testid': `reset_vm-configuration-${option.value}-option`,

              label: t(`form.select.${option.value}.label`, option.value),
            }))}
          />
        </Form.Item>

        {watchedConfiguration === VmConfiguration.AUTOMATIC && (
          <>
            <Form.Item
              name="language"
              required
              label={t('form.language.label', 'Language')}
            >
              <Select
                id="language"
                data-testid="reset_vm-language-select"
                options={optionWithFlag(
                  VM_LANGUAGES_WITH_FLAG_OPTIONS,
                  'language',
                )}
              />
            </Form.Item>

            <Form.Item
              name="keyboard"
              required
              label={t('form.keyboard.label', 'Keyboard')}
            >
              <Select
                id="keyboard"
                data-testid="reset_vm-keyboard-select"
                options={optionWithFlag(KEYBOARD_OPTIONS, 'keyboard')}
              />
            </Form.Item>
          </>
        )}
        <Flex gap="middle" vertical>
          <Button
            data-testid="reset_vm-confirm-button"
            danger
            block
            loading={isSubmitting}
            htmlType="submit"
          >
            {t('subscription.resetVm.resetLabel', 'Reset my Shadow PC')}
          </Button>
          <Button
            data-testid="reset_vm-cancel-button"
            type="default"
            onClick={onCancel}
            disabled={isSubmitting}
            block
          >
            {t('global.cancel', 'Cancel')}
          </Button>
        </Flex>
      </Form>
    </Flex>
  );
};

export default ResetVmForm;
