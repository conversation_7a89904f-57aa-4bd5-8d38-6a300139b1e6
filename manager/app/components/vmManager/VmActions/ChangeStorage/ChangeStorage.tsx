import { Alert, Flex } from 'antd';
import { Trans, useTranslation } from 'react-i18next';
import { NotificationState } from 'types';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { IVmManager } from '@/app/types/adminManagers';
import { SHOP_URL } from '@/app/utils/constants';

interface IChangeStorage {
  closeModal: () => void;
  isOpen: boolean;
  subscription: IVmManager;
}

const ChangeStorage = ({
  closeModal,
  isOpen,
  subscription,
}: IChangeStorage) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  const handleChangeStorage = () => {
    if (isB2b) {
      window.location.href = `${SHOP_URL}?funnel=b2b_update_storage&subscription=${subscription.id}`;
    } else {
      window.location.href = `${SHOP_URL}?funnel=update_storage&subscription=${subscription.id}`;
    }
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onOk={closeModal}
      onCancel={closeModal}
      title={t('subscription.plan.storage.ctaLabel', 'Manage extra storage')}
    >
      <Flex vertical gap={24}>
        <Alert
          message={t('subscription.plan.storage.alert.title', 'Warning')}
          description={
            <Trans
              i18nKey="subscription.plan.storage.alert.description"
              defaults="This change requires a restart: your Shadow PC will shut down immediately after confirmation."
            />
          }
          type={NotificationState.WARNING}
          showIcon
        />
        <Button
          data-testid="change_vm_storage-confirm-button"
          block
          htmlType="submit"
          onClick={() => {
            handleChangeStorage();
          }}
        >
          {t('global.continue', 'Continue')}
        </Button>
        <Button
          data-testid="change_vm_storage-cancel-button"
          type="default"
          onClick={closeModal}
          block
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default ChangeStorage;
