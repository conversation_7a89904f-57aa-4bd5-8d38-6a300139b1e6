import { Flex, message, Typography } from 'antd';
import React, { FC, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Currency, ProductType, RetentionType } from 'types';
import { dataLayerEvent, findSubscriptionItemByType, logError } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import { useApplySubscriptionRetention } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { usePrice } from '@/app/hooks/usePrice';
import { DeleteVmStepProps } from '@/app/types/deleteVm';
import { SHOP_URL } from '@/app/utils/constants';

const DeleteVmDiscount: FC<DeleteVmStepProps> = ({
  onClose,
  setCurrentStep,
  subscription,
  dataLayerEventPayload,
  retention,
  catalog,
}) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();
  const [isAccepting, setIsAccepting] = useState(false);
  const applySubscriptionRetention = useApplySubscriptionRetention();

  const currentPlan = findSubscriptionItemByType(
    subscription,
    ProductType.PLAN,
  );

  const regularRetentionPrice = currentPlan?.price
    ? formatPrice(currentPlan?.price, subscription.currency_code as Currency)
    : '';
  const discountedRetentionPrice = currentPlan?.price
    ? formatPrice(
        currentPlan?.price - (retention?.coupon?.discount_amount || 0),
        subscription?.currency_code as Currency,
      )
    : '';

  const isRetentionUpgrade =
    catalog?.products.byId[currentPlan?.name as string]?.meta_data?.retention
      ?.type === RetentionType.UPGRADE;

  const retentionUpgradeOfferId = catalog?.products.byId[
    retention?.upgrade?.target as string
  ]?.offers.find(
    offerId =>
      catalog?.offers.byId[offerId]?.periodicity ===
      catalog?.offers.byId[currentPlan?.id as string]?.periodicity,
  );

  const regularUpgradePrice = formatPrice(
    (catalog?.offers.byId[retentionUpgradeOfferId as string]?.price || 0) * 100,
    subscription.currency_code as Currency,
  );

  const discountedUpgradePrice = formatPrice(
    (catalog?.offers.byId[retentionUpgradeOfferId as string]?.price || 0) *
      100 -
      (retention?.coupon?.discount_amount || 0),
    subscription.currency_code as Currency,
  );

  const discountValue = formatPrice(
    retention?.coupon?.discount_amount || 0,
    subscription.currency_code as Currency,
  );

  const handleAcceptClick = async () => {
    setIsAccepting(true);

    try {
      await applySubscriptionRetention.mutateAsync(subscription.id);
      dataLayerEvent('churning_process', {
        ...dataLayerEventPayload,
        antiChurnEligible: true,
        antiChurnRevokeChurn: true,
      });
      if (isRetentionUpgrade) {
        window.location.href = `${SHOP_URL}?funnel=retention_upgrade&subscription=${subscription?.id}&offer=${retentionUpgradeOfferId}`;
      } else {
        setCurrentStep('discount-accepted');
      }
    } catch (e) {
      logError('handleAccept', e);
      message.error(
        t(
          'subscription.cancelVm.discount.notification.error',
          'There was an error, please try again later or contact support.',
        ),
      );
      onClose();
    }
  };

  const handleDeclineClick = () => {
    setCurrentStep('survey');
  };

  return (
    <>
      <Typography.Text style={{ textAlign: 'center' }}>
        {isRetentionUpgrade ? (
          <Trans
            i18nKey="subscription.cancelVm.discount.upgradeDescription"
            defaults="Enjoy {{ offerName }} for <bold>just {{ discountedPrice }}</bold> for the first month instead of {{ regularPrice }}: <bold>{{ discountValue }} off</bold> and adjusted based on what you’ve already paid!"
            values={{
              offerName: t(
                `subscription.details.name.${retention?.upgrade?.target}`,
              ),
              discountedPrice: discountedUpgradePrice,
              regularPrice: regularUpgradePrice,
              discountValue: discountValue,
            }}
            components={{ bold: <strong /> }}
          />
        ) : (
          <Trans
            i18nKey="subscription.cancelVm.discount.description"
            defaults="That’s why we want to offer you your Shadow PC for <bold>only {{ discountedPrice }}</bold> next month instead of {{ regularPrice }}."
            values={{
              discountedPrice: discountedRetentionPrice,
              regularPrice: regularRetentionPrice,
            }}
            components={{ bold: <strong /> }}
          />
        )}
      </Typography.Text>
      <Flex gap="middle" vertical>
        <Button
          data-testid="vm_discount-confirm-button"
          loading={isAccepting}
          onClick={handleAcceptClick}
          block
        >
          {isRetentionUpgrade
            ? t(
                'subscription.cancelVm.discount.acceptDiscountButton.upgradeLabel',
                {
                  defaultValue:
                    'Yes, I want to try {{ offerName }} for a month at {{ discountedPrice }}',
                  offerName: t(
                    `subscription.details.name.${retention?.upgrade?.target}`,
                  ),
                  discountedPrice: discountedUpgradePrice,
                },
              )
            : t('subscription.cancelVm.discount.acceptDiscountButton.label', {
                defaultValue:
                  'Yes, I want to keep Shadow PC for another month at {{ discountedPrice }}',
                discountedPrice: discountedRetentionPrice,
              })}
        </Button>
        <Button
          data-testid="vm_discount-decline-button"
          disabled={isAccepting}
          onClick={handleDeclineClick}
          block
          type="default"
        >
          {t(
            'subscription.cancelVm.discount.declineDiscountButton.label',
            'No, I definitely want to cancel',
          )}
        </Button>
      </Flex>
    </>
  );
};

export default DeleteVmDiscount;
