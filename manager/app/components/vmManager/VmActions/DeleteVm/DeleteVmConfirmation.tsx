import { List, Typography } from 'antd';
import React, { FC } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { DeleteVmStepProps } from '@/app/types/deleteVm';

const DeleteVmConfirmation: FC<DeleteVmStepProps> = ({
  onClose,
  lastBillingDate,
}) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const email = currentMemberQuery.data?.user?.email || '';

  return (
    <>
      {t(
        'subscription.cancelVm.confirmation.subtitle',
        'We are sad to see you leave.',
      )}
      <List>
        <List.Item>
          <Typography>
            <Trans
              i18nKey="subscription.cancelVm.confirmation.subscription.item-1"
              defaults="You will still have access to your Shadow until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>"
              values={{ lastBillingDate }}
              components={{ bold: <strong /> }}
            />
          </Typography>
        </List.Item>
        <List.Item>
          <Typography>
            <Trans
              i18nKey="subscription.cancelVm.confirmation.subscription.item-2"
              defaults="We will send you a confirmation email to <bold>{{ email }}</bold>"
              values={{ email }}
              components={{ bold: <strong /> }}
            />
          </Typography>
        </List.Item>
        <List.Item>
          <Typography>
            {t(
              'subscription.cancelVm.confirmation.subscription.item-3',
              "You won't be charged anymore.",
            )}
          </Typography>
        </List.Item>
        <List.Item>
          <Typography>
            <Trans
              i18nKey="subscription.cancelVm.confirmation.subscription.changedYourMind"
              defaults="We are always improving Shadow so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime."
              components={{ bold: <strong /> }}
            />
          </Typography>
        </List.Item>
      </List>
      <Button data-testid="cancel_vm-ok-button" block onClick={onClose}>
        {t('global.ok', 'OK')}
      </Button>
    </>
  );
};

export default DeleteVmConfirmation;
