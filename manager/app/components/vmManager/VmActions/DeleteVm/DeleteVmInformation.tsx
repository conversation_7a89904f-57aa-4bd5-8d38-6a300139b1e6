import { Flex, message, Typography } from 'antd';
import React, { FC, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { dataLayerEvent, logError } from 'utils';

import CancelInfo from './CancelInfo/CancelInfo';

import Button from '@/app/components/ui/Button/Button';
import { useCancelSubscription } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { DeleteVmStepProps } from '@/app/types/deleteVm';

const DeleteVmInformation: FC<DeleteVmStepProps> = ({
  onClose,
  setCurrentStep,
  subscription,
  cancellationReasons,
  lastBillingDate,
  dataLayerEventPayload,
}) => {
  const { t } = useTranslation();
  const [isCancelling, setIsCancelling] = useState(false);
  const cancelSubscription = useCancelSubscription();

  const handleConfirmClick = async () => {
    setIsCancelling(true);

    try {
      await cancelSubscription.mutateAsync({
        cancellationReasons,
        subscriptionId: subscription.id,
      });
      setCurrentStep('confirmation');
      dataLayerEvent('churning_process', {
        ...dataLayerEventPayload,
        antiChurnRevokeChurn: false,
      });
    } catch (e) {
      logError('handleCancelVM', e);
      message.error(
        t(
          'subscription.cancelVm.notification.error',
          'There was an error when trying to cancel your subscription, please try again later or contact support.',
        ),
      );
      onClose();
    }
  };

  return (
    <>
      <Typography.Text>
        <Trans
          i18nKey="subscription.cancelVm.information.subtitle"
          defaults="If you cancel your subscription, your access to Shadow will end on <bold>{{ lastBillingDate }}</bold>"
          values={{ lastBillingDate }}
          components={{ bold: <strong /> }}
        />
      </Typography.Text>
      <CancelInfo
        title={t(
          'subscription.cancelVm.information.whenSubscriptionEnds.title',
          'When your subscription ends',
        )}
        items={[
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionEnds.shadowAccess',
              "You'll lose access to your Shadow",
            ),
            description: t(
              'subscription.cancelVm.information.whenSubscriptionEnds.dataAccess',
              "You'll no longer be able to access games, data, or software on your high-performance PC.",
            ),
          },
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionEnds.reSubscribe.title',
              'Restart your subscription anytime!',
            ),
            description: (
              <Trans
                i18nKey="subscription.cancelVm.information.whenSubscriptionEnds.description"
                defaults="If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!"
                values={{ lastBillingDate }}
                components={{ bold: <strong /> }}
              />
            ),
          },
        ]}
      />
      <CancelInfo
        title={t(
          'subscription.cancelVm.information.whenSubscriptionActive.title',
          'While your subscription is still active',
        )}
        items={[
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionActive.enjoy',
              'Enjoy the most out of your games',
            ),
            description: t(
              'subscription.cancelVm.information.whenSubscriptionActive.access',
              'You can still access your Shadow until the end of your subscription. You still have time to make it to the top of the leaderboard.',
            ),
          },
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionActive.backupTip1',
              "Don't forget to back up your files",
            ),
            description: t(
              'subscription.cancelVm.information.whenSubscriptionActive.backupTip2',
              'Make sure to transfer important files from Shadow to your local PC. Your data will be deleted after your subscription ends.',
            ),
          },
        ]}
      />
      <Flex gap="middle" vertical>
        <Button
          data-testid="cancel_vm-confirm-button"
          block
          loading={isCancelling}
          onClick={handleConfirmClick}
        >
          {t('global.confirm', 'Confirm')}
        </Button>
        <Button
          data-testid="cancel_vm-cancel-button"
          type="default"
          onClick={onClose}
          block
          disabled={isCancelling}
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </>
  );
};

export default DeleteVmInformation;
