import { List, Typography } from 'antd';
import { ReactNode } from 'react';

import useStyles from './CancelInfo.styles';

interface ICancelInfosProps {
  title: string;
  items: Array<ICancelInfoItem>;
}

interface ICancelInfoItem {
  label: string;
  description: ReactNode;
}

const CancelInfo = ({ title, items }: ICancelInfosProps) => {
  const { styles } = useStyles();

  const renderListItem = ({ label, description }: ICancelInfoItem) => (
    <List.Item>
      <List.Item.Meta
        title={<Typography.Text strong>{label}</Typography.Text>}
        description={<Typography.Text>{description}</Typography.Text>}
      />
    </List.Item>
  );

  return (
    <div>
      <Typography.Title level={4} className={styles.deleteVmKeep__title}>
        {title}
      </Typography.Title>
      <List dataSource={items} renderItem={item => renderListItem(item)}></List>
    </div>
  );
};

export default CancelInfo;
