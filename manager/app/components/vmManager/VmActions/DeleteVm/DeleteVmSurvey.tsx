import { Form, Input, Flex, Radio, Typography } from 'antd';
import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import { DeleteVmStepProps } from '@/app/types/deleteVm';
import { CancellationReasonKey } from '@/app/types/forms';

const DeleteVmSurvey: FC<DeleteVmStepProps> = ({
  setCurrentStep,
  cancellationReasons,
  setCancellationReasons,
}) => {
  const { t } = useTranslation();

  const [form] = Form.useForm();
  const otherReason = 'other_reason';

  const selectedReason: string | undefined = Form.useWatch('reason', form);
  const commentValue: string | undefined = Form.useWatch('comment', form);

  const onFinish = (values: any) => {
    setCancellationReasons({
      reasons: [values.reason],
      comment: values.comment,
    });
    setCurrentStep('information');
  };

  const handleSkipClick = () => {
    setCurrentStep('information');
  };

  const isButtonDisabled =
    !selectedReason || (selectedReason === otherReason && !commentValue);

  const subscriptionCancelReasons = [
    {
      key: CancellationReasonKey.PriceTooHigh,
      label: t(
        'subscription.cancellationReasons.reason.priceTooHigh',
        'The price is too high for what I do with my Shadow',
      ),
    },
    {
      key: CancellationReasonKey.PersonalFinancialIssue,
      label: t(
        'subscription.cancellationReasons.reason.personalFinancialIssue',
        'I would like to keep my Shadow but I prefer to stop my subscription for financial issues',
      ),
    },
    {
      key: CancellationReasonKey.LatencyIssue,
      label: t(
        'subscription.cancellationReasons.reason.latencyIssue',
        'I felt latency and/or delay in my actions',
      ),
    },
    {
      key: CancellationReasonKey.StabilityIssue,
      label: t(
        'subscription.cancellationReasons.reason.stabilityIssue',
        'I felt instability (bugs, updates, …)',
      ),
    },
    {
      key: CancellationReasonKey.IncompatibilitySoftwareOrGame,
      label: t(
        'subscription.cancellationReasons.reason.incompatibilitySoftwareOrGame',
        'Incompatibility of the software or the game for which I chose Shadow',
      ),
    },
    {
      key: CancellationReasonKey.NoNeedAnymore,
      label: t(
        'subscription.cancellationReasons.reason.noNeedAnymore',
        "I don't need it anymore",
      ),
    },
    {
      key: CancellationReasonKey.ShadowSpecTooWeak,
      label: t(
        'subscription.cancellationReasons.reason.shadowSpecTooWeak',
        'The technical specifications are too weak for what I want to do',
      ),
    },
    {
      key: CancellationReasonKey.TooManyConstraints,
      label: t(
        'subscription.cancellationReasons.reason.tooManyConstraints',
        'Too many constraints? (autoshutdown…)',
      ),
    },
  ];

  return (
    <>
      <Typography.Text>
        {t(
          'subscription.cancelVm.survey.subtitle',
          'Please select your main reason for canceling. It will only take a minute.',
        )}
      </Typography.Text>
      <Form form={form} onFinish={onFinish} initialValues={cancellationReasons}>
        <Form.Item name="reason">
          <Radio.Group>
            <Flex vertical gap={16}>
              {subscriptionCancelReasons.map(reason => (
                <Radio
                  key={reason.key}
                  value={reason.key}
                  data-testid={`cancel_vm_survey-${reason.key}-radio`}
                >
                  {reason.label}
                </Radio>
              ))}

              <Radio
                value={otherReason}
                data-testid="cancel_vm_survey-other-radio"
              >
                {t('subscription.cancelVm.otherReason.title', 'Other')}
              </Radio>
            </Flex>
          </Radio.Group>
        </Form.Item>
        {selectedReason === otherReason && (
          <Form.Item name="comment">
            <Input.TextArea
              aria-label="comment"
              id="cancel-vm-modal-comment-input"
              data-testid="cancel_vm_survey-comment-input"
              rows={5}
              placeholder={t(
                'subscription.cancelVm.otherReason.placeholder',
                'Please, describe the issue you had',
              )}
            />
          </Form.Item>
        )}
        <Flex gap="middle" vertical>
          <Button
            data-testid="cancel_vm_survey-next-button"
            block
            htmlType="submit"
            disabled={isButtonDisabled}
          >
            {t('global.next', 'Next')}
          </Button>
          <Button
            data-testid="cancel_vm_survey-skip-button"
            block
            type="default"
            onClick={handleSkipClick}
          >
            {t('global.skipReason', "I don't want to answer")}
          </Button>
        </Flex>
      </Form>
    </>
  );
};

export default DeleteVmSurvey;
