import { LoadingOutlined } from '@ant-design/icons';
import { Flex, Spin } from 'antd';
import { FC, useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { type ISubscription } from 'types';
import { DATE_FORMAT_BY_MARKET } from 'utils';

import DeleteVmConfirmation from './DeleteVmConfirmation';
import DeleteVmDiscount from './DeleteVmDiscount';
import DeleteVmDiscountAccepted from './DeleteVmDiscountAccepted';
import DeleteVmInformation from './DeleteVmInformation';
import DeleteVmSurvey from './DeleteVmSurvey';

import Modal from '@/app/components/ui/Modal/Modal';
import { useGetSubscriptionRetention } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { useCatalog } from '@/app/hooks/useCatalog';
import { useDate } from '@/app/hooks/useDate';
import { DeleteVmStep, DeleteVmStepProps } from '@/app/types/deleteVm';
import { CancellationReasons } from '@/app/types/subscriptions';

interface IDeleteVmProps {
  setIsOpen: (isOpen: boolean) => void;
  subscription: ISubscription;
  isOpen: boolean;
  dataLayerEventPayload: Record<string, unknown>;
}

const DeleteVm: FC<IDeleteVmProps> = ({
  setIsOpen,
  subscription,
  isOpen,
  dataLayerEventPayload,
}) => {
  const { t } = useTranslation();
  const subscriptionRetentionQuery = useGetSubscriptionRetention(
    subscription.id,
  );
  const catalogQuery = useCatalog();

  const { getSubscriptionNextBillingDate } = useDate();

  const [currentStep, setCurrentStep] = useState<DeleteVmStep | null>(null);
  const [lastBillingDate, setLastBillingDate] = useState<string | null>(null);
  const [cancellationReasons, setCancellationReasons] =
    useState<CancellationReasons>({
      reasons: [],
      comment: '',
    });
  const onClose = () => {
    setIsOpen(false);
  };

  const getTitle = (step: DeleteVmStep | null) => {
    switch (step) {
      case 'discount':
        return t(
          'subscription.cancelVm.discount.title',
          'We are sad to let you go!',
        );
      case 'discount-accepted':
        return t(
          'subscription.cancelVm.discountAccepted.title',
          'Delighted to keep you with us!',
        );
      case 'information':
        return (
          <Trans
            i18nKey="subscription.cancelVm.information.title"
            defaults="Here's some information before you cancel"
            values={{ lastBillingDate }}
            components={{ bold: <strong /> }}
          />
        );
      case 'survey':
        return t(
          'subscription.cancelVm.survey.title',
          "We're always improving our service and your feedback matters",
        );
      case 'confirmation':
        return t(
          'subscription.cancelVm.confirmation.title',
          'Your subscription has been canceled',
        );
      default:
        return '';
    }
  };

  const props: DeleteVmStepProps = {
    cancellationReasons,
    catalog: catalogQuery.data || null,
    dataLayerEventPayload,
    lastBillingDate,
    onClose,
    retention: subscriptionRetentionQuery.data || null,
    setCancellationReasons,
    setCurrentStep,
    subscription,
  };

  // We need this useEffect because when the subscription is
  // cancelled from the modal, the lastBillingDate is null.
  useEffect(() => {
    const subscriptionNextBillingDate = getSubscriptionNextBillingDate(
      subscription,
      DATE_FORMAT_BY_MARKET,
    );
    if (subscriptionNextBillingDate) {
      setLastBillingDate(subscriptionNextBillingDate);
    }
  }, [getSubscriptionNextBillingDate, subscription]);

  useEffect(() => {
    if (
      subscriptionRetentionQuery.isLoading ||
      !subscriptionRetentionQuery.isFetched ||
      catalogQuery.isLoading ||
      !catalogQuery.isFetched
    ) {
      return;
    }

    if (subscriptionRetentionQuery.data?.eligibility) {
      setCurrentStep('discount');
    } else {
      setCurrentStep('survey');
    }
  }, [
    subscriptionRetentionQuery.data?.eligibility,
    subscriptionRetentionQuery.isLoading,
    subscriptionRetentionQuery.isFetched,
    catalogQuery.isLoading,
    catalogQuery.isFetched,
  ]);

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      open={isOpen}
      onCancel={onClose}
      aria-labelledby="cancel-vm-title"
      aria-describedby="cancel-vm-description"
      title={getTitle(currentStep)}
    >
      {!currentStep && (
        <Flex justify="center">
          <Spin size="large" indicator={<LoadingOutlined spin />} />
        </Flex>
      )}
      {currentStep === 'discount' && <DeleteVmDiscount {...props} />}
      {currentStep === 'discount-accepted' && (
        <DeleteVmDiscountAccepted {...props} />
      )}
      {currentStep === 'survey' && <DeleteVmSurvey {...props} />}
      {currentStep === 'information' && <DeleteVmInformation {...props} />}
      {currentStep === 'confirmation' && <DeleteVmConfirmation {...props} />}
    </Modal>
  );
};

export default DeleteVm;
