import { Flex, Typography } from 'antd';
import React, { FC } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Currency } from 'types';

import Button from '@/app/components/ui/Button/Button';
import { usePrice } from '@/app/hooks/usePrice';
import { DeleteVmStepProps } from '@/app/types/deleteVm';

const DeleteVmDiscountAccepted: FC<DeleteVmStepProps> = ({
  onClose,
  subscription,
  retention,
}) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();

  const discountAmount = formatPrice(
    retention?.coupon?.discount_amount || 0,
    subscription?.currency_code as Currency,
  );

  return (
    <>
      <Flex gap="middle" vertical>
        <Typography.Text style={{ textAlign: 'center' }}>
          <Trans
            i18nKey="subscription.cancelVm.discountAccepted.confirmationText2"
            defaults={
              'Good news! We will deduct <bold>{{ amount }}</bold> from your next bill.'
            }
            values={{ amount: discountAmount }}
            components={{ bold: <strong /> }}
          />
        </Typography.Text>
        <Typography.Text style={{ textAlign: 'center' }}>
          {t(
            'subscription.cancelVm.discountAccepted.confirmationText',
            'You can now continue to play, create, work, or do anything you want on your Shadow PC',
          )}
        </Typography.Text>
      </Flex>
      <Button
        data-testid="vm_discount_accepted-confirm-button"
        onClick={onClose}
        block
      >
        {t('global.ok', 'OK')}
      </Button>
    </>
  );
};

export default DeleteVmDiscountAccepted;
