import { Alert, Flex } from 'antd';
import { Trans, useTranslation } from 'react-i18next';
import { NotificationState, ProductFamilyId } from 'types';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { IVmManager } from '@/app/types/adminManagers';
import { SHOP_URL } from '@/app/utils/constants';

interface IChangePlan {
  closeModal: () => void;
  isOpen: boolean;
  subscription: IVmManager;
}

const ChangePlan = ({ closeModal, isOpen, subscription }: IChangePlan) => {
  const { t } = useTranslation();

  const handleChangePlan = () => {
    window.location.href = `${SHOP_URL}?funnel=change_plan&familyId=${ProductFamilyId.CLOUDPC}&subscription=${subscription?.id}`;
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onOk={closeModal}
      onCancel={closeModal}
      title={t('subscription.plan.vm.action.changePlan.title', 'Change plan')}
    >
      <Flex vertical gap={24}>
        <Alert
          message={t(
            'subscription.plan.vm.action.changePlan.alert.title',
            'Warning',
          )}
          description={
            <Trans
              i18nKey="subscription.plan.vm.action.changePlan.alert.description"
              defaults="This change requires a restart: your Shadow PC will shut down immediately after confirmation."
            />
          }
          type={NotificationState.WARNING}
          showIcon
        />
        <Button
          data-testid="change_vm_plan-confirm-button"
          block
          htmlType="submit"
          onClick={() => {
            handleChangePlan();
          }}
        >
          {t('global.continue', 'Continue')}
        </Button>
        <Button
          data-testid="change_vm_plan-cancel-button"
          type="default"
          onClick={closeModal}
          block
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default ChangePlan;
