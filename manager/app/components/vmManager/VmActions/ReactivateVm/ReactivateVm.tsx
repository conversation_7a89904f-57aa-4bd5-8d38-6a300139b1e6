import { Flex, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { trackEvent } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { useReactivateSubscription } from '@/app/hooks/reactQuery/subscriptions/useSubscription';

type ReactivateVmProps = {
  closeModal: () => void;
  subscriptionId: string;
  isOpen: boolean;
};

export const ReactivateVm = ({
  closeModal,
  isOpen,
  subscriptionId,
}: ReactivateVmProps) => {
  const { t } = useTranslation();
  const { mutateAsync, isLoading } = useReactivateSubscription();

  const handleReactivateVm = () => {
    mutateAsync(subscriptionId)
      .then(() => {
        message.success(
          t(
            'subscription.reactivateVm.notification.success',
            'You have successfully reactivated your subscription.',
          ),
        );
        trackEvent('click', {
          event_category: 'shadow_reactivate',
          event_label: 'reactivate_confirmed',
        });
      })
      .catch(() => {
        message.error(
          t(
            'subscription.reactivateVm.notification.error',
            'We could not reactivate your subscription, please try again later.',
          ),
        );
      })
      .finally(closeModal);
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onCancel={closeModal}
      title={t('subscription.reactivateVm.title', 'Reactivate my Shadow')}
    >
      <Flex gap="middle" vertical>
        <Button
          data-testid="reactivate_vm-confirm-button"
          block
          loading={isLoading}
          onClick={handleReactivateVm}
        >
          {t('global.confirm', 'Confirm')}
        </Button>
        <Button
          data-testid="reactivate_vm-cancel-button"
          type="default"
          onClick={closeModal}
          disabled={isLoading}
          block
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Modal>
  );
};
