import { Flex, message, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { useCancelScheduledChanges } from '@/app/hooks/reactQuery/subscriptions/useSubscription';

type CancelScheduledChangeProps = {
  closeModal: () => void;
  subscriptionId: string;
  isOpen: boolean;
};

export const CancelScheduledChange = ({
  closeModal,
  isOpen,
  subscriptionId,
}: CancelScheduledChangeProps) => {
  const { t } = useTranslation();
  const { mutateAsync, isLoading } = useCancelScheduledChanges();

  const handleCancelScheduledChange = () => {
    mutateAsync(subscriptionId)
      .then(() => {
        message.success(
          t(
            'subscription.cancelScheduledChange.notification.success',
            'Your change was canceled successfully',
          ),
        );
      })
      .catch(() => {
        message.error(
          t(
            'subscription.cancelScheduledChange.notification.error',
            `An error occurred when updating your scheduled change. Please try again later.`,
          ),
        );
      })
      .finally(closeModal);
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onCancel={closeModal}
      title={t(
        'subscription.cancelScheduledChange.title',
        'Cancel Scheduled Change',
      )}
    >
      <Flex vertical gap="large">
        <Flex justify="center" style={{ textAlign: 'center' }}>
          <Typography.Text>
            {t(
              'subscription.cancelScheduledChange.description',
              `A modification to your subscription was scheduled. If you proceed with the cancellation, your subscription will remain unchanged as it is today. Are you sure you want to proceed with this cancellation?`,
            )}
          </Typography.Text>
        </Flex>
        <Flex gap="middle" vertical>
          <Button
            data-testid="cancel_scheduled_change-confirm-button"
            block
            loading={isLoading}
            onClick={handleCancelScheduledChange}
          >
            {t('global.confirm', 'Confirm')}
          </Button>
          <Button
            data-testid="cancel_scheduled_change-cancel-button"
            type="default"
            onClick={closeModal}
            disabled={isLoading}
            block
          >
            {t('global.cancel', 'Cancel')}
          </Button>
        </Flex>
      </Flex>
    </Modal>
  );
};
