import { UserDeleteOutlined } from '@ant-design/icons';
import { Flex, Form, Grid, Input, message, Typography } from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { IMemberDetails } from 'types';
import { computeErrorMessage } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import MemberDetails from '@/app/components/ui/MemberDetail/MemberDetails';
import Modal from '@/app/components/ui/Modal/Modal';
import { useUnassignMemberFromSubscription } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import {
  ApiError,
  IUnassignMemberFromSubscriptionPayload,
} from '@/app/types/api';

interface IVmMemberUnassignForm {
  isAssigningOrRevokingDisabled: boolean;
  subscriptionId: string;
  user: IMemberDetails;
}

const VmMemberUnassignForm = ({
  isAssigningOrRevokingDisabled,
  subscriptionId,
  user,
}: IVmMemberUnassignForm) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const { mutateAsync, isLoading } = useUnassignMemberFromSubscription();

  const handleModal = () => {
    setIsModalOpen(!isModalOpen);
  };

  /** ACTION NOTIFICATIONS */
  const onMemberUnassignSuccess = () => {
    message.success(
      `${t(
        'list.vm.assignOrRevokeMember.revoke.notification.success',
        'Successfully revoke {{email}}',
        {
          email: user.email,
        },
      )}`,
    );
  };

  const onMemberUnassignError = (error: string) => {
    message.error(
      `${t(
        'list.vm.assignOrRevokeMember.revoke.notification.error',
        'Error revoke member {{email}}: {{error}}',
        {
          email: user.email,
          error,
        },
      )}`,
    );
  };

  const handleSubmit = (values: IUnassignMemberFromSubscriptionPayload) => {
    mutateAsync({
      subscriptionId: subscriptionId,
      memberId: values.memberId,
    })
      .then(() => {
        onMemberUnassignSuccess();
      })
      .catch(e => {
        let errorMessage = computeErrorMessage(e);
        switch ((e as ApiError)?.code) {
          case 412:
            errorMessage = t(
              'list.vm.assignOrRevokeMember.error.vmNotStopped',
              'Cannot assign or revoke new users until this PC is stopped.',
            );
            break;
        }
        onMemberUnassignError(errorMessage);
      })
      .finally(() => {
        handleModal();
      });
  };

  return (
    <Flex align="center" justify="space-between" gap="large">
      <MemberDetails
        email={user.email}
        firstName={user.first_name}
        lastName={user.last_name}
      />
      <Button
        icon={<UserDeleteOutlined />}
        data-testid="revoke_member-button"
        onClick={handleModal}
        disabled={isAssigningOrRevokingDisabled}
        loading={isLoading}
        noWrap
        danger
      >
        {screens.md &&
          t('list.vm.assignOrRevokeMember.revoke.label', 'Revoke access')}
      </Button>
      <Modal
        open={isModalOpen}
        destroyOnClose={true}
        onOk={handleModal}
        onCancel={handleModal}
        title={t(
          'list.vm.assignOrRevokeMember.revoke.modal.title',
          'Revoke access',
        )}
      >
        <Flex vertical gap={24}>
          <Typography.Text>
            <Trans i18nKey="list.vm.assignOrRevokeMember.revoke.modal.content">
              The user will not have access to this Shadow PC anymore, but will
              still remains in your users list.
              <br />
              <br />
              The Shadow PC will not be deleted.
            </Trans>
          </Typography.Text>
          <Form
            form={form}
            onFinish={handleSubmit}
            initialValues={{
              memberId: user.id,
            }}
          >
            <Form.Item hidden name="memberId">
              <Input />
            </Form.Item>
            <Flex vertical gap={16}>
              <Button
                data-testid="revoke_member-confirm-button"
                danger
                block
                loading={isLoading}
                htmlType="submit"
              >
                {t('global.revoke', 'Revoke')}
              </Button>
              <Button
                data-testid="revoke_member-cancel-button"
                type="default"
                onClick={handleModal}
                disabled={isLoading}
                block
              >
                {t('global.cancel', 'Cancel')}
              </Button>
            </Flex>
          </Form>
        </Flex>
      </Modal>
    </Flex>
  );
};

export default VmMemberUnassignForm;
