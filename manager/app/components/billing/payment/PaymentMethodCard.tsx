import { Card, Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

import PaymentMethod from './paymentMethod/PaymentMethod';

const PaymentMethodCard = () => {
  const { t } = useTranslation();

  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('account.user.paymentMethod.title', 'Payment method')}
        </Typography.Title>
        <PaymentMethod />
      </Flex>
    </Card>
  );
};

export default PaymentMethodCard;
