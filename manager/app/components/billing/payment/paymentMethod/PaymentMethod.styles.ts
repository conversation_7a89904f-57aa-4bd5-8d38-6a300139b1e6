import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    paymentMethod__container: css`
      display: flex;
      padding: ${token.marginSM}px;
      align-items: center;
      background: ${token.colorBgElevated};
      border: 2px solid ${token.colorPrimary};
      border-radius: ${token.borderRadius}px;
    `,
    paymentMethod__wrapper: css`
      display: flex;
      align-items: center;
      flex: 1;
      gap: ${token.marginXS}px;
    `,
  };
});
