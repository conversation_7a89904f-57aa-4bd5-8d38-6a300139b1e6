import { BankOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { useQueryClient } from '@tanstack/react-query';
import { Image, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  IMemberDetails,
  IPaymentMethod,
  PaymentMethodStatus,
  PaymentMethodType,
} from 'types';
import { CARD_PATH, SVG_PATH } from 'utils';

import useStyles from './PaymentMethod.styles';
import UpdatePaymentMethod from '../updatePaymentMethod/UpdatePaymentMethod';

import ApiError from '@/app/components/errors/apiError';
import Button from '@/app/components/ui/Button/Button';
import LocalLoader from '@/app/components/ui/Loader/LocalLoader';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { usePaymentDetails } from '@/app/hooks/reactQuery/user/useUser';

const PaymentMethod = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { styles } = useStyles();

  const paymentDetailsQuery = usePaymentDetails();
  const currentMemberQuery = useCurrentMember();
  const isB2B = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const [paymentData, setPaymentData] = useState<IPaymentMethod | undefined>();

  const [openPaymentModal, setOpenPaymentModal] = useState<boolean>(false);

  const getPaymentValue = (paymentMethod: IPaymentMethod) => {
    switch (paymentMethod?.type) {
      case PaymentMethodType.CARD:
        return `XXXX XXXX XXXX ${paymentMethod.card?.masked_number}`;
      case PaymentMethodType.BANK_ACCOUNT:
        return `XXXXXXXXXXXXXXXXXXXXXXX${paymentMethod.bank_account?.last4}`;
      case PaymentMethodType.PAYPAL:
        return paymentMethod.paypal?.email;
      default:
        return '';
    }
  };

  useEffect(() => {
    if (paymentDetailsQuery.isSuccess) {
      const primaryPaymentMethod = paymentDetailsQuery?.data?.find(
        paymentDetail =>
          [PaymentMethodStatus.VALID, PaymentMethodStatus.EXPIRING].includes(
            paymentDetail.status,
          ) && paymentDetail.is_primary,
      );

      setPaymentData(primaryPaymentMethod as IPaymentMethod);
    }
  }, [paymentDetailsQuery?.data, paymentDetailsQuery.isSuccess]);

  const renderPaymentMethodIcon = (paymentMethod: IPaymentMethod) => {
    switch (paymentMethod?.type) {
      case PaymentMethodType.CARD:
        return (
          <Image
            src={`${CARD_PATH}${paymentMethod?.card?.brand}.svg`}
            width="30px"
            height="24px"
            preview={false}
          />
        );
      case PaymentMethodType.BANK_ACCOUNT:
        return <BankOutlined width={24} />;
      case PaymentMethodType.PAYPAL:
        return (
          <Image
            src={`${SVG_PATH}banks/paypal.svg`}
            width="90px"
            height="24px"
            preview={false}
          />
        );
      case PaymentMethodType.GOOGLE_PAY:
        return (
          <Image
            src={`${SVG_PATH}banks/google-pay.png`}
            width="55px"
            height="26px"
            preview={false}
          />
        );
      case PaymentMethodType.APPLE_PAY:
        return (
          <Image
            src={`${SVG_PATH}banks/apple-pay.png`}
            width="63px"
            height="26px"
            preview={false}
          />
        );
      default:
        return null;
    }
  };

  const onUpdatePaymentMethodModalClose = () => {
    queryClient.invalidateQueries(['paymentDetails']);
    setOpenPaymentModal(false);
  };

  if (paymentDetailsQuery.isLoading) {
    return <LocalLoader />;
  }

  if (paymentDetailsQuery.isError) {
    return <ApiError />;
  }

  return (
    <div className={styles.paymentMethod__container}>
      <div className={styles.paymentMethod__wrapper}>
        <Typography.Text>
          {paymentData
            ? t(`account.payment.type.${paymentData?.type}`)
            : t('account.payment.type.none', 'No payment method')}
        </Typography.Text>
        {renderPaymentMethodIcon(paymentData as IPaymentMethod)}
        {paymentData && (
          <Typography.Text>
            {getPaymentValue(paymentData as IPaymentMethod)}
          </Typography.Text>
        )}
      </div>
      {paymentData ? (
        <Button
          data-testid="payment_method-edit-button"
          onClick={() => setOpenPaymentModal(true)}
          icon={<EditOutlined />}
          iconPosition="end"
        >
          {t('account.payment.edit.link', 'Edit')}
        </Button>
      ) : (
        <Button
          data-testid="payment_method-add-button"
          onClick={() => setOpenPaymentModal(true)}
          icon={<PlusOutlined />}
          iconPosition="end"
        >
          {t('account.payment.add.link', 'Add a new payment method')}
        </Button>
      )}
      <UpdatePaymentMethod
        isOpen={openPaymentModal}
        onClose={onUpdatePaymentMethodModalClose}
        isB2B={isB2B}
      />
    </div>
  );
};

export default PaymentMethod;
