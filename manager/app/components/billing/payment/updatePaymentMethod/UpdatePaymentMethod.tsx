import { IModalProps } from 'types';

import useStyles from './UpdatePaymentMethod.styles';

import Modal from '@/app/components/ui/Modal/Modal';
import { SHOP_URL } from '@/app/utils/constants';

interface IUpdatePaymentMethodProps extends IModalProps {
  isB2B: boolean;
}
const UpdatePaymentMethod = ({
  isOpen,
  onClose,
  isB2B,
}: IUpdatePaymentMethodProps) => {
  const { styles } = useStyles();

  return (
    <Modal
      className={styles.modalContent}
      open={isOpen}
      onCancel={() => onClose()}
      isPaymentMethod
    >
      <div className={styles.modalContent}>
        <iframe
          className={styles.iframe}
          src={`${SHOP_URL}account-content?page_type=update_payment_method&is_b2b=${
            isB2B ? '1' : '0'
          }`}
          width="100%"
          height="100%"
        />
      </div>
    </Modal>
  );
};

export default UpdatePaymentMethod;
