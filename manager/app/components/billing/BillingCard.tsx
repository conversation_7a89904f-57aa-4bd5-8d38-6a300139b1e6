import { Card, Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

import BillingForm from './BillingForm';

const BillingCard = () => {
  const { t } = useTranslation();

  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('account.billing.title', 'Billing information')}
        </Typography.Title>
        <BillingForm />
      </Flex>
    </Card>
  );
};

export default BillingCard;
