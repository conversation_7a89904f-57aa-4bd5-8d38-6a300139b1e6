import { Link } from '@remix-run/react';
import { Col, Form, Input, Row, Flex, Select, message } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { logError } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import Flag from '@/app/components/ui/Flag';
import LocalLoader from '@/app/components/ui/Loader/LocalLoader';
import useBillingForm from '@/app/hooks/form/useBillingForm';
import { useBillingDetails } from '@/app/hooks/reactQuery/user/useUser';
import { ROUTES_PATH } from '@/app/utils/constants';

const formData = {
  first_name: '',
  last_name: '',
  address1: '',
  zipcode: '',
  city: '',
  country: '',
};

const BillingForm = () => {
  const { t } = useTranslation();

  // const { market } = useConfig();
  const billingDetailsQuery = useBillingDetails();

  const onSuccess = () => {
    message.success(
      t(
        'account.billing.update.success.message',
        'Your billing information has been updated.',
      ),
    );
  };

  const onError = (error: string) => {
    logError('Error updating billing information', error);
    message.error(
      `${t(
        'account.billing.update.error.message',
        `An error occurred when updating your billing information:`,
      )} ${error}`,
    );
  };

  const { control, reset, onSubmit, isDirty, isSubmitting } = useBillingForm(
    onSuccess,
    onError,
    formData,
  );

  const countryDefault = [
    {
      value: billingDetailsQuery.data?.country,
      label: billingDetailsQuery.data?.country ? (
        <Flag
          flex={true}
          value={billingDetailsQuery.data.country}
          flag={
            billingDetailsQuery.data.country.toUpperCase() as keyof typeof Flags
          }
        />
      ) : (
        billingDetailsQuery.data?.country
      ),
    },
  ];

  const handleBillingFormSubmit = () => {
    onSubmit();
  };

  useEffect(() => {
    if (billingDetailsQuery.isSuccess) {
      reset(billingDetailsQuery.data);
    }
  }, [billingDetailsQuery.data, billingDetailsQuery.isSuccess, reset]);

  if (!billingDetailsQuery.isSuccess) {
    return <LocalLoader />;
  }

  return (
    <Form
      layout="vertical"
      onFinish={handleBillingFormSubmit}
      initialValues={{
        first_name: billingDetailsQuery.data?.first_name,
        last_name: billingDetailsQuery.data?.last_name,
        address1: billingDetailsQuery.data?.address1,
        zipcode: billingDetailsQuery.data?.zipcode,
        city: billingDetailsQuery.data?.city,
        country: billingDetailsQuery.data?.country,
      }}
    >
      <Flex vertical>
        <Row gutter={24}>
          <Col xs={24} sm={24} md={12}>
            <Controller
              name="first_name"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Form.Item
                  name="first_name"
                  label={t('form.firstname.label', 'Firstname')}
                  validateStatus={error && 'error'}
                  help={error && error?.message}
                  hasFeedback={!!error}
                >
                  <Input
                    {...field}
                    id="billing-firstname-input"
                    data-testid="billing-firstname-input"
                    size="large"
                  />
                </Form.Item>
              )}
            />
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Controller
              name="last_name"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Form.Item
                  name="last_name"
                  label={t('form.lastname.label', 'Lastname')}
                  validateStatus={error && 'error'}
                  help={error && error?.message}
                  hasFeedback={!!error}
                >
                  <Input
                    {...field}
                    id="billing-lastname-input"
                    data-testid="billing-lastname-input"
                    size="large"
                  />
                </Form.Item>
              )}
            />
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Controller
              name="address1"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Form.Item
                  name="address1"
                  label={t('form.address1.label', 'Address')}
                  validateStatus={error && 'error'}
                  help={error && error?.message}
                  hasFeedback={!!error}
                >
                  <Input
                    {...field}
                    id="billing-address-input"
                    data-testid="billing-address-input"
                    size="large"
                  />
                </Form.Item>
              )}
            />
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Controller
              name="zipcode"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Form.Item
                  name="zipcode"
                  label={t('form.zipcode.label', 'Zipcode')}
                  validateStatus={error && 'error'}
                  help={error && error?.message}
                  hasFeedback={!!error}
                >
                  <Input
                    {...field}
                    id="billing-zipcode-input"
                    data-testid="billing-zipcode-input"
                    size="large"
                  />
                </Form.Item>
              )}
            />
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Controller
              name="city"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Form.Item
                  name="city"
                  label={t('form.city.label', 'City')}
                  validateStatus={error && 'error'}
                  help={error && error?.message}
                  hasFeedback={!!error}
                >
                  <Input
                    {...field}
                    id="billing-city-input"
                    data-testid="billing-city-input"
                    size="large"
                  />
                </Form.Item>
              )}
            />
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Controller
              name="country"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Form.Item
                  name="country"
                  label={t('form.country.label', 'Country')}
                  validateStatus={error && 'error'}
                  help={
                    <Trans
                      i18nKey="account.billing.contactSupport"
                      defaults="In order to change your country, <0>contact the support</0>."
                      components={[<Link to={ROUTES_PATH.SUPPORT} />]}
                    />
                  }
                  hasFeedback={!!error}
                >
                  <Select
                    {...field}
                    id="billing-country-input"
                    data-testid="billing-country-input"
                    disabled
                    size="large"
                    options={countryDefault}
                  />
                </Form.Item>
              )}
            />
          </Col>
        </Row>
        <Flex justify="flex-end">
          <Button
            data-testid="billing-submit-button"
            disabled={!isDirty || isSubmitting}
            loading={isSubmitting}
            htmlType="submit"
            blockMobile
          >
            {t(
              'account.billing.update.submit.label',
              'Save billing information',
            )}
          </Button>
        </Flex>
      </Flex>
    </Form>
  );
};

export default BillingForm;
