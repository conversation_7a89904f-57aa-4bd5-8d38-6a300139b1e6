import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';

import BillingTable from './BillingTable';
import ApiError from '../errors/apiError';
import LocalLoader from '../ui/Loader/LocalLoader';

import {
  useBillingDetails,
  useInvoices,
} from '@/app/hooks/reactQuery/user/useUser';
import { IInvoice } from '@/app/types/api';

const InvoicesManager = () => {
  const invoicesQuery = useInvoices();
  const [invoices, setInvoices] = useState<[] | IInvoice[]>([]);
  const billingDetailsQuery = useBillingDetails();

  useEffect(() => {
    if (invoicesQuery.isSuccess) {
      setInvoices(invoicesQuery.data as IInvoice[]);
    }
  }, [invoicesQuery.data, invoicesQuery.isSuccess]);

  if (isEmpty(billingDetailsQuery.data)) {
    return <></>;
  }

  if (invoicesQuery.isLoading) {
    return <LocalLoader />;
  }

  if (invoicesQuery.isError) {
    return <ApiError />;
  }

  return <BillingTable invoices={invoices} />;
};

export default InvoicesManager;
