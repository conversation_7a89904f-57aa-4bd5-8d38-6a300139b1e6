import {
  CloudDownloadOutlined,
  FileOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { Dropdown, MenuProps, message, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { logError } from 'utils';

import Button from '../ui/Button/Button';

import {
  useDownloadCsvInvoice,
  useInvoiceDownloadLink,
} from '@/app/hooks/reactQuery/user/useUser';

interface IInvoicesDownloadProps {
  invoiceId: string;
}

const InvoicesDownload = ({ invoiceId }: IInvoicesDownloadProps) => {
  const { t } = useTranslation();
  const downloadCsvInvoice = useDownloadCsvInvoice();
  const fetchInvoiceDownloadLink = useInvoiceDownloadLink();

  const downloadCSVInvoiceOnClick = async () => {
    try {
      await downloadCsvInvoice.mutateAsync(invoiceId, {
        onSuccess: csvInvoiceBlob => {
          if (!csvInvoiceBlob) {
            throw 'Empty blob received';
            return;
          }
          // Create blob link to download
          const url = window.URL.createObjectURL(new Blob([csvInvoiceBlob]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `Shadow_invoice_${invoiceId}.csv`);
          // Append to html page
          document.body.appendChild(link);
          // Force download
          link.click();
          // Clean up and remove the link
          link.parentNode?.removeChild(link);
        },
        onError: error => {
          throw error.message;
        },
      });
    } catch (error) {
      logError('Error on csv download request:', error);
      message.error(
        `${t(
          '.invoices.list.download.error',
          `An error occurred while downloading your invoice, please try again later`,
        )} ${error}`,
      );
    }
  };

  const downloadPDFInvoiceOnClick = async () => {
    try {
      await fetchInvoiceDownloadLink.mutateAsync(invoiceId, {
        onSuccess: invoiceDownloadResponse => {
          if (!invoiceDownloadResponse?.link) {
            throw `No download link for invoice ${invoiceId}`;
          }
          window.location.href = invoiceDownloadResponse?.link;
        },
        onError: error => {
          throw error.message;
        },
      });
    } catch (error) {
      logError('Error pdf link download request:', error);
      message.error(
        `${t(
          '.invoices.list.download.error',
          `An error occurred while downloading your invoice, please try again later`,
        )} ${error}`,
      );
    }
  };
  const items: MenuProps['items'] = [
    {
      ...{ 'data-testid': `download_invoice_pdf-${invoiceId}-button` },
      label: (
        <Typography.Link onClick={downloadPDFInvoiceOnClick}>
          {t('invoices.list.body.link.pdf', 'Download PDF')}
        </Typography.Link>
      ),
      key: t('invoices.list.body.link.pdf', 'Download PDF'),
      icon: <FileOutlined />,
    },
    {
      ...{ 'data-testid': `download_invoice_csv-${invoiceId}-button` },
      label: (
        <Typography.Link onClick={downloadCSVInvoiceOnClick}>
          {t('invoices.list.body.link.csv', 'Download CSV')}
        </Typography.Link>
      ),
      key: t('invoices.list.body.link.csv', 'Download CSV'),
      icon: <TableOutlined />,
    },
  ];

  return (
    <Dropdown menu={{ items }} trigger={['click']}>
      <Button
        data-testid={`download_invoice-${invoiceId}-button`}
        color="default"
        shape={undefined}
        variant="link"
      >
        <CloudDownloadOutlined />
      </Button>
    </Dropdown>
  );
};

export default InvoicesDownload;
