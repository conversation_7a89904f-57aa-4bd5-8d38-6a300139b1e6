import { Card, Col, Flex, Form, Input, message, Row, Typography } from 'antd';
import { useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { IMemberDetails } from 'types';
import { HIDDEN_VAT_NUMBER_MARKETS } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import useCompanyForm from '@/app/hooks/form/useCompanyForm';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { useBillingDetails } from '@/app/hooks/reactQuery/user/useUser';
import { useConfig } from '@/app/hooks/store/useConfig';

const formData = {
  company: '',
  vat_number: '',
};

const CompanyCard = () => {
  const { t } = useTranslation();
  const { market } = useConfig();
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const billingDetailsQuery = useBillingDetails();

  const onSuccess = () => {
    message.success(
      t(
        'account.company.update.success.message',
        'Your company information has been updated.',
      ),
    );
  };

  const onError = (error: string) => {
    message.error(
      `${t(
        'account.company.update.error.message',
        `An error occurred when updating your company information:`,
      )} ${error}`,
    );
  };

  const { onSubmit, control, reset, isDirty, isSubmitting } = useCompanyForm(
    onSuccess,
    onError,
    formData,
  );

  const shouldDisplayVatField = !HIDDEN_VAT_NUMBER_MARKETS.includes(market);

  const handleFormSubmit = () => {
    onSubmit();
  };

  useEffect(() => {
    if (billingDetailsQuery.isSuccess) {
      reset(billingDetailsQuery.data);
    }
  }, [reset, billingDetailsQuery.isSuccess, billingDetailsQuery.data]);

  if (!isB2b) {
    return null;
  }

  return (
    billingDetailsQuery.isSuccess && (
      <Card bordered={false}>
        <Flex vertical gap="large">
          <Typography.Title level={2}>
            {t('company.title', 'Your company')}
          </Typography.Title>
          <Form
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              company: billingDetailsQuery.data?.company,
              vat_number: billingDetailsQuery.data?.vat_number,
            }}
          >
            <Row gutter={24}>
              <Col xs={24} sm={24} md={shouldDisplayVatField ? 12 : 24}>
                <Controller
                  name="company"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <Form.Item
                      name="company"
                      validateStatus={error && 'error'}
                      help={error && error?.message}
                      hasFeedback={!!error}
                      label={t('form.companyName.label', 'Company name')}
                    >
                      <Input
                        {...field}
                        id="company-input"
                        data-testid="company-name-input"
                        size="large"
                      />
                    </Form.Item>
                  )}
                />
              </Col>
              {shouldDisplayVatField && (
                <Col xs={24} sm={24} md={12}>
                  <Controller
                    name="vat_number"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <Form.Item
                        name="vat_number"
                        validateStatus={error && 'error'}
                        help={error && error?.message}
                        hasFeedback={!!error}
                        label={t('form.vatNumber.label', 'VAT number')}
                      >
                        <Input
                          {...field}
                          id="vat_number-input"
                          data-testid="company-vat-input"
                          size="large"
                        />
                      </Form.Item>
                    )}
                  />
                </Col>
              )}
            </Row>
            <Flex justify="flex-end">
              <Button
                data-testid="company-submit-button"
                htmlType="submit"
                disabled={!isDirty || isSubmitting}
                loading={isSubmitting}
                blockMobile
              >
                {t('company.button', 'Save company information')}
              </Button>
            </Flex>
          </Form>
        </Flex>
      </Card>
    )
  );
};

export default CompanyCard;
