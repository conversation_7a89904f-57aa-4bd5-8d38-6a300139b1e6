import { Typography } from 'antd';
import { useMemo } from 'react';
import { Language } from 'types';
import { formatDate, DATE_FORMAT_BY_LANGUAGE } from 'utils';

import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { DEFAULT_LANGUAGE } from '@/app/utils/constants';

interface IInvoicesDateProps {
  date: number;
}

const InvoicesDate = ({ date }: IInvoicesDateProps) => {
  const currentMemberQuery = useCurrentMember();

  const formattedDate = useMemo(() => {
    const dateInMilliseconds =
      date * 1000 > new Date().getTime() ? date : date * 1000;

    const userLanguage = currentMemberQuery?.data?.user?.language
      ? (currentMemberQuery?.data?.user?.language as Language)
      : DEFAULT_LANGUAGE;

    return formatDate(
      dateInMilliseconds,
      DATE_FORMAT_BY_LANGUAGE[userLanguage],
    );
  }, [date, currentMemberQuery.data]);

  return <Typography.Text>{formattedDate}</Typography.Text>;
};

export default InvoicesDate;
