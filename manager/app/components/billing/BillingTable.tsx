import { InfoCircleOutlined } from '@ant-design/icons';
import {
  Badge,
  Flex,
  Grid,
  Space,
  Table,
  TableProps,
  Tooltip,
  Typography,
} from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import InvoicesDate from './InvoicesDate';
import InvoicesDownload from './InvoicesDownload';

import { usePrice } from '@/app/hooks/usePrice';
import { IInvoice } from '@/app/types/api';
import { INVOICE_STATUS_COLOR, InvoicesStatus } from '@/app/types/invoices';
import {
  INVOICES_PER_PAGE,
  LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
} from '@/app/utils/constants';

interface IInvoicesListProps {
  invoices: IInvoice[];
}

const BillingTable = ({ invoices }: IInvoicesListProps) => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const { formatPrice } = usePrice();
  const [pageSize, setPageSize] = useState(INVOICES_PER_PAGE);

  const renderTotalInvoices = (total: number) => {
    return total > 0 ? (
      <Trans
        i18nKey="invoices.total"
        defaults="Total: {{total}} invoices"
        values={{ total }}
      />
    ) : null;
  };

  const onPaginationChange = (page: number, size: number) => {
    if (size !== pageSize) {
      setPageSize(size);
    }
  };

  const renderDateColumn = ({
    status,
    date,
  }: Pick<IInvoice, 'status' | 'date'>) => {
    return screens.md ? (
      <Space>
        <InvoicesDate date={date} />
      </Space>
    ) : (
      <Space>
        <InvoicesDate date={date} />
        <Badge status={INVOICE_STATUS_COLOR[status]} />
        {status !== InvoicesStatus.PAID && (
          <Tooltip
            placement="top"
            title={
              <Typography.Text>
                {t(
                  'invoices.list.body.statusTooltip.notPaid',
                  "Your invoice hasn't been paid, please update your payment method or retry a payment.",
                )}
              </Typography.Text>
            }
            arrow={true}
          >
            <Typography.Text type="danger">
              <InfoCircleOutlined />
            </Typography.Text>
          </Tooltip>
        )}
      </Space>
    );
  };

  const renderStatusColumn = ({ status }: IInvoice) => (
    <Space>
      <Badge
        status={INVOICE_STATUS_COLOR[status]}
        text={t(`invoices.list.body.status.${status}`)}
      />
      {status !== InvoicesStatus.PAID && (
        <Tooltip
          placement="top"
          title={
            <Typography.Text>
              {t(`invoices.list.body.statusTooltip.${status}`)}
            </Typography.Text>
          }
          arrow={true}
        >
          <Typography.Text type={INVOICE_STATUS_COLOR[status]}>
            <InfoCircleOutlined />
          </Typography.Text>
        </Tooltip>
      )}
    </Space>
  );

  const columns: TableProps<IInvoice>['columns'] = [
    {
      title: t('invoices.list.heading.date', 'Date'),
      dataIndex: 'date',
      key: 'date',
      render: (_, { status, date }) => renderDateColumn({ status, date }),
    },
    {
      title: t('invoices.list.heading.status', 'Status'),
      key: 'status',
      dataIndex: 'tags',
      responsive: ['md'],
      render: (_, status) => renderStatusColumn(status),
      width: '25%',
    },
    {
      title: t('invoices.list.heading.total', 'Amount'),
      key: 'amount',
      dataIndex: 'total',
      render: (_, amount) => <Space>{formatPrice(amount.total)}</Space>,
      width: '25%',
    },
    {
      key: 'download',
      render: (_, invoice) => (
        <Flex justify="end">
          <InvoicesDownload invoiceId={invoice.id} />
        </Flex>
      ),
      width: '5%',
    },
  ];

  if (invoices.length === 0) {
    return (
      <Typography.Text>
        {t('invoices.noInvoice', 'No invoices')}
      </Typography.Text>
    );
  }

  return (
    <Table
      columns={columns}
      dataSource={invoices}
      pagination={{
        hideOnSinglePage:
          invoices?.length < LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
        showSizeChanger: true,
        showQuickJumper: false,
        defaultCurrent: 1,
        total: invoices?.length,
        showTotal: total => renderTotalInvoices(total),
        pageSize: pageSize,
        responsive: true,
        onShowSizeChange: (current, size) => onPaginationChange(current, size),
      }}
      rowKey={item => item.id}
    />
  );
};

export default BillingTable;
