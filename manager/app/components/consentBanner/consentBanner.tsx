import { DidomiSDK, IDidomiObject } from '@didomi/react';
import { useTheme } from 'antd-style';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { useAuthentication } from '@/app/hooks/reactQuery/user/useAuthentication';
import { useConfig } from '@/app/hooks/store/useConfig';
import { useSocial } from '@/app/hooks/store/useSocial';
import { IPurposeStatus } from '@/app/types/tracking';
import {
  DIDOMI_DEFAULT_NOTICE_ID,
  DIDOMI_PUBLIC_API_KEY,
  PRIVACY_POLICY_URL,
} from '@/app/utils/constants';
import { getPurposeStatus } from '@/app/utils/tracking';

const ConsentBanner = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { didomiLanguage } = useConfig();
  const { setCanLoadLiveChat } = useSocial();
  const { isAuthenticated } = useAuthentication();

  const getDidomiConfig = useCallback(
    () => ({
      app: {
        name: 'Shadow',
        country: 'FR',
        apiKey: DIDOMI_PUBLIC_API_KEY,
        privacyPolicyURL: PRIVACY_POLICY_URL,
      },
      tagManager: {
        provider: 'gtm',
      },
      notice: {
        position: 'popup',
        denyOptions: {
          link: true,
          button: 'none',
          cross: false,
        },
      },
      theme: {
        color: theme.colorPrimary,
        linkColor: theme.colorPrimary,
        font: 'Nexa Text',
        buttons: {
          regularButtons: {
            backgroundColor: theme.colorWhite,
            borderRadius: '100px',
            borderWidth: 0,
            textColor: theme.colorPrimary,
          },
          highlightButtons: {
            borderRadius: '100px',
            borderWidth: 0,
          },
        },
      },
      preferences: {
        content: {
          title: t('consentBanner.title', 'Shadow consent banner'),
        },
      },
      languages: {
        enabled: [didomiLanguage], // disable browser language detection by forcing user's language
        default: 'en',
      },
    }),
    [didomiLanguage], // eslint-disable-line react-hooks/exhaustive-deps
  );

  const toggleLiveChat = function (status: IPurposeStatus) {
    setCanLoadLiveChat(status.preferences ?? false);
  };

  const onReady = function (Didomi: IDidomiObject) {
    toggleLiveChat(getPurposeStatus(Didomi));

    Didomi.on('consent.changed', () => {
      toggleLiveChat(getPurposeStatus(Didomi));
    });
  };

  if (!didomiLanguage || !isAuthenticated) {
    return <></>;
  }

  return (
    <DidomiSDK
      iabVersion={2}
      config={getDidomiConfig()}
      noticeId={DIDOMI_DEFAULT_NOTICE_ID}
      gdprAppliesGlobally={true}
      onReady={onReady}
      onNoticeClickAgree={() => {
        // Disabled for now, as the new sprinklr chat does not add cookies
        // setCanLoadLiveChat(true);
      }}
    />
  );
};

export default ConsentBanner;
