import { DownloadOutlined } from '@ant-design/icons';
import { Flex, Grid } from 'antd';
import { useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
interface IUserActionsProps {
  openDownloadDataModal: () => void;
  isLoading?: boolean;
  isDisabled?: boolean;
}

const UserActions = ({
  openDownloadDataModal,
  isLoading,
  isDisabled,
}: IUserActionsProps) => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;

  const screens = useBreakpoint();

  return (
    <Flex justify={!screens.md ? 'center' : 'flex-end'} gap={24} wrap="wrap">
      <Button
        data-testid="user-download-button"
        secondary
        icon={<DownloadOutlined width={24} />}
        onClick={openDownloadDataModal}
        blockMobile
      >
        {t('account.user.downloadData.label', 'Download my data')}
      </Button>
      <Button
        data-testid="user-submit-button"
        htmlType="submit"
        disabled={isDisabled}
        loading={isLoading}
        blockMobile
      >
        {t('account.user.form.submit.label', 'Save account information')}
      </Button>
    </Flex>
  );
};

export default UserActions;
