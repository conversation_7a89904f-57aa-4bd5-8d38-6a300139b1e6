import { useQueryClient } from '@tanstack/react-query';
import {
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Typography,
} from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DateFormat, IMemberDetails, Market } from 'types';
import {
  FORM_VALIDATION_MIN_AGE,
  FORM_VALIDATION_USER_NAME_MAX_LENGTH,
  formatDate,
  subYears,
} from 'utils';

import UserActions from './UserAction';
import DownloadModale from '../DownloadModale';

import Flag from '@/app/components/ui/Flag';
import PhoneInputUI from '@/app/components/ui/phoneInput/PhoneInput';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { useUpdateUserDetails } from '@/app/hooks/reactQuery/user/useUser';
import { useFormDirty } from '@/app/hooks/useFormDirty';
import { IUpdateUserDetailsPayload } from '@/app/types/api';
import { LANGUAGES_WITH_FLAG_OPTIONS } from '@/app/utils/constants';

const UserForm = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];
  const country = currentMemberQuery.data?.user?.country as Market;
  const { mutateAsync, isLoading } = useUpdateUserDetails();

  const initialValues = {
    first_name: currentMemberQuery.data?.user?.first_name,
    last_name: currentMemberQuery.data?.user?.last_name,
    phone: currentMemberQuery.data?.user?.phone ?? '',
    language: currentMemberQuery.data?.user?.language,
    birthdate: currentMemberQuery.data?.user?.birthdate
      ? dayjs(currentMemberQuery.data?.user?.birthdate as string)
      : '',
  };

  const { isDirty, handleFieldsChange } = useFormDirty(form, initialValues);
  const handleSubmit = (values: IUpdateUserDetailsPayload) => {
    mutateAsync({
      first_name: values.first_name,
      last_name: values.last_name,
      birthdate: !isB2b
        ? formatDate(values.birthdate, DateFormat.HYPHEN_US)
        : '',
      phone: values.phone,
      language: values.language,
    })
      .then(() => {
        message.success(
          t(
            'account.user.update.success.message',
            'Your personal information has been updated.',
          ),
        );
      })
      .catch(e => {
        message.error(
          `${t(
            'account.user.update.error.message',
            `An error occurred when updating your personal information:`,
          )} ${e}`,
        );
      })
      .finally(() => {
        queryClient.refetchQueries(['userDetails']);
      });
  };

  const [isModaleOpen, setIsModaleOpen] = useState<boolean>(false);

  const languageOptions = LANGUAGES_WITH_FLAG_OPTIONS.map(language => ({
    value: language.value,
    label: language.flag ? (
      <Flag
        strong
        flex={true}
        value={language.label}
        flag={language.flag.toUpperCase() as keyof typeof Flags}
      />
    ) : (
      language.label
    ),
    ...{ 'data-testid': `user_language-${language.value}-option` },
  }));

  return (
    <>
      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        onFieldsChange={handleFieldsChange}
        initialValues={initialValues}
        requiredMark={false}
      >
        <Row gutter={24}>
          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="first_name"
              label={t('form.firstName.label', 'First name')}
              required
              rules={[
                {
                  required: true,
                  message: t(
                    'form.firstName.error.required',
                    'First name is required',
                  ),
                },
                {
                  max: FORM_VALIDATION_USER_NAME_MAX_LENGTH,
                  message: t(
                    'form.default.error.maxLength',
                    'Maximum allowed: {{ maxLength }} characters',
                    {
                      maxLength: FORM_VALIDATION_USER_NAME_MAX_LENGTH,
                    },
                  ),
                },
              ]}
            >
              <Input
                id="first_name-input"
                data-testid="user_first_name-input"
                size="large"
                placeholder={t('form.firstName.label', 'First name')}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="last_name"
              label={t('form.lastName.label', 'Last name')}
              rules={[
                {
                  required: true,
                  message: t(
                    'form.lastName.error.required',
                    'Last name is required',
                  ),
                },
                {
                  max: FORM_VALIDATION_USER_NAME_MAX_LENGTH,
                  message: t(
                    'form.default.error.maxLength',
                    'Maximum allowed: {{ maxLength }} characters',
                    {
                      maxLength: FORM_VALIDATION_USER_NAME_MAX_LENGTH,
                    },
                  ),
                },
              ]}
            >
              <Input
                id="last_name-input"
                data-testid="user_last_name-input"
                size="large"
                placeholder={t('form.lastName.label', 'Last name')}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="phone"
              label={t('form.phone.label', 'Phone number')}
              rules={[
                {
                  required: true,
                  message: t('form.phone.error.required', 'Phone is required'),
                },
              ]}
            >
              <PhoneInputUI
                data-testid="user_phone-input"
                defaultCountry={country}
                value={currentMemberQuery.data?.user?.phone ?? ''}
              />
            </Form.Item>
          </Col>
          {!isB2b && (
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                name="birthdate"
                label={t('form.birthdate.label', 'Birthdate')}
                tooltip={
                  <Typography.Text>
                    {t(
                      'form.birthdate.error.invalid',
                      'You must be 15 or older',
                    )}
                  </Typography.Text>
                }
                rules={[
                  {
                    required: true,
                    message: t(
                      'form.birthdate.error.required',
                      'Birthdate is required',
                    ),
                  },
                  {
                    validator: (_, value: Date) => {
                      if (value < subYears(FORM_VALIDATION_MIN_AGE)) {
                        return Promise.resolve();
                      } else {
                        return Promise.reject(
                          t(
                            'form.birthdate.error.invalid',
                            'You must be 15 or older',
                          ),
                        );
                      }
                    },
                  },
                ]}
              >
                <DatePicker
                  id="birthdate-input"
                  //maxDate={dayjs(subYears(FORM_VALIDATION_MIN_AGE))}
                  data-testid="user_birthdate-input"
                  format={{
                    format:
                      country.toLowerCase() === 'us' ||
                      country.toLowerCase() === 'ca'
                        ? 'MM/DD/YYYY'
                        : 'DD/MM/YYYY',
                    type: 'mask',
                  }}
                  placeholder={t('form.birthdate.label', 'Birthdate')}
                  size="large"
                  style={{
                    width: '100%',
                  }}
                  allowClear={false}
                  disabledDate={current =>
                    // disable date selection AFTER today
                    current && current > dayjs().endOf('day')
                  }
                />
              </Form.Item>
            </Col>
          )}
          <Col xs={24} sm={24} md={isB2b ? 12 : 24}>
            <Form.Item
              name="language"
              label={t('form.language.label', 'Language')}
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                data-testid="user_language-input"
                options={languageOptions}
                size="large"
                id="language"
                disabled={languageOptions.length === 1}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <UserActions
              openDownloadDataModal={() => setIsModaleOpen(true)}
              isLoading={isLoading}
              isDisabled={!isDirty}
            />
          </Col>
        </Row>
      </Form>
      <DownloadModale
        isModaleOpen={isModaleOpen}
        setIsModaleOpen={setIsModaleOpen}
      />
    </>
  );
};

export default UserForm;
