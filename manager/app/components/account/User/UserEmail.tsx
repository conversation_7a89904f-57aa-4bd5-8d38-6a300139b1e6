import { EditOutlined, MailOutlined } from '@ant-design/icons';
import { Flex, Form, Grid, Input, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { UserRole } from 'types';

import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import {
  AUTH_RESEND_VERIFICATION_EMAIL,
  AUTH_SETTINGS,
} from '@/app/utils/constants';
import { isKratosFederationProvider } from '@/app/utils/persistedUser';

const UserEmail = () => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const currentMemberQuery = useCurrentMember();
  const { user } = useAuth();

  const [email, setEmail] = useState<string>('');
  const [isMember, setIsMember] = useState(false);

  const displayResendVerificationEmailLink =
    !user?.profile.email_verified && isKratosFederationProvider();

  const isNotMemberAndIsKratosFedered =
    !isMember && isKratosFederationProvider();

  useEffect(() => {
    if (currentMemberQuery.isSuccess) {
      setEmail(currentMemberQuery.data?.user?.email ?? '');
      setIsMember(currentMemberQuery.data?.role === UserRole.MEMBER);
    }
  }, [
    currentMemberQuery.data?.role,
    currentMemberQuery.data?.user?.email,
    currentMemberQuery.isSuccess,
  ]);

  return (
    <Form.Item
      help={
        displayResendVerificationEmailLink && (
          <Typography.Link
            data-testid="resend_verification_email-link"
            href={AUTH_RESEND_VERIFICATION_EMAIL}
            target="_blank"
          >
            {t(
              'account.user.resendVerificationEmail.label',
              'Resend the verification email',
            )}
          </Typography.Link>
        )
      }
    >
      <Input
        prefix={<MailOutlined />}
        placeholder={currentMemberQuery.data?.user?.email}
        variant="filled"
        value={email}
        size="large"
        disabled
        addonAfter={
          isNotMemberAndIsKratosFedered && (
            <Typography.Link
              data-testid="edit_email-link"
              href={AUTH_SETTINGS}
              target="_blank"
            >
              <Flex gap="small">
                {screens.md &&
                  t('account.user.editEmail.label', 'Edit my email')}
                <EditOutlined />
              </Flex>
            </Typography.Link>
          )
        }
      />
    </Form.Item>
  );
};

export default UserEmail;
