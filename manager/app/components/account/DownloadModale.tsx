import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { logError } from 'utils';

// import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import WordConfirmationForm from './WordConfirmationForm';

import Modal from '@/app/components/ui/Modal/Modal';
import { usePersonalData } from '@/app/hooks/reactQuery/user/useUser';
import { useConfig } from '@/app/hooks/store/useConfig';
import { FORM_VALIDATION_WORD_COMPARE_DOWNLOAD_DATA } from '@/app/utils/constants';
import { downloadObjectAsJson } from '@/app/utils/download';

interface IDownloadModaleProps {
  isModaleOpen: boolean;
  setIsModaleOpen: (isModaleOpen: boolean) => void;
}

const DownloadModale = ({
  isModaleOpen,
  setIsModaleOpen,
}: IDownloadModaleProps) => {
  const { t } = useTranslation();
  const { refetch: refetchPersonalData } = usePersonalData();
  const { language } = useConfig();

  const [isDownloadingData, setIsDownloadingData] = useState<boolean>(false);

  const downloadPersonalData = async () => {
    setIsDownloadingData(true);

    try {
      const { data } = await refetchPersonalData();
      downloadObjectAsJson(data ?? {}, 'shadow_customer_data');
    } catch (e) {
      logError('downloadPersonalData', e);
    } finally {
      setIsDownloadingData(false);
      closeModal();
    }
  };

  const closeModal = () => {
    setIsModaleOpen(false);
  };

  return (
    <Modal
      destroyOnClose={true}
      closable={!isDownloadingData}
      maskClosable={!isDownloadingData}
      open={isModaleOpen}
      onCancel={() => setIsModaleOpen(false)}
      title={t('downloadData.modal.title', 'Download my personal data')}
    >
      <WordConfirmationForm
        isLoading={isDownloadingData}
        onSubmitting={isSubmitting => setIsDownloadingData(isSubmitting)}
        onSuccess={downloadPersonalData}
        onCancel={closeModal}
        wordToCompare={FORM_VALIDATION_WORD_COMPARE_DOWNLOAD_DATA[language]}
      />
    </Modal>
  );
};

export default DownloadModale;
