import { LockOutlined } from '@ant-design/icons';
import { Card, Flex, Typography } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import { AUTH_SOCIALS_LOGIN } from '@/app/utils/constants';

const LoginMethodCard = () => {
  const { t } = useTranslation();
  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('account.user.socials.title', 'Socials Login')}
        </Typography.Title>
        <Typography.Text>
          <Trans i18nKey={'account.user.socials.text'}>
            To log in to your Shadow account, you can set up your Shadow
            password, or link an external account. Email communications from
            Shadow will continue to be sent to the email address of your Shadow
            account.
          </Trans>
        </Typography.Text>
        <Flex justify="flex-end">
          <Button
            data-testid="manage_login-button"
            href={AUTH_SOCIALS_LOGIN}
            secondary
            icon={<LockOutlined />}
            blockMobile
          >
            {t('account.user.socials.buttons.text', 'Manage my social logins')}
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
};

export default LoginMethodCard;
