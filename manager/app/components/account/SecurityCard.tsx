import { EditOutlined, LockOutlined } from '@ant-design/icons';
import { Card, Flex, Grid, Typography } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import {
  AUTH_SECURITY_HUB,
  AUTH_SETTINGS,
  SUPPORT_SECURITY_URL,
} from '@/app/utils/constants';

const SecurityCard = () => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;

  const screens = useBreakpoint();

  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('account.user.security.title', 'Security')}
        </Typography.Title>
        <Typography.Text>
          <Trans
            i18nKey="account.user.security.text"
            defaults="In your Security Hub, you’ll able to secure your account effectively by accessing your sessions history or strengthen your Shadow by adding two factor authentication and generating backup codes. <CustomLink href='https://support.shadow.tech/en_US/articles/authentication/how-does-twofactor-authentication-2fa-work-on-shadow/64e3101304a01d0067a5ef1f'>Learn more about security at Shadow.</CustomLink>"
            components={{
              CustomLink: (
                <a
                  data-testid="security_learn_more-link"
                  href={SUPPORT_SECURITY_URL}
                  target="_blank"
                />
              ),
            }}
          />
        </Typography.Text>
        <Flex
          justify={!screens.md ? 'center' : 'flex-end'}
          gap={24}
          wrap="wrap"
        >
          <Button
            data-testid="edit_password-button"
            secondary
            icon={<EditOutlined width={24} />}
            href={AUTH_SETTINGS}
            blockMobile
          >
            {t('account.user.security.buttons.password', 'Edit my password')}
          </Button>
          <Button
            data-testid="manage_security-button"
            icon={<LockOutlined width={24} />}
            href={AUTH_SECURITY_HUB}
            blockMobile
          >
            {t(
              'account.user.security.buttons.hub',
              'Manage my security settings',
            )}
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
};

export default SecurityCard;
