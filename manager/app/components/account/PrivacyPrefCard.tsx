import { LockOutlined } from '@ant-design/icons';
import { Card, Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';

const PrivacyPrefCard = () => {
  const { t } = useTranslation();
  const openPrivacyPreferences = () => {
    window.Didomi?.preferences?.show();
  };

  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('account.privacy.title', 'Manage your privacy preferences')}
        </Typography.Title>
        <Flex justify="flex-end">
          <Button
            data-testid="manage_privacy_preferences-button"
            onClick={openPrivacyPreferences}
            secondary
            icon={<LockOutlined />}
            blockMobile
          >
            {t('account.privacy.cta.label', 'Change my privacy preferences')}
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
};

export default PrivacyPrefCard;
