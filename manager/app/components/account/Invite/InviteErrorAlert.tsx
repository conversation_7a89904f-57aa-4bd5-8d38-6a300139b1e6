import { useNavigate } from '@remix-run/react';
import { Alert, Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { SVG_PATH } from 'utils';

import useStyles from './InviteErrorAlert.styles';

import Button from '@/app/components/ui/Button/Button';
import { ROUTES_PATH } from '@/app/utils/constants';

export const InviteErrorAlert = ({
  errorMessage,
}: {
  errorMessage: string;
}) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const navigate = useNavigate();
  const { signoutRedirect } = useAuth();

  const handleGoToManager = () => {
    navigate(ROUTES_PATH.VM_MANAGER);
  };

  const handleLogOut = () => {
    void signoutRedirect();
  };

  return (
    <Flex justify="center" align="center" vertical className={styles.container}>
      <img
        className={styles.logo}
        src={`${SVG_PATH}logos/shadow-gradient.svg`}
        alt={t('header.logoAlt', 'Shadow')}
      />
      <Alert
        className={styles.alert}
        message={t('userManager.inviteError.title', 'Invitation failed')}
        description={t(
          'userManager.inviteError.description',
          'An error occurred while processing your invitation. Please try again or contact support for assistance. (Error: {{errorMessage}})',
          { errorMessage },
        )}
        type="error"
        showIcon
      />
      <Flex
        justify="center"
        align="flex-start"
        gap="small"
        className={styles.buttons}
      >
        <Button
          data-testid="invite-error-alert-gotomanager-button"
          onClick={handleGoToManager}
        >
          {t('userManager.inviteError.goToManager', 'Continue to Manager')}
        </Button>
        <Button
          data-testid="invite-error-alert-logout-button"
          secondary
          onClick={handleLogOut}
        >
          {t('header.logout.label', 'Logout')}
        </Button>
      </Flex>
    </Flex>
  );
};
