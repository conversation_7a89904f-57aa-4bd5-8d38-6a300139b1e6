import { useSearchParams } from '@remix-run/react';
import { Alert } from 'antd';
import { useTranslation } from 'react-i18next';

export const InviteSuccessAlert = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const showInviteSuccess = searchParams.has('invite_success');

  return (
    <>
      {showInviteSuccess && (
        <Alert
          message={t('userManager.inviteSuccess.title', 'Invitation confirmed')}
          description={t(
            'userManager.inviteSuccess.description',
            'You can now access your account and start using Shadow. Please also take a moment to review and complete your profile information below.',
          )}
          type="success"
          showIcon
        />
      )}
    </>
  );
};
