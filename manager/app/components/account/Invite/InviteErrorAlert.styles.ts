import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    container: css`
      height: 100vh;
      padding: 16px;
      max-width: 500px;
      margin: 0 auto;
    `,

    alert: css`
      margin-top: 16px;
      margin-bottom: 16px;
    `,

    logo: css`
      width: 85px;
      margin-bottom: 8px;
    `,

    buttons: css`
      width: 100%;
      flex-direction: column;
      gap: 16px;
      align-items: center;

      @media (min-width: ${token.screenSM}px) {
        flex-direction: row;
      }
    `,
  };
});
