import { Card, Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';

import UserEmail from './User/UserEmail';
import UserForm from './User/UserForm';
import UserInfoCardSkeleton from './UserInfoCardSkeleton';

import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';

const UserInfoCard = () => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();

  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('account.user.title', 'Account information')}
        </Typography.Title>
        {!currentMemberQuery.isSuccess ? (
          <UserInfoCardSkeleton />
        ) : (
          <>
            <UserEmail />
            <UserForm />
          </>
        )}
      </Flex>
    </Card>
  );
};

export default UserInfoCard;
