import { Flex, Form, Input } from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import Button from '@/app/components/ui/Button/Button';
import type { IFormProps } from '@/app/types/forms';

interface WordConfirmationForm extends IFormProps {
  wordToCompare: string | undefined;
  isLoading?: boolean;
}

const WordConfirmationForm = ({
  isLoading,
  onSuccess = () => null,
  wordToCompare = '',
  onCancel = () => null,
}: WordConfirmationForm) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [disabledSubmit, setDisabledSubmit] = useState(true);

  const handleFormChange = () => {
    const hasSameValue = form.getFieldValue('word') === wordToCompare;
    setDisabledSubmit(!hasSameValue);
  };

  return (
    <Flex gap={16} vertical>
      <Form
        form={form}
        onFieldsChange={handleFormChange}
        onFinish={onSuccess}
        layout="vertical"
        requiredMark={false}
      >
        <Flex vertical gap={16}>
          <Form.Item
            name="word"
            validateTrigger={false}
            label={
              <div>
                <Trans
                  i18nKey="form.word.info"
                  defaults="Please write <bold>{{ wordToCompare }}</bold> below in order to continue:"
                  values={{ wordToCompare: `${wordToCompare}` }}
                  components={{
                    bold: <strong style={{ whiteSpace: 'pre-wrap' }} />,
                  }}
                />
              </div>
            }
            rules={[
              {
                required: true,
                message: t('form.default.error.required', 'Field is required'),
              },
              {
                pattern: new RegExp(`\\b(${wordToCompare})\\b`, 'g'),
                message: t(
                  'form.word.error.mismatch',
                  'Sorry the word you entered is different from what we ask you to type. \nPlease verify your input and correct it.',
                ),
              },
            ]}
          >
            <Input
              data-testid="word_confirmation-input"
              id="wordConfirmation-form-input"
              placeholder={wordToCompare}
            />
          </Form.Item>
          <Button
            data-testid="word_confirmation-continue-button"
            disabled={disabledSubmit}
            block
            htmlType="submit"
            loading={isLoading}
          >
            {t('global.continue', 'Continue')}
          </Button>
          <Button
            data-testid="word_confirmation-cancel-button"
            type="default"
            onClick={onCancel}
            block
            disabled={isLoading}
          >
            {t('global.cancel', 'Cancel')}
          </Button>
        </Flex>
      </Form>
    </Flex>
  );
};

export default WordConfirmationForm;
