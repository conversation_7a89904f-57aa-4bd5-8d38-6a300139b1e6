import { Flex, Grid, Skeleton } from 'antd';

const UserInfoCard = () => {
  const { useBreakpoint } = Grid;

  const screens = useBreakpoint();

  return (
    <>
      <Skeleton
        active
        paragraph={{
          rows: 5,
        }}
        round
      />
      <Skeleton.Input active size={'large'} />
      <Flex justify={!screens.md ? 'center' : 'flex-end'} gap={24} wrap="wrap">
        <Flex style={{ width: '200px' }}>
          <Skeleton.Button active size={'large'} shape={'round'} block={true} />
        </Flex>
        <Flex style={{ width: '200px' }}>
          <Skeleton.Button active size={'large'} shape={'round'} block={true} />
        </Flex>
      </Flex>
    </>
  );
};

export default UserInfoCard;
