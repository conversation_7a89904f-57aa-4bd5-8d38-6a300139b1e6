import { Skeleton, Flex, Divider } from 'antd';
import React from 'react';

import useStyles from './TableSkeleton.styles';

const TableSkeleton = () => {
  const { styles } = useStyles();
  const length = 5;

  return (
    <Flex vertical gap="middle">
      {[...Array(length)].map((_, index) => (
        <React.Fragment key={`skelton-item-${index}`}>
          <Flex key={index} gap="middle" justify="space-between">
            <Skeleton.Input active={true} />
            <Skeleton.Avatar active={true} shape={'circle'} />
            <Skeleton.Input active={true} block={true} />
          </Flex>
          {length - 1 !== index && (
            <Divider className={styles.tableSkeleton__divider} />
          )}
        </React.Fragment>
      ))}
    </Flex>
  );
};

export default TableSkeleton;
