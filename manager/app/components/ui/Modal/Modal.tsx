import { Modal as AntdModal, ModalProps, Typography } from 'antd';

import useStyles from './Modal.styles';

interface IModal extends ModalProps {
  isPaymentMethod?: boolean;
  title?: React.ReactNode;
}

const Modal = ({
  isPaymentMethod = false,
  title = undefined,
  ...props
}: IModal) => {
  const { styles } = useStyles({ isPaymentMethod: isPaymentMethod });

  return (
    <AntdModal
      {...props}
      className={styles.modal}
      footer={null}
      centered
      width={600}
    >
      {title && (
        <Typography.Title level={3} className={styles.modal__title}>
          {title}
        </Typography.Title>
      )}
      {props.children}
    </AntdModal>
  );
};

export default Modal;
