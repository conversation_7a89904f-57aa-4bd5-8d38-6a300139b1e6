import { createStyles } from 'antd-style';

export default createStyles(
  ({ token, css }, props: { isPaymentMethod: boolean }) => {
    return {
      modal: css`
        .ant-modal-content {
          ${props.isPaymentMethod && `background: ${token.colorBgLayout};`}
          padding: ${token.paddingMD}px;

          @media (min-width: ${token.screenMD}px) {
            padding: ${token.paddingLG}px;
          }
        }

        .ant-modal-body {
          display: flex;
          gap: ${token.marginXL}px;
          flex-direction: column;
        }
      `,
      modal__title: css`
        text-align: center;
      `,
    };
  },
);
