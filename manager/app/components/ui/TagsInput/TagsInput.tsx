import { message, Select } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ITag, SelectOption } from 'types';
import {
  FORM_VALIDATION_SPECIAL_CHAR,
  FORM_VALIDATION_VM_TAG_MAX_LENGTH,
  FORM_VALIDATION_VM_TAG_MIN_LENGTH,
} from 'utils';

interface Props {
  availableTags: ITag[];
  appliedTags: ITag[];
  onSaveConfirm: (newTagNameValue: string) => Promise<void>;
  onDeleteSingleTag: (tagToRemove: ITag) => Promise<void>;
  onDeleteAllTags: () => Promise<void>;
  isSubmittingTags: boolean;
}

export const TagsInput = ({
  availableTags,
  appliedTags,
  onSaveConfirm,
  onDeleteSingleTag,
  onDeleteAllTags,
  isSubmittingTags,
}: Props) => {
  const { t } = useTranslation();

  const [selectedTags, setSelectedTags] = useState<string[]>(
    appliedTags.map(tag => tag.name),
  );

  const optionsAvailable: SelectOption[] = availableTags
    .filter(
      availableTag =>
        !appliedTags.some(appliedTag => appliedTag.id === availableTag.id),
    )
    .map(tag => ({
      value: tag.id,
      label: tag.name,
      ...{ 'data-testid': `vm_tag-${tag.id}-option` },
    }));

  const onDeselect = (value: string, option: any) => {
    const label = option?.label ? option.label : value;
    const tagToRemove = appliedTags.find(
      appliedTag => appliedTag.name === label,
    );

    if (tagToRemove) {
      onDeleteSingleTag(tagToRemove);
      setSelectedTags(prevTags => prevTags.filter(tag => tag !== label));
    }
  };

  const onClear = () => {
    onDeleteAllTags();
    setSelectedTags([]);
  };

  const onChange = (values: string[]) => {
    const validTags = values.filter(value => {
      if (
        value.length < FORM_VALIDATION_VM_TAG_MIN_LENGTH ||
        value.length > FORM_VALIDATION_VM_TAG_MAX_LENGTH ||
        FORM_VALIDATION_SPECIAL_CHAR.test(value)
      ) {
        message.error(
          t(
            'tagManager.notification.errorLength',
            'Tag must be at least {{minLength}}-{{maxLength}} characters long and contain no special characters',
            {
              minLength: FORM_VALIDATION_VM_TAG_MIN_LENGTH,
              maxLength: FORM_VALIDATION_VM_TAG_MAX_LENGTH,
            },
          ),
        );
        return false;
      }
      return true;
    });

    setSelectedTags(validTags);
  };

  const onSelect = (value: string, option: any) => {
    if (
      value.length < FORM_VALIDATION_VM_TAG_MIN_LENGTH ||
      value.length > FORM_VALIDATION_VM_TAG_MAX_LENGTH ||
      FORM_VALIDATION_SPECIAL_CHAR.test(value)
    ) {
      return;
    }

    onSaveConfirm(option?.label ? option.label : value);
  };

  return (
    <Select
      id="tags"
      notFoundContent={t('tagManager.noTags', 'No existing tags')}
      data-testid="tag-input"
      value={selectedTags}
      mode="tags"
      placeholder={t('tagManager.placeholder', 'Add a tag')}
      onChange={onChange}
      onSelect={onSelect}
      allowClear={true}
      onClear={onClear}
      filterSort={(optionA, optionB) => {
        return optionA?.label.localeCompare(optionB?.label);
      }}
      onDeselect={onDeselect}
      optionFilterProp="label"
      options={optionsAvailable}
      loading={isSubmittingTags}
    />
  );
};
