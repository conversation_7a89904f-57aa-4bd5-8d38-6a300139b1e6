import { Pagination, PaginationProps, Typography } from 'antd';
import { Trans } from 'react-i18next';

import { SelectWithoutSearch } from './SelectWithoutSearch';

import {
  LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
  LIST_MANAGER_PAGE_OPTIONS,
} from '@/app/utils/constants';

interface ITablePagination extends PaginationProps {
  // For Later: dataType: string; for total render according to type of data
  setCurrentPage: (currentPage: number) => void;
  setPageSize: (pageSize: number) => void;
  totalItems: number;
}

const TablePagination = ({
  current,
  pageSize,
  setPageSize,
  setCurrentPage,
  totalItems,
}: ITablePagination) => {
  const onPaginationChange = (page: number, size: number) => {
    if (size !== pageSize) {
      setPageSize(size);
      setCurrentPage(1);
    }
  };

  const renderTotal = (total: number) => {
    return total > 0 ? (
      <Typography.Text>
        <Trans
          i18nKey="list.vm.total"
          defaults="Total: {{total}} Shadow PC"
          values={{ total }}
        />
      </Typography.Text>
    ) : null;
  };

  const renderPageSizeOptions = (total: number) => {
    const options = LIST_MANAGER_PAGE_OPTIONS;

    options.map((number, index) => {
      if (number > total) {
        return options.splice(index + 1, options.length);
      }
    });
    return options;
  };

  return (
    <Pagination
      align="end"
      hideOnSinglePage={totalItems < LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE}
      showSizeChanger={true}
      defaultCurrent={1}
      current={current}
      total={totalItems}
      showTotal={total => renderTotal(total)}
      pageSize={pageSize}
      pageSizeOptions={renderPageSizeOptions(totalItems)}
      responsive={true}
      onChange={setCurrentPage}
      onShowSizeChange={(currentPage, size) =>
        onPaginationChange(currentPage, size)
      }
      selectComponentClass={SelectWithoutSearch}
    />
  );
};

export default TablePagination;
