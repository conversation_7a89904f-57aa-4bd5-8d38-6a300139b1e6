import { CloseOutlined } from '@ant-design/icons';
import { Flex } from 'antd';
import { ReactNode } from 'react';

interface Props {
  onClose: () => void;
  actions?: ReactNode;
  title: string | ReactNode;
}

export const DrawerHeader = ({ onClose, title, actions }: Props) => (
  <Flex justify="space-between" align="center" gap="large">
    <Flex gap="middle">
      <CloseOutlined data-testid="close_drawer-button" onClick={onClose} />
      {title}
    </Flex>
    {actions}
  </Flex>
);
