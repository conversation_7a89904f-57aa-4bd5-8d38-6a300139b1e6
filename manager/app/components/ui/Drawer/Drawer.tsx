import { Drawer as DrawerAntd, Grid } from 'antd';

import useStyles from './Drawer.styles';

import { IUserManager, IVmManager } from '@/app/types/adminManagers';

interface Props {
  children: React.ReactNode;
  data: IVmManager | IUserManager;
  header?: React.ReactNode;
  onClose: () => void;
  footer?: React.ReactNode;
  isOpen: boolean;
}

export const Drawer = ({
  children,
  data,
  footer,
  header,
  isOpen,
  onClose,
}: Props) => {
  const { styles } = useStyles();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  return (
    <DrawerAntd
      classNames={{
        body: styles.drawer__body,
        header: styles.drawer__header,
        wrapper: styles.drawer__wrapper,
        footer: styles.drawer__footer,
      }}
      key={`vmDrawer-${data.id}`}
      placement="right"
      width={screens.lg ? '700px' : '100vw'}
      open={isOpen}
      onClose={() => onClose()}
      closable={false}
      title={header}
      footer={footer}
    >
      <div className={styles.drawer__configuration}>{children}</div>
    </DrawerAntd>
  );
};
