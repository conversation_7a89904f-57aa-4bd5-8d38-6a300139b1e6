import { Divider, Flex } from 'antd';
import { ReactNode } from 'react';

import useStyles from './DrawerSection.styles';

interface Props {
  children: ReactNode;
  loaderComponent?: ReactNode;
}

export const DrawerSection = ({ children }: Props) => {
  const { styles } = useStyles();

  return (
    <div>
      <Flex className={styles.drawer__section} vertical gap={24}>
        {children}
      </Flex>
      <Divider />
    </div>
  );
};
