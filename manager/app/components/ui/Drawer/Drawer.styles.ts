import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    drawer__wrapper: css`
      @media (min-width: ${token.screenMD}px) {
        border-radius: 16px 0 0 16px;
        overflow: hidden;
      }
    `,

    drawer__header: css`
      &.ant-drawer-header {
        padding: ${token.paddingSM}px ${token.paddingMD}px;
      }
    `,

    drawer__body: css`
      &.ant-drawer-body {
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0;
      }
    `,

    drawer__configuration: css`
      overflow-x: auto;
      flex: 1;
    `,

    drawer__footer: css`
      border-width: 1px;

      button {
        padding-inline: ${token.paddingMD}px;
      }
    `,
  };
});
