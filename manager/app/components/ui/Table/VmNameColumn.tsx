import { Flex, Grid, Tooltip, Typography } from 'antd';

import { VmStatus } from '../../vmManager/VmStatus/VmStatus';

import { IVmManager } from '@/app/types/adminManagers';

interface Props {
  vm: IVmManager;
}

export const VmNameColumn = ({ vm }: Props) => {
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const nameToRender = vm.name || `VM #${vm.id}`;

  return (
    <>
      {!screens.xl ? (
        <Tooltip
          style={{ overflow: 'hidden' }}
          placement="top"
          title={
            <>
              <Typography.Text>{nameToRender}</Typography.Text>
              <VmStatus subscription={vm} />
            </>
          }
          arrow={true}
        >
          <Flex gap="small" align="center">
            <VmStatus subscription={vm} onlyBadge={true} />
            <Typography.Text ellipsis>{nameToRender}</Typography.Text>
          </Flex>
        </Tooltip>
      ) : (
        <Tooltip
          placement="topLeft"
          title={<Typography.Text>{nameToRender}</Typography.Text>}
        >
          {nameToRender}
        </Tooltip>
      )}
    </>
  );
};
