import { Badge, Tooltip, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DATE_FORMAT_WITH_SLASH_BY_MARKET,
  formatDate,
  numberOfDaysToNow,
  timeAgo,
} from 'utils';

import useStyles from './Table.styles';

import { useConfig } from '@/app/hooks/store/useConfig';
import { NUMBER_OF_DAYS_FOR_NEW_SUB } from '@/app/utils/constants';

interface Props {
  createdAt: number;
}

export const CreatedAtColumn = ({ createdAt }: Props) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const { market } = useConfig();

  return (
    <Tooltip
      placement="topLeft"
      title={
        <Typography.Text>
          {formatDate(
            createdAt * 1000,
            DATE_FORMAT_WITH_SLASH_BY_MARKET[market],
          )}
        </Typography.Text>
      }
    >
      {numberOfDaysToNow(createdAt) <= NUMBER_OF_DAYS_FOR_NEW_SUB && (
        <Badge.Ribbon
          text={t('global.new', 'New')}
          placement="start"
          className={styles.column__name__ribbon}
        />
      )}
      {timeAgo(createdAt)}
    </Tooltip>
  );
};
