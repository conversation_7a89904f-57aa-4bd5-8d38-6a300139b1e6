import { Select, Tag, Typography } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserRole } from 'types';

import { handleUpdateUserRole } from '../../userManager/UserDrawer/UserActions/UpdateUserRole';

import {
  useCurrentMember,
  useUpdateUserRole,
} from '@/app/hooks/reactQuery/member/useMember';
import { IUserManager } from '@/app/types/adminManagers';
import { USER_ROLES, UserStatus } from '@/app/utils/constants';
import { getRules } from '@/app/utils/rules';

export const USER_STATUS_COLOR: any = {
  [UserStatus.PENDING]: 'blue',
  [UserStatus.EXPIRED]: 'volcano',
  [UserStatus.DISABLED]: 'red',
};

interface Props {
  user: IUserManager;
}

export const RoleColumn = ({ user }: Props) => {
  const { t } = useTranslation();
  const { data: currentMemberData } = useCurrentMember();
  const updateUserRole = useUpdateUserRole();
  const [userRole, setUserRole] = useState<UserRole>(user.role);
  const currentMemberRole = currentMemberData?.role ?? UserRole.MEMBER;

  const rules = getRules(currentMemberRole);

  const roleOptions = USER_ROLES.filter(role => {
    const currentRules = rules[currentMemberRole] as Record<string, any>;
    const userRules = rules[user.role] as Record<string, any>;

    return (
      currentRules?.setRole?.[role.value] && userRules?.canBecome?.[role.value]
    );
  }).map(role => ({
    value: role.value,
    label: (
      <Typography.Text style={{ fontWeight: 200, textTransform: 'capitalize' }}>
        {t(`list.user.role.${role.label}`)}
      </Typography.Text>
    ),
    ...{ 'data-testid': `user_role-${role.value}-option` },
  }));

  const handleChange = (value: UserRole) => {
    handleUpdateUserRole(t, user, updateUserRole, value, setUserRole);
  };

  const isStatusInactive =
    user.status === UserStatus.PENDING || user.status === UserStatus.DISABLED
      ? true
      : false;

  // if user is not active, display status tag
  if (isStatusInactive) {
    return (
      <Tag color={USER_STATUS_COLOR[user.status]}>
        {t(`list.user.status.${user.status}`)}
      </Tag>
    );
  }

  /**
   * Checks if there are no valid role options or if the only available role option
   * matches the user's current role. This is used to determine whether to display
   * the role as plain text instead of a dropdown.
   */
  if (
    roleOptions.length === 0 ||
    (roleOptions.length === 1 && roleOptions[0]?.value === user.role)
  ) {
    return (
      <Typography.Text style={{ textTransform: 'capitalize' }}>
        {t(`list.user.role.${user.role}`)}
      </Typography.Text>
    );
  }

  /**
   * Checks if the current member's role allows setting roles for other users.
   * This condition determines whether the role selection dropdown should be displayed.
   */
  if (rules[currentMemberRole]?.canSetRoles) {
    return (
      <Select
        options={roleOptions}
        popupMatchSelectWidth={false}
        value={userRole}
        variant="borderless"
        size="small"
        onClick={e => e.stopPropagation()}
        onChange={handleChange}
      />
    );
  }

  // else, display role
  return (
    <Typography.Text style={{ textTransform: 'capitalize' }}>
      {t(`list.user.role.${userRole}`)}
    </Typography.Text>
  );
};
