import { Flex } from 'antd';
import { useTranslation } from 'react-i18next';

import { SettingsIcon } from '../../Icon';
import Button from '../Button/Button';

interface Props {
  handleDrawer: (id: string) => void;
  id: string;
  type: 'vm' | 'user';
}

export const ActionsColumn = ({ handleDrawer, id, type }: Props) => {
  const { t } = useTranslation();

  return (
    <Flex justify="end">
      <Button
        data-testid={`${type}_table-${id}-action`}
        title={
          type === 'vm'
            ? t(
                'list.vm.table.action',
                'See more information about this Shadow PC',
              )
            : t(
                'list.user.table.action',
                'See more information about this user',
              )
        }
        type="text"
        variant="text"
        shape="circle"
        onClick={() => handleDrawer(id)}
        icon={<SettingsIcon />}
      />
    </Flex>
  );
};
