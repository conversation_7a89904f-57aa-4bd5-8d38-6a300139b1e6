import { Grid, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { IMemberDetails } from 'types';

import MemberDetails from '../MemberDetail/MemberDetails';

import { IUserManager } from '@/app/types/adminManagers';

interface Props {
  user?: IMemberDetails | IUserManager;
}

export const UserColumn = ({ user }: Props) => {
  const { t } = useTranslation();

  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  // if user affected
  if (user) {
    return (
      <MemberDetails
        email={user.email}
        firstName={user.first_name}
        lastName={user.last_name}
        hasTooltip
      />
    );
  }

  // if no user affected
  return (
    <>
      {screens.lg ? (
        <Typography.Text type="secondary">
          {t(
            'list.vm.assignOrRevokeMember.assign.noUser',
            'No user is assigned',
          )}
        </Typography.Text>
      ) : (
        '-'
      )}
    </>
  );
};
