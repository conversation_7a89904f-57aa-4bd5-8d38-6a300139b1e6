import { createStyles } from 'antd-style';

export default createStyles(({ css }) => {
  return {
    table: css`
      tr.ant-table-row {
        cursor: pointer;
        position: relative;
      }
      .ant-ribbon-wrapper {
        position: initial;
      }
      &.ant-table-wrapper td.ant-table-cell {
        position: initial;
      }
    `,

    column__name__ribbon: css`
      font-size: 12px;
      line-height: 16px;
    `,
  };
});
