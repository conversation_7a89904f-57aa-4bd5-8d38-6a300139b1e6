import {
  blue,
  magenta,
  gold,
  green,
  volcano,
  cyan,
  purple,
} from '@ant-design/colors';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Flex, Tooltip, Typography } from 'antd';

import useStyles from './MemberDetails.styles';

interface MemberCard {
  email: string;
  firstName?: string;
  isAvatarOnly?: boolean;
  hasTooltip?: boolean;
  lastName?: string;
  hasEllipsis?: boolean;
}

const MemberDetails = ({
  email,
  firstName,
  isAvatarOnly = false,
  hasTooltip = false,
  lastName,
  hasEllipsis = true,
}: MemberCard) => {
  const { styles } = useStyles({ isAvatarOnly: isAvatarOnly });

  const renderAvatar = () => (
    <Avatar
      style={{
        backgroundColor: getTwoToneColor()?.background,
        color: getTwoToneColor()?.text,
        flexShrink: 0,
        textTransform: 'uppercase',
      }}
    >
      {firstName && lastName ? (
        firstName.charAt(0) + lastName.charAt(0)
      ) : (
        <UserOutlined />
      )}
    </Avatar>
  );

  const renderUserInfo = () => (
    <Flex vertical style={{ overflow: 'hidden' }}>
      {(firstName || lastName) &&
        (hasTooltip ? (
          <Typography.Text ellipsis={hasEllipsis}>
            {firstName} {lastName}
          </Typography.Text>
        ) : (
          <Typography.Title level={5} ellipsis={hasEllipsis}>
            {firstName} {lastName}
          </Typography.Title>
        ))}
      <Typography.Text type="secondary" ellipsis={hasEllipsis}>
        {email}
      </Typography.Text>
    </Flex>
  );

  const getTwoToneColor = () => {
    const colors = [
      { background: green[2], text: green[6] },
      { background: cyan[2], text: cyan[6] },
      { background: volcano[2], text: volcano[6] },
      { background: gold[2], text: gold[6] },
      { background: magenta[2], text: magenta[6] },
      { background: blue[2], text: blue[6] },
      { background: purple[2], text: purple[6] },
    ];

    const userName = firstName ? firstName : email;
    const letter = userName.charCodeAt(1) - 96;
    const letterModulo = letter % colors.length;
    return colors[letterModulo];
  };

  return (
    <Tooltip
      placement="topLeft"
      title={hasTooltip && renderUserInfo()}
      zIndex={300}
      overlayStyle={{ maxWidth: 'fit-content' }}
    >
      <Flex align="center" gap="middle" className={styles.container}>
        {renderAvatar()}
        {!isAvatarOnly && renderUserInfo()}
      </Flex>
    </Tooltip>
  );
};

export default MemberDetails;
