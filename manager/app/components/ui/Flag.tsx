import * as Flags from 'country-flag-icons/react/3x2';

interface Datacenter {
  value: string | number;
  flag: keyof typeof Flags;
  strong?: boolean;
  flex?: boolean;
}

const Flag = ({ flag, value, strong = false, flex = false }: Datacenter) => {
  const FlagToDiplay = Flags[flag];

  return (
    <div
      style={{
        fontWeight: strong ? '400' : '200',
        display: flex ? 'flex' : 'block',
      }}
    >
      <FlagToDiplay
        style={{ width: '24px', verticalAlign: 'text-top', marginRight: '8px' }}
      />
      {value}
    </div>
  );
};

export default Flag;
