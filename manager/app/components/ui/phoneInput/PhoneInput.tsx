import { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { PhoneInput, PhoneInputRefType } from 'react-international-phone';
import { CountryCode, Market } from 'types';

import 'react-international-phone/style.css';

import useStyles from './PhoneInput.styles';

interface IPhoneInput {
  defaultCountry: CountryCode | Market;
  value: string;
}

const PhoneInputUI = forwardRef<PhoneInputRefType, IPhoneInput>(
  ({ defaultCountry, value, ...props }, ref) => {
    const { t } = useTranslation();
    const { styles } = useStyles({ value: value });

    return (
      <PhoneInput
        ref={ref}
        className={styles.phoneInputContainer}
        inputClassName={styles.phoneInput}
        {...props}
        defaultCountry={defaultCountry.toLowerCase()}
        value={value.replace(/\s+/g, '')}
        placeholder={t('form.phone.label', 'Phone number')}
        inputProps={{
          // @ts-expect-error we just need to pass the data-testid props
          'data-testid': 'user_phone-input',
        }}
      />
    );
  },
);

PhoneInputUI.displayName = 'PhoneInputUI';

export default PhoneInputUI;
