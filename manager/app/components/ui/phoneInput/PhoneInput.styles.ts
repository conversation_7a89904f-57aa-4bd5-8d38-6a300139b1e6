import { colorsPro } from '@blade-group/design-tokens';
import { createStyles } from 'antd-style';

const colors = colorsPro.light;

export default createStyles(({ token, css }, props: { value: string }) => {
  return {
    phoneInputContainer: css`
      display: flex;
      height: 50px;

      border: 2px solid ${token.colorBorder};
      border-radius: 8px;
      box-sizing: border-box;
      align-items: center;
      transition: all ${token.motionDurationMid};

      // change border color on focus and hover
      &:has(input:focus),
      &:hover {
        border-color: ${token.colorPrimary};
      }

      // change button css
      .react-international-phone-country-selector-button {
        border: 0;
        height: 46px;
        border-radius: 5px 0 0 5px;
        padding: 0 0 0 10px;
        &:hover,
        &:active {
          background: ${colors.surface.backgroundHigh};
        }

        img {
          width: 30px;
          margin: 0;
        }
      }
    `,

    phoneInput: css`
      .react-international-phone-input-container
        &.react-international-phone-input {
        border: 0;
        height: 100%;
        border-radius: 0 5px 5px 0;
        flex: 1;
        padding: 12px 11px;
        font-family: 'Nexa Text';
        font-weight: 400;
        font-size: 16px;
        transition: all ${token.motionDurationMid};
        /* do not use for the moment
       background: ${props.value ? colors.surface.backgroundHigh : 'unset'}; */

        &::placeholder {
          color: ${token.colorTextPlaceholder};
        }
        &:focus {
          background: ${colors.surface.backgroundHigh};
        }
      }
    `,
  };
});
