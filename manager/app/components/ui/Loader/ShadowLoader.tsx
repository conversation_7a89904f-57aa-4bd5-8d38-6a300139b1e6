import styled from '@emotion/styled';
import Lottie from 'lottie-react';

import globalLoaderAnimation from './globalLoaderAnimation.json';

import { useDelayBeforeDisplay } from '@/app/hooks/useDelayBeforeDisplay';

interface IShadowLoaderProps {
  color?: string;
  isPathVisible?: boolean;
  size?: number;
  sizeStroke?: number;
}

const Container = styled('div', {
  shouldForwardProp: prop =>
    !['color', 'size', 'sizeStroke', 'isPathVisible'].includes(prop),
})<Pick<IShadowLoaderProps, 'color' | 'isPathVisible' | 'size' | 'sizeStroke'>>`
  svg {
    overflow: visible;
  }

  svg > g {
    clip-path: unset;
  }

  /* Hide the logo path */
  g:first-of-type > g:first-of-type {
    ${({ isPathVisible }) => !isPathVisible && 'display: none;'};
  }

  path {
    ${({ color }) => color && `stroke: ${color};`}

    ${({ sizeStroke }) => sizeStroke && `stroke-width: ${sizeStroke};`}
  }

  ${({ size }) => size && `width: ${size}px; height: ${size}px;`}
`;

const ShadowLoader = ({
  color,
  isPathVisible = true,
  size,
  ...props
}: IShadowLoaderProps) => {
  // When loaders are displayed directly the user perception of slowness is increased
  // We delay the display of the loader to avoid that
  const showLoader = useDelayBeforeDisplay();

  return showLoader ? (
    <Container
      color={color}
      isPathVisible={isPathVisible}
      size={size}
      {...props}
    >
      <Lottie animationData={globalLoaderAnimation} loop={true} />
    </Container>
  ) : null;
};

export default ShadowLoader;
