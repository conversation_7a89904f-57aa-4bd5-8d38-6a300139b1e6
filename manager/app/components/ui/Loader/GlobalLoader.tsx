import { Flex, Typography } from 'antd';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import useStyles from './GlobalLoader.styles';
import ShadowLoader from './ShadowLoader';

import { useDelayBeforeDisplay } from '@/app/hooks/useDelayBeforeDisplay';

interface IGlobalLoaderProps {
  fullHeight?: boolean;
  text?: string | ReactNode;
}

const GlobalLoader = ({ fullHeight = false, text }: IGlobalLoaderProps) => {
  // When loaders are displayed directly the user perception of slowness is increased
  // We delay the display of the loader to avoid that
  const showLoader = useDelayBeforeDisplay();
  const { t } = useTranslation();
  const { styles } = useStyles({ fullHeight: fullHeight });

  return showLoader ? (
    <Flex
      vertical
      align="center"
      justify="center"
      className={styles.globalLoader}
      gap={24}
    >
      <ShadowLoader />
      <Typography.Text>
        {text ?? t('global.loading', 'Loading...')}
      </Typography.Text>
    </Flex>
  ) : null;
};

export default GlobalLoader;
