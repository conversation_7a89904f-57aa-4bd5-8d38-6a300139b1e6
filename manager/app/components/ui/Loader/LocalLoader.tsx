import { Flex } from 'antd';

import useStyles from './LocalLoader.styles';
import ShadowLoader from './ShadowLoader';

const LocalLoader = ({ justify = 'center', width = 56, ...props }) => {
  const { styles } = useStyles();

  return (
    <Flex
      align="center"
      justify={justify}
      {...props}
      className={styles.localLoader}
    >
      <ShadowLoader size={width} sizeStroke={50} isPathVisible={false} />
    </Flex>
  );
};

export default LocalLoader;
