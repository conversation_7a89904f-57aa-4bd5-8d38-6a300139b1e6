import { Button as AntdButton, ButtonProps } from 'antd';
import { ButtonVariantType } from 'antd/lib/button';
import { forwardRef, ForwardedRef } from 'react';

import useStyles from './Button.styles';

export enum ButtonColorEnum {
  SECONDARY = 'secondary',
}

export type ButtonColorParameter = `${ButtonColorEnum}`;

export type ButtonColor = {
  [index in ButtonColorParameter]: {
    default: { background?: string; color?: string };
    hover: { background?: string; color?: string };
  };
};

interface IButton extends ButtonProps {
  'data-testid': string;
  block?: boolean;
  blockMobile?: boolean;
  justify?: 'left' | 'right' | 'center';
  noWrap?: boolean;
  secondary?: boolean;
  variant?: ButtonVariantType;
}

const Button = forwardRef(
  (
    {
      block = false,
      blockMobile = false,
      justify = 'center',
      noWrap = false,
      secondary = false,
      variant = 'solid',
      ...props
    }: IButton,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    const { styles } = useStyles({
      block: block,
      blockMobile: blockMobile,
      justify: justify,
      noWrap: noWrap,
      secondary: secondary,
      variant: variant,
    });
    // const sizing = props.size === 'large' ? 'middle' : 'large';
    const classNames = [styles.button, props.className].join(' ');
    // const { useBreakpoint } = Grid;

    // const screens = useBreakpoint();

    return (
      <AntdButton
        ref={ref}
        type="primary"
        shape="round"
        variant={variant}
        block={block}
        {...props}
        onClick={
          !props.loading && !props.disabled && props.onClick
            ? props.onClick
            : () => {}
        }
        className={classNames}
        // size={screens.md ? props.size : sizing}
      />
    );
  },
);

export default Button;
