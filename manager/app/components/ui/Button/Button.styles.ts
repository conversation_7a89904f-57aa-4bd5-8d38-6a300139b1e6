import { colors as clr } from '@blade-group/shade';
import { ButtonVariantType } from 'antd/lib/button';
import { createStyles } from 'antd-style';

import { ButtonColor } from './Button';

export default createStyles(
  (
    { token, css },
    props: {
      block: boolean;
      blockMobile: boolean;
      justify: string;
      noWrap: boolean;
      secondary?: boolean;
      variant: ButtonVariantType;
    },
  ) => {
    const buttonColors = props.secondary ? BUTTON_COLORS.secondary : null;

    return {
      button: css`
        // Default size
        white-space: ${props.noWrap ? 'nowrap' : 'normal'};
        ${props.variant === 'filled' && 'border: none;'}
        ${props.block && 'height: auto;'}
        display: flex;
        align-items: center;
        justify-content: ${props.justify};
        width: ${props.blockMobile ? '100%' : 'fit-content'};
        max-width: 100%;

        @media (min-width: ${token.screenLG}px) {
          width: fit-content;
        }

        // Color management
        &.ant-btn.ant-btn-variant-solid.ant-btn-primary {
          ${buttonColors &&
          `background-color: ${buttonColors.default.background};
           color: ${buttonColors.default.color};
          `};
          box-shadow: unset;
        }
        &.ant-btn.ant-btn-variant-solid.ant-btn-primary:not(:disabled):not(
            .ant-btn-disabled
          ):hover {
          ${buttonColors &&
          `background-color: ${buttonColors.hover.background};
           color: ${buttonColors.hover.color};
          `};
        }
        &.ant-btn:disabled,
        &.ant-btn[disabled] {
          opacity: 0.4;
        }
      `,
    };
  },
);

export const BUTTON_COLORS: ButtonColor = {
  secondary: {
    default: {
      background: clr.surface.secondary,
      color: clr.on.secondary,
    },
    hover: {
      background: clr.surface.primary,
      color: clr.on.primary,
    },
  },
};
