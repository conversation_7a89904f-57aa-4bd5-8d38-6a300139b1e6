import { SendOutlined } from '@ant-design/icons';
import { Form, Input, message, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { NotificationState } from 'types';
import { logError } from 'utils';

import useStyles from './UserManagerOption.styles';

import Button from '@/app/components/ui/Button/Button';
import { useInviteMember } from '@/app/hooks/reactQuery/member/useMember';
import { ApiError, IInviteFormPayload } from '@/app/types/api';

const UserManagerOptions = () => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const [form] = Form.useForm();
  const { mutateAsync, isLoading } = useInviteMember();

  const handleSubmit = (values: IInviteFormPayload) => {
    mutateAsync({
      invite_email: encodeURIComponent(values.invite_email),
    })
      .then(() => {
        message.success({
          type: NotificationState.SUCCESS,
          content: t(
            'list.user.invite.success.message',
            '{{ email }} will receive an email shortly inviting him to create an account in your team and get access to his Shadow PC.',
            { email: values.invite_email },
          ),
          duration: 4,
        });
        form.resetFields();
      })
      .catch(e => {
        logError('inviteEmail', e);
        switch ((e as ApiError)?.code) {
          case 409:
            form.setFields([
              {
                name: 'invite_email',
                errors: [
                  t(
                    'list.user.invite.error.message',
                    'You have already sent an invitation to this email',
                  ),
                ],
              },
            ]);
            break;
          default:
            message.error(
              t(
                'list.user.invite.error.title',
                'An error has occured when sending an invitation',
              ),
            );
        }
      })
      .finally();
  };

  return (
    <Form form={form} onFinish={handleSubmit} style={{ width: '100%' }}>
      <Space.Compact className={styles.userManager__form__space}>
        <Form.Item
          name="invite_email"
          className={styles.userManager__form__input}
          rules={[
            {
              required: true,
              message: t('form.email.error.required', 'Email is required'),
            },
            {
              type: 'email',
              message: t(
                'form.email.error.email',
                'Field must be a valid email',
              ),
            },
          ]}
        >
          <Input
            id="invite_email"
            data-testid="invite_email-input"
            placeholder={t('global.email', 'Email')}
            onInput={e =>
              ((e.target as HTMLInputElement).value = (
                e.target as HTMLInputElement
              ).value.toLowerCase())
            }
          />
        </Form.Item>
        <Button
          data-testid="invite_email-button"
          htmlType="submit"
          type="primary"
          loading={isLoading}
          icon={<SendOutlined />}
        >
          {t('global.invite', 'Invite')}
        </Button>
      </Space.Compact>
    </Form>
  );
};

export default UserManagerOptions;
