import { Input } from 'antd';
import { useTranslation } from 'react-i18next';

import useStyles from './UserSearch.styles';

export const UserSearch = ({
  searchQuery,
  setSearchQuery,
  isLoading = false,
}: {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  isLoading?: boolean;
}) => {
  const { styles } = useStyles();
  const { t } = useTranslation();

  return (
    <Input.Search
      loading={isLoading}
      className={styles.search}
      placeholder={t(
        'list.user.searchPlaceholder',
        'Search for User name / email / tags',
      )}
      size="large"
      value={searchQuery}
      onChange={e => setSearchQuery(e.target.value)}
      onSearch={value => setSearchQuery(value)}
      onClear={() => setSearchQuery('')}
      allowClear
    />
  );
};
