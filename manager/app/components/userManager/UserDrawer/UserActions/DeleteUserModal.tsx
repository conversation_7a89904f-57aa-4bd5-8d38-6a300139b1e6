import { Flex, Form, Input, message, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { computeErrorMessage, logError } from 'utils';

import Button from '@/app/components/ui/Button/Button';
import Modal from '@/app/components/ui/Modal/Modal';
import { useDeleteMember } from '@/app/hooks/reactQuery/member/useMember';
import { ApiError, IDeleteMemberPayload } from '@/app/types/api';
import { UserStatus } from '@/app/utils/constants';

interface Props {
  closeModal: () => void;
  isOpen: boolean;
  memberId: string;
  status: string;
  inviteEmail: string;
}

const DeleteUserModal = ({
  closeModal,
  inviteEmail,
  memberId,
  status,
  isOpen,
}: Props) => {
  const { t } = useTranslation();
  const { mutateAsync, isLoading } = useDeleteMember();
  const [form] = Form.useForm();

  /** ACTION NOTIFICATIONS */
  const onDeleteMemberSuccess = () => {
    message.success(
      `${t(
        'list.user.deleteMember.notification.success',
        'Successfully deleted member {{email}}',
        {
          email: inviteEmail,
        },
      )}`,
    );
  };

  const onDeleteMemberError = (error: string) => {
    message.error(
      `${t(
        'list.user.deleteMember.notification.error',
        'Error deleting member {{email}}: {{error}}',
        {
          email: inviteEmail,
          error,
        },
      )}`,
    );
  };

  /** ACTION HANDLER */
  const handleDeleteMember = (values: IDeleteMemberPayload) => {
    mutateAsync({
      invite_email: inviteEmail,
      member_id: status === UserStatus.PENDING ? null : values.member_id,
    })
      .then(() => {
        onDeleteMemberSuccess();
      })
      .catch(e => {
        logError('handleDeleteMember', e);
        let errorMessage = computeErrorMessage(e);
        switch ((e as ApiError)?.code) {
          case 400:
            errorMessage = t(
              'list.user.deleteMember.notification.alreadyAssigned',
              'Cannot delete, member is currently assigned to a PC.',
            );
            break;
        }
        onDeleteMemberError(errorMessage);
      })
      .finally(() => {
        closeModal;
      });
  };

  return (
    <Modal
      modalRender={modal => (
        <div onClick={e => e.stopPropagation()}>{modal}</div>
      )}
      destroyOnClose={true}
      open={isOpen}
      onOk={closeModal}
      onCancel={closeModal}
      title={t('list.user.deleteMember.modal.title', 'Delete user')}
    >
      <Typography.Text style={{ textAlign: 'center' }}>
        {t(
          'list.user.deleteMember.modal.content',
          'This operation cannot be undone.',
        )}
      </Typography.Text>
      <Form
        form={form}
        onFinish={handleDeleteMember}
        initialValues={{
          member_id: memberId,
          invite_email: inviteEmail,
        }}
      >
        <Flex vertical gap="middle">
          <Form.Item hidden name="member_id">
            <Input />
          </Form.Item>
          <Button
            data-testid="delete_user-confirm-button"
            danger
            block
            loading={isLoading}
            htmlType="submit"
          >
            {t('list.user.deleteMember.label', 'Delete user')}
          </Button>
          <Button
            data-testid="delete_user-cancel-button"
            type="default"
            disabled={isLoading}
            onClick={closeModal}
            block
          >
            {t('global.cancel', 'Cancel')}
          </Button>
        </Flex>
      </Form>
    </Modal>
  );
};

export default DeleteUserModal;
