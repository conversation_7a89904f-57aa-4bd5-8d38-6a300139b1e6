import { UseMutationResult } from '@tanstack/react-query';
import { message } from 'antd';
import { TFunction } from 'i18next';
import { ErrorTypes } from 'types';
import { APIError, computeErrorMessage, logError } from 'utils';

import { IUserManager } from '@/app/types/adminManagers';
import { ApiError } from '@/app/types/api';

const onActivateMemberSuccess = (t: TFunction, user: IUserManager) => {
  message.success(
    `${t(
      'list.user.activateMember.notification.success',
      'Successfully reactivated member {{email}}',
      {
        email: user.email,
      },
    )}`,
  );
};

const onActivateMemberError = (
  t: TFunction,
  user: IUserManager,
  error: ErrorTypes,
) => {
  const errorMessage = computeErrorMessage(error);
  message.error(
    `${t(
      'list.user.activateMember.notification.error',
      'Error reactivating member {{email}}: {{error}}',
      {
        email: user.email,
        errorMessage,
      },
    )}`,
  );
};

export const handleActivateMember = (
  t: TFunction,
  user: IUserManager,
  activateMember: UseMutationResult<
    string | undefined,
    APIError,
    string,
    unknown
  >,
) => {
  activateMember
    .mutateAsync(user.id)
    .then(() => {
      onActivateMemberSuccess(t, user);
    })
    .catch(e => {
      logError('handleActivateMember', e);
      let errorMessage = computeErrorMessage(e);
      switch ((e as ApiError)?.code) {
        case 400:
          errorMessage = t(
            'list.user.deleteMember.notification.alreadyAssigned',
            'Cannot delete, member is currently assigned to a PC.',
          );
          break;
      }
      onActivateMemberError(t, user, errorMessage);
    });
};
