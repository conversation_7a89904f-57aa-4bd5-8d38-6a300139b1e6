import { UseMutationResult } from '@tanstack/react-query';
import { message } from 'antd';
import { TFunction } from 'i18next';
import { APIError, computeErrorMessage, logError } from 'utils';

import { IUserManager } from '@/app/types/adminManagers';
import { ApiError } from '@/app/types/api';

const onResendInvitationSuccess = (t: TFunction, user: IUserManager) => {
  message.success(
    `${t(
      'list.user.resendInvitation.notification.success',
      'Successfully resend invitation to {{email}}',
      {
        email: user.email,
      },
    )}`,
  );
};

const onResendInvitationError = (
  t: TFunction,
  user: IUserManager,
  error: string,
) => {
  message.error(
    `${t(
      'list.user.resendInvitation.notification.error',
      'Error resending invitation to {{email}}: {{error}}',
      {
        email: user.email,
        error,
      },
    )}`,
  );
};

export const handleResendInvitation = (
  t: TFunction,
  user: IUserManager,
  resendInvitation: UseMutationResult<
    string | undefined,
    APIError,
    string,
    unknown
  >,
) => {
  const email = encodeURIComponent(user.email);
  resendInvitation
    .mutateAsync(email)
    .then(() => {
      onResendInvitationSuccess(t, user);
    })
    .catch(e => {
      logError('handleActivateMember', e);
      let errorMessage = computeErrorMessage(e);
      switch ((e as ApiError)?.code) {
        case 400:
          errorMessage = t(
            'list.user.deleteMember.notification.alreadyAssigned',
            'Cannot delete, member is currently assigned to a PC.',
          );
          break;
      }
      onResendInvitationError(t, user, errorMessage);
    });
};
