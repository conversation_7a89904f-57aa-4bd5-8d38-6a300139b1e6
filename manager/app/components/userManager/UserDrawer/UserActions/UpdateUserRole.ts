import { UseMutationResult } from '@tanstack/react-query';
import { message } from 'antd';
import { TFunction } from 'i18next';
import { ErrorTypes, UserRole } from 'types';
import { APIError, computeErrorMessage, logError } from 'utils';

import { IUserManager } from '@/app/types/adminManagers';
import { IUpdateUserRolePayload } from '@/app/types/api';

const onUpdateUserRoleSuccess = (
  t: TFunction,
  user: IUserManager,
  role: UserRole,
) => {
  message.success(
    `${t(
      'list.user.updateUserRole.notification.success',
      'Successfully updated member {{email}} to {{role}}',
      {
        email: user.email,
        role: t(`list.user.role.${role}`),
      },
    )}`,
  );
};

const onUpdateUserRoleError = (
  t: TFunction,
  user: IUserManager,
  role: UserRole,
  error: ErrorTypes,
) => {
  const errorMessage = computeErrorMessage(error);
  message.error(
    `${t(
      'list.user.updateUserRole.notification.error',
      'Error updating member {{email}} to {{role}}: {{error}}',
      {
        email: user.email,
        role: t(`list.user.role.${role}`),
        errorMessage,
      },
    )}`,
  );
};

export const handleUpdateUserRole = (
  t: TFunction,
  user: IUserManager,
  updateUserRole: UseMutationResult<
    string | undefined,
    APIError,
    IUpdateUserRolePayload,
    unknown
  >,
  role: UserRole,
  setUserRole?: (role: UserRole) => void,
) => {
  updateUserRole
    .mutateAsync({ memberId: user.id, role })
    .then(() => {
      setUserRole?.(role);
      onUpdateUserRoleSuccess(t, user, role);
    })
    .catch(e => {
      logError('handleUpdateUserRole', e);
      const errorMessage = computeErrorMessage(e);

      onUpdateUserRoleError(t, user, role, errorMessage);
    });
};
