import DeleteUserModal from './DeleteUserModal';

import { IUserManager } from '@/app/types/adminManagers';

export type UserActionType = 'deleteUser' | undefined;

interface Props {
  actionModal: UserActionType;
  setActionModal: (action: UserActionType) => void;
  user: IUserManager;
}

export const UserActionsModal = ({
  actionModal,
  setActionModal,
  user,
}: Props) => (
  <DeleteUserModal
    memberId={user.id}
    status={user.status}
    inviteEmail={user.email}
    isOpen={actionModal === 'deleteUser'}
    closeModal={() => setActionModal(undefined)}
  />
);
