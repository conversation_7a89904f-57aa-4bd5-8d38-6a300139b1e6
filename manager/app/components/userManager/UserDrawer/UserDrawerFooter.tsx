import {
  DeleteOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { Divider } from 'antd';
import { ReactNode } from 'react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { UserRole } from 'types';

import { handleResendInvitation } from './UserActions/ResendInvitation';
import { UserActionType } from './UserActions/UserActionsModal';
import Button from '../../ui/Button/Button';

import {
  useCurrentMember,
  useResendInvitation,
} from '@/app/hooks/reactQuery/member/useMember';
import { IUserManager } from '@/app/types/adminManagers';
import { getRules } from '@/app/utils/rules';

interface IButtonData {
  dataTestId: string;
  displayed: boolean;
  disabled?: boolean;
  icon: ReactNode;
  key: string;
  label: string;
  onClick: () => void;
  danger?: boolean;
}

interface Props {
  setActionModal: (action: UserActionType) => void;
  user: IUserManager;
}

export const UserDrawerFooter = ({ setActionModal, user }: Props) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const currentMemberRole = currentMemberQuery.data?.role ?? UserRole.MEMBER;

  const resendInvitation = useResendInvitation();
  const rules = getRules(currentMemberRole);

  const buttonData: IButtonData[] = [
    {
      key: 'resendInvitation',
      dataTestId: 'resend_email_invitation-button',
      displayed: user.status === 'pending',
      disabled: !rules[currentMemberRole]?.canResendInvitation,
      onClick: () => handleResendInvitation(t, user, resendInvitation),
      icon: <MailOutlined />,
      label: t(
        'account.user.resendVerificationEmail.label',
        'Resend the verification email',
      ),
    },
    {
      key: 'deleteUser',
      dataTestId: 'delete_user-button',
      displayed: true,
      disabled:
        !rules[currentMemberRole]?.canDelete[user.role] &&
        !rules[user.role]?.canBeDeleted,
      // TODO: Disabled when user is assigned to a vm. Wait for backend
      // disabled: user.isAssigned,      onClick: () => setActionModal('deleteUser'),
      onClick: () => setActionModal('deleteUser'),
      danger: true,
      icon: <DeleteOutlined />,
      label: t('list.user.deleteMember.label', 'Delete'),
    },
  ];

  return (
    <>
      {buttonData.map(
        (button, index) =>
          button.displayed && (
            <React.Fragment key={`skelton-item-${index}`}>
              {index > 0 && <Divider />}
              <Button
                key={button.key}
                data-testid={button.dataTestId}
                color={button.danger ? 'danger' : 'default'}
                variant="text"
                shape="default"
                justify="left"
                type="default"
                block
                icon={button.icon}
                onClick={button.onClick}
                title={button.label}
                disabled={button.disabled}
              >
                {button.label}
              </Button>
            </React.Fragment>
          ),
      )}
    </>
  );
};
