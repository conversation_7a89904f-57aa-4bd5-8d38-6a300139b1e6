import { useState } from 'react';
import { IUseNavigationRules } from 'types';

import {
  UserActionType,
  UserActionsModal,
} from './UserActions/UserActionsModal';
import { UserDrawerFooter } from './UserDrawerFooter';
import { TagManager } from '../../TagManager/TagManager';
import { Drawer } from '../../ui/Drawer/Drawer';
import { DrawerHeader } from '../../ui/Drawer/DrawerHeader/DrawerHeader';
import MemberDetails from '../../ui/MemberDetail/MemberDetails';

import { IUserManager } from '@/app/types/adminManagers';

interface Props {
  userDrawerRules: IUseNavigationRules;
  isOpen: boolean;
  onClose: () => void;
  userData: IUserManager;
}

export const UserDrawer = ({
  userDrawerRules,
  isOpen,
  onClose,
  userData,
}: Props) => {
  const [actionModal, setActionModal] = useState<UserActionType>(undefined);

  return (
    <>
      <Drawer
        data={userData}
        isOpen={isOpen}
        onClose={onClose}
        header={
          <DrawerHeader
            onClose={onClose}
            title={
              <MemberDetails
                email={userData.email}
                firstName={userData.first_name}
                lastName={userData.last_name}
              />
            }
          />
        }
        footer={
          userDrawerRules.userDrawer.footer && (
            <UserDrawerFooter user={userData} setActionModal={setActionModal} />
          )
        }
      >
        {userDrawerRules.userDrawer.tagManager && (
          <TagManager
            entity="user"
            id={userData.id}
            initialTags={userData.tags}
          />
        )}
      </Drawer>
      <UserActionsModal
        user={userData}
        actionModal={actionModal}
        setActionModal={setActionModal}
      />
    </>
  );
};
