import { Grid, Table, TableColumnsType } from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { IUseNavigationRules } from 'types';

import { UserDrawer } from './UserDrawer/UserDrawer';
import { ActionsColumn } from '../ui/Table/ActionColumn';
import { CreatedAtColumn } from '../ui/Table/CreatedAtColumn';
import { RoleColumn } from '../ui/Table/RoleColumn';
import { TagsColumn } from '../ui/Table/TagsColumn';
import { UserColumn } from '../ui/Table/UserColumn';

import TableSkeleton from '@/app/components/ui/Skeleton/tableSkeleton/TableSkeleton';
import useStyles from '@/app/components/ui/Table/Table.styles';
import { IUserManager } from '@/app/types/adminManagers';
import { LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE } from '@/app/utils/constants';

interface Props {
  data?: IUserManager[];
  isLoading?: boolean;
  userDrawerRules: IUseNavigationRules;
}

export const UserTable = ({ data, isLoading, userDrawerRules }: Props) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [pageSize, setPageSize] = useState(LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE);
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(
    undefined,
  );
  const selectedUserData = data?.find(vm => vm.id === selectedUserId);

  const renderTotalUsers = (total: number) => {
    return total > 0 ? (
      <Trans
        i18nKey="list.user.total"
        defaults="Total: {{total}} users"
        values={{ total }}
      />
    ) : null;
  };

  const onPaginationChange = (page: number, size: number) => {
    if (size !== pageSize) {
      setPageSize(size);
    }
  };

  const onDrawerClose = () => {
    setIsDrawerOpen(false);
  };

  const handleDrawer = (id: string) => {
    setSelectedUserId(id);
    setIsDrawerOpen(true);
  };

  const emptySkeleton = {
    emptyText: <TableSkeleton />,
  };

  const columns: TableColumnsType<IUserManager> = [
    {
      key: 'createdAt',
      dataIndex: 'createdAt',
      title: t('list.heading.created', 'Created'),
      showSorterTooltip: false,
      sorter: (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      sortDirections: ['ascend', 'descend'],
      render: (_, { createdAt }) => <CreatedAtColumn createdAt={createdAt} />,
      width: 200,
      responsive: ['xl'],
    },
    {
      key: 'user',
      dataIndex: 'user',
      title: t('list.heading.user', 'User'),
      showSorterTooltip: false,
      sorter: (a, b) =>
        `${a.last_name}${a.first_name}`.localeCompare(
          `${b.last_name}${b.first_name}`,
        ),
      sortDirections: ['ascend', 'descend'],
      render: (_, user) => <UserColumn user={user} />,
      ellipsis: true,
    },
    {
      key: 'tags',
      dataIndex: 'tags',
      title: t('list.heading.tags', 'Tags'),
      render: (_, { tags }) => <TagsColumn tags={tags} />,
      responsive: ['lg'],
      width: screens.xxl ? 200 : 'auto',
    },
    {
      key: 'role',
      dataIndex: 'role',
      title: t('list.heading.role', 'Role'),
      showSorterTooltip: false,
      sorter: (a, b) => a.role.localeCompare(b.role),
      sortDirections: ['ascend', 'descend'],
      render: (_, user) => <RoleColumn user={user} />,
      width: screens.md ? 200 : 'auto',
    },
    {
      key: 'actions',
      align: 'right',
      render: (_, user) =>
        userDrawerRules.userDrawer.drawer && (
          <ActionsColumn handleDrawer={handleDrawer} id={user.id} type="user" />
        ),
      width: 75,
      responsive: ['sm'],
    },
  ];

  return (
    <>
      <Table
        className={styles.table}
        dataSource={data || []}
        locale={isLoading ? emptySkeleton : undefined}
        columns={columns}
        pagination={{
          hideOnSinglePage: data?.length
            ? data?.length < LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE
            : true,
          showSizeChanger: true,
          showQuickJumper: false,
          defaultCurrent: 1,
          total: data?.length,
          showTotal: total => renderTotalUsers(total),
          pageSize: pageSize,
          responsive: true,
          onShowSizeChange: (current, size) =>
            onPaginationChange(current, size),
        }}
        rowKey={item => item.id}
        onRow={rowData => {
          return {
            'data-testid': `user_table-${rowData.id}-row`,
            onClick: () => {
              userDrawerRules.userDrawer.drawer && handleDrawer(rowData.id);
            },
          };
        }}
      />
      {selectedUserData && (
        <UserDrawer
          userDrawerRules={userDrawerRules}
          isOpen={isDrawerOpen}
          onClose={onDrawerClose}
          userData={selectedUserData}
        />
      )}
    </>
  );
};
