import { Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { NotificationState } from 'types';

const ApiError = () => {
  const { t } = useTranslation();

  return (
    <Alert
      type={NotificationState.ERROR}
      message={t('api.error.title', 'Something went wrong...')}
      description={t(
        'api.error.description',
        'An error has occurred fetching your data. Reload the page or contact support',
      )}
    />
  );
};

export default ApiError;
