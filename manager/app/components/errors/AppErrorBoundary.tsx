import React from 'react';

export class AppErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // eslint-disable-next-line no-console
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px' }}>
          <h1>Something went wrong</h1>
          {this.state.error && (
            <>
              <p>{this.state.error.message}</p>

              <pre style={{ whiteSpace: 'pre-wrap' }}>
                {this.state.error.stack}
              </pre>

              <a href="/">Go to Homepage</a>
            </>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}
