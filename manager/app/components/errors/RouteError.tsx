import { Flex, Typography } from 'antd';
import <PERSON><PERSON> from 'lottie-react';
import { useTranslation } from 'react-i18next';

import animation404 from '@/app/components/ui/lottie/404.json';

const RouteError = ({ error }: any) => {
  const { t } = useTranslation();

  return (
    <>
      {error.status === 404 ? (
        <>
          <Typography.Title level={1}>
            {t('notFound.title', '404 not found')}
          </Typography.Title>
          <Flex gap={40} vertical>
            <Typography.Text>
              {t(
                'notFound.description',
                'The page your are looking for does not exist.',
              )}
            </Typography.Text>
            <Lottie
              animationData={animation404}
              style={{ height: 200 }}
              loop={true}
            />
          </Flex>
        </>
      ) : (
        <>
          <Typography.Title level={1}>
            {error.status} {error.statusText}
          </Typography.Title>
          <Typography.Text>{error.data}</Typography.Text>
        </>
      )}
    </>
  );
};

export default RouteError;
