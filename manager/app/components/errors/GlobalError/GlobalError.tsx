import { Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { NotificationState } from 'types';

import useStyles from './GlobalError.styles';

interface GlobalErrorProps {
  error?: Error | null;
}

const GlobalError: React.FC<GlobalErrorProps> = ({ error }) => {
  const { t } = useTranslation();
  const { styles } = useStyles();

  return (
    <div className={styles.globalError}>
      <Alert
        message={t('login.error.title', 'Oops, something is broken')}
        description={
          error?.message
            ? error.message
            : t(
                'login.error.description',
                'An error occurred when attempting to logged you in. Please try again later or contact support if the problem persists.',
              )
        }
        type={NotificationState.ERROR}
      />
    </div>
  );
};

export default GlobalError;
