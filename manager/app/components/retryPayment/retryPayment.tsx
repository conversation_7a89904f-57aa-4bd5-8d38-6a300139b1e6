import { Link } from '@remix-run/react';
import { Typography } from 'antd';
import { useState } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { NotificationState } from 'types';
import { logError } from 'utils';

import LocalLoader from '@/app/components//ui/Loader/LocalLoader';
import { useIsSubscriptionOnHold } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import {
  HasUnpaidInvoiceResult,
  useRetryPayment,
} from '@/app/hooks/reactQuery/user/useUser';
import { ROUTES_PATH } from '@/app/utils/constants';

interface IRetryPaymentProps {
  paymentDueInfo: HasUnpaidInvoiceResult;
  setRetryPaymentState: (state: NotificationState) => void;
  setTitle: (title: string) => void;
  isB2b: boolean;
}

const RetryPayment = ({
  paymentDueInfo,
  setRetryPaymentState,
  isB2b,
  setTitle,
}: IRetryPaymentProps) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [showRetryPaymentSuccess, setShowRetryPaymentSuccess] = useState(false);
  const [showRetryPaymentError, setShowRetryPaymentError] = useState(false);
  const retryPayment = useRetryPayment();
  const { isCloudPcSubscriptionOnHold, isDriveSubscriptionOnHold } =
    useIsSubscriptionOnHold();

  const getProductName = () => {
    if (isCloudPcSubscriptionOnHold) {
      return t('infoBanner.unpaid.infos.productName.cloudpc', 'Shadow PC');
    } else if (isDriveSubscriptionOnHold) {
      return t(
        'infoBanner.unpaid.infos.productName.shadow-drive',
        'Shadow Drive',
      );
    }
  };

  const setIsRetryingPayment = (isRetryingPayment: boolean) => {
    setIsLoading(isRetryingPayment);
  };

  const onRetryPayment = () => {
    setIsRetryingPayment(true);

    retryPayment.mutate(
      {},
      {
        onSuccess: () => {
          setShowRetryPaymentSuccess(true);
          // TODO Mehdi: onShowRetryPaymentSuccess();
          setRetryPaymentState(NotificationState.SUCCESS);
          setIsRetryingPayment(false);
          setTitle(t('infoBanner.unpaid.success.title'));
        },
        onError: error => {
          logError('Error retrying payment for unpaid invoices', error);
          setShowRetryPaymentError(true);
          setIsRetryingPayment(false);
          setRetryPaymentState(NotificationState.ERROR);
          setTitle(t('infoBanner.unpaid.error.title'));
        },
      },
    );
  };

  if (isLoading) {
    return <LocalLoader />;
  }

  return (
    <>
      <Typography>
        {paymentDueInfo.hasUnpaidInvoice && !showRetryPaymentSuccess && (
          <>
            {showRetryPaymentError ? (
              <Trans
                i18nKey="infoBanner.unpaid.error.content"
                defaults="Your payment has been declined. Please try again later, or update your payment method by clicking <0>here</0>"
                components={[
                  <Link to={`${ROUTES_PATH.BILLING_PAYMENT_METHOD}`} />,
                ]}
              />
            ) : (
              <Trans
                i18nKey={
                  isB2b
                    ? 'infoBanner.unpaid.infos.contentBusiness'
                    : 'infoBanner.unpaid.infos.content'
                }
                values={{
                  productName: getProductName(),
                  daysBeforeTermination: paymentDueInfo.daysBeforeCancel,
                }}
                components={[
                  <Typography.Link onClick={onRetryPayment} />,
                  <Link to={`${ROUTES_PATH.BILLING_PAYMENT_METHOD}`} />,
                ]}
              />
            )}
          </>
        )}
        {showRetryPaymentSuccess &&
          t(
            'infoBanner.unpaid.success.content',
            'Your payment has been successful. For more information and details, please check your invoice',
          )}
      </Typography>
    </>
  );
};

export default RetryPayment;
