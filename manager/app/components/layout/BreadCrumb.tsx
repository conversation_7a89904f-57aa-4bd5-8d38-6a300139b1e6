import { Link, useLocation } from '@remix-run/react';
import { Breadcrumb } from 'antd';

const BreadCrumb = () => {
  const location = useLocation();
  const { pathname } = location;
  const pathnames = pathname.split('/').filter(item => item);

  const createBreadcrumbItems = () => {
    return pathnames.map((item, index) => {
      const cleanerName = item.replace(/-/g, ' ');
      const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
      const isLast = index === pathnames.length - 1;

      return {
        title: isLast ? (
          cleanerName
        ) : (
          <Link to={`${routeTo}`}>{cleanerName}</Link>
        ),
      };
    });
  };

  // return nothing if on home page
  if (pathnames.length === 0) {
    return;
  }

  return (
    <Breadcrumb
      style={{ marginBottom: '40px' }}
      separator=">"
      items={createBreadcrumbItems()}
    />
  );
};

export default BreadCrumb;
