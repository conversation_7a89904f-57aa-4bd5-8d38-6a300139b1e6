import { useLocation } from '@remix-run/react';
import { Flex, Layout } from 'antd';
import React, { ReactNode } from 'react';

import useStyles from './AppLayout.styles';
import FooterManager from './footer/Footer';
import HeaderManager from './header/Header';
import SiderManager from './sider/Sider';
import InfoBanner from '../infoBanner/infoBanner';

import { ROUTES_PATH, UNPROTECTED_ROUTES } from '@/app/utils/constants';

const { Content } = Layout;

type Props = {
  children: ReactNode;
};

const AppLayout: React.FC<Props> = ({ children }) => {
  const location = useLocation();
  const fullLayoutRoutes = [
    ROUTES_PATH.VM_MANAGER,
    ROUTES_PATH.USER_MANAGER,
    ROUTES_PATH.BILLING_INVOICES,
  ];
  const isFullLayout = fullLayoutRoutes.includes(location.pathname);
  const isUnprotectedRoute = UNPROTECTED_ROUTES.includes(location.pathname);

  const { styles } = useStyles({ isFullLayout: isFullLayout });

  // For unprotected routes, do not render the layout (which includes the header, sider, footer)
  // They make unnecessary API calls and are not needed for the route (invite and invite-confirmation)
  if (isUnprotectedRoute) {
    return <>{children}</>;
  }

  return (
    <Layout className={styles.layout}>
      <HeaderManager />
      <Layout>
        <SiderManager />
        <Layout>
          <Content className={styles.layout__main}>
            {/* // disable breadcrumb for the moment
            <BreadCrumb /> */}
            <Flex vertical gap="large" className={styles.layout__main__inner}>
              <InfoBanner />
              {children}
            </Flex>
          </Content>
          <FooterManager />
        </Layout>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
