import { useLocation } from '@remix-run/react';
import { Grid, Menu, MenuProps } from 'antd';
import React, { useState } from 'react';

import useStyles from './Menu.styles';
import { useNavigation } from '../../../hooks/navigation/useNavigation';
import { ROUTES_PATH } from '../../../utils/constants';

interface LeftMenuProps {
  setOpen?: (open: boolean) => void;
}

const LeftMenu = ({ setOpen }: LeftMenuProps) => {
  const { useBreakpoint } = Grid;
  const { styles } = useStyles();

  const screens = useBreakpoint();
  const menuItems = useNavigation();

  const location = useLocation();
  const { pathname } = location;
  const currentFolder =
    `/${location.pathname.split('/')[1]}` === '/'
      ? ROUTES_PATH.VM
      : `/${location.pathname.split('/')[1]}`;
  const [stateOpenKeys, setStateOpenKeys] = useState([currentFolder]);

  const onClose = () => {
    if (setOpen) {
      setOpen(false);
    }
  };

  const onOpenChange: MenuProps['onOpenChange'] = openKeys => {
    setStateOpenKeys(openKeys);
  };

  return screens.md ? (
    <Menu
      className={styles.menu}
      defaultOpenKeys={currentFolder ? [currentFolder] : []}
      openKeys={stateOpenKeys}
      selectedKeys={[pathname]}
      onOpenChange={onOpenChange}
      mode="inline"
      items={menuItems}
    />
  ) : (
    <Menu
      className={styles.menu}
      defaultOpenKeys={currentFolder ? [currentFolder] : []}
      openKeys={stateOpenKeys}
      selectedKeys={[pathname]}
      onOpenChange={onOpenChange}
      mode="inline"
      items={menuItems}
      onClick={onClose}
    />
  );
};

export default LeftMenu;
