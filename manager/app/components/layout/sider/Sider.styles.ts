import { createStyles } from 'antd-style';

export default createStyles(
  ({ token, css }, props: { collapsed: boolean; siderWidth: number }) => {
    return {
      sider__footer: css`
        padding: ${token.paddingXS}px;
        position: fixed;
        bottom: 0;
        width: ${props.collapsed ? '80px' : `${props.siderWidth}px`};
        background-color: none;
        border-top: 1px solid ${token.colorBorderSecondary};
        cursor: pointer;
        color: ${token.colorTextBase};
        transition: width 0.2s ease-out;
      `,
    };
  },
);
