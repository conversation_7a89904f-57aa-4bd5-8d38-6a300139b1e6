import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { Flex, Layout, Grid } from 'antd';
import React, { useState } from 'react';

import useStyles from './Sider.styles';
import LeftMenu from '../menu/Menu';

const { Sider } = Layout;

const SiderManager = () => {
  const { useBreakpoint } = Grid;

  const screens = useBreakpoint();
  const [collapsed, setCollapsed] = useState(false);
  const siderWidth = 240;

  const { styles } = useStyles({
    collapsed: collapsed,
    siderWidth: siderWidth,
  });

  return (
    screens.md && (
      <Sider
        collapsible
        collapsed={collapsed}
        width={siderWidth}
        trigger={null}
        breakpoint="lg"
      >
        <LeftMenu />
        <Flex
          data-testid="navigation-collapse-button"
          className={styles.sider__footer}
          onClick={() => setCollapsed(prev => !prev)}
        >
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </Flex>
      </Sider>
    )
  );
};

export default SiderManager;
