import { createStyles } from 'antd-style';

export default createStyles(
  ({ token, css }, props: { isFullLayout: boolean }) => {
    return {
      layout: css`
        min-height: 100vh;
      `,
      layout__main: css`
        padding: ${token.paddingMD}px;
        display: flex;
        justify-content: center;

        @media (min-width: ${token.screenLG}px) {
          padding: ${token.paddingLG}px ${token.paddingXL}px;
        }
      `,
      layout__main__inner: css`
        width: ${props.isFullLayout ? '100%' : '800px'};
      `,
    };
  },
);
