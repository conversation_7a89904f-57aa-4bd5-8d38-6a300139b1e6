import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    header: css`
      height: 48px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid ${token.colorBorder};
      padding: 0;
      z-index: 1000;

      @media (min-width: ${token.screenMD}px) {
        padding: 0 ${token.paddingMD}px;
      }
    `,

    header__content: css`
      width: 100%;
    `,

    header__logo: css`
      width: 80px;

      @media (min-width: ${token.screenMD}px) {
        width: 85px;
        margin-bottom: 8px;
      }
    `,

    header__drawer__mask: css`
      display: none;
    `,

    header__drawer__wrapper: css`
      &.ant-drawer-content-wrapper {
        top: 48px;
        box-shadow: unset;
      }
    `,

    header__drawer__body: css`
      &.ant-drawer-body {
        padding: 16px 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    `,
  };
});
