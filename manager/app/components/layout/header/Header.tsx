import { CloseOutlined, MenuOutlined } from '@ant-design/icons';
import { Flex, Layout, Drawer, Grid, Col, Row } from 'antd';
import { useLayoutEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SVG_PATH } from 'utils';

import AccountInfo from './AccountInfo/AccountInfo';
import useStyles from './Header.styles';
import LeftMenu from '../menu/Menu';

import Button from '@/app/components/ui/Button/Button';

const { Header } = Layout;

const HeaderManager = () => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const [isOpen, setIsOpen] = useState(false);

  const handleDrawer = () => {
    setIsOpen(!isOpen);
  };

  useLayoutEffect(() => {
    window.scrollTo(0, 0);
  }, [isOpen]);

  return (
    <>
      <Header className={styles.header}>
        {screens.md ? (
          <Flex
            className={styles.header__content}
            align="center"
            justify="space-between"
          >
            <img
              className={styles.header__logo}
              src={`${SVG_PATH}logos/shadow-gradient.svg`}
              alt={t('header.logoAlt', 'Shadow')}
            />
            <AccountInfo />
          </Flex>
        ) : (
          <Row align="middle" className={styles.header__content}>
            <Col span={3}>
              <Flex justify="center">
                <Button
                  shape="circle"
                  data-testid="header-navigation-button"
                  type="text"
                  onClick={handleDrawer}
                  icon={isOpen ? <CloseOutlined /> : <MenuOutlined />}
                />
              </Flex>
            </Col>
            <Col offset={3} span={12}>
              <Flex justify="center">
                <img
                  className={styles.header__logo}
                  src={`${SVG_PATH}logos/shadow-gradient.svg`}
                  alt={t('header.logoAlt', 'Shadow')}
                />
              </Flex>
            </Col>
            <Col span={3} offset={3}>
              <Flex justify="center">
                <AccountInfo />
              </Flex>
            </Col>
          </Row>
        )}
      </Header>
      <Drawer
        classNames={{
          body: styles.header__drawer__body,
          mask: styles.header__drawer__mask,
          wrapper: styles.header__drawer__wrapper,
        }}
        closable={false}
        onClose={handleDrawer}
        open={isOpen}
        width="100vw"
        placement="left"
      >
        <LeftMenu setOpen={setIsOpen} />
      </Drawer>
    </>
  );
};

export default HeaderManager;
