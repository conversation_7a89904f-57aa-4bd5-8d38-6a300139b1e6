import { But<PERSON>, Di<PERSON>r, <PERSON>lex, Grid, Popover, Tag, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';

import { UserIcon } from '@/app/components/Icon';
import MemberDetails from '@/app/components/ui/MemberDetail/MemberDetails';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';

const AccountInfo = () => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const auth = useAuth();
  const currentMemberQuery = useCurrentMember();
  const role = currentMemberQuery.data?.role;

  const handleLogOut = () => {
    void auth.signoutRedirect();
  };

  return (
    <Popover
      arrow={false}
      placement="bottomRight"
      content={
        <Flex vertical gap="middle">
          <Flex gap="small" align="center">
            {currentMemberQuery.data?.user?.email && (
              <MemberDetails
                email={currentMemberQuery.data?.user?.email}
                firstName={currentMemberQuery.data?.user?.first_name}
                lastName={currentMemberQuery.data?.user?.last_name}
                hasEllipsis={false}
              />
            )}
          </Flex>
          <Divider />
          <Flex align="spaceBetween" justify="space-between">
            {role && <Tag>{t(`list.user.role.${role}`)}</Tag>}
            <Typography.Link
              data-testid="header-logout-button"
              onClick={handleLogOut}
              title={t('header.logout.label', 'Logout')}
            >
              {t('header.logout.label', 'Logout')}
            </Typography.Link>
          </Flex>
        </Flex>
      }
      trigger={['click']}
    >
      {screens.md ? (
        <Button
          data-testid="header-user-button"
          color="default"
          variant="link"
          icon={<UserIcon />}
          iconPosition="end"
        >
          {currentMemberQuery.data?.user?.first_name}
        </Button>
      ) : (
        <Button
          data-testid="header-user-button"
          shape="circle"
          type="text"
          icon={<UserIcon />}
        />
      )}
    </Popover>
  );
};

export default AccountInfo;
