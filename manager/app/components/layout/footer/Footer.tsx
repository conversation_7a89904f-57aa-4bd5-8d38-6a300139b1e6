import { Flex, Layout, Typography } from 'antd';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import useStyles from './Footer.styles';

import { useUrl } from '@/app/hooks/useUrl';
import {
  COOKIES_URL,
  LANDING_URL,
  LEGAL_URL,
  PRIVACY_POLICY_URL,
} from '@/app/utils/constants';

const FooterManager = () => {
  const { t } = useTranslation();
  const { getCguUrl } = useUrl();
  const { styles } = useStyles();

  const footerListItems = useMemo(() => {
    return [
      {
        href: LANDING_URL,
        label: t('footer.companyName', '© Shadow'),
      },
      {
        href: getCguUrl(true),
        label: t('footer.cgu', 'Terms of Use'),
      },
      {
        href: LEGAL_URL,
        label: t('footer.legal', 'Legal'),
      },
      {
        href: PRIVACY_POLICY_URL,
        label: t('footer.privacy', 'Privacy'),
      },
      {
        href: COOKIES_URL,
        label: t('footer.cookies', 'Cookies'),
      },
    ];
  }, [t, getCguUrl]);

  return (
    <Layout.Footer className={styles.footer}>
      <nav>
        <Flex wrap="wrap" justify="center" style={{ gap: '12px 24px' }}>
          {footerListItems.map((item, key) => (
            <Typography.Link
              className={styles.footer__link}
              key={key}
              href={item.href}
              target="_blank"
            >
              {item.label}
            </Typography.Link>
          ))}
        </Flex>
      </nav>
    </Layout.Footer>
  );
};

export default FooterManager;
