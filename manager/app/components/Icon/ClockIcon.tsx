import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';
import React from 'react';

type CustomIconComponentProps = GetProps<typeof Icon>;

const ClockSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 22 22"
    fill="none"
  >
    <g stroke="currentColor" strokeWidth="1.5" strokeLinecap="round">
      <circle cx="11" cy="11" r="10" />
      <path d="M11 7V10.7324C11 10.8996 11.0836 11.0557 11.2226 11.1484L14 13" />
    </g>
  </svg>
);

export const ClockIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={ClockSvg} {...props} />
);
