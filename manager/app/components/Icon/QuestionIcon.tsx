import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';
import React from 'react';

type CustomIconComponentProps = GetProps<typeof Icon>;

const QuestionSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 22 22"
    fill="none"
  >
    <g
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="1" y="1" width="20" height="20" rx="10" />
      <path d="M8.5 8.80247C8.71144 8.20149 9.12878 7.69473 9.6781 7.37193C10.2274 7.04913 10.8733 6.93113 11.5013 7.03884C12.1293 7.14654 12.6989 7.47299 13.1092 7.96038C13.5195 8.44776 13.7441 9.06462 13.7432 9.7017C13.7432 11.5001 11.0451 12.3994 11.0451 12.3994" />
      <path d="M11.1191 15.9978H11.1287" />
    </g>
  </svg>
);

export const QuestionIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={QuestionSvg} {...props} />
);
