import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';
import React from 'react';

type CustomIconComponentProps = GetProps<typeof Icon>;

const CloudSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 22 14"
    fill="none"
  >
    <path
      d="M4.33333 13.2222C2.49238 13.2222 1 11.7298 1 9.88889C1 8.06056 2.52707 6.55405 4.34902 6.55563C4.35421 6.55563 4.3586 6.55164 4.35907 6.54647C4.64402 3.43609 7.25974 1 10.4444 1C12.8351 1 14.9052 2.37277 15.9093 4.37305C15.9113 4.3769 15.9155 4.37908 15.9198 4.37847C18.5452 4.00249 21 6.10488 21 8.77778C21 11.2324 19.0102 13.2222 16.5556 13.2222L4.33333 13.2222Z"
      stroke="currentColor"
      strokeWidth="1.5"
    />
  </svg>
);

export const CloudIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={CloudSvg} {...props} />
);
