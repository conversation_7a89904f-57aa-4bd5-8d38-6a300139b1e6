import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const AlwaysOnSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g fill="currentColor">
      <path d="M 13.96875 21.316406 C 13.78125 21.367188 13.589844 21.414062 13.398438 21.453125 C 12.898438 21.554688 12.578125 22.039062 12.679688 22.539062 C 12.683594 22.566406 12.691406 22.59375 12.699219 22.621094 C 12.835938 23.074219 13.296875 23.355469 13.765625 23.257812 C 13.996094 23.210938 14.222656 23.15625 14.449219 23.097656 C 14.941406 22.964844 15.230469 22.457031 15.101562 21.96875 C 14.96875 21.472656 14.460938 21.183594 13.96875 21.316406 Z M 13.96875 21.316406 " />
      <path d="M 7.539062 20.804688 C 7.363281 20.722656 7.183594 20.636719 7.011719 20.546875 C 6.558594 20.3125 6.003906 20.488281 5.769531 20.9375 C 5.652344 21.160156 5.636719 21.410156 5.703125 21.632812 C 5.773438 21.859375 5.929688 22.0625 6.160156 22.183594 C 6.363281 22.289062 6.574219 22.390625 6.789062 22.488281 C 7.253906 22.695312 7.796875 22.484375 8.007812 22.019531 C 8.214844 21.554688 8.003906 21.011719 7.539062 20.804688 Z M 7.539062 20.804688 " />
      <path d="M 10.710938 21.613281 C 10.519531 21.597656 10.320312 21.574219 10.128906 21.550781 C 9.625 21.476562 9.15625 21.828125 9.085938 22.332031 C 9.066406 22.46875 9.078125 22.601562 9.113281 22.726562 C 9.214844 23.0625 9.5 23.324219 9.867188 23.375 C 10.097656 23.40625 10.332031 23.433594 10.5625 23.453125 C 11.070312 23.492188 11.515625 23.113281 11.558594 22.605469 C 11.597656 22.097656 11.21875 21.65625 10.710938 21.613281 Z M 10.710938 21.613281 " />
      <path d="M 16.941406 19.941406 C 16.78125 20.050781 16.613281 20.15625 16.445312 20.257812 C 16.078125 20.480469 15.921875 20.921875 16.039062 21.3125 C 16.0625 21.386719 16.09375 21.457031 16.132812 21.523438 C 16.394531 21.960938 16.960938 22.101562 17.398438 21.839844 C 17.597656 21.71875 17.796875 21.589844 17.984375 21.460938 C 18.40625 21.171875 18.511719 20.597656 18.222656 20.175781 C 17.933594 19.757812 17.359375 19.652344 16.941406 19.941406 Z M 16.941406 19.941406 " />
      <path d="M 20.566406 17.445312 C 20.152344 17.148438 19.574219 17.242188 19.277344 17.65625 C 19.164062 17.8125 19.042969 17.96875 18.921875 18.121094 C 18.71875 18.367188 18.664062 18.6875 18.75 18.972656 C 18.800781 19.140625 18.902344 19.296875 19.050781 19.417969 C 19.445312 19.742188 20.023438 19.683594 20.347656 19.289062 C 20.496094 19.109375 20.636719 18.921875 20.773438 18.734375 C 21.070312 18.320312 20.976562 17.746094 20.566406 17.445312 Z M 20.566406 17.445312 " />
      <path d="M 21.855469 14.085938 C 21.367188 13.945312 20.855469 14.226562 20.710938 14.714844 C 20.65625 14.902344 20.597656 15.089844 20.53125 15.273438 C 20.460938 15.464844 20.460938 15.667969 20.515625 15.851562 C 20.597656 16.121094 20.800781 16.351562 21.089844 16.453125 C 21.566406 16.625 22.09375 16.375 22.269531 15.894531 C 22.347656 15.675781 22.417969 15.453125 22.484375 15.230469 C 22.625 14.742188 22.34375 14.230469 21.855469 14.085938 Z M 21.855469 14.085938 " />
      <path d="M 23.585938 8.078125 L 22.375 8.453125 C 21.453125 5.636719 19.519531 3.320312 16.898438 1.914062 C 16.632812 1.769531 16.363281 1.640625 16.09375 1.519531 C 16.09375 1.519531 16.089844 1.519531 16.089844 1.519531 C 16.078125 1.511719 16.066406 1.507812 16.054688 1.503906 C 13.664062 0.457031 11.019531 0.261719 8.484375 0.945312 C 8.464844 0.949219 8.445312 0.953125 8.425781 0.960938 C 8.339844 0.984375 8.253906 1.007812 8.171875 1.03125 C 8.03125 1.074219 7.894531 1.121094 7.757812 1.167969 C 7.742188 1.171875 7.726562 1.179688 7.710938 1.1875 C 5.445312 1.96875 3.519531 3.414062 2.144531 5.347656 C 2.140625 5.351562 2.132812 5.359375 2.128906 5.363281 C 1.996094 5.554688 1.863281 5.75 1.742188 5.945312 C 1.734375 5.960938 1.726562 5.972656 1.71875 5.984375 C 1.597656 6.183594 1.480469 6.386719 1.367188 6.59375 C 0.230469 8.71875 -0.207031 11.09375 0.0898438 13.4375 C 0.0898438 13.441406 0.0898438 13.445312 0.09375 13.453125 C 0.09375 13.457031 0.09375 13.460938 0.09375 13.464844 C 0.175781 14.089844 0.308594 14.714844 0.492188 15.328125 C 0.980469 16.949219 1.796875 18.402344 2.914062 19.65625 C 3.253906 20.035156 3.835938 20.070312 4.214844 19.730469 C 4.597656 19.390625 4.628906 18.808594 4.289062 18.425781 C 3.730469 17.800781 3.265625 17.117188 2.890625 16.375 C 2.886719 16.367188 2.882812 16.359375 2.878906 16.351562 C 2.789062 16.175781 2.707031 15.996094 2.628906 15.816406 C 2.628906 15.816406 2.628906 15.816406 2.628906 15.8125 C 2.488281 15.484375 2.363281 15.144531 2.257812 14.796875 C 1.050781 10.789062 2.59375 6.597656 5.792969 4.25 C 5.839844 4.214844 5.886719 4.183594 5.933594 4.148438 C 6.753906 3.570312 7.679688 3.109375 8.695312 2.804688 C 9.714844 2.496094 10.746094 2.367188 11.757812 2.398438 C 11.773438 2.398438 11.789062 2.398438 11.804688 2.398438 C 11.867188 2.402344 11.929688 2.40625 11.992188 2.40625 C 12.015625 2.410156 12.039062 2.410156 12.066406 2.414062 C 15.214844 2.609375 18.121094 4.359375 19.75 7.109375 C 19.753906 7.109375 19.753906 7.113281 19.753906 7.113281 C 19.777344 7.152344 19.800781 7.195312 19.824219 7.238281 C 19.828125 7.242188 19.832031 7.246094 19.835938 7.25 C 20.148438 7.796875 20.410156 8.382812 20.613281 9.003906 L 19.402344 9.378906 C 19.066406 9.484375 19.03125 9.726562 19.328125 9.917969 L 21.964844 11.625 C 22.261719 11.816406 22.613281 11.707031 22.746094 11.382812 L 23.953125 8.480469 C 24.085938 8.15625 23.921875 7.972656 23.585938 8.078125 Z M 23.585938 8.078125 " />
      <path d="M 8.074219 12.023438 C 7.1875 12.683594 6.582031 13.253906 6.257812 13.738281 C 5.933594 14.222656 5.742188 14.738281 5.675781 15.289062 L 11.238281 15.289062 L 11.238281 13.777344 L 8.34375 13.777344 C 8.515625 13.609375 8.660156 13.476562 8.789062 13.371094 C 8.914062 13.269531 9.164062 13.089844 9.535156 12.832031 C 10.164062 12.386719 10.597656 11.980469 10.835938 11.609375 C 11.074219 11.242188 11.191406 10.851562 11.191406 10.445312 C 11.191406 10.0625 11.089844 9.71875 10.882812 9.410156 C 10.671875 9.101562 10.386719 8.875 10.027344 8.726562 C 9.664062 8.578125 9.15625 8.503906 8.503906 8.503906 C 7.878906 8.503906 7.390625 8.582031 7.035156 8.738281 C 6.683594 8.894531 6.410156 9.117188 6.214844 9.410156 C 6.019531 9.703125 5.886719 10.109375 5.8125 10.632812 L 7.671875 10.78125 C 7.722656 10.40625 7.824219 10.144531 7.972656 9.996094 C 8.125 9.847656 8.316406 9.773438 8.554688 9.773438 C 8.78125 9.773438 8.96875 9.84375 9.121094 9.988281 C 9.269531 10.132812 9.34375 10.304688 9.34375 10.507812 C 9.34375 10.699219 9.269531 10.898438 9.117188 11.105469 C 8.964844 11.316406 8.617188 11.621094 8.074219 12.023438 Z M 8.074219 12.023438 " />
      <path d="M 16.832031 15.289062 L 16.832031 14.042969 L 17.667969 14.042969 L 17.667969 12.601562 L 16.832031 12.601562 L 16.832031 8.503906 L 15.214844 8.503906 L 11.839844 12.515625 L 11.839844 14.042969 L 15.214844 14.042969 L 15.214844 15.289062 Z M 13.433594 12.601562 L 15.214844 10.503906 L 15.214844 12.601562 Z M 13.433594 12.601562 " />
    </g>
  </svg>
);

export const AlwaysOnIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={AlwaysOnSvg} {...props} />
);
