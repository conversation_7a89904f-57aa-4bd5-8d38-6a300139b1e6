import { DeleteOutlined, InboxOutlined } from '@ant-design/icons';
import {
  Form,
  message,
  Flex,
  Card,
  Typography,
  Select,
  Input,
  Upload,
} from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import SelectSupport from './SelectSupport';

import Button from '@/app/components/ui/Button/Button';
import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { useConfig } from '@/app/hooks/store/useConfig';
import {
  useSendSupportMessage,
  useUploadMessageAttachment,
} from '@/app/hooks/support/useSupport';
import { useSupportFormOptions } from '@/app/hooks/support/useSupportFormOption';
import useSupportFormSubject from '@/app/hooks/support/useSupportFormTopic';
import useScrollOnRender from '@/app/hooks/useScrollOnRender';
import { IFile, ISupportForm } from '@/app/types/api';
import { ISupportSelectValues, MainTopic, Topic } from '@/app/types/support';
import { formatSupportFormPayload } from '@/app/utils/formatSupportFormPayload';

const SupportForm = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // hooks needed to format the api call
  const { market } = useConfig();
  const supportFormOptions = useSupportFormOptions();
  const { subjectOptions } = useSupportFormOptions();
  const { data: currentMemberQueryData } = useCurrentMember();
  const supportFormSubjects = useSupportFormSubject();
  const { mutateAsync: uploadFiles } = useUploadMessageAttachment();

  const initialFormValues: ISupportSelectValues = {
    mainTopic: undefined,
    shadowPCOS: undefined,
    shadowPCIssue: undefined,
    shadowPCSub: undefined,
    shadowDriveOS: undefined,
    subscriptionIssue: undefined,
    message: undefined,
    datacenter: undefined,
  };

  const [selectedValues, setSelectedValues] = useState(initialFormValues);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {},
  );
  const [isUploading, setIsUploading] = useState(false);

  const myRef = useScrollOnRender(
    selectedValues.mainTopic !== undefined,
    selectedValues,
  );

  const handleMainTopicChange = (value: MainTopic) => {
    form.resetFields([
      'shadowPCOS',
      'shadowPCIssue',
      'shadowPCSub',
      'shadowDriveOS',
      'subscriptionIssue',
      'message',
    ]);
    setSelectedValues({
      ...initialFormValues,
      mainTopic: value,
    });
  };

  const { mutateAsync, isLoading } = useSendSupportMessage();

  const handleSubmit = async (formValues: ISupportForm) => {
    if (!currentMemberQueryData) {
      return;
    }

    try {
      // Upload files if any
      let uploadedFileIds: string[] | undefined = undefined;
      if (fileList.length > 0) {
        // Set uploading state to true and show notification
        setIsUploading(true);
        message.loading({
          content: t(
            'support.form.notification.uploading',
            'Uploading files and sending your ticket...',
          ),
          key: 'uploadingMessage',
          duration: 0,
        });

        const filesPayload: IFile[] = fileList.map(file => ({
          file: file.thumbUrl, // base64 encoded file
          fileName: file.name,
          market,
        }));

        // Upload files to server
        uploadedFileIds = await uploadFiles(filesPayload);

        // Set uploading state to false and close notification
        setIsUploading(false);
        message.destroy('uploadingMessage');
      }

      // Create payload with uploaded file IDs
      const payload = formatSupportFormPayload(
        formValues,
        supportFormOptions,
        selectedValues,
        currentMemberQueryData,
        t,
        market,
      );

      // Add uploaded files token to payload
      // TODO: get token field from zendesk file upload response and add it to the payload
      if (uploadedFileIds && uploadedFileIds.length > 0) {
        payload.comment.uploads = uploadedFileIds;
      }

      await mutateAsync(payload);

      message.success(
        t(
          'support.form.notification.success',
          'Your message has been send to our support team!',
        ),
      );
      setSelectedValues(initialFormValues); // Reset selected values
      setFileList([]); // Clear file list
      form.resetFields();
    } catch (error) {
      message.error(
        t(
          'support.form.notification.error',
          'An error occurred when sending your message to our support team. Please try again later.',
        ),
      );
    }
  };

  return (
    <Card bordered={false} ref={myRef}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('support.form.title', 'Contact the support team')}
        </Typography.Title>
        <Typography.Text>
          <Trans
            i18nKey="support.form.description"
            defaults="Need help or have questions about Shadow services? Our support team is here to help, and will get back to you as soon as possible.\n\nOur Customer Support is available in English and French from <bold>Monday</bold> to <bold>Friday</bold> and during the following opening hours:\n<bold>English: 10am to 6pm (EST & CET)\nFrench: 10am to 6pm (CET)</bold>\n\nTo get started, please select a topic below:"
            components={{ bold: <strong /> }}
          />
        </Typography.Text>
        <Form
          form={form}
          onFinish={handleSubmit}
          initialValues={initialFormValues}
          layout="vertical"
        >
          {/* Main topic selector */}
          <Form.Item
            key={Topic.MAIN_TOPIC}
            name={Topic.MAIN_TOPIC}
            rules={[
              {
                required: true,
                message: t(
                  'form.select.error.default',
                  'Please select an option',
                ),
              },
            ]}
          >
            <Select
              data-testid={`contact_support-mainTopic-select`}
              size="large"
              options={subjectOptions}
              placeholder={t(
                'support.form.placeholder.selectTopic',
                'Select a topic',
              )}
              onChange={value => handleMainTopicChange(value)}
            />
          </Form.Item>
          {/* Annexe topic selector & text area */}
          {selectedValues.mainTopic && (
            <>
              <SelectSupport
                data={supportFormSubjects}
                mainTopic={selectedValues.mainTopic}
                setSelectedValues={setSelectedValues}
              />
              <Form.Item
                name="message"
                rules={[
                  {
                    required: true,
                    message: t(
                      'form.default.error.required',
                      'Field is required',
                    ),
                  },
                ]}
              >
                <Input.TextArea
                  data-testid="contact_support-message-input"
                  size="large"
                  id="support_message-input"
                  placeholder={t(
                    'support.form.placeholder.message',
                    'Please provide additional details to help us efficiently solve your request.',
                  )}
                  rows={7}
                />
              </Form.Item>
              <Form.Item name="files">
                <Upload.Dragger
                  name="files"
                  multiple
                  listType="picture"
                  showUploadList={{
                    showPreviewIcon: true,
                    showRemoveIcon: true,
                    showDownloadIcon: false,
                    removeIcon: <DeleteOutlined />,
                  }}
                  beforeUpload={file => {
                    // Define maximum sizes
                    const maxFileSize = 10 * 1024 * 1024; // 10MB per file
                    const maxTotalSize = 20 * 1024 * 1024; // 20MB total

                    // Check individual file size
                    if (file.size > maxFileSize) {
                      message.error(
                        t(
                          'support.form.attachments.sizeError',
                          'File size must be less than 10MB',
                        ),
                      );
                      return Upload.LIST_IGNORE;
                    }

                    // Calculate current total size
                    const currentTotalSize = fileList.reduce(
                      (total, existingFile) => total + existingFile.size,
                      0,
                    );

                    // Check if adding this file would exceed the global limit
                    if (currentTotalSize + file.size > maxTotalSize) {
                      message.error(
                        t(
                          'support.form.attachments.totalSizeError',
                          'Total file size cannot exceed 20MB',
                        ),
                      );
                      return Upload.LIST_IGNORE;
                    }

                    // Initialize progress for this file
                    setUploadProgress(prev => ({
                      ...prev,
                      [file.uid]: 0,
                    }));

                    // Convert file to base64
                    return new Promise((resolve, reject) => {
                      const reader = new FileReader();

                      // Add progress event listener
                      reader.onprogress = event => {
                        if (event.lengthComputable) {
                          const progress = Math.round(
                            (event.loaded / event.total) * 100,
                          );
                          setUploadProgress(prev => ({
                            ...prev,
                            [file.uid]: progress,
                          }));
                        }
                      };

                      reader.readAsDataURL(file);
                      reader.onload = () => {
                        const newFile = {
                          ...file,
                          thumbUrl: reader.result,
                          percent: 100, // Set to 100% when base64 conversion is complete
                          name: file.name, // Ensure filename is explicitly set
                        };
                        setFileList(prev => [...prev, newFile]);
                        // Set progress to 100% when complete
                        setUploadProgress(prev => ({
                          ...prev,
                          [file.uid]: 100,
                        }));
                        resolve(false); // Prevent auto upload
                      };
                      reader.onerror = error => {
                        // Reset progress on error
                        setUploadProgress(prev => ({
                          ...prev,
                          [file.uid]: 0,
                        }));
                        reject(error);
                      };
                    });
                  }}
                  onRemove={file => {
                    setFileList(prev =>
                      prev.filter(item => item.uid !== file.uid),
                    );
                    // Remove progress for this file
                    setUploadProgress(prev => {
                      const newProgress = { ...prev };
                      delete newProgress[file.uid];
                      return newProgress;
                    });
                    return true;
                  }}
                  progress={{
                    strokeColor: {
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    },
                    strokeWidth: 3,
                    format: percent => `${percent}%`,
                  }}
                  fileList={fileList.map(file => ({
                    ...file,
                    percent: uploadProgress[file.uid] || 0,
                  }))}
                >
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">
                    {t(
                      'support.form.attachments.dragText',
                      'Click or drag files to this area to upload',
                    )}
                  </p>
                  <p className="ant-upload-hint">
                    {t(
                      'support.form.attachments.hint',
                      'You can attach screenshots or other files (20MB maximum total size) to help us understand your issue better.',
                    )}
                  </p>
                </Upload.Dragger>
              </Form.Item>
            </>
          )}
          <Flex justify="flex-end">
            <Button
              data-testid="contact_support-submit-button"
              htmlType="submit"
              loading={isLoading || isUploading}
              disabled={isLoading || isUploading}
              blockMobile
            >
              {t('global.send', 'Send')}
            </Button>
          </Flex>
        </Form>
      </Flex>
    </Card>
  );
};

export default SupportForm;
