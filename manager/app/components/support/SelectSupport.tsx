import { DownOutlined } from '@ant-design/icons';
import { Flex, Form, Select, Typography } from 'antd';
import { useGetAllSubscriptions, useGetSubscription } from 'hooks';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  OrderColumns,
  ProductFamilyId,
  ProductType,
  SortableColumns,
  UserRole,
} from 'types';

import ShadowLoader from '../ui/Loader/ShadowLoader';

import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import {
  ISelectOption,
  ISupportFormChoice,
  ISupportSelectValues,
  MainTopic,
  Topic,
} from '@/app/types/support';

interface ISelectSupport {
  data: ISupportFormChoice[];
  mainTopic: MainTopic;
  setSelectedValues: Dispatch<SetStateAction<ISupportSelectValues>>;
}

const SelectSupport = ({
  data,
  mainTopic,
  setSelectedValues,
}: ISelectSupport) => {
  const { t } = useTranslation();
  const [selectedSubscriptionId, setSelectedSubscriptionId] = useState<
    string | null
  >(null);
  const { data: subscriptionData } = useGetSubscription(
    selectedSubscriptionId || '',
  );

  const handleFormChange = (value: any, current: any) => {
    if (current.id === Topic.SHADOW_PC_SUB && value) {
      try {
        const parsedValue = JSON.parse(value);
        setSelectedSubscriptionId(parsedValue.id);
      } catch (error) {
        // Silent fail if parsing fails
      }
    }

    setSelectedValues(prevState => ({
      ...prevState,
      [current.id]: value,
    }));
  };

  useEffect(() => {
    if (subscriptionData?.meta_data?.datacenter) {
      setSelectedValues(prevState => ({
        ...prevState,
        datacenter: subscriptionData.meta_data.datacenter || undefined,
      }));
    }
  }, [subscriptionData, setSelectedValues]);

  const currentMemberQuery = useCurrentMember();
  const currentMemberRole = currentMemberQuery.data?.role;

  const { data: subscriptionsData, isFetching: isSubscriptionsFetching } =
    useGetAllSubscriptions(
      currentMemberRole === UserRole.MEMBER,
      ProductFamilyId.CLOUDPC,
      OrderColumns.ASCEND,
      SortableColumns.SUB_NAME,
      mainTopic === MainTopic.SHADOW_PC_ISSUE,
    );

  const subscriptionsOptions: ISelectOption[] =
    subscriptionsData?.map(sub => {
      const foundPlan = sub.items.find(
        ({ item_type }) => item_type === ProductType.PLAN,
      );

      const offerNameWithoutSpecialCharacters = `${t(
        `subscription.details.name.${foundPlan?.name}`,
      )}`.replace(/[\W_]+/g, '_');

      return {
        label: sub.name ? (
          <Flex gap="small">
            <Typography.Title level={5}>{sub.name}</Typography.Title>
            <Typography.Text type="secondary"> - </Typography.Text>
            <Typography.Text type="secondary">VM #{sub.id}</Typography.Text>
          </Flex>
        ) : (
          <Typography.Text type="secondary">VM #{sub.id}</Typography.Text>
        ),
        // we need id and offer name to be able to get back both info in payload
        value: JSON.stringify({
          id: sub.id,
          value: offerNameWithoutSpecialCharacters,
        }),
        toFilter: `${sub.name} ${sub.id}`,
        ...{ key: sub.id },
        ...{ 'data-testid': `contact_support-${sub.id}-option` },
      };
    }) || [];

  return data.map(
    currentTopic =>
      mainTopic === currentTopic.id && (
        <React.Fragment key={currentTopic.id}>
          {currentTopic.choices.map(children => {
            const isLoadingSubscriptions =
              isSubscriptionsFetching && children.id === Topic.SHADOW_PC_SUB;
            const options =
              children.id === Topic.SHADOW_PC_SUB
                ? subscriptionsOptions
                : children.options;

            return (
              <Form.Item
                key={children?.id}
                name={children?.id}
                rules={[
                  {
                    required: !children.optional,
                    message: t(
                      'form.select.error.default',
                      'Please select an option',
                    ),
                  },
                ]}
              >
                <Select
                  data-testid={`contact_support-${children.id}-select`}
                  options={options}
                  size="large"
                  onChange={value => handleFormChange(value, children)}
                  placeholder={children.label}
                  // next are only used for Topic.SHADOW_PC_SUB
                  loading={isLoadingSubscriptions}
                  disabled={isLoadingSubscriptions}
                  suffixIcon={
                    isLoadingSubscriptions ? (
                      <ShadowLoader size={24} />
                    ) : (
                      <DownOutlined />
                    )
                  }
                  showSearch={children.id === Topic.SHADOW_PC_SUB}
                  notFoundContent={t('global.noResults', 'No results')}
                  filterOption={(inputValue, option) => {
                    return (
                      children.id === Topic.SHADOW_PC_SUB &&
                      option!
                        .toFilter!.toUpperCase()
                        .indexOf(inputValue.toUpperCase()) !== -1
                    );
                  }}
                />
              </Form.Item>
            );
          })}
        </React.Fragment>
      ),
  );
};

export default SelectSupport;
