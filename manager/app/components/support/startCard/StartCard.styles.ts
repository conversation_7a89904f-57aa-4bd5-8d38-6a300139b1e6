import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    support__card: css`
      display: flex;
      gap: ${token.sizeSM}px;
      position: relative;
      flex-direction: column;
      border-radius: ${token.borderRadius}px;
      border: 1px solid ${token.colorBorder};
      background: ${token.colorBgContainer};
      justify-content: start;
      transition: all 0.4s;
      max-width: 100%;
      flex: 1 1 0;
      padding: ${token.sizeLG}px ${token.sizeMD}px;

      &:hover {
        border-color: blue;
        background: ${token.colorBgBase};
      }

      @media (min-width: ${token.screenXL}px) {
        max-width: 33%;
      }
    `,

    support__card__link: css`
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      opacity: 0;
    `,

    support__card__title: css`
      &.ant-typography {
        color: ${token.colorPrimary};
      }
      display: flex;
      gap: 12px;
    `,

    support__card__icon: css`
      font-size: 20px;
    `,
  };
});
