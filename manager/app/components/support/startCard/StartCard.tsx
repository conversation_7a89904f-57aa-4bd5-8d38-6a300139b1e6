import { Card, Flex, Grid, Typography } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from './StartCard.styles';
import { ListIcon, QuestionIcon } from '../../Icon';

import { useCurrentMember } from '@/app/hooks/reactQuery/member/useMember';
import { FAQ_URL, WEB_SUPPORT_URL } from '@/app/utils/constants';

const StartCard = () => {
  const { t } = useTranslation();

  const { useBreakpoint } = Grid;
  const { styles } = useStyles();

  const screens = useBreakpoint();
  const currentMemberQuery = useCurrentMember();

  return (
    <Card bordered={false}>
      <Flex vertical gap="large">
        <Typography.Title level={2}>
          {t('support.ressources.title', 'Get Started')}
        </Typography.Title>
        <Typography.Text>
          {t(
            'support.ressources.description',
            'Everything you need to know when getting started with <PERSON>. Check out these guides first to get the most from your Shadow experience.',
          )}
          <Trans
            i18nKey="support.ressources.userId"
            defaults="For information, your id is: {{uid}}"
            values={{ uid: currentMemberQuery.data?.user?.id }}
          />
        </Typography.Text>
        <Flex gap={24} vertical={screens.lg ? false : true}>
          {/* Help Center */}
          <div className={styles.support__card}>
            <a
              data-testid="help_center-link"
              className={styles.support__card__link}
              href={WEB_SUPPORT_URL}
              target="_blank"
              title={t('support.ressources.helpcenter.title', 'Help center')}
            >
              {t('support.ressources.helpcenter.title', 'Help center')}
            </a>
            <Typography.Title level={4} className={styles.support__card__title}>
              <QuestionIcon className={styles.support__card__icon} />
              {t('support.ressources.helpcenter.title', 'Help center')}
            </Typography.Title>
            <Typography.Text>
              {t(
                'support.ressources.helpcenter.description',
                'Learn how to use Shadow, fix a problem, and get answers to your questions.',
              )}
            </Typography.Text>
          </div>

          {/* FAQ */}
          <div className={styles.support__card}>
            <a
              data-testid="faq-link"
              className={styles.support__card__link}
              href={FAQ_URL}
              target="_blank"
              title={t('support.ressources.faq.title', 'FAQ')}
            >
              {t('support.ressources.faq.title', 'FAQ')}
            </a>
            <Typography.Title level={4} className={styles.support__card__title}>
              <ListIcon className={styles.support__card__icon} />
              {t('support.ressources.faq.title', 'FAQ')}
            </Typography.Title>
            <Typography.Text>
              {t(
                'support.ressources.faq.description',
                'This page provides answers to frequently asked questions.',
              )}
            </Typography.Text>
          </div>
        </Flex>
      </Flex>
    </Card>
  );
};

export default StartCard;
