import { UserRole } from 'types';

/**
 * Generates a set of rules based on the provided user role.
 * These rules define permissions and actions that can be performed by users
 * with specific roles in the system.
 *
 * @param {UserRole} role - The role of the current user.
 */
export const getRules = (role: UserRole) => {
  return {
    [UserRole.OWNER]: {
      setRole: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: true,
        [UserRole.MANAGER]: true,
        [UserRole.MEMBER]: true,
      },
      canBecome: {
        [UserRole.OWNER]: true,
        [UserRole.ADMIN]: false,
        [UserRole.MANAGER]: false,
        [UserRole.MEMBER]: false,
      },
      canDelete: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: true,
        [UserRole.MANAGER]: true,
        [UserRole.MEMBER]: true,
      },
      canSetRoles: true,
      canBeDeleted: false,
      canResendInvitation: true,
      canSearchUsers: true,
      canInviteUsers: true,
    },
    [UserRole.ADMIN]: {
      setRole: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: true,
        [UserRole.MANAGER]: true,
        [UserRole.MEMBER]: true,
      },
      canBecome: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: true,
        [UserRole.MANAGER]: role === UserRole.OWNER, // only owner can promote
        [UserRole.MEMBER]: role === UserRole.OWNER, // only owner can promote
      },
      canDelete: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: false,
        [UserRole.MANAGER]: false,
        [UserRole.MEMBER]: true,
      },
      canSetRoles: true,
      canBeDeleted: role === UserRole.OWNER, // only owner can delete
      canResendInvitation: true,
      canSearchUsers: true,
      canInviteUsers: true,
    },
    [UserRole.MANAGER]: {
      setRole: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: false,
        [UserRole.MANAGER]: true,
        [UserRole.MEMBER]: true,
      },
      canBecome: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: true,
        [UserRole.MANAGER]: true,
        [UserRole.MEMBER]: role === UserRole.OWNER || role === UserRole.ADMIN, // only owner or admin can promote
      },
      canDelete: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: false,
        [UserRole.MANAGER]: false,
        [UserRole.MEMBER]: true,
      },
      canSetRoles: true,
      canBeDeleted: role === UserRole.OWNER || role === UserRole.ADMIN, // only owner or admin can delete
      canResendInvitation: true,
      canSearchUsers: true,
      canInviteUsers: true,
    },
    [UserRole.MEMBER]: {
      setRole: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: false,
        [UserRole.MANAGER]: false,
        [UserRole.MEMBER]: false,
      },
      canBecome: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: true,
        [UserRole.MANAGER]: true,
        [UserRole.MEMBER]: true,
      },
      canDelete: {
        [UserRole.OWNER]: false,
        [UserRole.ADMIN]: false,
        [UserRole.MANAGER]: false,
        [UserRole.MEMBER]: false,
      },
      canSetRoles: false,
      canBeDeleted: true,
      canResendInvitation: false,
      canSearchUsers: false,
      canInviteUsers: false,
    },
  };
};

/**
 * Generates navigation rules based on the provided user role.
 * These rules define which sections of the application are accessible
 * to users with specific roles.
 *
 */
export const getNavigationRules = () => {
  return {
    [UserRole.OWNER]: {
      useNavigation: {
        shadowPC: true,
        shadowPCUsers: true,
        shadowDrive: true,
        account: true,
        accountSecurity: true,
        billing: true,
        billingDetails: true,
        billingMethod: true,
        billingInvoices: true,
        gameStore: false,
        support: true,
        downloads: true,
      },
      userDrawer: {
        drawer: true,
        footer: true,
        tagManager: true,
      },
    },
    [UserRole.ADMIN]: {
      useNavigation: {
        shadowPC: true,
        shadowPCUsers: true,
        shadowDrive: true,
        account: false,
        accountSecurity: false,
        billing: true,
        billingDetails: true,
        billingMethod: true,
        billingInvoices: true,
        gameStore: false,
        support: false,
        downloads: true,
      },
      userDrawer: {
        drawer: true,
        footer: true,
        tagManager: false,
      },
    },
    [UserRole.MANAGER]: {
      useNavigation: {
        shadowPC: true,
        shadowPCUsers: true,
        shadowDrive: false,
        account: true,
        accountSecurity: true,
        billing: false,
        billingDetails: false,
        billingMethod: false,
        billingInvoices: false,
        gameStore: false,
        support: true,
        downloads: true,
      },
      userDrawer: {
        drawer: true,
        footer: true,
        tagManager: true,
      },
    },
    [UserRole.MEMBER]: {
      useNavigation: {
        shadowPC: true,
        shadowPCUsers: false,
        shadowDrive: false,
        account: false,
        accountSecurity: false,
        billing: false,
        billingDetails: false,
        billingMethod: false,
        billingInvoices: false,
        gameStore: false,
        support: false,
        downloads: true,
      },
      userDrawer: {
        drawer: false,
        footer: false,
        tagManager: false,
      },
    },
  };
};

export const getVmRules = () => {
  return {
    [UserRole.OWNER]: {
      vmDrawer: {
        drawer: true,
        footer: true,
        title: true,
      },
      vmConfiguration: {
        VmConfigurationPlan: true,
        VmConfigurationStorage: true,
        VmConfigurationDatacenter: true,
        VmConfigurationAlwaysOn: true,
        VmConfigurationPeriodicity: true,
      },
      vmTagManager: true,
      vmMemberAssign: true,
      canCreateVm: true,
      canSearchVm: true,
      pushNewOffer: true,
      promoBanner: true,
      promoAlert: true,
      scheduleChange: true,
      canCloneVm: true,
      canResetVm: true,
      canDeleteVm: true,
      canReactivateVm: true,
    },
    [UserRole.ADMIN]: {
      vmDrawer: {
        drawer: true,
        footer: true,
        title: true,
      },
      vmConfiguration: {
        VmConfigurationPlan: true,
        VmConfigurationStorage: true,
        VmConfigurationDatacenter: true,
        VmConfigurationAlwaysOn: true,
        VmConfigurationPeriodicity: true,
      },
      vmTagManager: true,
      vmMemberAssign: true,
      canCreateVm: true,
      canSearchVm: true,
      pushNewOffer: true,
      promoBanner: true,
      promoAlert: true,
      scheduleChange: true,
      canCloneVm: false,
      canResetVm: true,
      canDeleteVm: true,
      canReactivateVm: true,
    },
    [UserRole.MANAGER]: {
      vmDrawer: {
        drawer: true,
        footer: true,
        title: false,
      },
      vmConfiguration: {
        VmConfigurationPlan: false,
        VmConfigurationStorage: false,
        VmConfigurationDatacenter: false,
        VmConfigurationAlwaysOn: false,
        VmConfigurationPeriodicity: false,
      },
      vmTagManager: false,
      vmMemberAssign: true,
      canCreateVm: false,
      canSearchVm: true,
      pushNewOffer: false,
      promoBanner: false,
      promoAlert: false,
      scheduleChange: false,
      canCloneVm: false,
      canResetVm: true,
      canDeleteVm: false,
      canReactivateVm: false,
    },
    [UserRole.MEMBER]: {
      vmDrawer: {
        drawer: true,
        footer: false,
        title: false,
      },
      vmConfiguration: {
        VmConfigurationPlan: false,
        VmConfigurationStorage: false,
        VmConfigurationDatacenter: false,
        VmConfigurationAlwaysOn: false,
        VmConfigurationPeriodicity: false,
      },
      vmTagManager: false,
      vmMemberAssign: false,
      canCreateVm: false,
      canSearchVm: false,
      pushNewOffer: false,
      promoBanner: false,
      promoAlert: false,
      scheduleChange: false,
      canCloneVm: false,
      canResetVm: false,
      canDeleteVm: false,
      canReactivateVm: false,
    },
  };
};
