import { IVdiStatus } from 'types';
import { fetchSpectrApi } from 'utils';

import { API_URL } from '../utils/constants';

import { IReadyToPlayConfig } from '@/app/types/api';

const fetchVmStatus = async (
  authToken: string,
  subscriptionId: string,
  useVdBackend: boolean = false,
): Promise<IVdiStatus | undefined> => {
  const apiUrl = useVdBackend
    ? `${API_URL}/pu/virtual-desktop/product-manager/products/cloudpc/subscriptions/${subscriptionId}/vm/status`
    : `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/status`;

  return fetchSpectrApi(`Bearer ${authToken}`, apiUrl);
};

const setVmStop = async (
  authToken: string,
  subscriptionId: string,
  useVdBackend: boolean = false,
): Promise<string | undefined> => {
  const apiUrl = useVdBackend
    ? `${API_URL}/pu/virtual-desktop/product-manager/products/cloudpc/subscriptions/${subscriptionId}/vm/stop`
    : `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/stop`;

  return fetchSpectrApi(`Bearer ${authToken}`, apiUrl, {
    method: 'POST',
  });
};

const updateVmReadyToPlay = async (
  authToken: string,
  subscriptionId: string,
  readyToPlayConfig: IReadyToPlayConfig,
  timezone: string,
  useVdBackend: boolean = false,
): Promise<string | undefined> => {
  const apiUrl = useVdBackend
    ? `${API_URL}/pu/virtual-desktop/product-manager/products/cloudpc/subscriptions/${subscriptionId}/vm/readytoplay`
    : `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/readytoplay`;

  return fetchSpectrApi(`Bearer ${authToken}`, apiUrl, {
    method: 'POST',
    body: {
      ...readyToPlayConfig,
      timezone,
    },
  });
};

const deleteVmReadyToPlay = async (
  authToken: string,
  subscriptionId: string,
  useVdBackend: boolean = false,
): Promise<null> => {
  const apiUrl = useVdBackend
    ? `${API_URL}/pu/virtual-desktop/product-manager/products/cloudpc/subscriptions/${subscriptionId}/vm/readytoplay`
    : `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/readytoplay`;

  return fetchSpectrApi(`Bearer ${authToken}`, apiUrl, {
    method: 'DELETE',
  });
};

const setVmDiskReset = async (
  authToken: string,
  subscriptionId: string,
  useVdBackend: boolean = false,
): Promise<string | undefined> => {
  const apiUrl = useVdBackend
    ? `${API_URL}/pu/virtual-desktop/product-manager/products/cloudpc/subscriptions/${subscriptionId}/vm/disk-reset`
    : `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/disk-reset`;

  return fetchSpectrApi(`Bearer ${authToken}`, apiUrl, {
    method: 'POST',
  });
};

export {
  fetchVmStatus,
  setVmDiskReset,
  setVmStop,
  updateVmReadyToPlay,
  deleteVmReadyToPlay,
};
