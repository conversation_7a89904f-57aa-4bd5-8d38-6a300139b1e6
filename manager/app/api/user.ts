import { IPaymentMethod } from 'types';
import {
  fetchSpectrApi,
  fetchSpectrApiPublic,
  fetchSpectrBlobApi,
} from 'utils';

import { API_URL } from '../utils/constants';

import {
  IUserDetails,
  IBillingDetails,
  IRetryPaymentPayload,
  IInvoice,
  IInvoiceDownload,
  IPersonalData,
  ILoginPayload,
  LoginResponse,
  IPaymentDue,
} from '@/app/types/api';

const updateUserDetails = async (
  authToken: string,
  userDetails: Partial<IUserDetails>,
): Promise<string | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account`, {
    method: 'PUT',
    body: {
      ...userDetails,
    },
  });
};

const updateCompanyDetails = async (
  authToken: string,
  companyDetails: Partial<IBillingDetails>,
): Promise<string | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/billing`, {
    method: 'PUT',
    body: {
      ...companyDetails,
    },
  });
};

const fetchBillingDetails = async (
  authToken: string,
): Promise<IBillingDetails | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/billing`);
};

const updateBillingDetails = async (
  authToken: string,
  billingDetails: IBillingDetails | Partial<IBillingDetails>,
): Promise<string | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/billing`, {
    method: 'POST',
    body: {
      ...billingDetails,
    },
  });
};

const fetchPaymentDetails = async (
  authToken: string,
): Promise<IPaymentMethod[] | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/payment`);
};

const retryPayment = async (
  authToken: string,
  payload: IRetryPaymentPayload,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/payment/retry`,
    {
      method: 'POST',
      body: payload,
    },
  );
};

const fetchPersonalData = async (
  authToken: string,
): Promise<IPersonalData | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/personal/data`,
  );
};

const fetchInvoices = async (
  authToken: string,
): Promise<IInvoice[] | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/invoices`);
};

const fetchInvoiceDownload = async (
  authToken: string,
  invoiceId: string,
): Promise<IInvoiceDownload | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/invoices/${invoiceId}/download`,
  );
};

const downloadCsvInvoice = async (
  authToken: string,
  invoiceId: string,
): Promise<Blob | undefined> => {
  return fetchSpectrBlobApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/invoices/${invoiceId}/csv`,
  );
};

const fetchPaymentDue = async (
  authToken: string,
): Promise<IPaymentDue | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/payment-due`,
  );
};

const login = (payload: ILoginPayload): Promise<LoginResponse | undefined> => {
  return fetchSpectrApiPublic<LoginResponse>(`${API_URL}/user/login`, {
    method: 'POST',
    body: payload,
  });
};

export {
  updateUserDetails,
  updateCompanyDetails,
  fetchBillingDetails,
  updateBillingDetails,
  retryPayment,
  fetchPaymentDetails,
  fetchPersonalData,
  fetchInvoices,
  downloadCsvInvoice,
  fetchInvoiceDownload,
  fetchPaymentDue,
  login,
};
