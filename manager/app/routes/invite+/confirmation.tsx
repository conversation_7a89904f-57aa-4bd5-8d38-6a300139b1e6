import { json, LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { useNavigate } from '@remix-run/react';
import { useEffect, useState } from 'react';
import { Locale } from 'types';
import { logError } from 'utils';

import { InviteErrorAlert } from '@/app/components/account/Invite/InviteErrorAlert';
import GlobalLoader from '@/app/components/ui/Loader/GlobalLoader';
import { useConfirmMemberInvite } from '@/app/hooks/reactQuery/member/useMember';
import { useAuthentication } from '@/app/hooks/reactQuery/user/useAuthentication';
import { useConfig } from '@/app/hooks/store/useConfig';
import i18nServer from '@/app/modules/i18n.server';
import { ROUTES_PATH } from '@/app/utils/constants';
import { computeMeta } from '@/app/utils/meta';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const locale = (await i18nServer.getLocale(request)) as Locale;
  const t = await i18nServer.getFixedT(locale);
  const title = t('meta.default.title', 'Shadow Manager');
  const description = t(
    'meta.default.description',
    'Manage your Shadow account profile, password, security options, product subscriptions, users and payment method',
  );

  return json({ description, locale, title });
};

export const meta: MetaFunction<typeof loader> = ({ data }: any) => {
  return computeMeta(data);
};

const InviteConfirmation = () => {
  const { inviteToken } = useConfig();
  const navigate = useNavigate();
  const confirmMemberInvite = useConfirmMemberInvite();
  const { isAuthenticated } = useAuthentication();
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (isAuthenticated) {
      const asyncFunc = async () => {
        try {
          if (inviteToken) {
            await confirmMemberInvite.mutateAsync(inviteToken);
            navigate(`${ROUTES_PATH.ACCOUNT_PERSONAL}?invite_success=true`);
          } else {
            navigate(ROUTES_PATH.VM_MANAGER);
          }
        } catch (e: any) {
          logError('confirmMemberInvite failed', e.message);
          setErrorMessage(e.message);
        }
      };
      asyncFunc();
    }
  }, [isAuthenticated]); // eslint-disable-line react-hooks/exhaustive-deps

  if (errorMessage) {
    return <InviteErrorAlert errorMessage={errorMessage} />;
  }

  return <GlobalLoader />;
};

export default InviteConfirmation;
