import { LoaderFunctionArgs, json } from '@remix-run/node';
import { MetaFunction, useSearchParams } from '@remix-run/react';
import { useEffect } from 'react';
import { Locale } from 'types';

import GlobalLoader from '@/app/components/ui/Loader/GlobalLoader';
import { useAuthentication } from '@/app/hooks/reactQuery/user/useAuthentication';
import { useConfig } from '@/app/hooks/store/useConfig';
import i18nServer from '@/app/modules/i18n.server';
import { computeMeta } from '@/app/utils/meta';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const locale = (await i18nServer.getLocale(request)) as Locale;
  const t = await i18nServer.getFixedT(locale);
  const title = t('meta.default.title', 'Shadow Manager');
  const description = t(
    'meta.default.description',
    'Manage your Shadow account profile, password, security options, product subscriptions, users and payment method',
  );

  return json({ description, locale, title });
};

export const meta: MetaFunction<typeof loader> = ({ data }: any) => {
  return computeMeta(data);
};

const Invite = () => {
  const [searchParams] = useSearchParams();
  const { signinRedirect } = useAuthentication();
  const { setInviteToken } = useConfig();

  useEffect(() => {
    const inviteToken = searchParams.get('token') || '';

    setInviteToken(inviteToken);

    void signinRedirect({
      extraQueryParams: {
        login_hint: searchParams.get('login_hint') || '',
        register: true,
        provider: searchParams.get('provider') || '',
        invite_token: inviteToken,
      },
      state: {
        invite: true,
        token: inviteToken,
      },
    });
  }, [searchParams, setInviteToken, signinRedirect]);

  return <GlobalLoader fullHeight />;
};

export default Invite;
