import { useScheduledChangesSubscription } from 'hooks';
import {
  ISubscription,
  ProductType,
  SubscriptionStatus,
  OfferPeriodicity,
  OfferPeriodUnit,
} from 'types';
import {
  findSubscriptionItemByType,
  getOffers,
  isOfferEnabledForSale,
} from 'utils';

import { useCatalog } from './useCatalog';

interface IUseCanUpdateBillingPeriodicityResponse {
  canUpdateBillingPeriodicity: boolean;
  validPeriodicities: OfferPeriodicity[];
  isLoading: boolean;
}

export const useCanUpdateBillingPeriodicity = (
  subscription: ISubscription,
): IUseCanUpdateBillingPeriodicityResponse => {
  const { data: catalog, isLoading: isCatalogLoading } = useCatalog();

  const {
    data: scheduledChangesSubscription,
    isLoading: isScheduledChangesLoading,
  } = useScheduledChangesSubscription(
    subscription?.id as string,
    subscription?.has_scheduled_changes,
  );

  const isLoading =
    isCatalogLoading ||
    (subscription?.has_scheduled_changes && isScheduledChangesLoading);

  const scheduledChangesItems = subscription?.has_scheduled_changes
    ? scheduledChangesSubscription?.items
    : [];

  const currentPlan = findSubscriptionItemByType(
    subscription,
    ProductType.PLAN,
  );

  const currentOffer = currentPlan
    ? catalog?.offers.byId[currentPlan?.id]
    : undefined;

  // Return early with loading state if any queries are still loading
  if (isLoading) {
    return {
      canUpdateBillingPeriodicity: false,
      validPeriodicities: [],
      isLoading: true,
    };
  }

  // We only want to update billing periodicity for "active" subscriptions
  if (
    !catalog ||
    !subscription ||
    subscription?.status !== SubscriptionStatus.ACTIVE ||
    !currentPlan ||
    !currentOffer
  ) {
    return {
      canUpdateBillingPeriodicity: false,
      validPeriodicities: [],
      isLoading: false,
    };
  }

  // Get all available offers for the current product
  const availableOffers = getOffers(
    catalog,
    currentOffer.product_id,
    offer =>
      offer.periodicity !== currentOffer.periodicity &&
      !offer.periodicity?.includes(OfferPeriodUnit.WEEK) && // Exclude weekly offers
      isOfferEnabledForSale({ offer }) &&
      !scheduledChangesItems?.find(item => item.id === offer.id), // Exclude offers that have a scheduled change
  );

  // Get all available periodicities for the current product
  const validPeriodicities = availableOffers.map(offer => offer.periodicity);

  const canUpdateBillingPeriodicity = validPeriodicities.length > 0;

  return {
    canUpdateBillingPeriodicity,
    validPeriodicities,
    isLoading: false,
  };
};
