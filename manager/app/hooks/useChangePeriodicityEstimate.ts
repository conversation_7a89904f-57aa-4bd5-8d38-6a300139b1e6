import { useIsMutating } from '@tanstack/react-query';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Currency, IEstimation, ISubscription, OfferPeriodicity } from 'types';
import {
  DATE_FORMAT_BY_LANGUAGE,
  computeSubscriptionItemPricesWithTargetPeriodicity,
  formatDate,
  logError,
} from 'utils';

import { useSubscriptionModificationEstimation } from '@/app/hooks/reactQuery/subscriptions/useSubscription';
import { useConfig } from '@/app/hooks/store/useConfig';
import { useCatalog } from '@/app/hooks/useCatalog';
import { usePrice } from '@/app/hooks/usePrice';

interface IChangePeriodicityEstimate {
  subscription: ISubscription | undefined;
  selectedPeriodicity: OfferPeriodicity | undefined;
}

export const useChangePeriodicityEstimate = ({
  subscription,
  selectedPeriodicity,
}: IChangePeriodicityEstimate) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();
  const { language, currency } = useConfig();

  const catalogQuery = useCatalog();
  const catalog = catalogQuery.data;

  const [estimation, setEstimation] = useState<IEstimation>();
  const [hasEstimationError, setHasEstimationError] = useState(false);

  const getSubscriptionModificationEstimation =
    useSubscriptionModificationEstimation(subscription?.id ?? '');

  const isEstimationLoading = !!useIsMutating({
    mutationKey: ['getSubscriptionModificationEstimation'],
  });

  // Fetches the estimate when selectedPeriodicity changes in the select form
  useEffect(() => {
    if (!selectedPeriodicity) {
      return;
    }

    const getEstimationData = async () => {
      setHasEstimationError(false);

      try {
        const itemPricesPayload =
          computeSubscriptionItemPricesWithTargetPeriodicity(
            catalog,
            subscription,
            selectedPeriodicity,
          );

        await getSubscriptionModificationEstimation.mutateAsync(
          {
            item_prices: itemPricesPayload,
          },
          {
            onSuccess: (estimationData: IEstimation | undefined) => {
              setEstimation(estimationData);
            },
          },
        );

        setHasEstimationError(false);
      } catch (error) {
        setEstimation(undefined);
        setHasEstimationError(true);

        logError(
          'Error during estimate API call for periodicity change : ',
          error,
        );

        message.error(
          t(
            'subscription.changeVmPeriodicity.estimateApiError',
            'An error occurred while getting an estimation for updating your subscription, please try again later or contact support.',
          ),
        );
      }
    };

    getEstimationData();

    // Do not add missing dependencie here, this causes infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPeriodicity]);

  const newPrice =
    estimation?.next_invoice_estimate?.amount_due ??
    estimation?.future_estimate?.custom_next_invoice_estimate;
  const currencyCode =
    estimation?.next_invoice_estimate?.currency_code ??
    estimation?.future_estimate?.next_invoice_estimate?.currency_code ??
    currency;

  const formattedNewPrice = newPrice
    ? formatPrice(newPrice, currencyCode as Currency)
    : '';

  if (
    !!estimation &&
    !isEstimationLoading &&
    !newPrice &&
    selectedPeriodicity
  ) {
    logError(
      `No new price found in estimation for subscription ${subscription?.id} with target periodicity ${selectedPeriodicity}`,
    );
  }

  const formattedSubscriptionNextBillingDate = subscription?.next_billing_at
    ? formatDate(
        subscription.next_billing_at * 1000,
        DATE_FORMAT_BY_LANGUAGE[language],
      )
    : '';

  return {
    isEstimationLoading,
    formattedNewPrice,
    formattedSubscriptionNextBillingDate,
    hasEstimationError,
  };
};
