{"extends": "../config/tsconfig.json", "include": ["**/*.ts", "**/*.tsx", ".react-router/types/**/*"], "exclude": ["node_modules"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["@react-router/node", "vite/client"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@": [""], "@/*": ["*"], "~/*": ["./app/*"]}, "rootDirs": [".", "./.react-router/types"]}}