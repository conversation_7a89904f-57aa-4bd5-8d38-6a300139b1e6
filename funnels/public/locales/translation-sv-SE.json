{"access-fees": {"modal": {"content": "", "contentWithFee": "", "title": ""}}, "b2b": {"billingDetails": {"confirmation": {"button": "", "contact": "", "description": "", "title": ""}}, "cart": {"description": "", "vmOnDemandDescription": ""}, "catalog": {"back": {"link": ""}, "datacenter": {"description": "", "link": "", "title": ""}, "product": {"description": "", "title": ""}}, "configuration": {"addons": {"title": ""}, "back": {"link": ""}, "detailsBack": {"link": ""}, "paymentError": {"description": "", "title": ""}, "title": ""}, "home": {"business": {"contentDescription": "", "menuDescription": "", "title": ""}, "button": "", "contact": {"button": ""}, "echoSession": {"button": "", "contentDescription": "", "menuDescription": "", "title": ""}, "educational": {"contentDescription": "", "menuDescription": "", "title": ""}, "largerCompanies": {"chipLabel": "", "contentDescription": "", "menuDescription": "", "title": ""}}, "optimalDatacenterModal": {"confirmLocationButton": "", "recommendedLabel": "", "selectDatacenterButton": "", "subtitle": "", "title": ""}, "payment": {"title": ""}, "unavailableCountry": ""}, "billingDetails": {"form": {"address": {"error": "", "label": ""}, "billingAddress": {"errorCountry": {"button": "", "description": "", "label": "", "support": ""}, "title": ""}, "birthday": {"error": "", "invalid": "", "label": ""}, "cguCheck": {"error": "", "label": ""}, "city": {"error": "", "label": ""}, "country": "", "firstName": {"error": "", "label": ""}, "lastName": {"error": "", "label": ""}, "organization": {"title": ""}, "personalInformation": {"title": ""}, "phone": {"label": ""}, "withdrawal": {"label": ""}, "zipcode": {"error": "", "label": "", "validityError": ""}, "company": {"label": ""}, "isCompany": {"label": "", "subLabel": ""}}, "funnelInconsistency": {"customer": {"b2b": {"cta": "", "description": "", "title": ""}, "b2c": {"cta": "", "description": "", "title": ""}}}}, "businessManager": {"vmOnDemand": {"buttons": {"apiDocumentation": {"label": ""}, "priceTable": {"label": ""}}}}, "cart": {"access-fees": {"text": ""}, "accessFee": "", "addCoupon": {"error": "", "invalid": "", "placeholder": "", "title": "", "needed": ""}, "buyNow": "", "cancel": {"title": ""}, "cancelAnytime": "", "continue": "", "coupon": {"description": {"forever": "", "limitedPeriod": "", "oneTime": ""}, "remove": {"label": ""}, "title": "", "previewMessage": ""}, "dedicatedSupport": "", "deliveryEstimationTime": {"instant": "", "label": ""}, "edit": {"title": ""}, "header": {"pricing": {"one-time": ""}, "family": {"cloudpc": "", "shadow-drive": "", "vm-on-demand": ""}}, "login": "", "makers": {"disclaimer": ""}, "price": {"free": ""}, "proceed": "", "securedPayment": "", "summary": {"product": {"name": {"cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "cloudpc-extrastorage-256go2019-c1": "", "cloudpc-extrastorage-256go2019-free-c1": "", "cloudpc-old-b2c-infinite2021-c1": "", "cloudpc-old-b2c-ultra2021-c1": "", "cloudpc-os-byol-win10enterprise": "", "cloudpc-os-byol-winserver2019": "", "cloudpc-os-win10enterprise": "", "cloudpc-os-winserver2019": "", "cloudpc_activation_charge": "", "shadow-drive-b2c-free": "", "shadow-drive-b2c-premium": "", "vmondemand_activation_charge": "", "vmondemand-b2b-metered-apiaccess2023": "", "cloudpc-b2c-discovery2024": "", "shadow-drive-b2c-premium_b": "", "shadow-drive-group_2To": "", "cloudpc-b2c-newboost2025": "", "cloudpc_update_charge": "", "cloudpc-24x7access-2023": "", "cloudpc-b2c-standard2025-summer": "", "cloudpc-b2p-newstandard2025": ""}}, "changeDatacenter": ""}, "summaryExtraStorage": {"value": ""}, "summarySubLine": {"label": ""}, "summaryZipcode": {"label": ""}, "total": {"creditsApplied": "", "explanationText": "", "firstPayment": "", "priceExcludingTaxes": "", "priceIncludingTaxes": "", "pricingFromDate": "", "pricingThen": "", "refund": {"label": ""}, "thenDiscount": "", "tooltip": "", "vat": {"withoutVAT": ""}, "discount": "", "unlogged": {"warning": ""}}, "update": "", "upgrade": "", "b2b": {"addCoupon": {"title": ""}}, "summaryDatacenter": {"label": ""}, "location": "", "auth": {"login": "", "register": "", "continue": ""}, "buy": "", "estimate": {"total": {"label": "", "labelAccessory": ""}}, "changeDatacenterLabel": "", "datacenterLabel": ""}, "catalog": {"addon": {"additionalStorage": "", "os": {"choice": {"customerOwnedLicense": {"checkbox": "", "label": "", "tooltip": "", "warning": ""}, "shadowOwnedLicense": {"checkbox": "", "label": "", "tooltip": ""}, "windows": {"home": {"label": ""}}}, "description": {"customerOwnedLicenseOnly": "", "default": ""}, "select": {"placeholder": ""}, "title": ""}, "period": {"monthly": ""}, "periodUnit": "", "storageSliderValue": ""}, "advancedSettings": {"configuration": {"auto": {"label": ""}, "information": "", "manual": {"label": ""}, "title": ""}, "keyboardLanguage": {"title": ""}, "language": {"title": ""}}, "changePlan": {"makers": {"content": "", "label": ""}, "standard": {"content": "", "label": ""}, "title": "", "back": {"link": ""}, "shadow-drive": {"content": "", "label": ""}, "titleDrive": "", "error": {"description": "", "title": ""}, "noOffers": {"description": "", "title": ""}}, "coupon": {"invalid": ""}, "degradedService": {"alert": {"description": "", "title": ""}}, "info": {"unavailable": {"body": "", "button": {"label": ""}, "title": ""}}, "item": {"activationFees": "", "advancedSettings": {"title": ""}, "free": "", "noActivationFees": "", "outOfStock": "", "restockWithDate": "", "startingPrice": "", "technicalSpecs": "", "isPrimaryStorageClonable": ""}, "pricing": {"month": ""}, "product": {"edit": {"label": ""}, "name": {"unknown": "", "cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": "", "cloudpc-b2b-premium2022": "", "cloudpc-b2b-standard2022": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2022-c12": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-standard2021-c12": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "cloudpc-extrastorage-256go2019-c1": "", "cloudpc-old-b2c-boost2019-c1": "", "cloudpc-old-b2c-boost2019-c12": "", "cloudpc-old-b2c-infinite2019-c1": "", "cloudpc-old-b2c-infinite2019-c12": "", "cloudpc-old-b2c-infinite2021-c1": "", "cloudpc-old-b2c-ultra2019-c1": "", "cloudpc-old-b2c-ultra2019-c12": "", "cloudpc-old-b2c-ultra2021-c1": "", "cloudpc-os-byol-win10enterprise": "", "cloudpc-os-byol-winserver2019": "", "cloudpc-os-win10enterprise": "", "cloudpc-os-winserver2019": "", "shadow-drive-b2c-free": "", "shadow-drive-b2c-premium": "", "vmondemand-b2b-metered-apiaccess2023": "", "cloudpc-b2c-discovery2024": "", "shadow-drive-b2c-premium_b": "", "shadow-drive-group_2To": "", "cloudpc-os-win10home": "", "cloudpc-b2c-newboost2025": "", "cloudpc-24x7access-2023": "", "cloudpc-b2p-newstandard2025 ": "", "cloudpc-b2c-standard2025-summer": "", "cloudpc-b2p-newstandard2025": ""}, "chip": {"cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2022-c12": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2c-power2023": "", "vmondemand-b2b-metered-apiaccess2023": ""}, "scheduledChangesName": {"change_plan": "", "default": "", "reduce_extra_storage": "", "remove_power_upgrade": "", "update_ram": ""}, "description": {"cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "vmondemand-b2b-metered-apiaccess2023": ""}, "technicalSpecs": {"cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": ""}, "periodicity": {"month": "", "year": ""}, "bareName": {"unknown": "", "cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": "", "cloudpc-b2b-premium2022": "", "cloudpc-b2b-standard2022": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2022-c12": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-standard2021-c12": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "cloudpc-old-b2c-boost2019-c1": "", "cloudpc-old-b2c-boost2019-c12": "", "cloudpc-old-b2c-infinite2019-c1": "", "cloudpc-old-b2c-infinite2019-c12": "", "cloudpc-old-b2c-infinite2021-c1": "", "cloudpc-old-b2c-ultra2019-c1": "", "cloudpc-old-b2c-ultra2019-c12": "", "cloudpc-old-b2c-ultra2021-c1": "", "cloudpc-os-byol-win10enterprise": "", "cloudpc-os-byol-winserver2019": "", "cloudpc-os-win10enterprise": "", "cloudpc-os-winserver2019": "", "shadow-drive-b2c-free": "", "shadow-drive-b2c-premium": "", "vmondemand-b2b-metered-apiaccess2023": "", "cloudpc-extrastorage-256go2019-c1": "", "cloudpc-b2c-discovery2024": "", "shadow-drive-b2c-premium_b": "", "shadow-drive-group_2To": "", "cloudpc-24x7access-2023": "", "cloudpc-b2p-newstandard2025": "", "cloudpc-b2c-standard2025-summer": "", "cloudpc-b2c-newboost2025": ""}}, "productAddons": {"back": {"link": ""}}, "productsList": {"back": {"link": ""}}, "shadow": {"alreadySubscribed": {"description": "", "label": ""}}, "zipcode": {"button": {"label": ""}, "description": "", "error": {"invalidLabel": "", "invalidTitle": "", "notAvailableLabel": "", "notAvailableTitle": ""}, "label": "", "placeholder": ""}, "technicalSpecs": {"cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "cloudpc-old-b2c-ultra2021-c1": "", "cloudpc-old-b2c-infinite2021-c1": "", "cloudpc-b2c-discovery2024": "", "cloudpc-b2c-newboost2025": "", "cloudpc-b2c-standard2025-summer": "", "cloudpc-b2p-newstandard2025": ""}, "family": {"cloudpc": {"name": "", "description": "", "tagline": "", "bullets": {"bullet-1": "", "bullet-2": "", "bullet-3": "", "bullet-4": ""}}, "shadow-drive": {"name": "", "description": "", "tagline": "", "bullets": {"bullet-1": "", "bullet-2": "", "bullet-3": "", "bullet-4": ""}}}, "includedFreeProduct": {"shadow-drive-b2c-free": {"cartLabel": "", "included": "", "label": ""}}, "vm": {"duplicate": "", "duplicateButton": "", "installButton": "", "tooltipCleanInstall": "", "tooltipDuplicate": "", "quantity": {"subtitle": "", "title": ""}}, "driveGroupUpdate": {"back": {"link": ""}, "error": {"description": "", "title": ""}, "maxUpgradeWarning": {"description": "", "title": ""}}}, "confirmation": {"customerSpace": {"link": ""}, "discord": {"link": ""}, "faq": {"link": "", "title": ""}, "helpCenter": {"link": ""}, "shadowDrive": {"banner": {"cta": "", "description": "", "title": ""}, "description": "", "faq": {"a1": "", "a2": "", "a3": "", "q1": "", "q2": "", "q3": ""}, "keypoints": {"k1": "", "k2": "", "k3": ""}, "title": ""}, "shadowPc": {"banner": {"cta": "", "description": "", "label": "", "title": ""}, "description": "", "faq": {"a1": "", "a2": "", "a3": "", "q1": "", "q2": "", "q3": ""}, "keypoints": {"k1": "", "k2": "", "k3": "", "k4": ""}, "title": ""}, "cartSummary": {"paymentInfos": {"invoice": "", "questionsOrder": "", "trust": ""}, "paymentMethod": "", "title": "", "total": {"title": "", "withReductions": ""}, "discount": ""}, "setupSteps": {"bullet1": {"cloudpc": "", "drive": ""}, "bullet2": {"cloudpc": "", "drive": ""}, "bullet3": {"cloudpc": "", "drive": ""}, "bullet4": {"drive": ""}, "title": {"cloudpc": "", "drive": ""}}, "thanking": {"confirmationEmail": "", "customerSpace": {"link": "", "title": ""}, "deliverOrder": {"time": "", "label": ""}, "discord": {"link": "", "title": ""}, "helpCenter": {"link": "", "title": ""}, "title": ""}}, "consentBanner": {"title": ""}, "driveFree": {"info": {"alreadySubscribed": {"title": ""}, "unavailable": {"body": "", "title": ""}}}, "errors": {"api": {"addPaymentMethod": "", "addPaypalAccount": "", "billingDetails": "", "cardPayment": "", "catalog": "", "checkPowerEarlyAccessCouponValidity": "", "datacenters": "", "estimate": "", "payment": "", "removePaymentMethod": "", "tooManyRequest": "", "updatePrimaryPaymentMethod": "", "updateSubscription": "", "zipcodeVerification": "", "addApplePayAccount": "", "addGooglePayAccount": ""}, "updateSubscription": {"invalidAccessCode": "", "missingOffer": ""}}, "footer": {"cgu": "", "companyName": "", "cookies": "", "legal": "", "privacy": ""}, "header": {"business": {"buttonLabel": ""}, "individual": {"buttonLabel": ""}, "link": {"logout": ""}, "login": {"welcome": {"label": ""}}, "logoAlt": "", "logoBusinessAlt": "", "selectLanguage": {"title": ""}, "selectMarket": {"title": ""}}, "payment": {"addCreditCard": {"button": "", "cardNumber": {"error": "", "label": ""}, "cvc": {"error": "", "label": "", "tooltip": ""}, "expiration": {"error": "", "label": ""}, "expiry": "", "name": {"error": "", "label": "", "placeholder": ""}, "title": ""}, "addPaypalAccount": {"title": ""}, "bank": {"3DSecure": ""}, "billingDetails": {"edit": ""}, "creditCard": {"cguCheck": {"error": "", "label": ""}, "label": "", "withdrawal": {"error": "", "label": ""}}, "description": "", "paypal": {"cguCheck": {"error": "", "label": ""}, "connectedUser": "", "label": "", "paymentSelectionLabel": "", "withdrawal": {"error": "", "label": ""}}, "selectCreditCard": {"select": {"label": "", "expiration": "", "expired": ""}, "title": ""}, "title": "", "unsupportedPlans": {"dataRazWarning": ""}, "updatePaymentMethod": {"card": "", "direct_debit": "", "paypal_express_checkout": "", "remove": {"cancel": "", "label": "", "title": {"card": "", "direct_debit": "", "paypal_express_checkout": "", "apple_pay": "", "google_pay": ""}}, "title": "", "update": {"label": ""}, "apple_pay": "", "google_pay": "", "addPaymentMethods": {"title": ""}, "selectPaymentMethods": {"title": ""}}, "newCreditCard": {"title": ""}, "applePay": {"connectedUser": "", "label": "", "paymentSelectionLabel": ""}, "googlepay": {"connectedUser": ""}, "googlePay": {"label": "", "paymentSelectionLabel": "", "connectedUser": ""}, "linked": {"cguCheck": {"error": "", "label": ""}, "withdrawal": {"error": "", "label": ""}}, "addApplePayAccount": {"title": ""}, "addGooglePayAccount": {"title": ""}}, "periodicity": {"noun": {"month_one": "", "month_other": "", "day_one": "", "day_other": "", "week_one": "", "week_other": "", "year_one": "", "year_other": ""}, "adjective": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}, "plan": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}}, "periodicitySwitch": {"label": "", "percentageChip": "", "recommendation": {"info": {"activationFees": "", "percent": ""}, "link": {"activationFees": "", "percent": ""}}, "title": ""}, "seo": {"b2b": {"billing": {"title": ""}, "configuration": {"title": ""}, "home": {"title": ""}, "payment": {"title": ""}}, "billing": {"title": ""}, "changePlan": {"title": ""}, "confirmation": {"title": ""}, "home": {"title": ""}, "payment": {"title": ""}, "alwaysOn": {"title": ""}, "retentionOfferUpgrade": {"title": ""}, "cloudpcConfigurator": {"title": ""}}, "storageUpdate": {"outOfStockMessage": "", "scheduledChangesMessage": "", "storage": {"extra": "", "included": ""}, "subtitle": "", "title": ""}, "subscription": {"resetDisk": {"alert": {"description": "", "scheduledChangesDescription": "", "title": "", "diskToReset": {"bothDisks": "", "cDisk": "", "dDisk": ""}}, "subtitle": "", "title": ""}, "plan": {"storage": {"clone": ""}}, "details": {"name": {"cloudpc-b2p-newstandard2025-EUR-Every": ""}}}, "topBanner": {"label": "", "logo_url": "", "sublabel": ""}, "price": {"subLine": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}}, "product": {"family": {"cloudpc": "", "shadow-drive": ""}}, "updateZipcode": {"confirmLocationButton": "", "noOffer": "", "title": ""}, "purchaseOverview": {"addStorageModal": {"addAction": "", "noStorageMenuItem": "", "subtitle": "", "title": ""}, "overview": {"drive": {"item1": "", "item2": "", "item3": "", "item4": ""}, "product": {"cloudpc-b2c-standard2023": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2c-power2023": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2c-A4000power-2022": {"cpu": "", "bandwidth": "", "gpu": "", "license": ""}, "cloudpc-b2p-nogpu-plan-a-2023": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2p-plan-a-2022": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2p-plan-c-2022": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2p-plan-d-2022": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2p-plan-e-2022": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2c-discovery2024": {"gpu": "", "cpu": "", "bandwidth": "", "license": ""}, "cloudpc-b2c-newboost2025": {"cpu": "", "bandwidth": "", "gpu": ""}, "cloudpc-b2p-newstandard2025": {"bandwidth ": "", "gpu": "", "cpu": ""}, "cloudpc-b2c-standard2025-summer": {"cpu": "", "bandwidth": "", "gpu": ""}}, "cloudpc": {"addExtraStorage": "", "editKeyboard": "", "extraStorage": "", "primaryStorage": "", "ram": "", "editLicense": "", "editLicenseTooltip": ""}, "discountPeriodicity": ""}, "paymentMethods": {"title": "", "step": {"title": ""}}, "outOfStock": "", "addPaymentMethods": {"title": ""}, "savedPaymentMethods": {"title": ""}, "editOsModal": {"byol": {"checkbox": {"label": "", "error": ""}, "title": "", "alert": ""}, "chooseAction": "", "shadowLicense": {"alert": ""}, "title": "", "product": {"cloudpc-os-byol-win10enterprise": {"alertMessage": ""}, "cloudpc-os-win10home": {"alertMessage": ""}, "cloudpc-os-win10enterprise": {"alertMessage": ""}}, "select": {"menuItem": {"period": "", "price": ""}}}}, "editDatacenterModal": {"subtitle": "", "title": "", "confirmAction": ""}, "editKeyboardAndLanguageModal": {"subtitle": "", "title": ""}, "getLocalizationModal": {"noDatacenterFound": "", "submit": "", "subtitle": "", "title": ""}, "paymentError": {"add_card_error": "", "card_declined": "", "expired_card": "", "incorrect_cvc": "", "processing_error": "", "authentication_failure": ""}, "cloudpcConfigurator": {"datacentersCard": {"title": "", "zone": {"eu": "", "na": ""}, "changeDatacenterButton": "", "tooltip": ""}}, "configurator": {"degradedServiceNotice": {"content": "", "title": ""}}, "unavailableOffer": {"alert": {"content": "", "title": ""}}, "datacenterRegion": {"name": {"ca-east": "", "de-central": "", "eu-central": "", "eu-west": "", "frcrx01": "", "insider": "", "us-east": "", "us-south": "", "us-west": ""}}, "datacenterStatus": {"info": {"best": "", "good": "", "usable": "", "unavailable": ""}, "label": {"best": "", "good": "", "usable": "", "unavailable": ""}}, "billingDetailsInfo": {"editButtonLabel": ""}, "offerCard": {"changeButtonLabel": "", "priceLabel": ""}, "driveGroupUpdate": {"description": "", "title": "", "addon": {"label": ""}}, "purchaseOverviez": {"editOsModal": {"byol": {"checkbox": {"error": ""}}}}, "{price}": "", "alwaysOn": {"activate": {"title": ""}}, "changePlan": {"updateDiscount": {"text": ""}}, "form": {"billingDetails": {"title": ""}, "address": {"error": {"required": ""}, "label": ""}, "city": {"error": {"required": ""}, "label": ""}, "companyName": {"error": {"maxLength": "", "minLength": "", "required": ""}, "label": ""}, "country": "", "firstName": {"error": {"required": ""}, "label": ""}, "lastName": {"error": {"required": ""}, "label": ""}, "phone": {"label": ""}, "vatNumber": {"error": {"invalid": "", "maxLength": "", "minLength": "", "required": "", "wrongFormat": ""}, "label": ""}, "word": {"error": {"required": "", "mismatch": ""}, "info": "", "label": ""}, "zipcode": {"error": {"required": "", "maxLength": "", "minLength": ""}, "label": ""}, "language": {"label": ""}}, "global": {"cancel": "", "continue": "", "delete": "", "ok": "", "storage": "", "loading": ""}, "notFound": {"description": "", "title": ""}}