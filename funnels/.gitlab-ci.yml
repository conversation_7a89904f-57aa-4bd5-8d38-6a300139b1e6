.set_tags:
  tags:
    - blade
    - docker

.funnels_build_template:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:v1.9.1-debug
    entrypoint: ['']
  script:
    - mkdir -p /kaniko/dot_envs
    - cat "$K8S_FUNNELS_ENV_FILE" > "$CI_PROJECT_DIR/funnels/.env.local"
    - echo -e "\nNEXT_PUBLIC_FEATURE_MESSAGE=${FEATURE_MESSAGE}" >> "$CI_PROJECT_DIR/funnels/.env.local"
    - >-
      echo
      "{\"auths\":{\"registry.gitlab.com\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}"
      > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --context ${CI_PROJECT_DIR} --dockerfile ${CI_PROJECT_DIR}/funnels/Dockerfile
      --destination ${CI_REGISTRY_IMAGE_PATH}:${DEPLOYMENT_IMAGE_TAG}
      --build-arg PROJECT_ID=${CI_PROJECT_ID}
      --build-arg TOKEN=${CI_JOB_TOKEN}
      --build-arg USERNAME=gitlab-ci-token
    - echo "DOCKER_IMAGE=${CI_REGISTRY_IMAGE_PATH}" >> build-funnels.env
    - echo "DOCKER_TAG=${DEPLOYMENT_IMAGE_TAG}" >> build-funnels.env

funnels_test:
  extends: .set_tags
  stage: prebuild
  image:
    name: node:20-alpine3.18
    entrypoint: ['']
  script:
    - apk update && apk add --no-cache libc6-compat python3 make g++
    - npm config set //gitlab.com/api/v4/projects/********/packages/npm/:_authToken=${CI_JOB_TOKEN}
    - yarn install --frozen-lockfile --non-interactive
    - yarn workspace funnels test
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" || $CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?(-preprod)?$/
      changes:
        - account/**/*
        - shop/**/*
        - funnels/**/*
        - utils/**/*

funnels_build_preview:
  extends:
    - .funnels_build_template
    - .set_tags
  variables:
    K8S_FUNNELS_ENV_FILE: $K8S_DEV_FUNNELS_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/funnels"
    - export DEPLOYMENT_IMAGE_TAG=`echo ${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME} | tr '/' '-'`
    - >
      export FEATURE_MESSAGE="{ \"feature\": \"${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}\", \"commit\": \"${CI_COMMIT_SHORT_SHA}\", \"build\": \"${CI_PIPELINE_ID}\" }"
  needs:
    - job: funnels_test
    - job: build_preview
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - funnels/**/*
        - utils/**/*

funnels_build_dev:
  extends:
    - .funnels_build_template
    - .set_tags
  variables:
    K8S_FUNNELS_ENV_FILE: $K8S_DEV_FUNNELS_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/funnels"
    - export DEPLOYMENT_IMAGE_TAG=latest
  needs:
    - job: funnels_test
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
      changes:
        - funnels/**/*
        - utils/**/*

funnels_build_preprod:
  extends:
    - .funnels_build_template
    - .set_tags
  variables:
    K8S_FUNNELS_ENV_FILE: $K8S_PREPROD_FUNNELS_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/funnels/preprod"
    - export DEPLOYMENT_IMAGE_TAG=${CI_COMMIT_TAG}
  needs:
    - job: funnels_test
  artifacts:
    reports:
      dotenv: build-funnels.env
  rules:
    - if: $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?-preprod$/

funnels_deploy_preprod:
  extends: .gitops_deploy
  script:
    - >-
      cd cbp-frontend/overlays/preprod/ ;
      kustomize edit set image funnels-img=${DOCKER_IMAGE}:${DOCKER_TAG} ;
      git commit -m "set funnels image tag to ${DOCKER_TAG} in bases" kustomization.yml ;
      git push origin main
  needs:
    - job: funnels_build_preprod
      artifacts: true
  rules:
    - if: $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?-preprod$/

funnels_build_prod:
  extends:
    - .funnels_build_template
    - .set_tags
  variables:
    K8S_FUNNELS_ENV_FILE: $K8S_PROD_FUNNELS_ENV
  before_script:
    - export CI_REGISTRY_IMAGE_PATH="${CI_REGISTRY_IMAGE}/funnels/prod"
    - export DEPLOYMENT_IMAGE_TAG=${CI_COMMIT_TAG}
  needs:
    - job: funnels_test
  rules:
    - if: $CI_COMMIT_TAG =~ /^v?[0-9]+[.][0-9]+([.][0-9]+)?$/
