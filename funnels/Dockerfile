FROM node:20-alpine3.18 AS runtime-image

FROM runtime-image as deps

ARG TOKEN

# Install necessary packages
RUN apk update && apk add --no-cache libc6-compat python3 make g++

WORKDIR /app

# Set the NPM token using npm config set
ENV NPM_TOKEN=${TOKEN}
COPY .npmrc .
RUN sed -i '$ s/#//' .npmrc

# Copy and install dependencies
COPY package.json yarn.lock ./
COPY ./funnels ./funnels
COPY ./common-components ./common-components
COPY ./utils ./utils
COPY ./types ./types
COPY ./hooks ./hooks
COPY ./mocks ./mocks
RUN yarn install --frozen-lockfile --non-interactive

# Rebuild the source code only when needed
FROM runtime-image AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/funnels/node_modules ./funnels/node_modules
COPY --from=deps /app/common-components/node_modules ./common-components/node_modules
COPY --from=deps /app/utils/node_modules ./utils/node_modules
COPY --from=deps /app/types/node_modules ./types/node_modules
COPY --from=deps /app/hooks/node_modules ./hooks/node_modules
COPY --from=deps /app/mocks/node_modules ./mocks/node_modules
COPY . .
ENV NEXT_TELEMETRY_DISABLED 1
RUN yarn workspace funnels build

# Production image, copy all the files and run next
FROM runtime-image AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 vite

COPY --from=builder --chown=vite:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=vite:nodejs /app/funnels/public ./funnels/public
COPY --from=builder --chown=vite:nodejs /app/funnels/node_modules ./funnels/node_modules
COPY --from=builder --chown=vite:nodejs /app/funnels/package.json ./package.json
COPY --from=builder --chown=vite:nodejs /app/funnels/build ./build
COPY --from=builder --chown=vite:nodejs /app/funnels/public ./public

USER vite

EXPOSE 5173

ENV PORT 5173

CMD ["yarn", "start"]

