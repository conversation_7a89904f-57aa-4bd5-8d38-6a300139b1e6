import { JwtPayload } from 'jwt-decode';

declare module 'jwt-decode' {
  export interface JwtPayloadFederation extends JwtPayload {
    federation: {
      provider: string;
      subject: string;
      type: string;
    };
  }
}

declare let google: any;
declare global {
  interface Window {
    LC_API: any;
    LiveChatWidget: any;
    dataLayer: any;
    opera: any;
    __localeId__: DatesLocale;
    Didomi: any;
    didomiOnReady: any;
    __shadow_auth__?: {
      removeUser: () => void;
    };
    ApplePaySession: any;
    Chargebee: any;
  }
}

declare module 'react-hook-clipboard';
