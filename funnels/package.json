{"name": "funnels", "private": true, "version": "1.0.0", "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "i18n": "node ../scripts/prepare-i18next.js && i18next --silent --config ./i18next-parser-config.cjs && node ../scripts/i18next-parser-postprocess.js", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "prebuild": "node ../scripts/copy-i18n-files-to-public.js", "start": "remix-serve ./build/server/index.js", "test": "echo \"No test specified\"", "tsc": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "5.1.4", "@blade-group/shade": "0.1.7", "@chargebee/chargebee-js-react-wrapper": "0.6.3", "@chargebee/chargebee-js-types": "^1.0.1", "@didomi/react": "^1.8.2", "@flagship.io/react-sdk": "^3.3.0", "@remix-run/dev": "^2.8.1", "@remix-run/node": "^2.8.1", "@remix-run/react": "^2.8.1", "@remix-run/serve": "^2.8.1", "@sentry/vite-plugin": "^3.1.0", "@tanstack/react-query": "^4.0.10", "@tanstack/react-query-devtools": "^4.0.10", "@types/react-helmet": "^6.1.11", "antd": "^5.25.0", "antd-style": "^3.6.2", "common-components": "*", "dayjs": "^1.11.13", "hooks": "*", "i18next": "^23.11.3", "i18next-browser-languagedetector": "^7.2.1", "i18next-fs-backend": "^2.3.1", "i18next-http-backend": "^2.5.1", "immer": "^9.0.12", "is-ip": "^5.0.1", "isbot": "^5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "motion": "^12.12.1", "msw": "2.1.2", "oidc-client-ts": "^2.0.1", "prettier": "^3.2.5", "react": "18.2.0", "react-cookie": "^8.0.1", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.28.0", "react-i18next": "^14.1.1", "react-oidc-context": "^2.1.0", "react-router": "^7.2.0", "react-router-dom": "^7.2.0", "react-youtube": "^10.1.0", "remix-flat-routes": "^0.6.4", "remix-i18next": "^7.0.2", "remix-utils": "^8.7.0", "types": "*", "utils": "*", "zustand": "^3.7.1"}, "devDependencies": {"@types/react": "18.2.25", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "8.10.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.3.3", "typescript-eslint": "^8.24.1", "vite": "^5.1.0", "vite-plugin-commonjs": "^0.10.4", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=18.0.0"}}