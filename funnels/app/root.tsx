import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider } from 'antd';
import { FC, useEffect, useState } from 'react';
import { CookiesProvider } from 'react-cookie';
import { createPortal } from 'react-dom';
import { AuthProvider } from 'react-oidc-context';
import {
  data,
  Links,
  LoaderFunctionArgs,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router';
import { useChangeLanguage } from 'remix-i18next/react';
import { ClientOnly } from 'remix-utils/client-only';
import { getClientIPAddress } from 'remix-utils/get-client-ip-address';
import { useHydrated } from 'remix-utils/use-hydrated';
import { IS_DEV } from 'utils';

import './styles/global.css';
import 'antd/dist/reset.css';

import ConsentBanner from '~/components/ConsentBanner/ConsentBanner';
import { AppErrorBoundary } from '~/components/Errors/AppErrorBoundary';
import RouteError from '~/components/Errors/RouteError';
import { GeolocalizeUser } from '~/components/GeolocalizeUser/GeolocalizeUser';
import { HTMLLangSetter } from '~/components/HTMLLangSetter/HTMLLangSetter';
import { AppLayout } from '~/components/Layout/AppLayout';
import { antTheme } from '~/config/antTheme';
import { oidcConfig } from '~/config/oauth2-config';
import { FlagshipContext } from '~/contexts/FlagshipContext';
import { TrackingProvider } from '~/contexts/TrackingContext';
import i18nServer, { localeCookie } from '~/modules/i18n.server';
import useStore from '~/store';
import { setDatepickerLocale } from '~/utils/datepickerLocale';
import { initializeDayjs } from '~/utils/dayjs';
import { removeOldHead } from '~/utils/remix';

initializeDayjs();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // At least 30 seconds of default stale time
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
});

export const handle = { i18n: ['translation'] };

// Sets the locale and headers for the root component
export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18nServer.getLocale(request);
  const socketIpAddress = getClientIPAddress(request);

  return json(
    { locale, socketIpAddress },
    { headers: { 'Set-Cookie': await localeCookie.serialize(locale) } },
  );
}

export function Head({ title }: { title?: string }) {
  const [renderHead, setRenderHead] = useState(false);
  const hydrated = useHydrated();

  // Ensures proper head tag management during hydration by removing old head elements
  // after the component is hydrated and re-rendered to prevent duplicate head content
  useEffect(() => {
    if (!hydrated) {
      return;
    }
    if (!renderHead) {
      // trigger re-render so we can remove the old head
      setRenderHead(true);
      return;
    }
    removeOldHead(document.head);
  }, [renderHead, hydrated]);

  return (
    <>
      {/* allow specifying a title in the head */}
      {/* useful for Error Boundaries */}
      {/* (must be first to override any from meta) */}
      {title && <title>{title}</title>}
      <Meta />
      <link rel="icon" type="image/png" href="/funnel/favicon.ico" />
      <script async src="https://pay.google.com/gp/p/js/pay.js"></script>
      <script async src="https://js.chargebee.com/v2/chargebee.js"></script>
      <Links />
    </>
  );
}

const Providers: FC<{
  title?: string;
  children?: React.ReactNode;
  locale: any;
}> = ({ children, locale }) => {
  return (
    <AuthProvider {...oidcConfig}>
      <QueryClientProvider client={queryClient}>
        <FlagshipContext>
          <TrackingProvider>
            <ClientOnly>{() => <HTMLLangSetter />}</ClientOnly>
            <ConfigProvider theme={antTheme} locale={locale}>
              <CookiesProvider>
                <AppLayout>
                  <GeolocalizeUser>
                    <ClientOnly>{() => <ConsentBanner />}</ClientOnly>
                    {children}
                  </GeolocalizeUser>
                </AppLayout>
              </CookiesProvider>
            </ConfigProvider>
          </TrackingProvider>
        </FlagshipContext>
        <ClientOnly>
          {() => IS_DEV && <ReactQueryDevtools initialIsOpen={false} />}
        </ClientOnly>
      </QueryClientProvider>
    </AuthProvider>
  );
};

export default function App() {
  const { locale, socketIpAddress } = useLoaderData<typeof loader>();
  const { setUserIp } = useStore();
  const ipAddress = import.meta.env.DEV ? '*************' : socketIpAddress;
  useChangeLanguage(locale);
  setUserIp(ipAddress ?? '*************');

  return (
    <AppErrorBoundary>
      <AppComponent locale={locale} />
    </AppErrorBoundary>
  );
}

const AppComponent: FC<{
  title?: string;
  children?: React.ReactNode;
  locale: any;
}> = ({ title, children, locale }) => {
  return (
    <>
      <ClientOnly>
        {() => createPortal(<Head title={title} />, document.head)}
      </ClientOnly>
      <Providers locale={locale}>{children ? children : <Outlet />}</Providers>
      <ScrollRestoration />
      <Scripts />
    </>
  );
};

// Handles UI rendering for 4xx and 5xx errors
// docs: https://remix.run/docs/en/main/route/error-boundary
export function ErrorBoundary() {
  const error = useRouteError();
  const { locale } = useLoaderData<typeof loader>();

  if (isRouteErrorResponse(error)) {
    return (
      <AppComponent title={error.statusText} locale={locale}>
        <RouteError error={error} />
      </AppComponent>
    );
  } else if (error instanceof Error) {
    return (
      <div>
        <h1>Error</h1>
        <p>{error.message}</p>
        <p>The stack trace is:</p>
        <pre>{error.stack}</pre>
      </div>
    );
  } else {
    return <h1>Unknown Error</h1>;
  }
}
