interface IIndexable {
  [key: string]: any;
}
export interface IBillingDetailsForm extends IIndexable {
  first_name: string;
  last_name: string;
  country: string | undefined;
  phone_number: string | undefined;
  birthdate: Date;
  address1: string;
  city: string;
  zipcode: string;
  b2b: boolean | undefined;
  company: string | undefined;
  vat_number: string | undefined;
}

interface ApiError extends Error {
  response: Response;
  error_code: string | undefined;
  code: number | undefined;
}

interface IVerifyCoupon extends IIndexable {
  coupon: string;
}

export type { ApiError, IVerifyCoupon };
