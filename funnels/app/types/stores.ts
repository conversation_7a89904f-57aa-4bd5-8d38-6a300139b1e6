import { AdvancedSettingsConfigurationList, KeyboardISO } from 'types';

// TODO : type parameters
export interface CartState {
  estimation: number | undefined;
  setEstimation: (estimation: undefined) => void;
  advancedSettingsConfiguration: AdvancedSettingsConfigurationList;
  setAdvancedSettingsConfiguration: (
    advancedSettingsConfiguration: AdvancedSettingsConfigurationList,
  ) => void;
  advancedSettingsLanguage: string;
  setAdvancedSettingsLanguage: (advancedSettingsConfiguration: string) => void;
  advancedSettingsKeyboard: KeyboardISO;
  setAdvancedSettingsKeyboard: (advancedSettingsKeyboard: KeyboardISO) => void;
  setKeyboardAndLanguage: (keyboard: KeyboardISO, language: string) => void;
}
