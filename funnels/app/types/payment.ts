import { OfferInCart } from '~/types/cart';

export type IPaymentSuccessPayload = {
  offers: OfferInCart[];
  paymentMean: string | undefined;
};

export enum PaymentIntentType {
  ADD_CREDIT_CARD = 'addCreditCard',
  CARD_PAYMENT = 'cardPayment',
  PAYPAL_PAYMENT = 'paypalPayment',
  GOOGLE_PAY_PAYMENT = 'googlePayPayment',
  APPLE_PAY_PAYMENT = 'applePayPayment',
}

export interface PaymentError {
  code: string | number;
  type: string;
  name?: string;
  message?: string;
  displayMessage?: string;
  vendor_code?: string;
  description?: {
    message: string;
    type: string;
    api_error_code: string;
    error_code: string;
    error_msg: string;
    http_status_code: number;
  };
}
