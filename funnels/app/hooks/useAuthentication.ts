import { SigninRedirectArgs } from 'oidc-client-ts';
import { useEffect, useMemo } from 'react';
import { useAuth } from 'react-oidc-context';
import { useLocation } from 'react-router-dom';

import { useRedirectAfterLoginSuccess } from '~/store/useRedirectAfterLoginSuccess';

export const useAuthentication = () => {
  const { setLoginRedirectUrl } = useRedirectAfterLoginSuccess();
  const location = useLocation();
  const defaultAuth = useAuth();

  const auth = useMemo(() => {
    return {
      ...defaultAuth,
      signinRedirect: (args?: SigninRedirectArgs) => {
        setLoginRedirectUrl(location.pathname + location.search);
        return defaultAuth.signinRedirect(args);
      },
    };
  }, [defaultAuth, location, setLoginRedirectUrl]);

  useEffect(() => {
    window.__shadow_auth__ = {
      removeUser: () => auth.removeUser(),
    };
  }, [auth]);

  useEffect(() => {
    return auth.events.addAccessTokenExpired(() => {
      void auth.signoutRedirect();
    });
  });

  return auth;
};
