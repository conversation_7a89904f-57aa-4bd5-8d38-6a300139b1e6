import { orderBy } from 'lodash';
import { useParams } from 'react-router';
import { IAnyProduct, ProductType } from 'types';
import { getOffer, isOfferEnabledForSale } from 'utils';

import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { B2cProductTarget } from '~/types/product';

interface UseGetProductsForTargetProps {
  target: B2cProductTarget;
  excludeDeeplinkOnly?: boolean;
}

interface UseGetProductsForTargetReturn {
  isLoading: boolean;
  isError: boolean;
  products: IAnyProduct[];
}

export const useGetProductsForTarget = ({
  target,
  excludeDeeplinkOnly = true,
}: UseGetProductsForTargetProps): UseGetProductsForTargetReturn => {
  const { familyId: productFamilyId } = useParams();

  const catalogQuery = useCatalog({ withStock: false });

  const isLoading = catalogQuery.isLoading;

  const isError =
    (!catalogQuery.data && !catalogQuery.isLoading) || catalogQuery.isError;

  if (isLoading || isError || !catalogQuery.data) {
    return {
      isLoading,
      isError,
      products: [],
    };
  }

  const filteredProducts = catalogQuery.data?.products.allIds
    .map(
      productId => catalogQuery.data?.products.byId[productId] as IAnyProduct,
    )
    .filter(p => {
      // Check basic product requirements
      const isValidProduct =
        p.tiers_level !== null &&
        p.item_family_id === productFamilyId &&
        p.type === ProductType.PLAN;

      // Target matching - if no target specified or matches current tab
      const matchesTargetOrNoTarget =
        !p.meta_data?.target || // no target specified
        !target || // no tab selected
        p.meta_data.target === target; // matches selected tab

      // Exclude plans accessible only via deeplink
      const shouldBeExcluded = excludeDeeplinkOnly && p.meta_data?.deeplinkOnly;

      // Early return if basic conditions are not met
      if (!isValidProduct || !matchesTargetOrNoTarget || shouldBeExcluded) {
        return false;
      }

      // Check if there's a valid offer for this plan
      const offerForPlan = getOffer(catalogQuery.data, p.id, offer => {
        // Check enabled status
        return isOfferEnabledForSale({ offer });
      });

      return offerForPlan !== undefined;
    });

  const sortedProducts = orderBy(
    filteredProducts,
    [product => product.tiers_level],
    ['desc', 'asc'],
  );

  return {
    isLoading,
    isError,
    products: sortedProducts || [],
  };
};
