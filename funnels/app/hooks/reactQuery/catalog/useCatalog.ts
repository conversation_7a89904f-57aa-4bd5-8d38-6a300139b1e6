import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { ICatalog } from 'types';
import { APIError } from 'utils';

import { fetchCatalog } from '~/api/catalog';
import { useCurrency } from '~/hooks/useCurrency';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { MAX_RETRY } from '~/utils/constants';

const useCatalog = ({ withStock = false }: { withStock?: boolean } = {}) => {
  const currency = useCurrency();
  const { region, country } = useLocale();
  const { datacenterName, zipcode } = useStore();

  const { user, isLoading } = useAuth();
  const authToken = user?.access_token;

  return useQuery<ICatalog, APIError>({
    queryKey: ['getCatalog'],
    queryFn: () =>
      fetchCatalog(authToken, {
        currency,
        datacenter: datacenterName,
        market: country,
        region,
        withStock,
        zipcode,
      }),
    enabled: !isLoading,
    staleTime: 600 * 1000,
    retry: MAX_RETRY,
  });
};

export { useCatalog };
