import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import {
  ICouponPayload,
  IMember,
  IPaymentIntentPayload,
  IPaymentMethod,
  ISubscription,
  ISubscriptionPayload,
  IUpdateBillingDetailsPayload,
  IUserDetails,
} from 'types';
import { APIError, ZONE_PER_MARKET } from 'utils';

import {
  addPaymentIntent,
  addPaymentMethod,
  addSubscription,
  fetchBillingDetails,
  fetchCurrentMember,
  fetchPaymentMethods,
  fetchUserDetails,
  publicVerifyCoupon,
  updateBillingDetails,
  verifyCoupon,
} from '~/api/user';
import { useLocale } from '~/hooks/useLocale';
import { PaymentIntentType } from '~/types/payment';

export const useUserDetails = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IUserDetails | undefined, APIError>({
    queryKey: ['userDetails'],
    queryFn: () => fetchUserDetails(authToken as string),
    enabled: !!authToken,
  });
};

export const useCurrentMember = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IMember | undefined, APIError>({
    queryKey: ['getCurrentMember'],
    queryFn: () => fetchCurrentMember(authToken as string),
    enabled: !!authToken,
  });
};

export const useBillingDetails = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<Awaited<ReturnType<typeof fetchBillingDetails>>, APIError>({
    queryKey: ['billingDetails'],
    queryFn: () => fetchBillingDetails(authToken),
    enabled: !!authToken,
  });
};

export const usePaymentMethods = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IPaymentMethod[], APIError>({
    queryKey: ['paymentMethods'],
    queryFn: () => fetchPaymentMethods(authToken),
    enabled: !!authToken,
  });
};

export const useUpdateBillingDetails = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<
    string | undefined,
    APIError,
    IUpdateBillingDetailsPayload
  >({
    mutationKey: ['updateBillingDetails'],
    mutationFn: (billingDetails: IUpdateBillingDetailsPayload) =>
      updateBillingDetails(user?.access_token as string, billingDetails),
    onSuccess: () => {
      queryClient.invalidateQueries(['billingDetails']);
      queryClient.invalidateQueries(['userDetails']);
    },
  });
};

export const useAddPaymentIntent = (paymentIntentType: PaymentIntentType) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useMutation<
    PaymentIntent | undefined,
    APIError,
    IPaymentIntentPayload
  >(
    [`addPaymentIntent_${paymentIntentType}`],
    (paymentIntentPayload: IPaymentIntentPayload) =>
      addPaymentIntent(authToken, paymentIntentPayload),
  );
};

export const useAddPaymentMethod = (isPayment?: boolean) => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<
    IPaymentMethod,
    APIError,
    {
      chargeBeeToken?: string;
      paymentIntent?: PaymentIntent;
    }
  >(
    ['addPaymentMethod', isPayment],
    ({ chargeBeeToken, paymentIntent }) =>
      addPaymentMethod(authToken, chargeBeeToken, paymentIntent),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['paymentMethods']);
      },
    },
  );
};

export const usePublicVerifyCoupon = () => {
  const { country } = useLocale();

  return useMutation<boolean | undefined, APIError, ICouponPayload>({
    mutationKey: ['publicCouponVerify'],
    mutationFn: (couponPayload: ICouponPayload) =>
      publicVerifyCoupon(couponPayload, ZONE_PER_MARKET[country]),
  });
};

export const useVerifyCoupon = () => {
  const { user } = useAuth();
  const { country } = useLocale();

  const authToken = user?.access_token;

  return useMutation<boolean | undefined, APIError, ICouponPayload>({
    mutationKey: ['coupon'],
    mutationFn: (couponPayload: ICouponPayload) =>
      verifyCoupon(authToken, couponPayload, ZONE_PER_MARKET[country]),
  });
};

export const useAddSubscription = (vmsCount: number) => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<ISubscription, APIError, ISubscriptionPayload>({
    mutationKey: ['addSubscription'],
    mutationFn: (payload: ISubscriptionPayload) =>
      addSubscription(authToken, payload, vmsCount),
    onSuccess: () => {
      queryClient.invalidateQueries(['subscriptions']);
    },
  });
};
