import { useMutation } from '@tanstack/react-query';
import { usePaginatedSubscriptions } from 'hooks';
import { useAuth } from 'react-oidc-context';
import { IEstimation, IEstimationPayload } from 'types';
import { APIError } from 'utils';

import {
  fetchEstimation,
  fetchNotLoggedEstimation,
  fetchSubscriptionModificationEstimation,
} from '~/api/estimation';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { getSubscriptionIdToModify } from '~/utils/subscription';

export const useEstimation = (vmsCount: number) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useMutation<IEstimation | undefined, APIError, IEstimationPayload>({
    mutationKey: ['addEstimate'],
    mutationFn: (estimatePayload: IEstimationPayload) =>
      fetchEstimation(authToken, estimatePayload, vmsCount),
  });
};

export const useNotLoggedEstimation = () => {
  const { geolocResults } = useStore();
  const { country, region } = useLocale();

  const billing_details = {
    country: country,
    zipcode: geolocResults?.zipcode ?? null,
    region: region.toUpperCase(),
    b2b: false,
  };

  return useMutation<IEstimation | undefined, APIError, IEstimationPayload>({
    mutationKey: ['addUnloggedEstimate'],
    mutationFn: (estimatePayload: IEstimationPayload) =>
      fetchNotLoggedEstimation({
        ...estimatePayload,
        billing_details,
      }),
  });
};

export const useSubscriptionModificationEstimation = (
  subscriptionId: string | null,
) => {
  const { user } = useAuth();
  const subscriptionsQuery = usePaginatedSubscriptions();
  const { productType } = useStore();
  const subscriptionIdToModify =
    subscriptionId ??
    getSubscriptionIdToModify(
      subscriptionsQuery.data?.items,
      productType as string,
    );
  const authToken = user?.access_token;

  return useMutation<IEstimation | undefined, APIError, IEstimationPayload>({
    mutationKey: ['getSubscriptionModificationEstimation'],
    mutationFn: (estimatePayload: IEstimationPayload) =>
      fetchSubscriptionModificationEstimation(
        authToken,
        estimatePayload,
        subscriptionIdToModify,
      ),
  });
};
