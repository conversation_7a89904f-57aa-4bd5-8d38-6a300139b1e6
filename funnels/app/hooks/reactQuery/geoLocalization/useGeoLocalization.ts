import { useQuery } from '@tanstack/react-query';
import { APIError } from 'utils';

import { fetchGeoLocalization } from '~/api/geoLocalization';
import useStore from '~/store';
import { IGeoLocationInfo } from '~/types/geoLocalization';

export const useGeoLocalization = () => {
  const { userIp } = useStore();

  return useQuery<IGeoLocationInfo | undefined, APIError>({
    queryKey: ['geoLocalization'],
    queryFn: () => fetchGeoLocalization(userIp as string),
  });
};
