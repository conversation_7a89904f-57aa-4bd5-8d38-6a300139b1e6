import { useState, useLayoutEffect } from 'react';

export const useWindowSize = (
  updateOnResize = false,
): { width: number; height: number } => {
  const [size, setSize] = useState({ width: 0, height: 0 });

  useLayoutEffect(() => {
    const updateSize = (): void => {
      // Check if we're in a browser environment
      if (typeof window !== 'undefined') {
        setSize({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }
    };

    updateSize();

    if (updateOnResize && typeof window !== 'undefined') {
      window.addEventListener('resize', updateSize);
      return () => window.removeEventListener('resize', updateSize);
    }
  }, [updateOnResize]);

  return size;
};
