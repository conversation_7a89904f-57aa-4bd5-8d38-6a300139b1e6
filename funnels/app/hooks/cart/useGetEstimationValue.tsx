import { message } from 'antd';
import { usePaginatedSubscriptions } from 'hooks';
import { isEmpty } from 'lodash';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { IEstimation, ISubscriptionItem } from 'types';
import { APIError, logError } from 'utils';

import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import {
  useEstimation,
  useNotLoggedEstimation,
} from '~/hooks/reactQuery/estimation/useEstimation';
import { useBillingDetails } from '~/hooks/reactQuery/user/useUser';
import useStore from '~/store';
import { COUPON_ERROR_CODES } from '~/utils/constants';
import { computeSubscriptionItemPricesFromOffersInCart } from '~/utils/subscription';

// Get estimation value (and store in component's state)
// TODO : this method implicitely converts an array offersInCart into a single object cartOffer (see setCartOffer), this might work but it most likely reveals a logic flaw in the code. To investigate.
export const useGetEstimationValue = (
  setEstimation: Dispatch<SetStateAction<IEstimation | undefined>>,
  canFetchEstimate: boolean | undefined = true,
) => {
  const { t } = useTranslation();
  const { coupon, offersInCart, vmsCount } = useStore();
  const catalogQuery = useCatalog();
  const billingDetailsQuery = useBillingDetails();

  const subscriptionsQuery = usePaginatedSubscriptions();

  const getEstimation = useEstimation(vmsCount);
  const getNotLoggedEstimation = useNotLoggedEstimation();

  // const getSubscriptionModificationEstimation =
  //   useSubscriptionModificationEstimation(subscriptionId);

  useEffect(() => {
    if (offersInCart.length > 0 && catalogQuery.isSuccess && canFetchEstimate) {
      const offersToEstimate = computeSubscriptionItemPricesFromOffersInCart(
        catalogQuery.data,
        offersInCart,
      );

      // TODO: making calls here gets more and more complex, a refactor is maybe needed.
      let methodToUse;

      // If the user is not logged in and the estimate first step is enabled, we use the not logged estimation API
      if (isEmpty(billingDetailsQuery.data)) {
        methodToUse = getNotLoggedEstimation;
      } else {
        // TODO : update when working on subscription update
        // if (shouldUseUpdateEstimationApi) {
        //   methodToUse = getSubscriptionModificationEstimation;
        // } else {
        //   methodToUse = getEstimation;
        // }
        methodToUse = getEstimation;
      }

      methodToUse.mutate(
        {
          item_prices: offersToEstimate as ISubscriptionItem[],
          ...(!!coupon && { coupon_ids: [coupon] }),
        },
        {
          onSuccess: (estimationData: IEstimation | undefined) => {
            setEstimation(estimationData);
          },
          onError: (error: APIError) => {
            logError('Error during estimate API call : ', error);

            message.error(
              `${t(
                'errors.api.estimate',
                'An error occurred calculating your order total, please try again later.',
              )} Error: ${error.message}`,
            );

            const isCouponError =
              error.error_code && COUPON_ERROR_CODES.includes(error.error_code);

            if (isCouponError) {
              // TODO remove
              // we reset the coupon to show the input again.
              // actorRef.send({
              //   type: 'global.SET_CONTEXT',
              //   payload: { coupon: null },
              // });
              message.error(
                `${t(
                  'cart.addCoupon.invalid',
                  'Sorry, but this coupon is invalid',
                )}`,
              );
            }
          },
        },
      );
    }
    // DO NOT add state, enqueueSnackbar, t, getSubscriptionModificationEstimation and getEstimation to dependencies here
    // This was triggering unnecessary or infinite loop of queries for estimate mutation
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    offersInCart,
    subscriptionsQuery.isSuccess,
    catalogQuery.isSuccess,
    coupon,
    canFetchEstimate,
    billingDetailsQuery.data,
    vmsCount,
  ]);
};
