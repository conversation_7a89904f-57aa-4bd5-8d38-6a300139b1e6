import {
  EstimationDiscountEntityType,
  IEstimation,
  PriceTypeEstimation,
  TaxObjectType,
  DiscountDurationType,
  ICatalog,
  ProductType,
  ProductAddonNecessity,
} from 'types';

import useStore from '~/store';
import { computeAmountWithDiscount } from '~/utils/estimation';

export const useCartTotalValues = (
  estimation: IEstimation | undefined,
  catalog: ICatalog | undefined,
) => {
  const { coupon } = useStore();

  const invoiceEstimate = estimation?.invoice_estimate ?? undefined;
  const customNextInvoiceEstimate =
    estimation?.future_estimate?.custom_next_invoice_estimate ||
    estimation?.custom_next_invoice_estimate;
  // NOTE: We have a future_estimate when the user has a current subscription and updates (storage) or upgrades (drive premium or power).
  // When it's a new subscription, estimation?.custom_next_invoice_estimate has the correct "regular" subscription amount.
  // But when we have a future_estimate, estimation?.custom_next_invoice_estimate is equal to the payment the user will make right now,
  // and the correct "regular" subcription amount is actually estimation?.future_estimate?.custom_next_invoice_estimate.

  // TODO: This is temporary until the backend sends us information about the invoice being prorated or not.
  // NOTE: The invoice estimate is prorated if we have a future_estimate when the user has a current subscription and updates (storage) or upgrades (drive premium or power).
  // But in the case of a churned user, we DON'T have a future_estimate.
  // "churned" means that a user churned (no current subscription) but still has an anniversary date,
  // meaning that first charge will be prorated to cover the time between activation and anniversary date.
  // In this case, the only (fallback) way to know if the invoice is prorated is to check if one of the invoice's items have a description that includes "Prorated charges".
  const proratedChargesKeywords = 'prorated charges';
  const isInvoiceProrated =
    !!estimation?.future_estimate ||
    !!invoiceEstimate?.line_items.find(lineItem =>
      lineItem.description?.toLowerCase()?.includes(proratedChargesKeywords),
    );

  const currentPlanLineItem = invoiceEstimate?.line_items.find(lineItem => {
    return (
      catalog?.offers.byId[lineItem.entity_id]?.item_type === ProductType.PLAN
    );
  });
  const currentPlanOffer = currentPlanLineItem
    ? catalog?.offers.byId[currentPlanLineItem.entity_id]
    : undefined;
  const currentPlan = currentPlanOffer
    ? catalog?.products.byId[currentPlanOffer.product_id]
    : undefined;

  const lineItems = catalog
    ? (invoiceEstimate?.line_items
        .map(lineItem => {
          const catalogOffer = catalog.offers.byId[lineItem.entity_id];

          if (!catalogOffer) {
            return undefined;
          }

          if (currentPlan && catalogOffer.item_type === ProductType.ADDON) {
            const addonParameter = currentPlan.addons_and_charges.find(
              parameter => parameter.id === catalogOffer.product_id,
            );
            if (addonParameter?.type === ProductAddonNecessity.MANDATORY) {
              return undefined;
            }
          }

          return {
            productId: catalogOffer.product_id,
            amount: lineItem.amount / 100,
          };
        })
        .filter(Boolean) as { productId: string; amount: number }[])
    : undefined;

  const discountAmount = invoiceEstimate?.discounts
    ? invoiceEstimate.discounts.reduce((discountSum, discount) => {
        return discountSum + discount.amount;
      }, 0) / 100
    : undefined;

  const isTaxInclusive =
    invoiceEstimate?.price_type === PriceTypeEstimation.TAX_INCLUSIVE;

  const currentAttachedCoupon = estimation?.attached_coupons?.find(
    attached_coupon =>
      attached_coupon.id.toUpperCase() === coupon?.toUpperCase(),
  );

  const hasLimitedPeriodDiscount =
    currentAttachedCoupon?.duration_type ===
    DiscountDurationType.LIMITED_PERIOD;

  const hasPermanentDiscount =
    currentAttachedCoupon?.duration_type === DiscountDurationType.FOREVER;

  const invoiceEstimatePromotionalCredits =
    invoiceEstimate?.discounts?.find(
      discount =>
        discount.entity_type ===
        EstimationDiscountEntityType.PROMOTIONAL_CREDITS,
    )?.amount || 0;

  const creditsAppliedAmount =
    ((invoiceEstimate?.credits_applied || 0) +
      invoiceEstimatePromotionalCredits) /
    100;

  const amountDue = (invoiceEstimate?.amount_due ?? 0) / 100;

  const taxesList =
    invoiceEstimate?.taxes.filter(tax => tax.object === TaxObjectType.TAX) ||
    [];

  const totalTaxesAmount =
    taxesList?.reduce((acc: number, tax) => acc + Number(tax.amount), 0) || 0;

  const amountWithoutTaxes = isTaxInclusive
    ? ((invoiceEstimate?.total || 0) - totalTaxesAmount) / 100
    : (invoiceEstimate?.sub_total || 0) / 100;

  const nextDiscountedPaymentsDuration =
    (currentAttachedCoupon?.duration_month || 0) - 1;

  const nextDiscountedPaymentsAmount =
    hasLimitedPeriodDiscount && nextDiscountedPaymentsDuration > 0
      ? computeAmountWithDiscount(
          customNextInvoiceEstimate || 0,
          currentAttachedCoupon,
        ) / 100
      : 0;

  const regularPaymentAmount = hasPermanentDiscount
    ? computeAmountWithDiscount(
        customNextInvoiceEstimate || 0,
        currentAttachedCoupon,
      ) / 100
    : (customNextInvoiceEstimate || 0) / 100;

  const shouldShowRegularPaymentDate =
    !nextDiscountedPaymentsAmount &&
    !!estimation?.subscription_estimate?.next_billing_at;

  const regularPaymentDateInMs = shouldShowRegularPaymentDate
    ? Number(estimation?.subscription_estimate?.next_billing_at) * 1000
    : undefined;

  return {
    creditsAppliedAmount,

    lineItems,
    discountAmount,

    amountDue,
    taxesList,
    amountWithoutTaxes,

    isTaxInclusive,
    isInvoiceProrated,

    nextDiscountedPaymentsAmount,
    nextDiscountedPaymentsDuration,

    regularPaymentAmount,
    regularPaymentDateInMs,
  };
};
