import { useDatacenterListForPlan } from 'hooks';
import { maxBy } from 'lodash';
import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router';
import { IAnyOffer, ProductFamilyId, ProductType } from 'types';
import {
  checkStockAvailability,
  DATACENTER_STATUS_WEIGHT,
  getOffer,
  getOffers,
  isOfferEnabledForSale,
} from 'utils';

import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { getOffersInCartToAddFromPlanOffer } from '~/utils/cart';

export const useAutoAddOfferToCart = () => {
  const catalogQuery = useCatalog();
  const { zipcode, geolocResults, setUserTarget } = useStore();
  const { country } = useLocale();
  const [offerAddedToCart, setOfferAddedToCart] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const periodicityParam = searchParams.get('periodicity');

  // We need to check if the periodicity is valid and with the correct format
  const offerPeriodicityRegex = /^\d+-(day|week|month|year)$/;
  const offerPeriodicity = periodicityParam?.match(offerPeriodicityRegex)
    ? periodicityParam
    : '1-month';

  const { familyId: productFamilyId, productId } = useParams();
  const { setDatacenterName, resetCartAndAddOfferToCart, offersInCart } =
    useStore();

  const datacentersQuery = useDatacenterListForPlan({
    zipcode: zipcode ?? geolocResults?.zipcode ?? null,
    country,
    planId: productId ?? undefined,
  });

  const isCatalogLoading = catalogQuery.isLoading;
  const isDatacentersQueryLoading = datacentersQuery.isLoading;

  const datacenters = datacentersQuery.data;
  const catalog = catalogQuery.data;

  useEffect(
    () => {
      const autoAddOfferToCart = () => {
        if (
          !catalog ||
          !productId ||
          isCatalogLoading ||
          isDatacentersQueryLoading
        ) {
          return;
        }

        // Check if the periodicity exists in the available offers
        const availableOffers = getOffers(catalog, productId);
        const periodicityExists = availableOffers.some(
          offer => offer.periodicity === offerPeriodicity,
        );

        // If periodicity doesn't exist, use 1-month and remove the param
        let effectivePeriodicity = offerPeriodicity;
        if (!periodicityExists && periodicityParam) {
          effectivePeriodicity = '1-month';
          // Remove the periodicity parameter from URL
          searchParams.delete('periodicity');
          setSearchParams(searchParams);
        }

        const offerToAdd = getOffer(catalog, productId, offer => {
          return offer.periodicity === effectivePeriodicity;
        }) as IAnyOffer;

        // Offer is already in cart
        if (offersInCart.find(offer => offer.id === offerToAdd.id)) {
          return;
        }

        // Set user target
        if (catalog.products.byId[offerToAdd.product_id]?.meta_data?.target) {
          setUserTarget(
            catalog.products.byId[offerToAdd.product_id]?.meta_data?.target,
          );
        }

        // No offer in catalog or is not available for sale
        if (
          !offerToAdd ||
          (offerToAdd.itemFamilyId === ProductFamilyId.CLOUDPC &&
            datacenters &&
            datacenters.every(
              datacenter => datacenter.status === 'unavailable',
            ))
        ) {
          // TODO : handle errors
          return;
        }

        const recommandedDatacenter = maxBy(
          datacenters?.filter(dc => dc.status !== 'unavailable'),
          dc => DATACENTER_STATUS_WEIGHT[dc.status],
        );

        const isOfferOutOfStock = !checkStockAvailability({
          datacentersList: datacenters,
          datacenterName: recommandedDatacenter?.name ?? null,
          planOrAddonId: offerToAdd.id,
          productType: ProductType.PLAN,
          productFamilyId: offerToAdd.itemFamilyId,
          checkAllDatacenters: true,
        });

        // Cart is inconsistent with product family id or
        // we're not in fast funnel and offer is out of stock
        if (
          offerToAdd.itemFamilyId !== productFamilyId ||
          !isOfferEnabledForSale({ offer: offerToAdd }) ||
          isOfferOutOfStock ||
          !catalog.products.byId[offerToAdd.product_id]?.meta_data?.target
        ) {
          // TODO : handle errors
          return;
        }

        if (recommandedDatacenter) {
          setDatacenterName(recommandedDatacenter.name);
        }

        resetCartAndAddOfferToCart(
          getOffersInCartToAddFromPlanOffer(catalog, offerToAdd),
        );
      };

      autoAddOfferToCart();
      setOfferAddedToCart(true);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      catalog,
      isCatalogLoading,
      isDatacentersQueryLoading,
      productId,
      offerPeriodicity,
      periodicityParam,
    ],
  );

  return { offerAddedToCart };
};
