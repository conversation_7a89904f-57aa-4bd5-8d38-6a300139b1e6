import { useFsFlag } from '@flagship.io/react-sdk';
import { compact, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { ProductType } from 'types';
import { logError } from 'utils';

import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import {
  useBillingDetails,
  usePublicVerifyCoupon,
  useVerifyCoupon,
} from '~/hooks/reactQuery/user/useUser';
import useStore from '~/store';
import { getCouponsFromPlanOfferInCartMetadata } from '~/utils/coupon';

export const useAutoApplyCoupon = (enabled: boolean) => {
  const couponVerifier = useVerifyCoupon();
  const publicCouponVerify = usePublicVerifyCoupon();
  const catalogQuery = useCatalog();
  const billingDetailsQuery = useBillingDetails();
  const { setCoupon, offersInCart, referralCode } = useStore();
  const [hasFinishedAutoApplyingCoupon, setHasFinishedAutoApplyingCoupon] =
    useState(false);
  const isEstimateFirstStepEnabled = useFsFlag(
    'feature_estimate_first_step_enabled',
    false,
  ).getValue();

  useEffect(() => {
    if (!enabled) {
      return;
    }
    const planOfferInCart = offersInCart.find(
      offerInCart => offerInCart.type === ProductType.PLAN,
    );

    if (!catalogQuery.isSuccess || !planOfferInCart || !catalogQuery?.data) {
      return;
    }

    setHasFinishedAutoApplyingCoupon(false);

    const couponFromMetadata = getCouponsFromPlanOfferInCartMetadata(
      planOfferInCart,
      catalogQuery.data,
    );

    // The referral code should have a priority over coupons from metadata.
    const couponsToCheck = compact([referralCode, couponFromMetadata]);

    // Verifies the coupon before auto adding it to cart. If there's an error, the user sees nothing.
    const checkAndApplyCoupon = (couponToCheckAndApply: string) => {
      return new Promise<boolean>(resolve => {
        const methodToUse =
          isEmpty(billingDetailsQuery.data) && isEstimateFirstStepEnabled
            ? publicCouponVerify
            : couponVerifier;
        methodToUse.mutate(
          { coupon: couponToCheckAndApply },
          {
            onSuccess: response => {
              if (response) {
                setCoupon(couponToCheckAndApply);
              }
              resolve(response ?? false);
            },
            onError: error => {
              if (couponToCheckAndApply === couponFromMetadata) {
                logError(
                  `Automatically retrieved coupon ${couponToCheckAndApply} from ${planOfferInCart.productId} metadata was rejected. Check why.`,
                  error,
                );
              }

              resolve(false);
            },
          },
        );
      });
    };

    const checkAllCouponsAndApplyFirstValidOne = async () => {
      for (const couponToCheck of couponsToCheck) {
        const hasCouponBeenApplied = await checkAndApplyCoupon(couponToCheck);
        if (hasCouponBeenApplied) {
          break;
        }
      }
    };

    checkAllCouponsAndApplyFirstValidOne().then(() => {
      setHasFinishedAutoApplyingCoupon(true);
    });

    // PLEASE DO NOT ADD anything, like couponVerifier, to deps because it causes infinite loop.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    enabled,
    offersInCart,
    catalogQuery.isSuccess,
    catalogQuery?.data,
    billingDetailsQuery?.data,
    referralCode,
  ]);

  return { hasFinishedAutoApplyingCoupon };
};
