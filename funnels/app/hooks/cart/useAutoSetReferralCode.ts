import { useEffect } from 'react';
import { useSearchParams } from 'react-router';

import useStore from '~/store';

export const useAutoSetReferralCode = () => {
  const [searchParams] = useSearchParams();
  const { setReferralCode, referralCode } = useStore();

  useEffect(() => {
    const referralCodeParam = searchParams.get('referralCode');

    if (referralCodeParam) {
      setReferralCode(referralCodeParam);
    } else if (referralCode !== null) {
      // Clear the referral code if it's not in the URL parameters
      setReferralCode('');
    }
  }, [searchParams, setReferralCode, referralCode]);
};
