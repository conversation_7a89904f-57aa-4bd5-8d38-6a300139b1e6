import { useTranslation } from 'react-i18next';
import { AddonType, IAnyProduct, ICatalog, ILicenseTypeChoice } from 'types';
import { WINDOWS_HOME_LICENSE, getAddons, getProductFromOfferId } from 'utils';

import { OfferInCart } from '~/types/cart';

export const useLicense = (
  plan: IAnyProduct,
  catalog: ICatalog,
  offerInCart: OfferInCart[],
) => {
  const { t } = useTranslation();

  const allowedOsLicenses = getAddons(catalog, plan.id, (_, addon) => {
    return addon.meta_data?.type === AddonType.OS;
  });

  if (!allowedOsLicenses) {
    return;
  }

  const shadowLicenses: ILicenseTypeChoice[] = allowedOsLicenses.map(addon => {
    const isByol = addon?.meta_data?.isByol;

    return {
      label: isByol
        ? t(
            `catalog.addon.os.choice.customerOwnedLicense.label`,
            `Use your own Windows license`,
          )
        : t(`cart.summary.product.name.${addon.id}`),
      value: addon.id,
      key: addon.id,
    };
  });

  const licenseTypeChoices: ILicenseTypeChoice[] = [
    {
      label: t('catalog.addon.os.choice.windows.home.label', 'Windows 10 Home'),
      value: WINDOWS_HOME_LICENSE,
      key: WINDOWS_HOME_LICENSE,
    },
    ...shadowLicenses,
  ];

  const osOfferInCart = offerInCart.find(offer => {
    const product = getProductFromOfferId(catalog, offer.id);

    if (product?.meta_data?.type === AddonType.OS) {
      return offer;
    }
  });

  // WINDOWS_HOME_LICENSE is the default selected license
  const selectedLicense =
    osOfferInCart !== undefined
      ? osOfferInCart?.productId
      : WINDOWS_HOME_LICENSE;

  const hasOsAddon = !!shadowLicenses.length;

  return {
    licenseTypeChoices,
    selectedLicense,
    hasOsAddon,
  };
};
