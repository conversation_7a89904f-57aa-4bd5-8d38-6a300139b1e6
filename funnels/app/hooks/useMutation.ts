import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export const useMutationStatus = (observedMutationArray: string[]) => {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const unsubscribe = queryClient
      .getMutationCache()
      .subscribe(({ type, mutation }) => {
        const mutationKeys = mutation?.options.mutationKey as string[];
        const mutationInProgress = mutationKeys?.some(mutationKey =>
          observedMutationArray.includes(mutationKey),
        );

        if (type === 'updated' && mutationInProgress) {
          switch (mutation.state.status) {
            case 'loading':
              setIsLoading(true);
              setIsSuccess(false);
              setIsError(false);
              break;
            case 'success':
              setIsLoading(false);
              setIsSuccess(true);
              break;
            case 'error':
              setIsLoading(false);
              setIsError(true);
              break;
          }
        }
      });

    return () => {
      unsubscribe();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  // No dependency needed, we only want to subscribe once and cleanup after component has been unmounted

  return {
    isLoading,
    isSuccess,
    isError,
  };
};
