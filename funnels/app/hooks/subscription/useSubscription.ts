import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { ISubscription, ISubscriptionPayload, ProductFamilyId } from 'types';
import { getTimezoneName, logError } from 'utils';

import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useAddSubscription } from '~/hooks/reactQuery/user/useUser';
import { useCurrency } from '~/hooks/useCurrency';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { useCart } from '~/store/useCart';
import { CheckoutPayload } from '~/types/purchase';
import { computeSubscriptionItemPricesFromOffersInCart } from '~/utils/subscription';

const useSubscription = () => {
  const { t } = useTranslation();
  const currency = useCurrency();
  const { country } = useLocale();
  const {
    zipcode,
    coupon,
    datacenterName,
    geolocResults,
    offersInCart,
    vmsCount,
  } = useStore();
  const { advancedSettingsLanguage, advancedSettingsKeyboard } = useCart();

  const catalogQuery = useCatalog();

  const addSubscription = useAddSubscription(vmsCount);

  const onError = (error: unknown) => {
    logError(
      'An error occurred during add/modify subscription mutation : ',
      error,
    );
    message.error(
      t(
        'errors.api.payment',
        'An error occurred during your payment, please try again later.',
      ),
    );
  };

  const createOrUpdateSubscription = async (
    paymentIntent: PaymentIntent,
    onCreateOrUpdateSuccess: (payload: CheckoutPayload) => void,
  ) => {
    const hasShadowOfferInCart = !!offersInCart.find(
      offerInCart => offerInCart.productFamilyId === ProductFamilyId.CLOUDPC,
    );
    const couponsIdsList = [...(coupon ? [coupon] : [])];

    const subscriptionPayload: ISubscriptionPayload = {
      zipcode: zipcode ?? geolocResults?.zipcode ?? null,
      country: country,
      coupon_ids: couponsIdsList,
      item_prices: computeSubscriptionItemPricesFromOffersInCart(
        catalogQuery?.data,
        offersInCart,
      ),
      payment_intent: {
        id: paymentIntent.id,
        gateway_account_id: paymentIntent.gateway_account_id,
        gw_token: paymentIntent.active_payment_attempt?.id_at_gateway,
        payment_method_type: paymentIntent.payment_method_type,
      },
      metadata: {
        ...(hasShadowOfferInCart &&
          !!datacenterName && { datacenter: datacenterName }),
        ...(geolocResults ?? {}),
        ...(hasShadowOfferInCart &&
          !!advancedSettingsLanguage &&
          !!advancedSettingsKeyboard && {
            ready_to_play: {
              language: advancedSettingsLanguage,
              keyboard: advancedSettingsKeyboard,
              timezone: getTimezoneName(),
            },
          }),
      },
    };

    try {
      addSubscription.mutate(subscriptionPayload, {
        onError: (error: unknown) => {
          throw error;
        },
        onSuccess: (data: ISubscription | undefined) => {
          if (data) {
            const payload = {
              products: offersInCart,
              transactionId: data.id,
              paymentMean: paymentIntent.payment_method_type,
              currency,
              market: country,
              subscriptionCreationDate: data.created_at,
            };
            onCreateOrUpdateSuccess(payload);
          }
        },
      });
    } catch (error) {
      onError(error);
    }
  };

  return {
    createOrUpdateSubscription,
  };
};

export default useSubscription;
