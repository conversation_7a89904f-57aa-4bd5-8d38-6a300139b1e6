import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { message } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { IPaymentIntentPayload, PaymentMethodType } from 'types';
import { APIError } from 'utils';

import { useCreatePaymentIntent } from '~/hooks/payment/useCreatePaymentIntent';
import {
  useAddPaymentMethod,
  usePaymentMethods,
} from '~/hooks/reactQuery/user/useUser';
import { useCurrency } from '~/hooks/useCurrency';
import { PaymentIntentType } from '~/types/payment';

export const useApplePay = (
  setApplePayButtonMustUnmount: (isApplePayButtonMustUnmount: boolean) => void,
) => {
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent>();
  const [isApplePayButtonMounted, setApplePayButtonMounted] = useState(false);

  const createPaymentIntent = useCreatePaymentIntent(
    PaymentIntentType.APPLE_PAY_PAYMENT,
  );
  const addPaymentMethod = useAddPaymentMethod();
  const paymentMethodsQuery = usePaymentMethods();
  const { t } = useTranslation();
  const { user } = useAuth();
  const currency = useCurrency();

  const initApplePayButton = async (chargebeeInstance: any) => {
    if (chargebeeInstance && !isApplePayButtonMounted && user?.access_token) {
      try {
        const paymentIntentOptions: IPaymentIntentPayload = {
          currency_code: currency,
          payment_method_type: PaymentMethodType.APPLE_PAY,
        };

        const applePayHandler = await chargebeeInstance.load('apple-pay');
        // eslint-disable-next-line prettier/prettier
        const applePayPaymentIntent =
          await createPaymentIntent(paymentIntentOptions);
        applePayHandler.setPaymentIntent(applePayPaymentIntent);

        await applePayHandler.mountPaymentButton('#apple-pay-button', {
          style: {
            layout: 'vertical',
          },
        });

        setApplePayButtonMounted(true);
        const authorizedPaymentInfo = await applePayHandler.handlePayment();
        const authorizedPaymentIntent = authorizedPaymentInfo.paymentIntent;

        const isNewApplePayPaymentMethodFromPaymentIntent =
          !paymentMethodsQuery.data?.find(
            paymentMethod =>
              paymentMethod.type === PaymentMethodType.APPLE_PAY &&
              paymentMethod.apple_pay?.email ===
                authorizedPaymentIntent?.payer_info?.customer.email,
          );

        if (isNewApplePayPaymentMethodFromPaymentIntent) {
          addPaymentMethod.mutate(
            { paymentIntent: authorizedPaymentIntent },
            {
              onSuccess: () => {
                setPaymentIntent(authorizedPaymentIntent);
              },
              onError: (error: APIError) => {
                throw error;
              },
            },
          );
        } else {
          setPaymentIntent(authorizedPaymentIntent);
        }
      } catch (error) {
        setApplePayButtonMustUnmount(true);

        message.error(
          t(
            'errors.api.addApplePayAccount',
            'An error occurred adding your Apple Pay account, please try again later.',
          ),
        );
      }
    }
  };

  return { initApplePayButton, paymentIntent };
};
