import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { message } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { IPaymentIntentPayload, PaymentMethodType } from 'types';
import { APIError } from 'utils';

import { useCreatePaymentIntent } from '~/hooks/payment/useCreatePaymentIntent';
import {
  useAddPaymentMethod,
  usePaymentMethods,
} from '~/hooks/reactQuery/user/useUser';
import { useCurrency } from '~/hooks/useCurrency';
import { PaymentIntentType } from '~/types/payment';

export const usePaypal = (
  setPaypalButtonMustUnmount: (isPaypalButtonMustUnmount: boolean) => void,
) => {
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent>();
  const [isPaypalButtonMounted, setPaypalButtonMounted] = useState(false);

  const createPaymentIntent = useCreatePaymentIntent(
    PaymentIntentType.PAYPAL_PAYMENT,
  );
  const addPaymentMethod = useAddPaymentMethod();
  const paymentMethodsQuery = usePaymentMethods();
  const { t } = useTranslation();
  const { user } = useAuth();
  const currency = useCurrency();

  const initPaypalButton = async (chargebeeInstance: any) => {
    if (chargebeeInstance && !isPaypalButtonMounted && user?.access_token) {
      try {
        const paymentIntentOptions: IPaymentIntentPayload = {
          currency_code: currency,
          payment_method_type: PaymentMethodType.PAYPAL,
        };

        const paypalHandler = await chargebeeInstance.load('paypal');
        // eslint-disable-next-line prettier/prettier
        const newPaypalPaymentIntent =
          await createPaymentIntent(paymentIntentOptions);
        paypalHandler.setPaymentIntent(newPaypalPaymentIntent);

        await paypalHandler.mountPaymentButton('#paypal-button', {
          style: {
            layout: 'vertical',
          },
        });

        setPaypalButtonMounted(true);
        const authorizedPaymentIntent = await paypalHandler.handlePayment();

        const isNewPaypalPaymentMethodFromPaymentIntent =
          !paymentMethodsQuery.data?.find(
            paymentMethod =>
              paymentMethod.type === PaymentMethodType.PAYPAL &&
              paymentMethod.paypal?.email ===
                authorizedPaymentIntent?.payer_info?.customer.email,
          );

        if (isNewPaypalPaymentMethodFromPaymentIntent) {
          addPaymentMethod.mutate(
            { paymentIntent: authorizedPaymentIntent },
            {
              onSuccess: () => {
                setPaymentIntent(authorizedPaymentIntent);
              },
              onError: (error: APIError) => {
                throw error;
              },
            },
          );
        } else {
          setPaymentIntent(authorizedPaymentIntent);
        }
      } catch (error) {
        setPaypalButtonMustUnmount(true);
        message.error(
          t(
            'errors.api.addPaypalAccount',
            'An error occurred adding your PayPal account, please try again later.',
          ),
        );
      }
    }
  };

  return { initPaypalButton, paymentIntent };
};
