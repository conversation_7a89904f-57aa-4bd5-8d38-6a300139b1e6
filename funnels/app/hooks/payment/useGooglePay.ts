import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { message } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { IPaymentIntentPayload, PaymentMethodType } from 'types';
import { APIError } from 'utils';

import { useCreatePaymentIntent } from '~/hooks/payment/useCreatePaymentIntent';
import {
  useAddPaymentMethod,
  usePaymentMethods,
} from '~/hooks/reactQuery/user/useUser';
import { useCurrency } from '~/hooks/useCurrency';
import { PaymentIntentType } from '~/types/payment';

export const useGooglePay = (
  setGooglePayButtonMustUnmount: (
    isGooglePayButtonMustUnmount: boolean,
  ) => void,
) => {
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent>();
  const [isGooglePayButtonMounted, setGooglePayButtonMounted] = useState(false);

  const createPaymentIntent = useCreatePaymentIntent(
    PaymentIntentType.GOOGLE_PAY_PAYMENT,
  );
  const addPaymentMethod = useAddPaymentMethod();
  const paymentMethodsQuery = usePaymentMethods();
  const { t } = useTranslation();
  const { user } = useAuth();
  const currency = useCurrency();

  const initGooglePayButton = async (chargebeeInstance: any) => {
    if (chargebeeInstance && !isGooglePayButtonMounted && user?.access_token) {
      try {
        const paymentIntentOptions: IPaymentIntentPayload = {
          currency_code: currency,
          payment_method_type: PaymentMethodType.GOOGLE_PAY,
        };

        const googlePayHandler = await chargebeeInstance.load('google-pay');
        // eslint-disable-next-line prettier/prettier
        const googlePayPaymentIntent =
          await createPaymentIntent(paymentIntentOptions);
        googlePayHandler.setPaymentIntent(googlePayPaymentIntent);

        await googlePayHandler.mountPaymentButton('#google-pay-button', {
          style: {
            layout: 'vertical',
          },
        });

        setGooglePayButtonMounted(true);
        const authorizedPaymentInfo = await googlePayHandler.handlePayment();
        const authorizedPaymentIntent = authorizedPaymentInfo.paymentIntent;

        const isNewGooglePayPaymentMethodFromPaymentIntent =
          !paymentMethodsQuery.data?.find(
            paymentMethod =>
              paymentMethod.type === PaymentMethodType.GOOGLE_PAY &&
              paymentMethod.google_pay?.email ===
                authorizedPaymentIntent?.payer_info?.customer.email,
          );

        if (isNewGooglePayPaymentMethodFromPaymentIntent) {
          addPaymentMethod.mutate(
            { paymentIntent: authorizedPaymentIntent },
            {
              onSuccess: () => {
                setPaymentIntent(authorizedPaymentIntent);
              },
              onError: (error: APIError) => {
                throw error;
              },
            },
          );
        } else {
          setPaymentIntent(authorizedPaymentIntent);
        }
      } catch (error) {
        setGooglePayButtonMustUnmount(true);
        message.error(
          t(
            'errors.api.addGooglePayAccount',
            'An error occurred adding your Google Pay account, please try again later.',
          ),
        );
      }
    }
  };

  return { initGooglePayButton, paymentIntent };
};
