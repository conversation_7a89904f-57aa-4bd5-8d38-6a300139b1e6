import { useCallback } from 'react';
import { IPaymentIntentPayload } from 'types';

import { useAddPaymentIntent } from '~/hooks/reactQuery/user/useUser';
import { PaymentIntentType } from '~/types/payment';

export function useCreatePaymentIntent(paymentIntentType: PaymentIntentType) {
  const addPaymentIntent = useAddPaymentIntent(paymentIntentType);

  return useCallback(
    async (paymentIntentOptions: IPaymentIntentPayload) =>
      addPaymentIntent.mutateAsync(paymentIntentOptions),
    [addPaymentIntent],
  );
}
