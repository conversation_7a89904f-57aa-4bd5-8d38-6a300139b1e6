import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { IPaymentIntentPayload, PaymentMethodType } from 'types';

import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import { useCreatePaymentIntent } from '~/hooks/payment/useCreatePaymentIntent';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useAddPaymentMethod } from '~/hooks/reactQuery/user/useUser';
import useSubscription from '~/hooks/subscription/useSubscription';
import { useCurrency } from '~/hooks/useCurrency';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { PaymentError, PaymentIntentType } from '~/types/payment';
import { CheckoutPayload } from '~/types/purchase';
import { getChargebeeInstance } from '~/utils/chargebee';
import { computeSubscriptionItemPricesFromOffersInCart } from '~/utils/subscription';

function useFormCardPayment(onSuccess: (payload: CheckoutPayload) => void) {
  const { t } = useTranslation();
  const currency = useCurrency();
  const { country } = useLocale();
  const { coupon, offersInCart, zipcode, geolocResults } = useStore();

  const catalogQuery = useCatalog();

  const addPaymentMethod = useAddPaymentMethod(true);
  const { createOrUpdateSubscription } = useSubscription();

  const { paymentInfo } = usePaymentContext();

  const createPaymentIntent = useCreatePaymentIntent(
    PaymentIntentType.CARD_PAYMENT,
  );

  const handlePaymentErrorMessage = (error: PaymentError | string) => {
    if (typeof error === 'string') {
      message.error({
        content: t(
          'errors.api.cardPayment',
          'An error occurred during your payment, please try again later.',
        ),
        key: 'card-payment-error',
        duration: 0,
      });
    } else if ('code' in error && error.type === 'ClientError') {
      // Cards failed errors
      message.error({
        content: t(`paymentError.${error.code}`),
        key: 'card-payment-error',
        duration: 0,
      });
    } else if (error.name === 'payment_intent_authentication_failure') {
      // 3DS failed errors
      message.error({
        content: t(
          `paymentError.authentication_failure`,
          'We are unable to authenticate your payment method. Please choose another one and try again.',
        ),
        key: `card-payment-error`,
        duration: 0,
      });
    } else {
      // Generic errors
      message.error({
        content: t(
          'errors.api.cardPayment',
          'An error occurred during your payment, please try again later.',
        ),
        key: 'card-payment-error',
        duration: 0,
      });
    }
    // throw error for PayForm form
    throw error;
  };

  const onSubmit = async () => {
    message.destroy('card-payment-error');

    const isCardPayment =
      paymentInfo &&
      (paymentInfo.choice === PaymentChoice.NEW_CARD ||
        paymentInfo.choice === PaymentChoice.SAVED_CARD);

    if (
      !isCardPayment ||
      (paymentInfo.choice === PaymentChoice.NEW_CARD &&
        !paymentInfo.info.hasValidCard)
    ) {
      // TODO: etienned que faire ? chargement infini si dans ce cas
      throw 'No valid card payment';
    }

    try {
      const chargebeeInstance = getChargebeeInstance(country);

      if (!chargebeeInstance) {
        throw 'Error when loading ChargeBee instance';
      }
      const couponsIdsList = [...(coupon ? [coupon] : [])];

      const itemPrices = computeSubscriptionItemPricesFromOffersInCart(
        catalogQuery.data,
        offersInCart,
      );

      const paymentIntentPayload = {
        zipcode: zipcode ?? geolocResults?.zipcode ?? null,
        country: country,
        coupon_ids: couponsIdsList,
        item_prices: itemPrices,
      };

      const paymentIntentOptions: IPaymentIntentPayload = {
        currency_code: currency,
        payment_method_type: PaymentMethodType.CARD,
        subscription: paymentIntentPayload,
      };

      const isSavedCard = paymentInfo.choice === PaymentChoice.SAVED_CARD;

      let token: string | null = null;
      if (isSavedCard) {
        paymentIntentOptions.reference_id = paymentInfo.info.id;
      } else if (paymentInfo.info.cardRef?.current) {
        const tokenizedCard = await paymentInfo.info.cardRef.current.tokenize({
          customer: { firstName: paymentInfo.info.cardHolder },
        });
        token = tokenizedCard.token;
      }

      const paymentIntent = await createPaymentIntent(paymentIntentOptions);
      if (!paymentIntent) {
        throw 'Missing payment intent';
      }

      const threeDSHandler = await chargebeeInstance.load3DSHandler();
      threeDSHandler.setPaymentIntent(paymentIntent, {
        stripe: {},
        adyen: {},
        braintree: {},
      });

      const authorizedPaymentIntent = await threeDSHandler.handleCardPayment(
        !token ? {} : { cbToken: token },
        {},
      );

      if (!isSavedCard && paymentInfo.info.cardRef?.current) {
        const reTokenizedCard = await paymentInfo.info.cardRef.current.tokenize(
          {
            customer: { firstName: paymentInfo.info.cardHolder },
          },
        );

        await addPaymentMethod.mutateAsync({
          chargeBeeToken: reTokenizedCard.token,
        });
      }

      await createOrUpdateSubscription(authorizedPaymentIntent, onSuccess);
    } catch (error) {
      handlePaymentErrorMessage(error as PaymentError);
    }
  };

  return {
    onSubmit,
  };
}

export default useFormCardPayment;
