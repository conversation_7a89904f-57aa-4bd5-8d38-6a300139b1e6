import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { logError } from 'utils';

import useSubscription from '~/hooks/subscription/useSubscription';
import { CheckoutPayload } from '~/types/purchase';

function useFormLinkedPayment(
  paymentIntent: PaymentIntent | null,
  onSuccess: (payload: CheckoutPayload) => void,
) {
  const { createOrUpdateSubscription } = useSubscription();

  const onSubmit = async () => {
    if (paymentIntent) {
      await createOrUpdateSubscription(paymentIntent, onSuccess);
    } else {
      logError('No payment intent found');
    }
  };

  return {
    onSubmit: onSubmit,
  };
}

export default useFormLinkedPayment;
