import { Language, Market } from 'types';
import { IMAGE_PATH } from 'utils';

import { useLocale } from '~/hooks/useLocale';
import {
  CLOUDPC_FAQ_URL,
  DISCORD_DRIVE_URLS,
  DISCORD_URLS,
  DRIVE_FAQ_URLS,
  DRIVE_YOUTUBE_URL,
  PLACEHOLDER_DRIVE_CONFIRMATION_VIDEO_URLS,
  SHADOW_YOUTUBE_URLS,
} from '~/utils/constants';

export const useUrl = () => {
  const { language, country } = useLocale();

  const getUrl = (
    url: Record<Market, string | Partial<Record<Language, string>>>,
  ) => {
    const urlForMarket = url[country];
    switch (typeof urlForMarket) {
      case 'string':
        return urlForMarket;
      case 'object':
        return urlForMarket[language];
      default:
        return '#';
    }
  };

  const getCguUrl = (isB2b = false) => {
    const fileSuffix = isB2b ? 'b2b' : 'b2c';
    return `https://statics.shadow.tech/terms-of-use/tc-${country.toUpperCase()}-${fileSuffix}.pdf`;
  };

  const getDiscordUrl = (isDriveProduct = false): string =>
    isDriveProduct ? DISCORD_DRIVE_URLS[language] : DISCORD_URLS[language];

  const getShadowVideoUrl = (isDriveProduct: boolean): string | undefined => {
    return isDriveProduct
      ? getUrl(DRIVE_YOUTUBE_URL)
      : getUrl(SHADOW_YOUTUBE_URLS);
  };

  const getConfirmationVideoPlaceholder = (isDriveProduct: boolean): string =>
    isDriveProduct
      ? PLACEHOLDER_DRIVE_CONFIRMATION_VIDEO_URLS[language]
      : `${IMAGE_PATH}confirmation-shadow.png`;

  const getGlobalFaqUrl = (isDriveProduct = false): string =>
    isDriveProduct ? DRIVE_FAQ_URLS[language] : CLOUDPC_FAQ_URL;

  return {
    getCguUrl,
    getConfirmationVideoPlaceholder,
    getDiscordUrl,
    getGlobalFaqUrl,
    getShadowVideoUrl,
  };
};
