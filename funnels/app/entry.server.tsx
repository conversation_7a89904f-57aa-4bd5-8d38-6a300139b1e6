/* eslint-disable @typescript-eslint/ban-ts-comment */
// TODO : find a way to fix that (infering type from react router wtf ??!)
// @ts-nocheck
import type { AppLoadContext, EntryContext } from '@remix-run/node';
import { createReadableStreamFromReadable } from '@remix-run/node';
import { RemixServer } from '@remix-run/react';
import { createInstance } from 'i18next';
import Backend from 'i18next-fs-backend';
import { isbot } from 'isbot';
import { resolve as rs } from 'node:path';
import { PassThrough } from 'node:stream';
import { renderToPipeableStream } from 'react-dom/server';
import { I18nextProvider, initReactI18next } from 'react-i18next';
import { Locale } from 'types';

import * as i18n from './config/i18n';
import i18nServer from './modules/i18n.server';
import { Head } from './root';
import { pipeBody, renderHead } from './utils/remix';

const ABORT_DELAY = 5_000;

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  // This is ignored so we can keep it in the template for visibility
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loadContext: AppLoadContext,
) {
  const isBot = isbot(request.headers.get('user-agent') || '');
  const onReadyCallback = isBot ? 'onAllReady' : 'onShellReady';

  const instance = await createI18nInstance({ request, remixContext });
  const locale = (await i18nServer.getLocale(request)) as Locale;

  const head = renderHead({ remixContext, request, Head });

  return new Promise((res, reject) => {
    let shellRendered = false;
    const { pipe, abort } = renderToPipeableStream(
      <I18nextProvider i18n={instance}>
        <RemixServer
          context={remixContext}
          url={request.url}
          abortDelay={ABORT_DELAY}
        />
      </I18nextProvider>,
      {
        [onReadyCallback]() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          responseHeaders.set('Content-Type', 'text/html');

          res(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            }),
          );

          pipeBody({ body, head, pipe, locale });
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          if (shellRendered) {
            // eslint-disable-next-line no-console
            console.error(error);
          }
        },
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

async function createI18nInstance({
  request,
  remixContext,
}: {
  request: Request;
  remixContext: EntryContext;
}) {
  const instance = createInstance();
  const lng = await i18nServer.getLocale(request);
  const ns = i18nServer.getRouteNamespaces(remixContext);

  await instance
    .use(initReactI18next)
    .use(Backend)
    .init({
      ...i18n,
      lng,
      ns,
      backend: { loadPath: rs(`./funnel/public/locales/${ns}-${lng}.json`) },
    });

  return instance;
}
