import { ReactNode, createContext, useContext, useMemo, useState } from 'react';

type SetFormParams = [formId: string] | [formId: null];

interface IFormContextValue {
  formId: string | null;
  setForm: (...params: SetFormParams) => void;
}

const FormContext = createContext<IFormContextValue | undefined>(undefined);

export const FormContextProvider = ({ children }: { children: ReactNode }) => {
  const [formId, setFormId] = useState<string | null>(null);

  const value = useMemo(() => {
    return {
      formId,
      setForm: (newFormId: string | null) => {
        setFormId(newFormId);
      },
    };
  }, [formId]);

  return <FormContext.Provider value={value}>{children}</FormContext.Provider>;
};

export const useFormContext = () => {
  return useContext(FormContext);
};
