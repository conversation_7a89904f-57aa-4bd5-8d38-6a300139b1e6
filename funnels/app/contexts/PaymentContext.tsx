import ChargebeeComponents from '@chargebee/chargebee-js-react-wrapper/dist/components/ComponentGroup';
import { PaymentIntent } from '@chargebee/chargebee-js-types';
import {
  Dispatch,
  ReactNode,
  RefObject,
  SetStateAction,
  createContext,
  useContext,
  useMemo,
  useState,
} from 'react';
import { IPaymentMethod } from 'types';

export enum PaymentChoice {
  NEW_CARD = 'new_card',
  SAVED_CARD = 'saved_card',
  PAYPAL = 'paypal',
  GOOGLE_PAY = 'google_pay',
  APPLE_PAY = 'apple_pay',
}

export enum CardChoice {
  NEW_CARD = 'new_card',
  SAVED_CARDS = 'save_cards',
}

interface ICartPaymentContextProviderProps {
  children: ReactNode;
}

interface IPaymentSavedCard {
  choice: PaymentChoice.SAVED_CARD;
  info: IPaymentMethod;
}

interface IPaymentNewCard {
  choice: PaymentChoice.NEW_CARD;
  info: {
    cardRef: RefObject<ChargebeeComponents>;
    hasValidCard: boolean;
    cardHolder: string;
  };
}

interface IPaymentPaypal {
  choice: PaymentChoice.PAYPAL;
  info: IPaymentMethod;
  paymentIntent: PaymentIntent;
}

interface IPaymentGooglePay {
  choice: PaymentChoice.GOOGLE_PAY;
  info: IPaymentMethod;
  paymentIntent: PaymentIntent;
}

interface IPaymentApplePay {
  choice: PaymentChoice.APPLE_PAY;
  info: IPaymentMethod;
  paymentIntent: PaymentIntent;
}

type IPaymentInfo =
  | IPaymentSavedCard
  | IPaymentNewCard
  | IPaymentPaypal
  | IPaymentGooglePay
  | IPaymentApplePay;

interface ICartPaymentContextValue {
  paymentInfo: IPaymentInfo | null;
  setPaymentInfo: Dispatch<SetStateAction<IPaymentInfo | null>>;
  paymentIntent: PaymentIntent | null;
  setPaymentIntent: Dispatch<SetStateAction<PaymentIntent | null>>;
}

const PaymentContext = createContext<ICartPaymentContextValue | undefined>(
  undefined,
);

export const PaymentProvider = ({
  children,
}: ICartPaymentContextProviderProps) => {
  const [paymentInfo, setPaymentInfo] = useState<IPaymentInfo | null>(null);
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(
    null,
  );

  const contextValue = useMemo(() => {
    return { paymentInfo, setPaymentInfo, paymentIntent, setPaymentIntent };
  }, [paymentInfo, paymentIntent]);

  return (
    <PaymentContext.Provider value={contextValue}>
      {children}
    </PaymentContext.Provider>
  );
};

export const usePaymentContext = () => {
  const cardPaymentContext = useContext(PaymentContext);

  if (!cardPaymentContext) {
    throw new Error('usePaymentContext must be used within PaymentProvider');
  }

  return cardPaymentContext;
};
