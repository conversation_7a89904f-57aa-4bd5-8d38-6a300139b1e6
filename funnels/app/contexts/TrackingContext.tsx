import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useState,
} from 'react';

export interface ITrackingContextValue {
  trackingEnabled: boolean;
  onTrackingEnabled: () => void;
  onTrackingDisabled: () => void;
}

export interface ITrackingProviderProps {
  children: ReactNode;
}

const TrackingContext = createContext<ITrackingContextValue | undefined>(
  undefined,
);

export const TrackingProvider = ({ children }: ITrackingProviderProps) => {
  const [trackingEnabled, setTrackingEnabled] = useState(false);
  const onTrackingEnabled = useCallback(() => {
    setTrackingEnabled(true);
  }, []);
  const onTrackingDisabled = useCallback(() => {
    setTrackingEnabled(false);
  }, []);

  return (
    <TrackingContext.Provider
      value={{ trackingEnabled, onTrackingDisabled, onTrackingEnabled }}
    >
      {children}
    </TrackingContext.Provider>
  );
};

export const useTracking = () => {
  const context = useContext(TrackingContext);
  if (!context) {
    throw new Error('useTracking must be used within TrackingProvider');
  }
  return context;
};
