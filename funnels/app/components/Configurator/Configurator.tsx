import { Col, Row } from 'antd';
import { ICatalog } from 'types';

import { Cart } from '~/components/Cart/Cart';
import { Cards } from '~/components/OverviewCards';
import { GlobalLoader } from '~/components/ui/Loader';
import { useAutoAddOfferToCart } from '~/hooks/cart/useAutoAddOfferToCart';
import useStore from '~/store';
import { getPlanOfferFromCart } from '~/utils/cart';

interface IConfiguratorProps {
  catalog: ICatalog;
}

export const Configurator = ({ catalog }: IConfiguratorProps) => {
  const { offersInCart } = useStore();

  // Auto add offer to cart from url
  const { offerAddedToCart } = useAutoAddOfferToCart();

  const planOffer = getPlanOfferFromCart(catalog, offersInCart);
  const plan = catalog?.products.byId[planOffer?.product_id ?? ''];

  if (!planOffer || !plan || !catalog || !offerAddedToCart) {
    return <GlobalLoader fullHeight />;
  }

  return (
    <Row gutter={[40, 16]}>
      <Col
        xs={{ flex: '100%' }}
        sm={{ flex: '100%' }}
        md={{ flex: '100%' }}
        lg={{ flex: '100%' }}
        xl={{ flex: '68%' }}
      >
        <Cards.cloudpc
          catalog={catalog}
          offer={planOffer}
          plan={plan}
          offersInCart={offersInCart}
        />
      </Col>
      <Col
        xs={{ flex: '100%' }}
        sm={{ flex: '100%' }}
        md={{ flex: '100%' }}
        lg={{ flex: '100%' }}
        xl={{ flex: '32%' }}
      >
        <Cart />
      </Col>
    </Row>
  );
};
