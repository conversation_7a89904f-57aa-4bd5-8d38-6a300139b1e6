import { useTranslation } from 'react-i18next';
import { getActivationFeesOffer } from 'utils';

import { Text } from '~/components/ui/Typography/Typography';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { formatPrice } from '~/utils/price';

export const AccessFeesExplanation = () => {
  const { t } = useTranslation();
  const catalogQuery = useCatalog();

  const activationFeesOffer = getActivationFeesOffer(catalogQuery.data);

  if (!activationFeesOffer) {
    return null;
  }

  return (
    <Text small>
      {t('cart.accessFee', {
        defaultValue:
          '*Including {{price}} of access fees applied only to the first payment in order to cover the setting up and configuration of your PC.',
        price: formatPrice(
          activationFeesOffer.price,
          activationFeesOffer.currency_code,
        ),
      })}
    </Text>
  );
};
