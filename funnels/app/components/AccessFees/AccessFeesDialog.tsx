import { QuestionCircleOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ICartSummaryProduct } from 'types';

import { Button } from '~/components/ui/Button/Button';

interface IAccessFeesDialogProps {
  cartSummaryProductOffer: ICartSummaryProduct['offers'][number];
}

export const AccessFeesDialog = ({
  cartSummaryProductOffer,
}: IAccessFeesDialogProps) => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const { amount, price } = cartSummaryProductOffer;

  const getAccessFeesContent = () => {
    if (amount && amount !== 0) {
      return (
        <Trans i18nKey="access-fees.modal.contentWithFee">
          Shadow is a service offering a high-end PC accessible to everyone in
          the cloud that lets you play, create and work on any connected device.
          The service includes access to a Windows license, new features that
          expand or enhance the experience (dual screen, VR compatibility,
          remote screen, etc.), and ongoing support.
          <br />
          <br />
          Shadow wants to continue to offer the latest technologies in cloud
          computing, and this, in the best conditions. This is why, in an
          inflationary economic context (increase in energy costs, components,
          etc.), we must take commercial actions while protecting our current
          community and promoting subscriptions over time. Shadow is therefore
          implementing a setup fee of {{ price }}, charged for all new
          subscribers or account reactivation. This setup fee does not apply to
          subscribers who have an active account without interruption.
        </Trans>
      );
    } else {
      return (
        <Trans i18nKey="access-fees.modal.content">
          Shadow is a service offering a high-end PC accessible to everyone in
          the cloud that lets you play, create and work on any connected device.
          The service includes access to a Windows license, new features that
          expand or enhance the experience (dual screen, VR compatibility,
          remote screen, etc.), and ongoing support.
          <br />
          <br />
          Shadow wants to continue to offer the latest technologies in cloud
          computing, and this, in the best conditions. This is why, in an
          inflationary economic context (increase in energy costs, components,
          etc.), we must take commercial actions while protecting our current
          community and promoting subscriptions over time. Shadow is therefore
          implementing a setup fee, charged for all new subscribers or account
          reactivation. This setup fee does not apply to subscribers who have an
          active account without interruption.
          <br />
          <br />
          At this time, and for a limited time, these setup fees are offered by
          Shadow. As part of this special offer, the setup fee will not be
          charged at the time of order.
        </Trans>
      );
    }
  };

  return (
    <>
      <Button
        type="link"
        data-testid="accessFees-link"
        size="small"
        onClick={handleClickOpen}
        icon={<QuestionCircleOutlined />}
      />
      <Modal
        data-test-id="accessFees-modal"
        open={open}
        onCancel={handleClose}
        aria-labelledby="Access-Fees-dialog-title"
        aria-describedby="Access-Fees-dialog-description"
        title={t('access-fees.modal.title', 'Why a setup fee?')}
      >
        {getAccessFeesContent()}
        <Button data-testid="accessFees-button" onClick={handleClose}>
          {t('global.ok', 'OK')}
        </Button>
      </Modal>
    </>
  );
};
