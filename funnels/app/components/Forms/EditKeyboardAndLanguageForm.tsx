import { CheckOutlined } from '@ant-design/icons';
import { Flex, Form, Select } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardISO, SelectOptionNew } from 'types';

import { Button } from '~/components/ui/Button/Button';
import { Flag } from '~/components/ui/Flag';
import { KeyboardIcon } from '~/components/ui/Icon';
import { Text, Title } from '~/components/ui/Typography/Typography';
import { useCart } from '~/store/useCart';
import {
  ADVANCED_SETTINGS_KEYBOARD_CONFIGURATIONS,
  ADVANCED_SETTINGS_LANGUAGES_OPTIONS,
} from '~/utils/constants';

interface EditKeyboardAndLanguagePayload {
  keyboardSettings: KeyboardISO;
  languageSettings: string;
}

interface Props {
  currentKeyboard: KeyboardISO;
  currentLanguage: string;
  onClose: () => void;
}

export const EditKeyboardAndLanguageForm = ({
  currentKeyboard,
  currentLanguage,
  onClose,
}: Props) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [keyboard, setKeyboard] = useState(currentKeyboard);
  const [language, setLanguage] = useState(currentLanguage);

  const { setKeyboardAndLanguage } = useCart();

  const handleSubmit = (values: EditKeyboardAndLanguagePayload) => {
    setKeyboardAndLanguage(values.keyboardSettings, values.languageSettings);
    onClose();
  };

  // Construct language options with custom label
  const languageOptions: SelectOptionNew[] =
    ADVANCED_SETTINGS_LANGUAGES_OPTIONS.map(languageOption => ({
      value: languageOption.value,
      label: (
        <Flex align="center" gap="small" justify="space-between">
          <Flag
            flex={true}
            value={languageOption.label}
            flag={
              languageOption.value.slice(-2).toUpperCase() as keyof typeof Flags
            }
          />
          {languageOption.value === currentLanguage && (
            <Title level={3} primary>
              <CheckOutlined />
            </Title>
          )}
        </Flex>
      ),
      ...{
        'data-testid': `languageSettings-${languageOption.value}-option`,
      },
    }));

  const keyboardOptions: SelectOptionNew[] =
    ADVANCED_SETTINGS_KEYBOARD_CONFIGURATIONS.map(keyboardOption => ({
      value: keyboardOption.value,
      label: (
        <Flex align="center" gap="small" justify="space-between">
          <Flex align="center" gap="small">
            <KeyboardIcon style={{ fontSize: '24px' }} />
            <Text>{keyboardOption.label}</Text>
          </Flex>
          {keyboardOption.value === currentKeyboard && (
            <Title level={3} primary>
              <CheckOutlined />
            </Title>
          )}
        </Flex>
      ),
      ...{
        'data-testid': `keyboardSettings-${keyboardOption.value}-option`,
      },
    }));

  return (
    <Form
      form={form}
      onFinish={handleSubmit}
      initialValues={{
        languageSettings: currentLanguage,
        keyboardSettings: currentKeyboard,
      }}
    >
      <Form.Item name="languageSettings">
        <Select
          data-test-id="addExtraStorageModal-select"
          options={languageOptions}
          onChange={values => setLanguage(values)}
          size="large"
        />
      </Form.Item>
      <Form.Item name="keyboardSettings">
        <Select
          data-test-id="editKeyboardAndLanguageModal-keyboard-select"
          options={keyboardOptions}
          onChange={values => setKeyboard(values)}
          size="large"
        />
      </Form.Item>
      <Flex vertical gap="middle">
        <Button
          data-testid="editKeyboardAndLanguageModal-add-button"
          disabled={
            language === currentLanguage && keyboard === currentKeyboard
          }
          block
          htmlType="submit"
        >
          {t('global.ok')}
        </Button>
        <Button
          data-testid="editKeyboardAndLanguageModal-cancel-button"
          onClick={() => onClose()}
          tertiary
          block
        >
          {t('global.cancel')}
        </Button>
      </Flex>
    </Form>
  );
};
