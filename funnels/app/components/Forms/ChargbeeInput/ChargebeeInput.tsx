import { Flex } from 'antd';
import {
  cloneElement,
  useState,
  ReactElement,
  ChangeEvent,
  ReactNode,
} from 'react';

import useStyles from '~/components/Forms/ChargbeeInput/ChargebeeInput.styles';
import { Text } from '~/components/ui/Typography/Typography';
import { IChargebeeStatus } from '~/types/chargebee';

interface Props {
  label: string;
  customErrorMessage?: string;
  children: ReactElement; // chargebee's react components are this type
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  'data-test-id': string;
  hideIcon?: boolean;
  onChange?: (hasError: boolean, value?: string) => void;
}

export const ChargebeeInput = (props: Props) => {
  const { styles } = useStyles();
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChanges = (
    status: IChargebeeStatus | ChangeEvent<HTMLInputElement>,
  ) => {
    const hasError =
      'nativeEvent' in status
        ? !status.target.value.length
        : !status.complete || status.empty || !!status.error;

    props.onChange?.(
      hasError,
      'nativeEvent' in status ? status.target.value : undefined,
    );

    if (!('nativeEvent' in status)) {
      if (props.customErrorMessage) {
        if (status.empty || status.error) {
          return setErrorMessage(props.customErrorMessage);
        }
      } else {
        if (status.empty) {
          return setErrorMessage('Missing required field.');
        }

        if (status.error) {
          return setErrorMessage(status.error.errorCode);
        }
      }

      setErrorMessage('');
    }
  };

  const defaultError = props.customErrorMessage ?? 'Missing required field';

  return (
    <Flex vertical gap="small">
      <Text small>{props.label}</Text>
      <Flex justify="space-between" data-test-id={props['data-test-id']}>
        <Flex gap="middle" flex="1" className={styles.chargebee__input}>
          {props.startIcon && props.startIcon}
          {cloneElement(props.children, {
            onBlur: handleInputChanges,
            onChange: handleInputChanges,
          })}
          {props.endIcon && props.endIcon}
          {props.hideIcon && <div className={styles.chargebee__hideIcon} />}
        </Flex>
      </Flex>
      <Text type="danger" small>
        {errorMessage && (errorMessage || defaultError)}
      </Text>
    </Flex>
  );
};
