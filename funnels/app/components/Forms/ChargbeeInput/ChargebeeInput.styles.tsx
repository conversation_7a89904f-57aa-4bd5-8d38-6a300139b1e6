import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    chargebee__input: css`
      position: relative;
      display: flex;
      gap: ${token.marginSM}px;
      height: 53px;
      align-items: center;
      background: #ffffff;
      border: ${token.lineWidth}px solid #ebeefa;
      border-radius: 8px;
      padding: 24px 16px;

      div {
        flex: 1;
      }

      .chargebeeInput {
        height: 15px; // to vertical-align chargebee iframe
      }

      // for input name
      input {
        background: 0 0;
        border: none;
        border-radius: 0;
        display: block;
        width: 100%;
        font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto,
          Helvetica Neue, sans-serif;
        font-size: 1em;
        line-height: 1.2em;
        height: 1.2em;
      }
      input:focus {
        outline: none;
      }
    `,

    chargebee__hideIcon: css`
      position: absolute;
      right: ${token.lineWidth}px;
      top: 0;
      width: 37px;
      height: 100%;
      background: ${token.colorBgBase};
      pointer-events: none;
      z-index: 1;
    `,
  };
});
