import { CheckOutlined } from '@ant-design/icons';
import { Alert, Checkbox, Flex, Form, Select } from 'antd';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import {
  AddonType,
  Currency,
  IAnyOffer,
  ICatalog,
  ILicenseTypeChoice,
  NotificationState,
  SelectOptionNew,
} from 'types';
import {
  getOffer,
  getProductFromId,
  getProductFromOfferId,
  WINDOWS_HOME_LICENSE,
} from 'utils';

import { Button } from '~/components/ui/Button/Button';
import { WindowsOfferIcon } from '~/components/ui/Icon';
import { Link, Text, Title } from '~/components/ui/Typography/Typography';
import useStore from '~/store';
import { getOfferInCart } from '~/utils/cart';
import { formatPrice } from '~/utils/price';

interface EditOsLicensePayload {
  osLicenseSettings: string;
}

interface Props {
  catalog: ICatalog;
  currency: Currency;
  licenseTypeChoices: ILicenseTypeChoice[];
  offer: IAnyOffer;
  onClose: () => void;
  currentOsLicense: string;
}

export const EditOsForm = ({
  catalog,
  currency,
  licenseTypeChoices,
  offer,
  onClose,
  currentOsLicense,
}: Props) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { offersInCart, removeOfferFromCart, setOfferToCart } = useStore();

  const [osLicense, setOsLicense] = useState(currentOsLicense);
  const [isConditionAccepted, setIsConditionAccepted] = useState(false);

  // is the selected product a BYOL - needed to display checkbox
  const selectedProduct = getProductFromId(catalog, osLicense);
  const isByol = selectedProduct ? selectedProduct?.meta_data?.isByol : false;

  const handleSubmit = (values: EditOsLicensePayload) => {
    const isWindowsHomeLicense =
      values.osLicenseSettings === WINDOWS_HOME_LICENSE;

    const osOffer = getOffer(catalog, values.osLicenseSettings);

    const allOsOfferInCart = offersInCart.filter(offerInCart => {
      const addonProduct = getProductFromOfferId(catalog, offerInCart.id);

      if (addonProduct?.meta_data?.type !== AddonType.OS) {
        return;
      }

      return offerInCart;
    });

    // remove older offer from cart
    if (!isEmpty(allOsOfferInCart)) {
      allOsOfferInCart.forEach(osOfferInCart => {
        removeOfferFromCart(osOfferInCart.id);
      });
    }

    // add new offer to cart if not default home license
    if (!isWindowsHomeLicense) {
      const newLicenseOffer = getOfferInCart(osOffer as IAnyOffer, offer.id);
      setOfferToCart(newLicenseOffer);
    }

    onClose();
  };

  // Construct isLicense options with custom label
  const osOptions: SelectOptionNew[] = licenseTypeChoices.map(osOption => {
    const licenseOffer = getOffer(catalog, osOption.value);
    const price = licenseOffer ? formatPrice(licenseOffer.price, currency) : 0;
    const hasPrice = licenseOffer && licenseOffer.price > 0;
    const isCurrentOption = osOption.key === currentOsLicense;

    return {
      value: osOption.value,
      label: (
        <Flex align="center" gap="small" justify="space-between">
          <Flex align="center" gap="small">
            <WindowsOfferIcon style={{ fontSize: '24px' }} />
            <Text>{osOption.label}</Text>
          </Flex>
          {(hasPrice || isCurrentOption) && (
            <Flex align="center" gap="small">
              {hasPrice && (
                <>
                  <Text primary>
                    <Trans
                      i18nKey={
                        'purchaseOverview.editOsModal.select.menuItem.price'
                      }
                      defaults={'<strong>{{price}}</strong>'}
                      values={{ price }}
                      components={{
                        strong: <strong />,
                      }}
                    />
                  </Text>
                  <Text small primary>
                    {t(
                      'purchaseOverview.editOsModal.select.menuItem.period',
                      'per month',
                    )}
                  </Text>
                </>
              )}
              {isCurrentOption && (
                <Title level={3} primary>
                  <CheckOutlined />
                </Title>
              )}
            </Flex>
          )}
        </Flex>
      ),
      ...{
        'data-testid': `osSettings-${osOption.value}-option`,
      },
    };
  });

  return (
    <Form
      form={form}
      onFinish={handleSubmit}
      initialValues={{
        osLicenseSettings: currentOsLicense,
      }}
    >
      <Form.Item
        name="osLicenseSettings"
        style={isByol ? { marginBottom: '4px' } : undefined}
      >
        <Select
          data-testid="osSettings-select"
          options={osOptions}
          onChange={values => setOsLicense(values)}
          size="large"
        />
      </Form.Item>
      {isByol && (
        <Form.Item
          name="osLicenseByol"
          rules={[
            {
              validator: () => {
                if (isConditionAccepted) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(
                    t(
                      'purchaseOverview.editOsModal.byol.checkbox.error',
                      'You must certify that you have a valid Windows license.',
                    ),
                  );
                }
              },
            },
          ]}
        >
          <Checkbox
            value="isConditionAccepted"
            onChange={() => setIsConditionAccepted(!isConditionAccepted)}
            data-test-id="select-os-modal-byol-checkbox"
          >
            {t(
              'purchaseOverview.editOsModal.byol.checkbox.label',
              'I hereby certify that I’m in possession of a valuable Windows license, and that I will register my Windows 10 Enterprise license during the first boot.',
            )}
          </Checkbox>
        </Form.Item>
      )}

      <Form.Item>
        <Alert
          type={NotificationState.WARNING}
          showIcon={isByol}
          message={
            isByol
              ? t('purchaseOverview.editOsModal.byol.title', 'Warning')
              : null
          }
          description={
            isByol ? (
              <Trans i18nKey={'purchaseOverview.editOsModal.byol.alert'}>
                The “Bring my own Windows license” option requires that you
                already have a valid license key for the operating system you
                select. Audits can be performed by Microsoft and you may be
                responsible if you have not activated your Windows correctly. If
                you have any questions about Windows licensing, you can contact
                our Sales Team at{' '}
                <Link small href="mailto:<EMAIL>">
                  <strong><EMAIL></strong>
                </Link>
                <br />
                <Link small href="https://shdw.me/HC-B2B-BYOL">
                  Learn more at.
                </Link>
              </Trans>
            ) : (
              <>
                {t(
                  'purchaseOverview.editOsModal.shadowLicense.alert',
                  'The license usage must accurately reflect your actual usage, as it may be audited by Windows.',
                )}
                <br />
                {t(
                  `purchaseOverview.editOsModal.product.${osLicense}.alertMessage`,
                )}
              </>
            )
          }
        />
      </Form.Item>
      <Flex vertical gap="middle">
        <Button
          data-testid="editDatacenterModal-add-button"
          disabled={osLicense === currentOsLicense}
          block
          htmlType="submit"
        >
          {t('global.ok', 'Ok')}
        </Button>
        <Button
          data-testid="cancel-drive-modal-cancel-button"
          onClick={() => onClose()}
          tertiary
          block
        >
          {t('global.cancel', 'Cancel')}
        </Button>
      </Flex>
    </Form>
  );
};
