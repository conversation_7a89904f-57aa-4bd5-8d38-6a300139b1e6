import { CheckOutlined } from '@ant-design/icons';
import { Flex, Form, Select } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IDatacenter, SelectOptionNew } from 'types';
import { getDatacenterName } from 'utils';

import { Button } from '~/components/ui/Button/Button';
import { Flag } from '~/components/ui/Flag';
import { Title } from '~/components/ui/Typography/Typography';
import useStore from '~/store';

interface EditDatacenterPayload {
  datacenter: string;
}
interface Props {
  datacenterList: IDatacenter[];
  currentDatacenterName: string;
  withoutCancelButton?: boolean;
  onClose: () => void;
}

export const EditDatacenterForm = ({
  datacenterList,
  currentDatacenterName,
  onClose,
}: Props) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { setDatacenterName } = useStore();
  const [datacenterValue, setDatacenterValue] = useState(currentDatacenterName);

  const initialValues = { datacenter: currentDatacenterName };

  const handleSubmit = (values: EditDatacenterPayload) => {
    setDatacenterValue(values.datacenter);
    setDatacenterName(values.datacenter);
    onClose();
  };

  const datacenterOptions: SelectOptionNew[] = datacenterList.map(dc => ({
    value: dc.name,
    label: dc.country ? (
      <Flex align="center" gap="small" justify="space-between">
        <Flag
          flex={true}
          value={getDatacenterName(dc)}
          flag={dc.country.toUpperCase() as keyof typeof Flags}
        />
        {dc.name === currentDatacenterName && (
          <Title level={3} primary>
            <CheckOutlined />
          </Title>
        )}
      </Flex>
    ) : (
      getDatacenterName(dc)
    ),
    ...{ 'data-testid': `editDatacenterModal-${dc.name}-option` },
  }));

  return (
    <Form form={form} onFinish={handleSubmit} initialValues={initialValues}>
      <Form.Item
        name="datacenter"
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Select
          data-test-id="editDatacenterModal-select"
          options={datacenterOptions}
          onChange={values => setDatacenterValue(values)}
          size="large"
        />
      </Form.Item>
      <Flex vertical gap="middle">
        <Button
          data-testid="editDatacenterModal-add-button"
          disabled={datacenterValue === currentDatacenterName}
          block
          htmlType="submit"
        >
          {t('editDatacenterModal.confirmAction', 'Confirm')}
        </Button>
        <Button
          data-testid="editDatacenterModal-cancel-button"
          onClick={() => onClose()}
          tertiary
          block
        >
          {t('global.cancel')}
        </Button>
      </Flex>
    </Form>
  );
};
