import {
  Checkbox,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Row,
} from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import type {
  IUpdateBillingDetailsPayload,
  Market,
  ProductTarget,
} from 'types';
import { ApiErrorCodes, B2bProductTarget, B2cProductTarget } from 'types';
import {
  FORM_VALIDATION_COMPANY_MAX_LENGTH,
  FORM_VALIDATION_COMPANY_MIN_LENGTH,
  FORM_VALIDATION_MIN_AGE,
  FORM_VALIDATION_TAX_NUMBER_MAX_LENGTH,
  FORM_VALIDATION_TAX_NUMBER_MIN_LENGTH,
  FORM_VALIDATION_ZIPCODE_MAX_LENGTH,
  FORM_VALIDATION_ZIPCODE_MIN_LENGTH,
  HIDDEN_VAT_NUMBER_MARKETS,
  subYears,
} from 'utils';

import { Flag } from '~/components/ui/Flag';
import { CalendarIcon } from '~/components/ui/Icon';
import { LocalLoader } from '~/components/ui/Loader';
import { Text } from '~/components/ui/Typography/Typography';
import { useFormContext } from '~/contexts/FormContext';
import {
  useBillingDetails,
  useCurrentMember,
  useUpdateBillingDetails,
} from '~/hooks/reactQuery/user/useUser';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { CheckoutPayload } from '~/types/purchase';
import { MARKETS_OPTIONS } from '~/utils/constants';

interface Props {
  isUserMarketDifferentFromCurrentMarket?: boolean;
  onSuccess: (payload?: Partial<CheckoutPayload>) => void;
}

export const BillingDetailsForm = ({
  isUserMarketDifferentFromCurrentMarket = false,
  onSuccess,
}: Props) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { country } = useLocale();
  const { userTarget } = useStore();
  const billingDetailsQuery = useBillingDetails();
  const billingDetails = billingDetailsQuery.data;
  const currentMemberQuery = useCurrentMember();
  const { mutateAsync, isLoading } = useUpdateBillingDetails();

  const displayB2BInputs = Form.useWatch('b2b', form);
  const formContext = useFormContext();

  const isB2bUser =
    !!currentMemberQuery.data?.user?.b2b &&
    (!!currentMemberQuery.data?.user?.first_name ||
      !!currentMemberQuery.data?.user?.last_name);

  const getCountryName = (currentMarket: Market) => {
    return MARKETS_OPTIONS.find(
      marketOption => marketOption.value === currentMarket.toLowerCase(),
    )?.label;
  };

  const getMarket = () => billingDetails?.country?.toLowerCase() ?? country;

  const formData = {
    first_name: '',
    last_name: '',
    country: getCountryName(country),
    phone_number: '',
    birthdate: new Date(),
    b2b: false,
    address1: '',
    city: '',
    zipcode: '',
    company: '',
    vat_number: '',
  };

  const handleSubmit = (values: IUpdateBillingDetailsPayload) => {
    mutateAsync({
      first_name: values.first_name,
      last_name: values.last_name,
      country: country,
      phone_number: values.phone_number,
      birthdate: dayjs(values.birthdate).format('YYYY-MM-DD'),
      address1: values.address1,
      city: values.city,
      zipcode: values.zipcode,
      b2b: values.b2b ?? undefined,
      company: values.b2b ? values.company : '',
      vat_number: values.b2b ? values.vat_number : '',
    })
      .then(() => {
        onSuccess();
      })
      .catch(e => {
        switch (e) {
          case ApiErrorCodes.INVALID_ZIPCODE:
            message.error(
              t(
                'errors.api.billingDetails.invalidZipcode',
                'invalid zip code for this country',
              ),
            );
            break;
          default:
            message.error(
              t(
                'errors.api.billingDetails',
                'An error occurred saving your billing information, please try again later.',
              ),
            );
        }
      })
      .finally();
  };

  useEffect(() => {
    if (formContext) {
      formContext.setForm('billing_details_form');

      return () => {
        formContext.setForm(null);
      };
    }
  }, []);

  useEffect(() => {
    if (billingDetailsQuery.isSuccess && currentMemberQuery.isSuccess) {
      if (
        isEmpty(billingDetailsQuery.data) &&
        (currentMemberQuery.data?.user?.first_name ||
          currentMemberQuery.data?.user?.last_name)
      ) {
        // Social login case, if the user has an account with pre-filled first or last name
        const prefilledFormData = {
          first_name: currentMemberQuery.data?.user?.first_name,
          last_name: currentMemberQuery.data?.user?.last_name,
        };

        form.resetFields([prefilledFormData]);
      } else if (!isEmpty(billingDetailsQuery.data)) {
        const updatedFormData = {
          ...billingDetailsQuery.data,
          birthdate: new Date(billingDetailsQuery.data?.birthdate),
          phone_number: billingDetailsQuery.data?.phone_number ?? '',
          country: getCountryName(billingDetailsQuery.data?.country as Market),
        };
        form.resetFields([updatedFormData]);
      }
    }
  }, [
    billingDetailsQuery.isSuccess,
    form,
    billingDetailsQuery.data,
    currentMemberQuery.isSuccess,
    currentMemberQuery.data,
  ]);

  useEffect(() => {
    if (billingDetailsQuery.isError) {
      message.error(billingDetailsQuery.error.message);
    }
  }, [billingDetailsQuery.isError, billingDetailsQuery.error]);

  // We disable billing address inputs if user market is different from current market
  const disableBillingAddressInput = isUserMarketDifferentFromCurrentMarket;

  const shouldDisplayVatField = !HIDDEN_VAT_NUMBER_MARKETS.includes(country);

  const disableB2bInfoInput =
    !isB2bUser &&
    ![B2bProductTarget.B2B, B2cProductTarget.MAKERS].includes(
      userTarget as ProductTarget,
    );

  if (billingDetailsQuery.isLoading) {
    return <LocalLoader />;
  }

  const initialValues = {
    ...formData,
    ...billingDetails,
    birthdate: billingDetails?.birthdate
      ? dayjs(billingDetails?.birthdate)
      : '',
  };

  return (
    <Form
      form={form}
      id="billing_details_form"
      disabled={isLoading}
      initialValues={initialValues}
      onFinish={handleSubmit}
    >
      <Row gutter={24}>
        <Col xs={24} sm={24} md={12}>
          <Form.Item
            name="first_name"
            rules={[
              {
                required: true,
                message: t(
                  'billingDetails.form.firstName.error',
                  'First name is required',
                ),
              },
            ]}
          >
            <Input
              id="billingDetails-firstname-input"
              data-test-id="billingDetails-firstname-input"
              placeholder={t(
                'billingDetails.form.firstName.label',
                'First name',
              )}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={24} md={12}>
          <Form.Item
            name="last_name"
            rules={[
              {
                required: true,
                message: t(
                  'billingDetails.form.lastName.error',
                  'Last name is required',
                ),
              },
            ]}
          >
            <Input
              id="billingDetails-lastname-input"
              data-test-id="billingDetails-lastname-input"
              placeholder={t('billingDetails.form.lastName.label', 'Last name')}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col xs={24} sm={24} md={12}>
          <Form.Item
            name="birthdate"
            rules={[
              { type: 'date' },
              {
                required: true,
                message: t(
                  'billingDetails.form.birthday.error',
                  'Birthdate is required',
                ),
              },
              {
                validator: (_, value: Date) => {
                  if (value < subYears(FORM_VALIDATION_MIN_AGE)) {
                    return Promise.resolve();
                  } else {
                    return Promise.reject(
                      t(
                        'billingDetails.form.birthday.invalid',
                        'You must be 15 or older',
                      ),
                    );
                  }
                },
              },
            ]}
          >
            <DatePicker
              id="billingDetails-birthday-input"
              data-test-id="billingDetails-birthday-input"
              placeholder={t('billingDetails.form.birthday.label', 'Birthday')}
              format={{
                format:
                  country.toLowerCase() === 'us' ||
                  country.toLowerCase() === 'ca'
                    ? 'MM/DD/YYYY'
                    : 'DD/MM/YYYY',
                type: 'mask',
              }}
              showNow={false}
              style={{
                width: '100%',
                borderRadius: '8px',
              }}
              suffixIcon={<CalendarIcon style={{ fontSize: '22px' }} />}
              allowClear={false}
              // TODO : if at one time we want to limit the datepicker windows to a minimum of 15 years ago
              // make the date picker open to current date minimum 15 years ago
              /*defaultPickerValue={dayjs(
                billingDetails?.birthdate ||
                  dayjs(subYears(FORM_VALIDATION_MIN_AGE)),
              )}*/
              /*disabledDate={current =>
                // disable date selection AFTER today
                (current && current > dayjs().endOf('day')) ||
                // disable date selection if not minimum 15 Years old
                (current && current > dayjs(subYears(FORM_VALIDATION_MIN_AGE)))
              }*/
              disabledDate={current =>
                // disable date selection AFTER today
                current && current > dayjs().endOf('day')
              }
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col xs={24} sm={24} md={12}>
          <Form.Item
            name="address1"
            rules={[
              {
                required: true,
                message: t(
                  'billingDetails.form.address.error',
                  'Billing address is required',
                ),
              },
            ]}
          >
            <Input
              id="billingDetails-address-input"
              data-test-id="billingDetails-address-input"
              disabled={disableBillingAddressInput || isLoading}
              placeholder={t(
                'billingDetails.form.address.label',
                'Billing address',
              )}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={24} md={12}>
          <Form.Item
            name="zipcode"
            rules={[
              {
                required: true,
                message: t(
                  t('billingDetails.form.zipcode.error', 'Zipcode is required'),
                ),
              },
              { min: FORM_VALIDATION_ZIPCODE_MIN_LENGTH },
              { max: FORM_VALIDATION_ZIPCODE_MAX_LENGTH },
            ]}
          >
            <Input
              id="billingDetails-zipcode-input"
              data-test-id="billingDetails-zipcode-input"
              disabled={disableBillingAddressInput || isLoading}
              placeholder={t('billingDetails.form.zipcode.label', 'Zipcode')}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col xs={24} sm={24} md={12}>
          <Form.Item
            name="city"
            rules={[
              {
                required: true,
                message: t(
                  'billingDetails.form.city.error',
                  'City is required',
                ),
              },
            ]}
          >
            <Input
              id="billingDetails-city-input"
              data-test-id="billingDetails-city-input"
              disabled={disableBillingAddressInput || isLoading}
              placeholder={t('billingDetails.form.city.label', 'City')}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={24} md={12}>
          <Form.Item name="country">
            <Input
              id="billingDetails-country-input"
              data-test-id="billingDetails-country-input"
              disabled
              placeholder={getCountryName(country)}
              prefix={
                <Flag flag={getMarket().toUpperCase() as keyof typeof Flags} />
              }
            />
          </Form.Item>
        </Col>
      </Row>
      {!disableB2bInfoInput && (
        <>
          <Form.Item name="b2b" valuePropName="checked">
            <Flex vertical gap="middle">
              <Checkbox defaultChecked={isB2bUser}>
                <Text>
                  {t('billingDetails.form.isCompany.label', 'I am a company')}
                </Text>
              </Checkbox>
              <Text>
                {t(
                  'billingDetails.form.isCompany.subLabel',
                  'These details will be indicated on your invoice and used to calculate taxes.',
                )}
              </Text>
            </Flex>
          </Form.Item>
          {displayB2BInputs && (
            <Row gutter={24}>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  name="company"
                  rules={[
                    {
                      required: true,
                      message: t(
                        'form.companyName.error.required',
                        'Company name is required',
                      ),
                    },
                    {
                      min: FORM_VALIDATION_COMPANY_MIN_LENGTH,
                      message: t('form.companyName.error.minLength', {
                        defaultValue:
                          'Company name must be at least {{minLength}} characters',
                        minLength: FORM_VALIDATION_COMPANY_MIN_LENGTH,
                      }),
                    },
                    {
                      max: FORM_VALIDATION_COMPANY_MAX_LENGTH,
                      message: t('form.companyName.error.maxLength', {
                        defaultValue:
                          'Company name must be at most {{maxLength}} characters',
                        maxLength: FORM_VALIDATION_COMPANY_MAX_LENGTH,
                      }),
                    },
                  ]}
                >
                  <Input
                    id="billingDetails-company-input"
                    data-test-id="billingDetails-company-input"
                    placeholder={t(
                      'billingDetails.form.company.label',
                      'Company name',
                    )}
                  />
                </Form.Item>
              </Col>
              {shouldDisplayVatField && (
                <Col xs={24} sm={24} md={12}>
                  <Form.Item
                    name="vat_number"
                    rules={[
                      {
                        min: FORM_VALIDATION_TAX_NUMBER_MIN_LENGTH,
                        message: t('form.vatNumber.error.minLength', {
                          defaultValue:
                            'VAT number must be at least {{minLength}} characters',
                          minLength: FORM_VALIDATION_TAX_NUMBER_MIN_LENGTH,
                        }),
                      },
                      {
                        max: FORM_VALIDATION_TAX_NUMBER_MAX_LENGTH,
                        message: t('form.vatNumber.error.maxLength', {
                          defaultValue:
                            'VAT number must be at most {{maxLength}} characters',
                          maxLength: FORM_VALIDATION_TAX_NUMBER_MAX_LENGTH,
                        }),
                      },
                      {
                        pattern: /^[a-zA-Z0-9]+$/,
                        message: t(
                          'form.vatNumber.error.wrongFormat',
                          'VAT number must contain only alphanumerical characters',
                        ),
                      },
                    ]}
                  >
                    <Input
                      value={''}
                      id="vat_number-input"
                      data-test-id="company-input"
                      placeholder={t('form.vatNumber.label', 'VAT number')}
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          )}
        </>
      )}
    </Form>
  );
};
