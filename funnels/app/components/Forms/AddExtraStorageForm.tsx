import { CheckOutlined } from '@ant-design/icons';
import { Flex, Form, Select } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Currency, IAnyOffer, OfferPeriodicity, SelectOptionNew } from 'types';

import { Button } from '~/components/ui/Button/Button';
import { HardDriveOfferIcon } from '~/components/ui/Icon';
import { Text, Title } from '~/components/ui/Typography/Typography';
import useStore from '~/store';
import { getOfferInCart } from '~/utils/cart';
import { getInfoFromPeriodicity } from '~/utils/catalog';
import { formatPrice } from '~/utils/price';

interface AddExtraStoragePayload {
  extraStorageQuantity: number;
}

export interface Props {
  extraStorageAddonOffer: IAnyOffer;
  offer: IAnyOffer;
  currentQuantity: number;
  price: number;
  periodicity: OfferPeriodicity;
  currency: Currency;
  onClose: () => void;
}

export const AddExtraStorageForm = ({
  extraStorageAddonOffer,
  price,
  periodicity,
  currency,
  currentQuantity,
  offer,
  onClose,
}: Props) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [period, periodUnit] = getInfoFromPeriodicity(periodicity);
  const [quantity, setQuantity] = useState(currentQuantity);

  const { setOfferToCart, removeOfferFromCart } = useStore();

  const handleSubmit = (values: AddExtraStoragePayload) => {
    if (values.extraStorageQuantity) {
      const newStorageOffer = getOfferInCart(
        extraStorageAddonOffer,
        offer.id,
        values.extraStorageQuantity,
      );
      setOfferToCart(newStorageOffer);
    } // if extra storage quantity is 0, remove it from cart
    else {
      removeOfferFromCart(extraStorageAddonOffer.id);
    }
    onClose();
  };

  const extraStorageOptions: SelectOptionNew[] = [0, 1, 2, 3, 4].map(count => ({
    value: count,
    label: (
      <Flex align="center" gap="small" justify="space-between">
        <Flex align="center" gap="small">
          <HardDriveOfferIcon style={{ fontSize: '24px' }} />
          <Text>
            {`+${t('global.storage', '{{size, storageUnit}}', {
              size: count * 256,
            })}`}
          </Text>
        </Flex>
        {(count > 0 || count === currentQuantity) && (
          <Flex
            data-testid={`addExtraStorageModal-${count}-option-price`}
            align="center"
            gap="small"
          >
            {count > 0 && (
              <>
                <Text primary strong>
                  +{formatPrice(price * count, currency)}
                </Text>
                <Text small primary>
                  {t(`periodicity.adjective.${periodUnit}`, {
                    count: period,
                  })}
                </Text>
              </>
            )}
            {count === currentQuantity && (
              <Title level={3} primary>
                <CheckOutlined />
              </Title>
            )}
          </Flex>
        )}
      </Flex>
    ),
    ...{ 'data-testid': `addExtraStorageModal-${count}-option` },
  }));

  return (
    <Form
      form={form}
      onFinish={handleSubmit}
      initialValues={{ extraStorageQuantity: currentQuantity }}
    >
      <Form.Item
        name="extraStorageQuantity"
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Select
          data-test-id="ddExtraStorageModal-select"
          options={extraStorageOptions}
          onChange={values => setQuantity(values)}
          size="large"
        />
      </Form.Item>
      <Flex vertical gap="middle">
        <Button
          data-testid="addExtraStorageModal-add-button"
          disabled={quantity === currentQuantity}
          block
          htmlType="submit"
        >
          {t('purchaseOverview.addStorageModal.addAction', 'Add')}
        </Button>
        <Button
          data-testid="addExtraStorageModal-cancel-buttonn"
          onClick={() => onClose()}
          tertiary
          block
        >
          {t('global.cancel')}
        </Button>
      </Flex>
    </Form>
  );
};
