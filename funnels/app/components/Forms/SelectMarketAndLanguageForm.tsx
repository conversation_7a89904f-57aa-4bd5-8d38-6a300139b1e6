import { Col, Form, Row, Select } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useTranslation } from 'react-i18next';
import { Language, Locale, Market } from 'types';
import { MARKETS_OPTIONS } from 'utils';

import { Flag } from '~/components/ui/Flag';
import { useLocale } from '~/hooks/useLocale';
import {
  LANGUAGES_PER_MARKET,
  LANGUAGES_WITH_FLAG_OPTIONS,
} from '~/utils/constants';

interface Props {
  currentMarket: Market;
  currentLanguage: any;
  setCurrentMarket: any;
  setCurrentLanguage: any;
}
export const SelectLanguagePopover = ({
  currentMarket,
  setCurrentMarket,
  currentLanguage,
  setCurrentLanguage,
}: Props) => {
  const { t } = useTranslation();
  const { resetAfterMarketChange, switchLocale } = useLocale();

  const onChangeMarket = (value: Market) => {
    setCurrentMarket(value);
    resetAfterMarketChange(value);

    // We need to reset the language to the default one for the new market after each change
    const defaultLanguageForNewMarket =
      LANGUAGES_PER_MARKET[value][0] ?? Language.EN;
    setCurrentLanguage(defaultLanguageForNewMarket);
    form.setFieldsValue({ language: defaultLanguageForNewMarket });
  };

  const changeLanguage = (value: Locale) => {
    const languageValue = value;
    const locale = `${languageValue.toUpperCase()}_${currentMarket.toUpperCase()}`;

    setCurrentLanguage(languageValue);
    switchLocale(Locale[locale as keyof typeof Locale]);
  };

  const marketLanguages = LANGUAGES_PER_MARKET[currentMarket];

  const marketOptions = MARKETS_OPTIONS.map(market => ({
    value: market.value,
    label: market.label ? (
      <Flag
        flex={true}
        value={market.label}
        flag={market.value.toUpperCase() as keyof typeof Flags}
      />
    ) : (
      market.label
    ),
    ...{ 'data-testid': `header-market-select-${market?.value}` },
  }));

  const languageOptions = marketLanguages.map(language => {
    const associatedLanguages = LANGUAGES_WITH_FLAG_OPTIONS.find(
      (item: any) => item.value === language,
    );

    return {
      value: associatedLanguages?.value,
      label: associatedLanguages?.label ? (
        <Flag
          flex={true}
          value={associatedLanguages.label}
          flag={associatedLanguages.flag?.toUpperCase() as keyof typeof Flags}
        />
      ) : (
        associatedLanguages?.label
      ),
      ...{
        'data-testid': `header-language-select-${associatedLanguages?.value}`,
      },
    };
  });

  const [form] = Form.useForm();

  const initialValues = {
    market: currentMarket,
    language: currentLanguage,
  };

  return (
    <Form form={form} initialValues={initialValues} layout="vertical">
      <Row gutter={24}>
        <Col xs={24} sm={24} md={marketLanguages.length !== 1 ? 12 : 24}>
          <Form.Item
            name="market"
            label={t('header.selectMarket.title', 'Country')}
          >
            <Select
              data-testid="header-market-select"
              size="large"
              onChange={onChangeMarket}
              options={marketOptions}
            />
          </Form.Item>
        </Col>
        {marketLanguages.length !== 1 && (
          <Col xs={24} sm={24} md={marketLanguages.length !== 1 ? 12 : 24}>
            <Form.Item
              name="language"
              label={t('form.language.label', 'Language')}
            >
              <Select
                data-testid="header-language-select"
                size="large"
                onChange={changeLanguage}
                options={languageOptions}
                value={currentLanguage}
              />
            </Form.Item>
          </Col>
        )}
      </Row>
    </Form>
  );
};
