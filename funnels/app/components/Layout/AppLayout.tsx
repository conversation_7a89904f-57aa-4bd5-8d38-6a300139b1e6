import { Layout } from 'antd';
import { ReactNode } from 'react';

import useStyles from '~/components/Layout/AppLayout.styles';
import { Footer } from '~/components/Layout/Footer/Footer';
import { Header } from '~/components/Layout/Header/Header';
import { useAutoSetReferralCode } from '~/hooks/cart/useAutoSetReferralCode';

interface Props {
  children: ReactNode;
  isB2b?: boolean;
  isHeaderTransparent?: boolean;
  burgerMenu?: ReactNode;
}

export const AppLayout = ({ children, burgerMenu }: Props) => {
  const { styles } = useStyles();
  useAutoSetReferralCode();

  return (
    <Layout className={styles.layout}>
      <Header burgerMenu={burgerMenu} />
      <main className={styles.layout__content}>
        <div className={styles.layout__wrapper}>{children}</div>
      </main>
      <Footer />
    </Layout>
  );
};
