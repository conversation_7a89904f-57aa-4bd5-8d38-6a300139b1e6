import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    header__container: css`
      display: flex;
      flex-direction: column;
      position: relative;
      display: flex;
      align-items: center;
      z-index: 1;
      background: ${token.colorBgBase};
      border-bottom: 1px solid;
      border-image-slice: 1;
      border-image-source: linear-gradient(
        to right,
        ${token.colorBgBase},
        ${token.colorPrimary} 50%,
        ${token.colorBgBase}
      );

      padding: 0 ${token.paddingMD}px;

      @media (min-width: ${token.screenLG}px) {
        padding: 0 ${token.paddingXL}px;
      }
    `,
    header__wrapper: css`
      display: flex;
      justify-content: space-between;
      max-width: 1248px;
      height: 50px;
      width: 100%;
      align-items: center;
      gap: 8px;

      @media (min-width: ${token.screenMD}px) {
      }

      @media (min-width: ${token.screenLG}px) {
        height: 60px;
      }
    `,

    header__links: css`
      display: flex;
      align-items: center;
      flex: 1;
      gap: 8px;
      justify-content: flex-end;

      @media (min-width: ${token.screenMD}px) {
        gap: 32px;
      }
    `,
  };
});
