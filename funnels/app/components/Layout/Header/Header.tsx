import { Flex, Grid } from 'antd';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { SVG_PATH } from 'utils';

import useStyles from '~/components/Layout/Header/Header.style';
import { HeaderAuthenticated } from '~/components/Layout/Header/HeaderAuthenticated';
import { SelectMarketAndLanguage } from '~/components/Layout/Header/SelectMarketAndLanguage/SelectMarketAndLanguage';
import { LANDING_URL } from '~/utils/constants';

interface Props {
  burgerMenu?: ReactNode;
}

export const Header = ({ burgerMenu }: Props) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  return (
    <>
      {/* TODO: Uncomment when the top banner is ready */}
      {/* <TopBanner /> */}
      <div className={styles.header__container}>
        <div className={styles.header__wrapper}>
          <a
            data-test-id="header-logo-link"
            href={LANDING_URL}
            target="_blank"
            title={t('header.logoAlt', 'Shadow')}
          >
            <img
              src={`${SVG_PATH}logos/shadow-gradient.svg`}
              alt={t('header.logoAlt', 'Shadow')}
              width={screens.sm ? '80px' : '65px'}
              height={screens.sm ? '18px' : '15px'}
            />
          </a>
          <Flex gap="small" align="center">
            <HeaderAuthenticated />
            <SelectMarketAndLanguage />
          </Flex>
          {burgerMenu}
        </div>
      </div>
    </>
  );
};
