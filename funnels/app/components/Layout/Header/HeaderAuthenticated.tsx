import { Flex, Grid } from 'antd';
import { useTranslation } from 'react-i18next';

import { Button } from '~/components/ui/Button/Button';
import { Text } from '~/components/ui/Typography/Typography';
import { useAuthentication } from '~/hooks/useAuthentication';
import useStore from '~/store';

export const HeaderAuthenticated = () => {
  const { t } = useTranslation();
  const auth = useAuthentication();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const { resetState } = useStore();

  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <Flex align="center" gap="small">
      {auth.user?.profile.name && screens.md && (
        <Text>
          {t('header.login.welcome.label', 'Welcome')} {auth.user?.profile.name}
        </Text>
      )}
      <Button
        data-testid="header-logout-link"
        variant="link"
        size="small"
        color="default"
        shape={undefined}
        onClick={() => {
          resetState();
          void auth.signoutRedirect();
        }}
        iconPosition="end"
        title={t('header.link.logout', 'Logout')}
      >
        ({t('header.link.logout', 'Logout')})
      </Button>
    </Flex>
  );
};
