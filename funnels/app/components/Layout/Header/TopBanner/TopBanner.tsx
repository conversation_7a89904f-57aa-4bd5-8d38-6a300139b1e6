import { CloseOutlined } from '@ant-design/icons';
import { Flex } from 'antd';
import { useState } from 'react';
import { useCookies } from 'react-cookie';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from '~/components/Layout/Header/TopBanner/TopBanner.styles';
import { Text } from '~/components/ui/Typography/Typography';
import { TOP_BANNER_COOKIE_NAME } from '~/utils/constants';

export const TopBanner = () => {
  const { t } = useTranslation();
  const { styles } = useStyles();

  const [cookies, setCookies] = useCookies([TOP_BANNER_COOKIE_NAME]);
  const isClosable = false;

  const [isTopBannerActive, setIsTopBannerActive] = useState(
    !cookies.top_banner_disabled,
  );

  const closeTopBanner = () => {
    setIsTopBannerActive(false);

    if (!cookies.top_banner_disabled) {
      setCookies(TOP_BANNER_COOKIE_NAME, true);
    }
  };

  if (!isTopBannerActive) {
    return null;
  }

  return (
    <Flex className={styles.banner__wrapper}>
      <Flex
        justify="space-between"
        gap="middle"
        className={styles.banner__content}
      >
        <Flex vertical gap="small">
          <Flex align="center" gap="middle">
            <img src={t('topBanner.logo_url')} />
            <Text>
              <Trans
                i18nKey="topBanner.label"
                components={{ bold: <strong /> }}
              />
            </Text>
          </Flex>
          <Text>
            <Trans
              i18nKey="topBanner.sublabel"
              components={{ bold: <strong /> }}
            />
          </Text>
        </Flex>
        {isClosable && <CloseOutlined onClick={closeTopBanner} />}
        <CloseOutlined onClick={closeTopBanner} />
      </Flex>
    </Flex>
  );
};
