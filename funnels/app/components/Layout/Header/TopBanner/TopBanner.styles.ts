import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    banner__wrapper: css`
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 10;
      width: 100%;
      min-height: 80px;
      padding: 20px 24px;
      background-color: ${token.colorBorderSecondary};
    `,

    banner__content: css`
      display: flex;
      align-items: center;
      gap: 16px;
      max-width: 1248px;
      height: 100%;
      margin: 0px auto;
      width: 100%;
    `,
  };
});
