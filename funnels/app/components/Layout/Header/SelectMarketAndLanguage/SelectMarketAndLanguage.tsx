import { DownOutlined } from '@ant-design/icons';
import { Grid, Popover } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useEffect, useState } from 'react';

import { SelectLanguagePopover } from '~/components/Forms/SelectMarketAndLanguageForm';
import useStyles from '~/components/Layout/Header/SelectMarketAndLanguage/SelectMarketAndLanguage.styles';
import { Button } from '~/components/ui/Button/Button';
import { Flag } from '~/components/ui/Flag';
import { useAuthentication } from '~/hooks/useAuthentication';
import { useLocale } from '~/hooks/useLocale';
import {
  LANGUAGES_OPTIONS,
  LANGUAGES_PER_MARKET,
  MARKETS_OPTIONS,
} from '~/utils/constants';

export const SelectMarketAndLanguage = () => {
  const { language, country } = useLocale();
  const auth = useAuthentication();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  useEffect(() => {
    setCurrentMarket(country);
    setCurrentLanguage(language);
  }, [country, language]);

  const [currentMarket, setCurrentMarket] = useState(country);
  const [currentLanguage, setCurrentLanguage] = useState(language);
  const { styles } = useStyles({
    onlyMarket: LANGUAGES_PER_MARKET[currentMarket].length === 1,
  });

  const activeLanguage = LANGUAGES_OPTIONS?.find(
    item => item.value === currentLanguage,
  );

  const activeCountry = MARKETS_OPTIONS?.find(
    item => item.value === currentMarket,
  );

  if (auth.isAuthenticated) {
    return null;
  }

  return (
    <Popover
      rootClassName={styles.language__popover}
      content={
        <SelectLanguagePopover
          currentMarket={currentMarket}
          setCurrentLanguage={setCurrentLanguage}
          currentLanguage={currentLanguage}
          setCurrentMarket={setCurrentMarket}
        />
      }
      placement="bottomRight"
      trigger="click"
      arrow={false}
    >
      <Button
        data-testid="header-languageAndMarket-button"
        type="text"
        icon={<DownOutlined style={{ fontSize: '10px' }} />}
        iconPosition="end"
      >
        <Flag
          flex={true}
          value={
            screens.sm
              ? `${activeLanguage?.label} (${activeCountry?.label})`
              : ''
          }
          flag={currentMarket?.toUpperCase() as keyof typeof Flags}
        />
      </Button>
    </Popover>
  );
};
