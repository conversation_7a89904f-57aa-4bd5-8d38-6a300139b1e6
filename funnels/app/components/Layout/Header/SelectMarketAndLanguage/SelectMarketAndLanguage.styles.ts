import { createStyles } from 'antd-style';

export default createStyles(
  (
    { token, css },
    props: {
      onlyMarket: boolean;
    },
  ) => {
    return {
      language__popover: css`
        width: 100%;

        background-color: none;
        max-width: 90vw;

        &.ant-popover .ant-popover-inner {
          padding: ${token.sizeLG}px ${token.sizeLG}px 0;
        }

        @media (min-width: ${token.screenSM}px) {
          max-width: ${props.onlyMarket ? '300px' : '560px'};
        }
      `,
    };
  },
);
