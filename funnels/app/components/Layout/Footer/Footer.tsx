import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import useStyles from '~/components/Layout/Footer/Footer.styles';
import { Link } from '~/components/ui/Typography/Typography';
import { useUrl } from '~/hooks/useUrl';
import {
  COOKIES_URL,
  LANDING_URL,
  LEGAL_URL,
  PRIVACY_POLICY_URL,
} from '~/utils/constants';

export const Footer = () => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const { getCguUrl } = useUrl();

  const footerListItems = useMemo(() => {
    return [
      {
        id: 'company',
        href: LANDING_URL,
        label: t('footer.companyName', '© Shadow'),
      },
      {
        id: 'cgu',
        href: getCguUrl(true),
        label: t('footer.cgu', 'Terms of Use'),
      },
      {
        id: 'legal',
        href: LEGAL_URL,
        label: t('footer.legal', 'Legal'),
      },
      {
        id: 'privacy',
        href: PRIVACY_POLICY_URL,
        label: t('footer.privacy', 'Privacy'),
      },
      {
        id: 'cookies',
        href: COOKIES_URL,
        label: t('footer.cookies', 'Cookies'),
      },
    ];
  }, [getCguUrl, t]);

  return (
    <footer className={styles.footer__wrapper}>
      <nav>
        <ul className={styles.footer__list}>
          {footerListItems.map((item, key) => (
            <li className={styles.footer__item} key={key}>
              <Link
                data-test-id={`footer-${item.id}-link`}
                href={item.href}
                target="_blank"
                small
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </footer>
  );
};
