import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    footer__wrapper: css`
      padding: ${token.paddingMD}px;
      background: ${token.colorBorderSecondary};

      @media (min-width: ${token.screenLG}px) {
        padding: ${token.paddingMD}px ${token.paddingLG}px;
      }
    `,

    footer__list: css`
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      column-gap: ${token.paddingSM}px;
      row-gap: ${token.paddingSM}px;
      margin: 0;

      @media (min-width: ${token.screenMD}px) {
        column-gap: ${token.paddingXL}px;
      }
    `,

    footer__item: css`
      & > a {
        display: flex;
        align-items: center;
        color: ${token.colorTextBase};

        &:hover {
          color: ${token.colorPrimary};
        }
      }
    `,
  };
});
