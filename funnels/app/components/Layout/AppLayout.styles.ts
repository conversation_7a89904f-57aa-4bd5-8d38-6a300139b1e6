import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    layout: css`
      min-height: 100vh;
    `,

    layout__content: css`
      display: flex;
      flex: auto;

      box-sizing: border-box;

      padding: ${token.paddingMD}px;

      @media (min-width: ${token.screenLG}px) {
        padding: ${token.paddingLG}px ${token.paddingXL}px;
      }
    `,

    layout__wrapper: css`
      margin: 0 auto;
      max-width: 1248px;
      width: 100%;
    `,
  };
});
