import { Flex, Menu, MenuProps } from 'antd';
import { motion } from 'motion/react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Title } from '~/components/ui/Typography/Typography';
import useStore from '~/store';
import { B2cProductTarget } from '~/types/product';

type MenuItem = Required<MenuProps>['items'][number];

interface Props {
  productTargetsKeys: any[];
  currentTab: string;
  setCurrentTab: (item: B2cProductTarget) => void;
}

export const ProductSelectionMenu = ({
  productTargetsKeys,
  currentTab,
  setCurrentTab,
}: Props) => {
  const { t } = useTranslation();
  const { setUserTarget } = useStore();

  const items: MenuItem[] = productTargetsKeys.map(item => {
    return {
      key: item,
      label: t(`catalog.changePlan.${item}.label`),
      selected: item === B2cProductTarget.STANDARD,
    };
  });

  const handleMenuClick: MenuProps['onClick'] = e => {
    // disable animation if clicked key is same as current key
    if (e.key === currentTab) {
      return null;
    }

    setCurrentTab(productTargetsKeys.find(item => item === e.key));
    setUserTarget(e.key as B2cProductTarget);
  };

  const MotionTitle = motion.create(Title);

  // force userTarget to standard
  useEffect(() => {
    setUserTarget(B2cProductTarget.STANDARD);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Flex vertical gap="large">
      <Menu
        selectedKeys={[currentTab]}
        onClick={handleMenuClick}
        mode="horizontal"
        items={items}
        style={{ background: 'transparent', justifyContent: 'center' }}
      />
      <MotionTitle
        level={4}
        centered
        initial={{ opacity: 0, y: 25 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 12.5 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
      >
        {t(`catalog.changePlan.${currentTab}.content`)}
      </MotionTitle>
    </Flex>
  );
};
