import { Flex } from 'antd';
import { useState } from 'react';

import { ProductSelectionContent } from '~/components/ProductSelection/ProductSelectionContent';
import { ProductSelectionMenu } from '~/components/ProductSelection/ProductSelectionMenu';
import { B2cProductTarget } from '~/types/product';

export const ProductSelection = () => {
  const productTargetsKeys: B2cProductTarget[] = [
    B2cProductTarget.STANDARD,
    B2cProductTarget.MAKERS,
  ];

  const [currentTab, setCurrentTab] = useState(productTargetsKeys[0]);

  return (
    <Flex vertical gap="large">
      <ProductSelectionMenu
        productTargetsKeys={productTargetsKeys}
        currentTab={currentTab as B2cProductTarget}
        setCurrentTab={setCurrentTab}
      />
      <ProductSelectionContent currentTab={currentTab as B2cProductTarget} />
    </Flex>
  );
};
