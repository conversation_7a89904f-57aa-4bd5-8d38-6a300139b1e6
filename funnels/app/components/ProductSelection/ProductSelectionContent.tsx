import { Al<PERSON>, Col, Grid, Row } from 'antd';
import { orderBy } from 'lodash';
import { useTranslation } from 'react-i18next';
import { B2cProductTarget, NotificationState } from 'types';

import { ProductSelectionCard } from '~/components/ProductSelection/ProductSelectionCard/ProductSelectionCard';
import { LocalLoader } from '~/components/ui/Loader';
import { useGetProductsForTarget } from '~/hooks/products/useGetProductsForTarget';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';

interface Props {
  currentTab: B2cProductTarget;
}

export const ProductSelectionContent = ({ currentTab }: Props) => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const catalogQuery = useCatalog();
  const catalog = catalogQuery.data;

  const { products, isLoading, isError } = useGetProductsForTarget({
    target: currentTab,
  });

  if (isLoading || !catalog) {
    return <LocalLoader />;
  }

  if (isError || catalogQuery.isError) {
    return (
      <Alert
        type={NotificationState.ERROR}
        message={t('catalog.changePlan.error.title', 'Something went wrong')}
        description={t(
          'catalog.changePlan.error.description',
          'We are unable to retrieve the list of available plans at this time.',
        )}
      />
    );
  }

  const sortedProducts = orderBy(
    products,
    [product => product.tiers_level],
    ['asc', 'desc'],
  );

  return (
    <Row justify={screens.lg ? 'center' : 'start'} gutter={[40, 40]}>
      {sortedProducts.map((product, index) => {
        return (
          <Col
            key={`col-${index}`}
            xs={24}
            sm={12}
            md={12}
            lg={sortedProducts.length > 4 ? 8 : 24 / sortedProducts.length}
            xl={sortedProducts.length > 4 ? 8 : 24 / sortedProducts.length}
          >
            <ProductSelectionCard
              catalog={catalog}
              product={product}
              index={index}
            />
          </Col>
        );
      })}
    </Row>
  );
};
