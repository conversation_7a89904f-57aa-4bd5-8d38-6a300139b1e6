import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    productCard__body: css`
      &.ant-card {
        height: 100%;
        width: 100%;
      }
      &.ant-card .ant-card-body {
        padding: 48px 0;
        height: 100%;
      }
    `,

    productCard__upper: css`
      height: 100%;
    `,

    productCard__name: css`
      border-width: ${token.lineWidth}px;
      border-style: solid;
      padding: 8px 20px;
      border-radius: 50px;
    `,

    productCard__noStock: css`
      margin: 10px 0;
    `,

    productCard__technicalSpecs: css`
      position: relative;
      width: 100%;
      flex: 1;
      align-self: start;
      border: ${token.lineWidth}px solid ${token.colorBorderSecondary};
      border-width: ${token.lineWidth}px 0 ${token.lineWidth}px;

      li {
        padding: 8px 24px;
        line-height: 16px;
        min-height: 50px;
        display: flex;
        align-items: center;
        font-weight: 400;
        color: ${token.colorTextPlaceholder};
      }

      li + li {
        border-top: ${token.lineWidth}px solid ${token.colorBorderSecondary};
      }
    `,
  };
});
