import { Flex } from 'antd';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router';
import { IAnyOffer, IAnyProduct, ICatalog } from 'types';
import {
  getOffer,
  getPrimaryStorageForPlan,
  getProductLowestPrice,
  getRamForPlan,
} from 'utils';

import useStyles from '~/components/ProductSelection/ProductSelectionCard/ProductSelectionCard.styles';
import { Button } from '~/components/ui/Button/Button';
import { Card } from '~/components/ui/Card/Card';
import { ArrowRightIcon } from '~/components/ui/Icon';
import { Title } from '~/components/ui/Typography/Typography';
import useStore from '~/store';
import { useProductStock } from '~/store/useStock';
import { formatPrice } from '~/utils/price';

interface Props {
  catalog: ICatalog;
  index: number;
  product: IAnyProduct;
}

export const ProductSelectionCard = ({ catalog, product, index }: Props) => {
  const { t } = useTranslation();
  const { userTarget } = useStore();
  const { styles } = useStyles();
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const productOffer = getOffer(catalog, product.id);
  const { isOutOfStock, outOfStockText } = useProductStock(productOffer?.stock);

  const lowestProductPrice = getProductLowestPrice(
    catalog,
    product.id,
    userTarget!,
  );

  const getTechnicalSpecs = (offer: IAnyOffer) => (
    <>
      <li>{t(`purchaseOverview.overview.product.${offer.product_id}.cpu`)}</li>
      <li>{t(`purchaseOverview.overview.product.${offer.product_id}.gpu`)}</li>
      <li>
        {t(
          `purchaseOverview.overview.cloudpc.ram`,
          '{{size, storageUnit}} RAM',
          {
            size: getRamForPlan(catalog, product.id),
          },
        )}
      </li>
      <li>
        {t(
          `purchaseOverview.overview.cloudpc.primaryStorage`,
          'C:\\ {{size, storageUnit}} SSD',
          {
            size: getPrimaryStorageForPlan(catalog, product.id),
          },
        )}
      </li>
    </>
  );

  const MotionCard = motion.create(Card);

  if (!productOffer) {
    return;
  }
  return (
    <MotionCard
      className={styles.productCard__body}
      footer={
        <ul className={styles.productCard__technicalSpecs}>
          {getTechnicalSpecs(productOffer)}
        </ul>
      }
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.7,
        delay: 0.1 + index * 0.1,
        type: 'tween',
        ease: [0.23, 1, 0.32, 1],
      }}
    >
      <Flex
        align="center"
        vertical
        gap="large"
        justify="space-between"
        className={styles.productCard__upper}
      >
        <Title level={4} className={styles.productCard__name}>
          {t(`catalog.product.bareName.${product.id}`)}
        </Title>
        <Flex align="center" vertical gap="middle">
          {lowestProductPrice ? (
            <Flex gap="small" align="center">
              <Title level={2} primary>
                {formatPrice(
                  lowestProductPrice.amount,
                  lowestProductPrice.currency,
                )}
              </Title>
              <Title level={5}>
                {` /${t('catalog.product.periodicity.month')}`}
              </Title>
            </Flex>
          ) : (
            <Title level={5}>{outOfStockText}</Title>
          )}
          <Button
            data-testid="button"
            disabled={isOutOfStock}
            icon={<ArrowRightIcon style={{ fontSize: '24px' }} />}
            iconPosition="end"
            onClick={() => navigate(`${pathname}/${product.id}`)}
          >
            {t('global.continue', 'Continue')}
          </Button>
        </Flex>
      </Flex>
    </MotionCard>
  );
};
