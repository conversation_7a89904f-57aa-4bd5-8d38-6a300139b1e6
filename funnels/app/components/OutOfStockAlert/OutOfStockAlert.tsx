import { Trans } from 'react-i18next';

import { Link } from '~/components/ui/Typography/Typography';

export const OutOfStockAlert = () => {
  const handleCancelFunnel = () => {
    // TODO : find what to do here
    // eslint-disable-next-line no-console
    console.log('Cadeau pour Luc : nous dire quoi faire ici');
  };

  return (
    <>
      <Trans i18nKey="purchaseOverview.outOfStock">
        This product is currently not available in this datacenter,{' '}
        <Link small onClick={handleCancelFunnel}>
          select an other product
        </Link>
        .
      </Trans>
    </>
  );
};
