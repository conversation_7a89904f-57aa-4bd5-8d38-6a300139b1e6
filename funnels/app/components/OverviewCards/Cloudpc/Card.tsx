import { Flex, Grid, Tooltip } from 'antd';
import { useDatacenterListForPlan } from 'hooks';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import {
  AddonType,
  Currency,
  IAddon,
  IAnyOffer,
  IAnyProduct,
  ICatalog,
  KeyboardISO,
  ProductType,
} from 'types';
import {
  WINDOWS_HOME_LICENSE,
  checkStockAvailability,
  getAddonOfferToDisplay,
  getAddons,
  getPrimaryStorageForPlan,
  getProductFromOfferId,
  getRamForPlan,
  isOfferEnabledForSale,
} from 'utils';

import { AddExtraStorageModal } from '~/components/modals/AddExtraStorageModal';
import { EditKeyboardAndLanguageModal } from '~/components/modals/EditKeyboardAndLanguageModal';
import { EditOsModal } from '~/components/modals/EditOsModal';
import {
  ConfigurationOptions,
  IConfigurationOptionProps,
} from '~/components/OverviewCards/Cloudpc/ConfigurationOptions';
import { PeriodicitySelect } from '~/components/OverviewCards/Cloudpc/PeriodicitySelect';
import { Button } from '~/components/ui/Button/Button';
import { Card } from '~/components/ui/Card/Card';
import {
  AddIcon,
  ArrowDownIcon,
  CpuOfferIcon,
  DiscIcon,
  EditIcon,
  GpuOfferIcon,
  HardDriveOfferIcon,
  InfoIcon,
  KeyboardIcon,
  MemoryOfferIcon,
  WindowsOfferIcon,
} from '~/components/ui/Icon';
import { Title } from '~/components/ui/Typography/Typography';
import { useLicense } from '~/hooks/useLicense';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { useCart } from '~/store/useCart';
import { OfferInCart } from '~/types/cart';
import {
  ADVANCED_SETTINGS_KEYBOARDS,
  ADVANCED_SETTINGS_LOCALE_PER_MARKET,
} from '~/utils/constants';

// import { useTracking } from '@/hooks/useTracking';

interface Props {
  catalog: ICatalog;
  plan: IAnyProduct;
  offer: IAnyOffer;
  offersInCart: OfferInCart[];
}

export const CloudpcOverviewCard = ({
  catalog,
  plan,
  offer,
  offersInCart,
}: Props) => {
  const { t } = useTranslation();
  // const { trackClick } = useTracking();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const { pathname } = useLocation();
  const canEditConfiguration = pathname.includes('/buy/');
  const { country, locale } = useLocale();
  const {
    advancedSettingsKeyboard: keyboard,
    advancedSettingsLanguage: language,
    setKeyboardAndLanguage,
  } = useCart();
  const [isAddStorageModalOpened, setIsAddStorageModalOpened] = useState(false);
  const [
    isEditKeyboardAndLanguageModalOpened,
    setIsEditKeyboardAndLanguageModalOpened,
  ] = useState(false);
  const [isEditOsModalOpened, setIsEditOsModalOpened] = useState(false);

  const initializedRef = useRef(false);

  const { zipcode, geolocResults, datacenterName } = useStore();
  const { data: datacentersList } = useDatacenterListForPlan({
    zipcode: zipcode ?? geolocResults?.zipcode ?? null,
    country,
    planId: plan.id,
  });

  // CART EXTRA STORAGE
  const extraStorageInCart = offersInCart.find(offerInCart => {
    if (offerInCart.type !== ProductType.ADDON) {
      return false;
    }
    const storageProduct = getProductFromOfferId(catalog, offerInCart.id);
    if (!storageProduct) {
      return false;
    }

    const metadata = (storageProduct as IAddon).meta_data;
    return metadata?.type === AddonType.STORAGE && !metadata?.isPrimary;
  });
  const extraStorageQuantity = extraStorageInCart
    ? extraStorageInCart.quantity
    : 0;
  const extraStorageSize = extraStorageQuantity
    ? extraStorageQuantity * 256
    : 0;

  // PLAN RAM AND PRIMARY STORAGE
  const planRamSize = getRamForPlan(catalog, offer.product_id);
  const planPrimaryStorageSize = getPrimaryStorageForPlan(
    catalog,
    offer.product_id,
  );

  // EXTRA STORAGE ADD MODAL
  const extraStorageAddon = getAddons(catalog, offer.product_id, (_, addon) => {
    return (
      addon.meta_data?.type === AddonType.STORAGE && !addon.meta_data.isPrimary
    );
  })?.[0];

  const extraStorageAddonOfferId = extraStorageAddon
    ? extraStorageAddon.offers.find(addonOfferId => {
        return (
          catalog.offers.byId[addonOfferId]?.periodicity === offer.periodicity
        );
      })
    : null;

  const extraStorageAddonOffer = extraStorageAddon
    ? getAddonOfferToDisplay(
        offer.id,
        extraStorageAddonOfferId,
        extraStorageAddon.id,
        catalog,
      )
    : null;

  const license = useLicense(plan, catalog, offersInCart);

  // WINDOWS HOME LICENSE is the default selected license
  const defaultLicense = license
    ? license.selectedLicense
    : WINDOWS_HOME_LICENSE;

  const currentOsProductId =
    offersInCart.find(offerInCart => {
      const addonProduct = getProductFromOfferId(catalog, offerInCart.id);
      return addonProduct?.meta_data?.type === AddonType.OS;
    })?.productId || defaultLicense;

  const handleAddExtraStorageButtonClick = () => {
    // trackClick('Extra Storage');
    setIsAddStorageModalOpened(true);
  };

  const handleEditKeyboardAndLanguageButtonClick = () => {
    // trackClick('Advanced Settings');
    setIsEditKeyboardAndLanguageModalOpened(true);
  };

  const handleAddStorageModalClose = () => {
    // trackClick('Extra Storage - Close');
    setIsAddStorageModalOpened(false);
  };

  const handleEditKeyboardAndLanguageModalClose = () => {
    // trackClick('Advanced Settings - Close');
    setIsEditKeyboardAndLanguageModalOpened(false);
  };

  const handleEditOsButtonClick = () => {
    // trackClick('Edit OS');
    setIsEditOsModalOpened(true);
  };

  const handleEditOsModalClose = () => {
    // trackClick('Edit OS - Close');
    setIsEditOsModalOpened(false);
  };

  if (!initializedRef.current) {
    if (!keyboard || !language) {
      const keyboardISOKey = locale.split('-').join('_').toUpperCase();
      setKeyboardAndLanguage(
        KeyboardISO[keyboardISOKey as keyof typeof KeyboardISO] ??
          KeyboardISO.EN_US,
        ADVANCED_SETTINGS_LOCALE_PER_MARKET[country],
      );
    }
    initializedRef.current = true;
  }

  const offerConfigurationData: IConfigurationOptionProps[] = [
    {
      icon: <CpuOfferIcon />,
      label: t(`purchaseOverview.overview.product.${offer.product_id}.cpu`),
    },
    {
      icon: <ArrowDownIcon />,
      label: t(
        `purchaseOverview.overview.product.${offer.product_id}.bandwidth`,
      ),
    },
    {
      icon: <GpuOfferIcon />,
      label: t(`purchaseOverview.overview.product.${offer.product_id}.gpu`),
    },
    {
      icon: <WindowsOfferIcon />,
      label: t(`catalog.product.name.${currentOsProductId}`),
      actionButton: canEditConfiguration && license && license.hasOsAddon && (
        <Flex align="center" gap="small">
          <Tooltip
            title={t(
              'purchaseOverview.overview.cloudpc.editLicenseTooltip',
              'The license usage must accurately reflect your actual usage, as it may be audited by Windows. Windows 10 home is made for an individual use only.',
            )}
          >
            <Title level={5} primary>
              <InfoIcon />
            </Title>
          </Tooltip>
          <Button
            data-testid="edit-license-button"
            size="small"
            secondary
            icon={<DiscIcon />}
            onClick={handleEditOsButtonClick}
            title={t(
              'purchaseOverview.overview.cloudpc.editLicense',
              'Change OS',
            )}
          >
            {screens.sm &&
              t('purchaseOverview.overview.cloudpc.editLicense', 'Change OS')}
          </Button>
        </Flex>
      ),
    },
    {
      icon: <MemoryOfferIcon />,
      label: t(
        `purchaseOverview.overview.cloudpc.ram`,
        '{{size, storageUnit}} RAM',
        {
          size: planRamSize,
        },
      ),
    },
    {
      icon: <HardDriveOfferIcon />,
      label: t(
        `purchaseOverview.overview.cloudpc.extraStorage`,
        'D:\\ {{size, storageUnit}} HDD',
        {
          size: extraStorageSize,
        },
      ),
      actionButton: canEditConfiguration &&
        extraStorageAddonOffer &&
        isOfferEnabledForSale({ offer: extraStorageAddonOffer }) &&
        checkStockAvailability({
          datacentersList,
          datacenterName,
          planOrAddonId: extraStorageAddonOffer.product_id,
          productType: ProductType.ADDON,
          productFamilyId: extraStorageAddonOffer.itemFamilyId,
        }) && (
          <>
            <Button
              data-testid="add-extra-storage-button"
              secondary
              size="small"
              icon={<AddIcon />}
              onClick={handleAddExtraStorageButtonClick}
              noWrap
              title={t(
                'purchaseOverview.overview.cloudpc.addExtraStorage',
                'add extra storage',
              )}
            >
              {screens.sm &&
                t(
                  'purchaseOverview.overview.cloudpc.addExtraStorage',
                  'add extra storage',
                )}
            </Button>
          </>
        ),
    },
    {
      icon: <HardDriveOfferIcon />,
      label: t(
        `purchaseOverview.overview.cloudpc.primaryStorage`,
        'C:\\ {{size, storageUnit}} SSD',
        {
          size: planPrimaryStorageSize,
        },
      ),
    },
    {
      icon: <KeyboardIcon />,
      label: ADVANCED_SETTINGS_KEYBOARDS[keyboard],
      actionButton: canEditConfiguration && (
        <Button
          data-testid="edit-keyboard-and-language-button"
          size="small"
          secondary
          icon={<EditIcon />}
          onClick={handleEditKeyboardAndLanguageButtonClick}
          noWrap
          title={t(
            'purchaseOverview.overview.cloudpc.editKeyboard',
            'edit language',
          )}
        >
          {screens.sm &&
            t(
              'purchaseOverview.overview.cloudpc.editKeyboard',
              'edit language',
            )}
        </Button>
      ),
    },
  ];

  return (
    <>
      <Card>
        <Flex justify="space-between" align="center" gap="large">
          <Title level={4} primary>
            {t(`catalog.product.name.${offer.product_id}`)}
          </Title>
          {canEditConfiguration && (
            <PeriodicitySelect catalog={catalog} offer={offer} plan={plan} />
          )}
        </Flex>
        <ConfigurationOptions configurationOptions={offerConfigurationData} />
      </Card>
      {extraStorageAddonOffer && (
        <AddExtraStorageModal
          extraStorageAddonOffer={extraStorageAddonOffer}
          offer={offer}
          price={extraStorageAddonOffer.price}
          periodicity={extraStorageAddonOffer.periodicity}
          currency={offer.currency_code.toLowerCase() as Currency}
          currentQuantity={extraStorageQuantity}
          isOpened={isAddStorageModalOpened}
          onClose={handleAddStorageModalClose}
        />
      )}
      {!!keyboard && !!language && (
        <EditKeyboardAndLanguageModal
          isOpened={isEditKeyboardAndLanguageModalOpened}
          currentKeyboard={keyboard}
          currentLanguage={language}
          onClose={handleEditKeyboardAndLanguageModalClose}
        />
      )}
      {!!license && license.hasOsAddon && (
        <EditOsModal
          catalog={catalog}
          currency={offer.currency_code.toLowerCase() as Currency}
          isOpened={isEditOsModalOpened}
          licenseTypeChoices={license.licenseTypeChoices}
          offer={offer}
          onClose={handleEditOsModalClose}
          selectedLicense={license.selectedLicense}
        />
      )}
    </>
  );
};
