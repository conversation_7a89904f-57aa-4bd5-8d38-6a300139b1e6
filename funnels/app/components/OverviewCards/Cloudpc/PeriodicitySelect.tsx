import { Flex, Select } from 'antd';
import { usePaginatedSubscriptions } from 'hooks';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router';
import {
  IAnyOffer,
  ICatalog,
  OfferPeriodUnit,
  ProductFamilyId,
  SelectOptionNew,
} from 'types';
import { getOffers, sortPlanOffers } from 'utils';

import { Text } from '~/components/ui/Typography/Typography';
import useStore from '~/store';
import { getOffersInCartToAddFromPlanOffer } from '~/utils/cart';

interface Props {
  catalog: ICatalog;
  offer: IAnyOffer;
  plan: any;
}

export const PeriodicitySelect = ({ catalog, offer, plan }: Props) => {
  const { t } = useTranslation();
  const { resetCartAndAddOfferToCart } = useStore();
  const [searchParams, setSearchParams] = useSearchParams();
  const periodicityParam = searchParams.get('periodicity');

  const subscriptionsQuery = usePaginatedSubscriptions(
    false,
    0,
    100,
    [],
    ProductFamilyId.CLOUDPC,
  );
  const subscriptions = subscriptionsQuery.data?.items ?? [];

  const unorderedPlanOffers = getOffers(
    catalog,
    plan.id,
    o => o.meta_data?.archived !== true,
  );
  const userAlreadyHasWeeklySub = subscriptions.some(
    subscription => subscription.billing_period_unit === OfferPeriodUnit.WEEK,
  );
  const isEligibleForWeeklyOffer = !userAlreadyHasWeeklySub;

  // Filter out weekly offers unless the user is eligible AND specifically requesting them
  const filteredPlanOffers =
    periodicityParam === '1-week' && isEligibleForWeeklyOffer
      ? unorderedPlanOffers
      : unorderedPlanOffers.filter(o => o.period_unit !== OfferPeriodUnit.WEEK);
  const planOffers = sortPlanOffers(filteredPlanOffers, 'asc');

  const isDeeplinkOnly = plan.meta_data?.deeplinkOnly;

  // Find the offer to select by default when coming from a deeplink with periodicity parameter
  const getDefaultValue = () => {
    if (periodicityParam) {
      // Handle weekly periodicity with eligibility check
      if (periodicityParam === '1-week' && !isEligibleForWeeklyOffer) {
        return offer.id;
      }

      // Parse the periodicity parameter (format: "1-month", "3-month", "1-week", etc.)
      const [periodStr = '1', unitStr] = periodicityParam.split('-');
      const period = parseInt(periodStr, 10);

      // Map the unit string to OfferPeriodUnit
      let periodUnit;
      switch (unitStr) {
        case 'week':
          periodUnit = OfferPeriodUnit.WEEK;
          break;
        case 'month':
          periodUnit = OfferPeriodUnit.MONTH;
          break;
        case 'year':
          periodUnit = OfferPeriodUnit.YEAR;
          break;
        default:
          return offer.id;
      }

      // Find the offer that matches the requested periodicity
      const matchingOffer = planOffers.find(
        o => o.period === period && o.period_unit === periodUnit,
      );

      // If no matching offer found, try to default to 1-month periodicity
      if (!matchingOffer) {
        const defaultMonthlyOffer = planOffers.find(
          o => o.period === 1 && o.period_unit === OfferPeriodUnit.MONTH,
        );
        return defaultMonthlyOffer?.id || offer.id;
      }

      return matchingOffer.id;
    }

    // When there's no periodicity parameter, find the default offer (1-month) by default
    // Prevent edge case when navigating from 1-week url to a no periodicity one
    const defaultOffer = planOffers.find(
      o => o.period === 1 && o.period_unit === OfferPeriodUnit.MONTH,
    );

    return defaultOffer?.id || offer.id;
  };

  if (planOffers.length <= 1 && isDeeplinkOnly) {
    return;
  }

  const periodicityOptions: SelectOptionNew[] = planOffers.map(planOffer => ({
    value: planOffer.id,
    label: (
      <Flex
        component="span"
        align="center"
        gap="middle"
        justify="space-between"
      >
        {t(`periodicity.plan.${planOffer.period_unit}`, {
          count: planOffer.period,
        })}
        {planOffer.meta_data?.discountDisplay && (
          <Text small primary>
            {t('purchaseOverview.overview.discountPeriodicity', {
              defaultValue: 'Save {{discount}}%',
              discount: planOffer.meta_data.discountDisplay,
            })}
          </Text>
        )}
      </Flex>
    ),
    ...{
      'data-testid': `choosePeriodicity-${planOffer.period}-option`,
    },
  }));

  const handleChangePeriodicity = (value: string) => {
    const newOffer = planOffers.find(planOffer => planOffer.id === value);
    if (!newOffer) {
      return;
    }

    const newCart = getOffersInCartToAddFromPlanOffer(catalog, newOffer);
    resetCartAndAddOfferToCart(newCart);

    // We reset the search value in URL to avoid confusions if user switch between periodicities
    searchParams.delete('periodicity');
    setSearchParams(searchParams);
  };

  if (planOffers.length <= 1) {
    return null;
  }

  return (
    <Select
      popupMatchSelectWidth={false}
      defaultValue={getDefaultValue()}
      options={periodicityOptions}
      onChange={handleChangePeriodicity}
    />
  );
};
