import { Avatar, Col, Row, Space } from 'antd';

import useStyles from '~/components/OverviewCards/Cloudpc/ConfigurationOptions.styles';
import { Text } from '~/components/ui/Typography/Typography';

export interface IConfigurationOptionProps {
  icon: any;
  label: string;
  actionButton?: React.ReactNode;
}

interface IConfigurationOptionsProps {
  configurationOptions: IConfigurationOptionProps[];
}

export const ConfigurationOptions = ({
  configurationOptions,
}: IConfigurationOptionsProps) => {
  const { styles } = useStyles();

  return (
    <Row gutter={[16, 16]}>
      {configurationOptions.map(option => (
        <Col
          key={`configuration-${option.label}`}
          xs={{ flex: '100%' }}
          lg={{ flex: '50%' }}
        >
          <Space align="center" size="small">
            <Avatar
              icon={option.icon}
              size={32}
              className={styles.configuration__avatar}
            />
            <Text small>{option.label}</Text>
            <>{option.actionButton}</>
          </Space>
        </Col>
      ))}
    </Row>
  );
};
