import * as Flags from 'country-flag-icons/react/3x2';

interface Props {
  flag: keyof typeof Flags;
  flex?: boolean;
  strong?: boolean;
  value?: string | number;
}

export const Flag = ({ flag, value, strong = false, flex = false }: Props) => {
  const FlagToDiplay = Flags[flag];

  return (
    <span
      style={{
        fontWeight: strong ? '400' : '200',
        display: flex ? 'flex' : 'block',
      }}
    >
      <FlagToDiplay
        style={{ width: '24px', verticalAlign: 'text-top', marginRight: '8px' }}
      />
      {value}
    </span>
  );
};
