import useStyles from '~/components/ui/Price/Price.styles';
import { Text, Title } from '~/components/ui/Typography/Typography';

interface IPriceProps {
  id?: string;
  priceLine: string;
  activationFeesLine?: string;
  subLine?: string;
}

export const Price = ({
  id,
  priceLine,
  activationFeesLine,
  subLine,
}: IPriceProps) => {
  const { styles } = useStyles();

  return (
    <div className={styles.price__container}>
      <Title primary level={4} data-test-id={id} className={styles.price__text}>
        {priceLine}
      </Title>
      {!!activationFeesLine && <Text primary>{activationFeesLine}</Text>}
      {!!subLine && <Text primary>{subLine}</Text>}
    </div>
  );
};
