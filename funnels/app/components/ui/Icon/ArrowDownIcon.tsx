import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const ArrowDownSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
  >
    <path d="M7.70703 15.7073L10.9999 19.0002C11.3904 19.3907 12.0236 19.3907 12.4141 19.0002L15.707 15.7073M11.707 18.7073L11.707 4.70728" />
  </svg>
);

export const ArrowDownIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={ArrowDownSvg} {...props} />
);
