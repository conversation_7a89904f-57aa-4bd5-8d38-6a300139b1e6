import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const SettingsSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 18 18"
    fill="none"
  >
    <g
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="4.09091" cy="4.09091" r="3.34091" />
      <circle cx="13.9093" cy="13.9083" r="3.34091" />
      <path d="M10.5039 3.75L17.2539 3.75" />
      <path d="M0.753906 14.25L7.50391 14.25" />
    </g>
  </svg>
);

export const SettingsIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={SettingsSvg} {...props} />
);
