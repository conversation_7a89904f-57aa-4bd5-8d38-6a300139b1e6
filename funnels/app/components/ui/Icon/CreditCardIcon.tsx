import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const CreditCardSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21.3984 3.73486H3.39844C2.29387 3.73486 1.39844 4.63029 1.39844 5.73486V17.7349C1.39844 18.8394 2.29387 19.7349 3.39844 19.7349H21.3984C22.503 19.7349 23.3984 18.8394 23.3984 17.7349V5.73486C23.3984 4.63029 22.503 3.73486 21.3984 3.73486Z" />
    <path d="M1.39844 9.73486H23.3984" />
  </svg>
);

export const CreditCardIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={CreditCardSvg} {...props} />
);
