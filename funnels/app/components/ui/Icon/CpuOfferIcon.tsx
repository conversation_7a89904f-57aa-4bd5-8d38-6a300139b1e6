import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const CpuOfferSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      fill="currentColor"
      stroke="none"
      d="M6 0C5.724 0 5.5 0.224 5.5 0.5V2H6.5V0.5C6.5 0.224 6.276 0 6 0ZM10 0C9.724 0 9.5 0.224 9.5 0.5V2H10.5V0.5C10.5 0.224 10.276 0 10 0ZM14 0C13.724 0 13.5 0.224 13.5 0.5V2H14.5V0.5C14.5 0.224 14.276 0 14 0ZM18 0C17.724 0 17.5 0.224 17.5 0.5V2H18.5V0.5C18.5 0.224 18.276 0 18 0ZM4 2.5C3.17752 2.5 2.5 3.17752 2.5 4V20C2.5 20.8225 3.17752 21.5 4 21.5H20C20.8225 21.5 21.5 20.8225 21.5 20V4C21.5 3.17752 20.8225 2.5 20 2.5H4ZM4 3.5H20C20.2825 3.5 20.5 3.71748 20.5 4V20C20.5 20.2825 20.2825 20.5 20 20.5H4C3.71748 20.5 3.5 20.2825 3.5 20V4C3.5 3.71748 3.71748 3.5 4 3.5ZM6 5C5.73478 5 5.48043 5.10536 5.29289 5.29289C5.10536 5.48043 5 5.73478 5 6C5 6.26522 5.10536 6.51957 5.29289 6.70711C5.48043 6.89464 5.73478 7 6 7C6.26522 7 6.51957 6.89464 6.70711 6.70711C6.89464 6.51957 7 6.26522 7 6C7 5.73478 6.89464 5.48043 6.70711 5.29289C6.51957 5.10536 6.26522 5 6 5ZM0.5 5.5C0.224 5.5 0 5.724 0 6C0 6.276 0.224 6.5 0.5 6.5H2V5.5H0.5ZM22 5.5V6.5H23.5C23.7765 6.5 24 6.276 24 6C24 5.724 23.7765 5.5 23.5 5.5H22ZM0.5 9.5C0.224 9.5 0 9.724 0 10C0 10.276 0.224 10.5 0.5 10.5H2V9.5H0.5ZM22 9.5V10.5H23.5C23.7765 10.5 24 10.276 24 10C24 9.724 23.7765 9.5 23.5 9.5H22ZM0.5 13.5C0.224 13.5 0 13.724 0 14C0 14.276 0.224 14.5 0.5 14.5H2V13.5H0.5ZM22 13.5V14.5H23.5C23.7765 14.5 24 14.276 24 14C24 13.724 23.7765 13.5 23.5 13.5H22ZM18 17C17.7348 17 17.4804 17.1054 17.2929 17.2929C17.1054 17.4804 17 17.7348 17 18C17 18.2652 17.1054 18.5196 17.2929 18.7071C17.4804 18.8946 17.7348 19 18 19C18.2652 19 18.5196 18.8946 18.7071 18.7071C18.8946 18.5196 19 18.2652 19 18C19 17.7348 18.8946 17.4804 18.7071 17.2929C18.5196 17.1054 18.2652 17 18 17ZM0.5 17.5C0.224 17.5 0 17.724 0 18C0 18.276 0.224 18.5 0.5 18.5H2V17.5H0.5ZM22 17.5V18.5H23.5C23.7765 18.5 24 18.276 24 18C24 17.724 23.7765 17.5 23.5 17.5H22ZM5.5 22V23.5C5.5 23.7765 5.7235 24 6 24C6.2765 24 6.5 23.7765 6.5 23.5V22H5.5ZM9.5 22V23.5C9.5 23.7765 9.7235 24 10 24C10.2765 24 10.5 23.7765 10.5 23.5V22H9.5ZM13.5 22V23.5C13.5 23.7765 13.7235 24 14 24C14.2765 24 14.5 23.7765 14.5 23.5V22H13.5ZM17.5 22V23.5C17.5 23.7765 17.7235 24 18 24C18.2765 24 18.5 23.7765 18.5 23.5V22H17.5Z"
    />
  </svg>
);

export const CpuOfferIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={CpuOfferSvg} {...props} />
);
