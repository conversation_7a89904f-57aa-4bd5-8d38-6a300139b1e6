import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const GpuOfferSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M1.63725 3.00004C1.58002 2.99923 1.5232 3.00981 1.47009 3.03115C1.41698 3.05249 1.36865 3.08417 1.32789 3.12435C1.28714 3.16454 1.25477 3.21242 1.23268 3.26522C1.21059 3.31802 1.19922 3.37469 1.19922 3.43192C1.19922 3.48915 1.21059 3.54582 1.23268 3.59862C1.25477 3.65142 1.28714 3.6993 1.32789 3.73949C1.36865 3.77967 1.41698 3.81135 1.47009 3.83269C1.5232 3.85403 1.58002 3.86461 1.63725 3.8638H2.06912C2.55139 3.8638 2.93288 4.24529 2.93288 4.72755V20.707C2.93207 20.7642 2.94264 20.821 2.96398 20.8742C2.98532 20.9273 3.01701 20.9756 3.05719 21.0164C3.09737 21.0571 3.14526 21.0895 3.19806 21.1116C3.25086 21.1337 3.30752 21.145 3.36475 21.145C3.42199 21.145 3.47865 21.1337 3.53145 21.1116C3.58425 21.0895 3.63214 21.0571 3.67232 21.0164C3.7125 20.9756 3.74419 20.9273 3.76553 20.8742C3.78687 20.821 3.79744 20.7642 3.79663 20.707V18.5476H5.09226V19.8432C5.09227 19.9578 5.13778 20.0676 5.21877 20.1486C5.29976 20.2296 5.4096 20.2751 5.52414 20.2751H7.25165C7.36619 20.2751 7.47603 20.2296 7.55702 20.1486C7.63801 20.0676 7.68351 19.9578 7.68352 19.8432V18.5476H8.54728V19.8432C8.54729 19.9578 8.5928 20.0676 8.67379 20.1486C8.75478 20.2296 8.86462 20.2751 8.97916 20.2751H15.4573C15.5718 20.2751 15.6817 20.2296 15.7627 20.1486C15.8437 20.0676 15.8892 19.9578 15.8892 19.8432V18.5476H16.7529V19.8432C16.753 19.9578 16.7985 20.0676 16.8794 20.1486C16.9604 20.2296 17.0703 20.2751 17.1848 20.2751H18.0486C18.1058 20.2759 18.1626 20.2654 18.2157 20.244C18.2688 20.2227 18.3172 20.191 18.3579 20.1508C18.3987 20.1106 18.431 20.0627 18.4531 20.0099C18.4752 19.9571 18.4866 19.9005 18.4866 19.8432C18.4866 19.786 18.4752 19.7293 18.4531 19.6765C18.431 19.6237 18.3987 19.5759 18.3579 19.5357C18.3172 19.4955 18.2688 19.4638 18.2157 19.4425C18.1626 19.4211 18.1058 19.4106 18.0486 19.4114H17.6167V18.5476H21.5036C22.214 18.5476 22.7992 17.9624 22.7992 17.252V6.88694C22.7992 6.17651 22.214 5.59131 21.5036 5.59131H3.79663V4.72755C3.79663 3.77857 3.0181 3.00004 2.06912 3.00004H1.63725ZM3.79663 6.45506H21.5036C21.7476 6.45506 21.9355 6.64291 21.9355 6.88694V17.252C21.9355 17.496 21.7476 17.6839 21.5036 17.6839H17.2515C17.2272 17.6798 17.2027 17.6778 17.1781 17.678C17.1572 17.6784 17.1363 17.6804 17.1157 17.6839H7.32419C7.27755 17.6762 7.22996 17.6762 7.18332 17.6839H5.59078C5.56653 17.6798 5.54198 17.6778 5.51739 17.678C5.49647 17.6784 5.47561 17.6804 5.45497 17.6839H3.79663V6.45506ZM16.7529 7.75069C16.4915 7.75069 16.2367 7.77758 15.9879 7.82239C15.9823 7.82228 15.9766 7.82228 15.971 7.82239C15.9183 7.82388 15.8662 7.83503 15.8175 7.85529C14.3665 8.17774 13.1912 9.23345 12.694 10.611C12.6673 10.656 12.649 10.7054 12.64 10.757C12.5073 11.1713 12.4342 11.6118 12.4342 12.0695C12.4342 13.148 12.8351 14.1331 13.4919 14.891C13.5127 14.9206 13.537 14.9476 13.5645 14.9711C14.3551 15.839 15.4901 16.3882 16.7529 16.3882C17.0127 16.3882 17.2657 16.3616 17.5129 16.3174C17.5762 16.3189 17.6389 16.3065 17.6968 16.2811C19.1445 15.9562 20.3169 14.9019 20.8128 13.5262C20.8386 13.4819 20.8563 13.4333 20.8651 13.3828C20.998 12.9681 21.0717 12.5275 21.0717 12.0695C21.0717 11.0057 20.6826 10.032 20.0418 9.27829C20.011 9.22542 19.9693 9.17971 19.9195 9.14417C19.1298 8.29008 18.0042 7.75069 16.7529 7.75069ZM5.52414 8.18257C5.46691 8.18176 5.41009 8.19233 5.35698 8.21367C5.30388 8.23501 5.25554 8.26669 5.21479 8.30688C5.17403 8.34706 5.14167 8.39495 5.11958 8.44775C5.09749 8.50055 5.08611 8.55721 5.08611 8.61444C5.08611 8.67168 5.09749 8.72834 5.11958 8.78114C5.14167 8.83394 5.17403 8.88183 5.21479 8.92201C5.25554 8.96219 5.30388 8.99388 5.35698 9.01522C5.41009 9.03656 5.46691 9.04713 5.52414 9.04632H10.7067C10.7639 9.04713 10.8207 9.03656 10.8738 9.01522C10.9269 8.99388 10.9753 8.96219 11.016 8.92201C11.0568 8.88183 11.0891 8.83394 11.1112 8.78114C11.1333 8.72834 11.1447 8.67168 11.1447 8.61444C11.1447 8.55721 11.1333 8.50055 11.1112 8.44775C11.0891 8.39495 11.0568 8.34706 11.016 8.30688C10.9753 8.26669 10.9269 8.23501 10.8738 8.21367C10.8207 8.19233 10.7639 8.18176 10.7067 8.18257H5.52414ZM16.7529 8.61444C17.5641 8.61444 18.307 8.89332 18.8955 9.35842C18.7923 9.40887 18.6911 9.4547 18.5834 9.517C17.8121 9.96227 17.3536 10.4107 17.0456 10.8084C16.9497 10.7858 16.8515 10.7742 16.7529 10.7738C16.6916 10.7746 16.6305 10.7796 16.5699 10.789C16.4261 10.427 16.3211 9.96212 16.3211 9.20827C16.3211 9.00125 16.3396 8.81329 16.3624 8.63806C16.4907 8.62372 16.6207 8.61444 16.7529 8.61444ZM15.4759 8.85991C15.4679 8.97417 15.4573 9.08423 15.4573 9.20827C15.4573 10.0991 15.6169 10.72 15.8074 11.1855C15.6974 11.3025 15.6102 11.4389 15.5501 11.5878C15.1654 11.5312 14.7114 11.3897 14.0596 11.0134C13.8794 10.9093 13.725 10.7992 13.5839 10.6912C13.946 9.85633 14.6275 9.19631 15.4759 8.85991ZM19.5298 10.0147C19.9546 10.5887 20.208 11.2978 20.208 12.0695C20.208 12.239 20.1917 12.4042 20.1683 12.5671C20.0739 12.5036 19.9849 12.4398 19.8781 12.3782C19.1077 11.9334 18.4901 11.7605 17.9921 11.6924C17.945 11.5387 17.8698 11.3951 17.7702 11.269C18.0119 10.9637 18.3624 10.6413 19.0152 10.2643C19.1945 10.1607 19.3662 10.0825 19.5298 10.0147ZM5.52414 10.342C5.46691 10.3411 5.41009 10.3517 5.35698 10.3731C5.30388 10.3944 5.25554 10.4261 5.21479 10.4663C5.17403 10.5064 5.14167 10.5543 5.11958 10.6071C5.09749 10.6599 5.08611 10.7166 5.08611 10.7738C5.08611 10.8311 5.09749 10.8877 5.11958 10.9405C5.14167 10.9933 5.17403 11.0412 5.21479 11.0814C5.25554 11.1216 5.30388 11.1533 5.35698 11.1746C5.41009 11.1959 5.46691 11.2065 5.52414 11.2057H10.7067C10.7639 11.2065 10.8207 11.1959 10.8738 11.1746C10.9269 11.1533 10.9753 11.1216 11.016 11.0814C11.0568 11.0412 11.0891 10.9933 11.1112 10.9405C11.1333 10.8877 11.1447 10.8311 11.1447 10.7738C11.1447 10.7166 11.1333 10.6599 11.1112 10.6071C11.0891 10.5543 11.0568 10.5064 11.016 10.4663C10.9753 10.4261 10.9269 10.3944 10.8738 10.3731C10.8207 10.3517 10.7639 10.3411 10.7067 10.342H5.52414ZM13.3376 11.5718C13.432 11.6353 13.521 11.6991 13.6277 11.7607C14.4041 12.209 15.0187 12.3812 15.5147 12.4499C15.562 12.6027 15.6372 12.7454 15.7365 12.8708C15.4945 13.1756 15.1421 13.4984 14.4906 13.8746C14.3114 13.9782 14.1397 14.0564 13.9761 14.1242C13.5513 13.5502 13.2979 12.8411 13.2979 12.0695C13.2979 11.8999 13.3142 11.7347 13.3376 11.5718ZM17.9558 12.5511C18.3405 12.6077 18.7945 12.7492 19.4463 13.1255C19.6265 13.2296 19.7809 13.3397 19.922 13.4478C19.5599 14.2826 18.8784 14.9426 18.03 15.279C18.038 15.1647 18.0486 15.0547 18.0486 14.9306C18.0486 14.0398 17.889 13.4189 17.6985 12.9535C17.8085 12.8364 17.8957 12.7 17.9558 12.5511ZM5.52414 12.9332C5.46691 12.9324 5.41009 12.943 5.35698 12.9643C5.30388 12.9857 5.25554 13.0173 5.21479 13.0575C5.17403 13.0977 5.14167 13.1456 5.11958 13.1984C5.09749 13.2512 5.08611 13.3079 5.08611 13.3651C5.08611 13.4223 5.09749 13.479 5.11958 13.5318C5.14167 13.5846 5.17403 13.6325 5.21479 13.6727C5.25554 13.7128 5.30388 13.7445 5.35698 13.7659C5.41009 13.7872 5.46691 13.7978 5.52414 13.797H10.7067C10.7639 13.7978 10.8207 13.7872 10.8738 13.7659C10.9269 13.7445 10.9753 13.7128 11.016 13.6727C11.0568 13.6325 11.0891 13.5846 11.1112 13.5318C11.1333 13.479 11.1447 13.4223 11.1447 13.3651C11.1447 13.3079 11.1333 13.2512 11.1112 13.1984C11.0891 13.1456 11.0568 13.0977 11.016 13.0575C10.9753 13.0173 10.9269 12.9857 10.8738 12.9643C10.8207 12.943 10.7639 12.9324 10.7067 12.9332H5.52414ZM16.4611 13.3305C16.5567 13.3531 16.6547 13.3647 16.7529 13.3651C16.8168 13.3645 16.8805 13.3591 16.9436 13.3491C17.0844 13.7126 17.1848 14.1833 17.1848 14.9306C17.1848 15.1377 17.1663 15.3256 17.1435 15.5009C17.0152 15.5152 16.8852 15.5245 16.7529 15.5245C15.9418 15.5245 15.1989 15.2456 14.6104 14.7805C14.7136 14.7301 14.8148 14.6842 14.9225 14.6219C15.6922 14.1775 16.152 13.7284 16.4611 13.3305ZM5.52414 15.0926C5.46691 15.0918 5.41009 15.1024 5.35698 15.1237C5.30388 15.145 5.25554 15.1767 5.21479 15.2169C5.17403 15.2571 5.14167 15.305 5.11958 15.3578C5.09749 15.4106 5.08611 15.4672 5.08611 15.5245C5.08611 15.5817 5.09749 15.6384 5.11958 15.6912C5.14167 15.744 5.17403 15.7919 5.21479 15.832C5.25554 15.8722 5.30388 15.9039 5.35698 15.9252C5.41009 15.9466 5.46691 15.9572 5.52414 15.9564H10.7067C10.7639 15.9572 10.8207 15.9466 10.8738 15.9252C10.9269 15.9039 10.9753 15.8722 11.016 15.832C11.0568 15.7919 11.0891 15.744 11.1112 15.6912C11.1333 15.6384 11.1447 15.5817 11.1447 15.5245C11.1447 15.4672 11.1333 15.4106 11.1112 15.3578C11.0891 15.305 11.0568 15.2571 11.016 15.2169C10.9753 15.1767 10.9269 15.145 10.8738 15.1237C10.8207 15.1024 10.7639 15.0918 10.7067 15.0926H5.52414ZM5.95602 18.5476H6.81977V19.4114H5.95602V18.5476ZM9.41103 18.5476H15.0254V19.4114H9.41103V18.5476Z" />
  </svg>
);

export const GpuOfferIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={GpuOfferSvg} {...props} />
);
