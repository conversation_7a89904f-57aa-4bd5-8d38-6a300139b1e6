import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const MemoryOfferSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M22.8008 5.06395C22.8008 4.59145 22.4093 4.19995 21.9368 4.19995L2.06478 4.19995C1.59228 4.19995 1.20078 4.59145 1.20078 5.06395L1.20078 16.2959C1.20078 16.5339 1.39484 16.728 1.63278 16.728L2.06478 16.728L2.06478 18.888C2.06478 19.3605 2.45628 19.752 2.92878 19.752L11.1368 19.752C11.3747 19.752 11.5688 19.5579 11.5688 19.32L11.5688 18.456C11.5688 18.213 11.7578 18.024 12.0008 18.024C12.2438 18.024 12.4328 18.213 12.4328 18.456L12.4328 19.32C12.4328 19.5579 12.6268 19.752 12.8648 19.752L21.0728 19.752C21.5453 19.752 21.9368 19.3605 21.9368 18.888L21.9368 16.728L22.3688 16.728C22.6067 16.728 22.8008 16.5339 22.8008 16.296L22.8008 5.06395ZM21.9368 5.06395L21.9368 15.864L2.06478 15.864L2.06478 5.06395L21.9368 5.06395ZM21.0728 7.57495C21.034 7.36908 20.8517 7.22058 20.6408 7.22395L17.6168 7.22395C17.3788 7.22395 17.1848 7.41801 17.1848 7.65595L17.1848 13.272C17.1848 13.5099 17.3788 13.704 17.6168 13.704L20.6408 13.704C20.8787 13.704 21.0728 13.5099 21.0728 13.272L21.0728 7.65595C21.0728 7.64245 21.0728 7.62895 21.0728 7.61545C21.0728 7.60195 21.0728 7.58845 21.0728 7.57495ZM21.0728 16.728L21.0728 18.888L13.2968 18.888L13.2968 18.456C13.2968 17.7455 12.7112 17.1599 12.0008 17.1599C11.2903 17.1599 10.7048 17.7455 10.7048 18.456L10.7048 18.888L2.92878 18.888L2.92878 16.728L3.79278 16.728L3.79278 18.0239L4.65678 18.0239L4.65678 16.728L5.52078 16.728L5.52078 18.0239L6.38478 18.0239L6.38478 16.728L7.24878 16.728L7.24878 18.0239L8.11278 18.0239L8.11278 16.728L8.97678 16.728L8.97678 18.0239L9.84078 18.024L9.84078 16.728L14.1608 16.728L14.1608 18.024L15.0248 18.024L15.0248 16.728L15.8888 16.728L15.8888 18.024L16.7528 18.024L16.7528 16.728L17.6168 16.728L17.6168 18.024L18.4808 18.024L18.4808 16.728L19.3448 16.728L19.3448 18.024L20.2088 18.024L20.2088 16.728L21.0728 16.728ZM20.2088 8.08795L20.2088 12.84L18.0488 12.84L18.0488 8.08795L20.2088 8.08795ZM16.3208 7.57495C16.282 7.36908 16.0997 7.22058 15.8888 7.22395L12.8648 7.22395C12.6268 7.22395 12.4328 7.41801 12.4328 7.65595L12.4328 13.272C12.4328 13.5099 12.6268 13.704 12.8648 13.704L15.8888 13.704C16.1267 13.704 16.3208 13.5099 16.3208 13.272L16.3208 7.65595C16.3208 7.64245 16.3208 7.62895 16.3208 7.61545C16.3208 7.60195 16.3208 7.58845 16.3208 7.57495ZM15.4568 8.08795L15.4568 12.84L13.2968 12.84L13.2968 8.08795L15.4568 8.08795ZM11.5688 7.57495C11.53 7.36908 11.3477 7.22058 11.1368 7.22395L8.11278 7.22395C7.87484 7.22395 7.68078 7.41801 7.68078 7.65595L7.68078 13.272C7.68078 13.5099 7.87484 13.704 8.11278 13.704L11.1368 13.704C11.3747 13.704 11.5688 13.5099 11.5688 13.272L11.5688 7.65595C11.5688 7.64245 11.5688 7.62895 11.5688 7.61545C11.5688 7.60195 11.5688 7.58845 11.5688 7.57495ZM10.7048 8.08795L10.7048 12.84L8.54478 12.8399L8.54478 8.08795L10.7048 8.08795ZM6.81678 7.57495C6.77797 7.36908 6.59572 7.22058 6.38478 7.22395L3.36078 7.22395C3.12284 7.22395 2.92878 7.41801 2.92878 7.65595L2.92878 13.272C2.92878 13.5099 3.12284 13.704 3.36078 13.704L6.38478 13.704C6.62272 13.704 6.81678 13.5099 6.81678 13.272L6.81678 7.65595C6.81678 7.64245 6.81678 7.62895 6.81678 7.61545C6.81678 7.60195 6.81678 7.58845 6.81678 7.57495ZM5.95278 8.08795L5.95278 12.8399L3.79278 12.8399L3.79278 8.08795L5.95278 8.08795Z" />
  </svg>
);

export const MemoryOfferIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={MemoryOfferSvg} {...props} />
);
