import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const KeyboardSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path
      d="M3.35938 5.55005H20.6396C21.4237 5.55028 22.0487 6.18244 22.0488 6.92798V17.5686C22.0488 18.3142 21.4238 18.9463 20.6396 18.9465H3.35938C2.57539 18.9465 1.94981 18.3149 1.94922 17.5696L1.95996 6.92896V6.92798C1.96004 6.17744 2.57999 5.55005 3.35938 5.55005Z"
      stroke="currentColor"
      strokeWidth="1.5"
      fill="none"
    />
    <path d="M10.9199 8.29194C10.9199 8.12626 11.0542 7.99194 11.2199 7.99194H12.7799C12.9456 7.99194 13.0799 8.12626 13.0799 8.29194V9.82002C13.0799 9.98571 12.9456 10.12 12.7799 10.12H11.2199C11.0542 10.12 10.9199 9.98571 10.9199 9.82002V8.29194Z" />
    <path d="M10.9199 11.4843C10.9199 11.3186 11.0542 11.1843 11.2199 11.1843H12.7799C12.9456 11.1843 13.0799 11.3186 13.0799 11.4843V13.0124C13.0799 13.1781 12.9456 13.3124 12.7799 13.3124H11.2199C11.0542 13.3124 10.9199 13.1781 10.9199 13.0124V11.4843Z" />
    <path d="M7.67969 8.29194C7.67969 8.12626 7.814 7.99194 7.97969 7.99194H9.53969C9.70537 7.99194 9.83969 8.12626 9.83969 8.29194V9.82002C9.83969 9.98571 9.70537 10.12 9.53969 10.12H7.97969C7.814 10.12 7.67969 9.98571 7.67969 9.82002V8.29194Z" />
    <path d="M7.67969 11.4843C7.67969 11.3186 7.814 11.1843 7.97969 11.1843H9.53969C9.70537 11.1843 9.83969 11.3186 9.83969 11.4843V13.0124C9.83969 13.1781 9.70537 13.3124 9.53969 13.3124H7.97969C7.814 13.3124 7.67969 13.1781 7.67969 13.0124V11.4843Z" />
    <path d="M6.59945 13.0124C6.59945 13.1781 6.46514 13.3124 6.29945 13.3124H4.73945C4.57377 13.3124 4.43945 13.1781 4.43945 13.0124V11.4843C4.43945 11.3186 4.57377 11.1843 4.73945 11.1843H6.29945C6.46514 11.1843 6.59945 11.3186 6.59945 11.4843V13.0124Z" />
    <path d="M6.59945 9.82002C6.59945 9.98571 6.46514 10.12 6.29945 10.12H4.73945C4.57377 10.12 4.43945 9.98571 4.43945 9.82002V8.29194C4.43945 8.12626 4.57377 7.99194 4.73945 7.99194H6.29945C6.46514 7.99194 6.59945 8.12626 6.59945 8.29194V9.82002Z" />
    <path d="M16.3197 17.2685C16.3197 17.4342 16.1854 17.5685 16.0197 17.5685H7.97969C7.814 17.5685 7.67969 17.4342 7.67969 17.2685V15.7404C7.67969 15.5747 7.814 15.4404 7.97969 15.4404H16.0197C16.1854 15.4404 16.3197 15.5747 16.3197 15.7404V17.2685Z" />
    <path d="M16.3202 13.0124C16.3202 13.1781 16.1858 13.3124 16.0202 13.3124H14.4602C14.2945 13.3124 14.1602 13.1781 14.1602 13.0124V11.4843C14.1602 11.3186 14.2945 11.1843 14.4602 11.1843H16.0202C16.1858 11.1843 16.3202 11.3186 16.3202 11.4843V13.0124Z" />
    <path d="M16.3202 9.82002C16.3202 9.98571 16.1858 10.12 16.0202 10.12H14.4602C14.2945 10.12 14.1602 9.98571 14.1602 9.82002V8.29194C14.1602 8.12626 14.2945 7.99194 14.4602 7.99194H16.0202C16.1858 7.99194 16.3202 8.12626 16.3202 8.29194V9.82002Z" />
    <path d="M19.5604 13.0124C19.5604 13.1781 19.4261 13.3124 19.2604 13.3124H17.7004C17.5347 13.3124 17.4004 13.1781 17.4004 13.0124V11.4843C17.4004 11.3186 17.5347 11.1843 17.7004 11.1843H19.2604C19.4261 11.1843 19.5604 11.3186 19.5604 11.4843V13.0124Z" />
    <path d="M19.5604 9.82002C19.5604 9.98571 19.4261 10.12 19.2604 10.12H17.7004C17.5347 10.12 17.4004 9.98571 17.4004 9.82002V8.29194C17.4004 8.12626 17.5347 7.99194 17.7004 7.99194H19.2604C19.4261 7.99194 19.5604 8.12626 19.5604 8.29194V9.82002Z" />
  </svg>
);

export const KeyboardIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={KeyboardSvg} {...props} />
);
