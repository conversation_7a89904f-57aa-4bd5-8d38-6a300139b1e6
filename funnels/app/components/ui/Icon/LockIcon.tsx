import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const LockSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 19 20"
    fill="none"
  >
    <g
      fill="none"
      stroke="currentColor"
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="1.5" y="7" width="16" height="12" rx="4" />
      <path d="M9.5 14L9.5 12" />
      <path d="M13.5 7V5C13.5 2.79086 11.7091 1 9.5 1V1C7.29086 1 5.5 2.79086 5.5 5L5.5 7" />
    </g>
  </svg>
);

export const LockIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={LockSvg} {...props} />
);
