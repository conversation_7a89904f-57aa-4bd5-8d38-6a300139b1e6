import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const AddSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M20.4853 11.9999H3.51472" />
    <path d="M12 3.51465V20.4852" />
  </svg>
);

export const AddIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={AddSvg} {...props} />
);
