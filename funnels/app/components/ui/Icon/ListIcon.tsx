import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const ListSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M8 6H21" />
      <path d="M8 12H21" />
      <path d="M8 18H21" />
      <path d="M4 6H4.01" />
      <path d="M4 12H4.01" />
      <path d="M4 18H4.01" />
    </g>
  </svg>
);

export const ListIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={ListSvg} {...props} />
);
