import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const DiscordSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 16 13"
    fill="currentColor"
    stroke="none"
  >
    <path d="M13.5535 1.08877C12.5042 0.573462 11.3941 0.207311 10.2526 0C10.1104 0.269815 9.9443 0.632718 9.82974 0.921426C8.59899 0.727182 7.37956 0.727182 6.17143 0.921426C6.05693 0.632783 5.88706 0.269815 5.74356 0C4.60093 0.207406 3.48991 0.57449 2.44012 1.09146C0.351121 4.40488 -0.215192 7.63591 0.0679331 10.8212C1.4535 11.9072 2.79624 12.567 4.11637 12.9987C4.44445 12.5251 4.73447 12.0238 4.98343 11.4998C4.50945 11.3104 4.05251 11.077 3.61806 10.8023C3.73239 10.7134 3.84403 10.6207 3.95281 10.5244C6.58543 11.8168 9.44593 11.8168 12.0472 10.5244C12.1564 10.62 12.268 10.7127 12.3819 10.8023C11.9467 11.0777 11.4889 11.3116 11.014 11.5012C11.2644 12.0273 11.5539 12.5291 11.8811 13C13.2024 12.5683 14.5464 11.9086 15.932 10.8212C16.2642 7.12868 15.3645 3.92724 13.5535 1.08871V1.08877ZM5.34212 8.86231C4.55181 8.86231 3.90368 8.08789 3.90368 7.14489C3.90368 6.20188 4.53799 5.42615 5.34212 5.42615C6.14631 5.42615 6.79437 6.2005 6.78056 7.14489C6.78181 8.08789 6.14631 8.86231 5.34212 8.86231ZM10.6578 8.86231C9.86749 8.86231 9.21943 8.08789 9.21943 7.14489C9.21943 6.20188 9.85368 5.42615 10.6578 5.42615C11.462 5.42615 12.1101 6.2005 12.0962 7.14489C12.0962 8.08789 11.462 8.86231 10.6578 8.86231V8.86231Z" />{' '}
  </svg>
);

export const DiscordIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={DiscordSvg} {...props} />
);
