import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const CalendarSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <rect x="3" y="3.5" width="18" height="18" rx="4" />
    <path d="M3 8.5H21" />
    <path d="M16.5 2L16.5 5" />
    <path d="M7.5 2L7.5 5" />
    <path d="M6.5 12.5H7.5" />
    <path d="M11.5 12.5H12.5" />
    <path d="M16.5 12.5H17.5" />
    <path d="M6.5 16.5H7.5" />
    <path d="M11.5 16.5H12.5" />
    <path d="M16.5 16.5H17.5" />
  </svg>
);

export const CalendarIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={CalendarSvg} {...props} />
);
