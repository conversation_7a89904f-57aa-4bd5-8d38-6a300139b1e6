import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const ScreenSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 40 40"
    fill="none"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      clipRule="evenodd"
      d="M 34.727 5.272 L 5.273 5.272 C 3.473 5.272 2 6.745 2 8.545 L 2 28.182 C 2 29.982 3.473 31.454 5.273 31.454 L 12.455 31.454 C 13.007 31.454 13.455 31.902 13.455 32.454 L 13.455 33.727 C 13.455 34.279 13.902 34.727 14.455 34.727 L 25.546 34.727 C 26.098 34.727 26.546 34.279 26.546 33.727 L 26.546 32.454 C 26.546 31.902 26.993 31.454 27.546 31.454 L 34.727 31.454 C 36.527 31.454 37.984 29.982 37.984 28.182 L 38 8.545 C 38 6.745 36.527 5.272 34.727 5.272 Z M 34.729 27.182 C 34.729 27.734 34.281 28.182 33.729 28.182 L 6.274 28.182 C 5.722 28.182 5.274 27.734 5.274 27.182 L 5.274 9.545 C 5.274 8.993 5.722 8.545 6.274 8.545 L 33.729 8.545 C 34.281 8.545 34.729 8.993 34.729 9.545 L 34.729 27.182 Z"
    />
  </svg>
);

export const ScreenIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={ScreenSvg} {...props} />
);
