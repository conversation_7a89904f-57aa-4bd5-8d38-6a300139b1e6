import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const HardDriveOfferSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M22.7992 12.24H1.19922" />
    <path d="M4.92522 4.7989L1.19922 12.2401V18.7201C1.19922 19.293 1.42679 19.8424 1.83187 20.2474C2.23695 20.6525 2.78635 20.8801 3.35922 20.8801H20.6392C21.2121 20.8801 21.7615 20.6525 22.1666 20.2474C22.5716 19.8424 22.7992 19.293 22.7992 18.7201V12.2401L19.0732 4.7989C18.8944 4.43903 18.6187 4.13618 18.2772 3.9244C17.9357 3.71262 17.5419 3.60031 17.14 3.6001H6.85842C6.45657 3.60031 6.06274 3.71262 5.72123 3.9244C5.37971 4.13618 5.10404 4.43903 4.92522 4.7989V4.7989Z" />
    <path d="M5.51953 16.5601H5.53033" />
    <path d="M9.83984 16.5601H9.85064" />
  </svg>
);

export const HardDriveOfferIcon = (
  props: Partial<CustomIconComponentProps>,
) => <Icon component={HardDriveOfferSvg} {...props} />;
