import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const DiscSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 16 16"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M8.33268 14.6673C12.0146 14.6673 14.9993 11.6825 14.9993 8.00065C14.9993 4.31875 12.0146 1.33398 8.33268 1.33398C4.65078 1.33398 1.66602 4.31875 1.66602 8.00065C1.66602 11.6825 4.65078 14.6673 8.33268 14.6673Z" />
    <path d="M8.33301 10C9.43758 10 10.333 9.10457 10.333 8C10.333 6.89543 9.43758 6 8.33301 6C7.22844 6 6.33301 6.89543 6.33301 8C6.33301 9.10457 7.22844 10 8.33301 10Z" />
  </svg>
);

export const DiscIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={DiscSvg} {...props} />
);
