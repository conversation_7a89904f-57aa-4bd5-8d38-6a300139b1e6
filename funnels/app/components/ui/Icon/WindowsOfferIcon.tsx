import Icon from '@ant-design/icons';
import type { GetProps } from 'antd';

type CustomIconComponentProps = GetProps<typeof Icon>;

const WindowsOfferSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 25"
  >
    <mask id="mask" fill="white">
      <path d="M0 3.61013L9.75 2.26113V11.7121H0V3.61013ZM10.949 2.11013L24 0.161133V11.5611H10.949V2.11013ZM0 12.7611H9.75V22.2121L0 20.8601V12.7611ZM10.949 12.7611H11.2748H24V24.1611L11.2748 22.3601H11.1" />
    </mask>
    <path
      fill="currentColor"
      stroke="none"
      d="M11.1 22.3601L10.6001 22.368L11.5999 22.3692L11.1 22.3601ZM11.2748 12.7611H11.7748L10.7749 12.752L11.2748 12.7611ZM10.4491 12.769L10.6001 22.368L11.5999 22.3523L11.4489 12.7533L10.4491 12.769ZM10.7749 12.752L10.6001 22.351L11.5999 22.3692L11.7747 12.7702L10.7749 12.752ZM10.7748 12.7611V22.3601H11.7748V12.7611H10.7748ZM0 3.61013L-0.137053 2.61957L-1 2.73897V3.61013H0ZM9.75 2.26113H10.75V1.11325L9.61295 1.27057L9.75 2.26113ZM9.75 11.7121V12.7121H10.75V11.7121H9.75ZM0 11.7121H-1V12.7121H0V11.7121ZM10.949 2.11013L10.8013 1.1211L9.949 1.24838V2.11013H10.949ZM24 0.161133H25V-0.999294L23.8523 -0.8279L24 0.161133ZM24 11.5611V12.5611H25V11.5611H24ZM10.949 11.5611H9.949V12.5611H10.949V11.5611ZM0 12.7611V11.7611H-1V12.7611H0ZM9.75 12.7611H10.75V11.7611H9.75V12.7611ZM9.75 22.2121L9.61265 23.2027L10.75 23.3604V22.2121H9.75ZM0 20.8601H-1V21.731L-0.137353 21.8507L0 20.8601ZM24 12.7611H25V11.7611H24V12.7611ZM24 24.1611L23.8599 25.1513L25 25.3126V24.1611H24ZM11.2748 22.3601L11.4149 21.37L11.3452 21.3601H11.2748V22.3601ZM0.137053 4.6007L9.88705 3.2517L9.61295 1.27057L-0.137053 2.61957L0.137053 4.6007ZM8.75 2.26113V11.7121H10.75V2.26113H8.75ZM9.75 10.7121H0V12.7121H9.75V10.7121ZM1 11.7121V3.61013H-1V11.7121H1ZM11.0967 3.09917L24.1477 1.15017L23.8523 -0.8279L10.8013 1.1211L11.0967 3.09917ZM23 0.161133V11.5611H25V0.161133H23ZM24 10.5611H10.949V12.5611H24V10.5611ZM11.949 11.5611V2.11013H9.949V11.5611H11.949ZM0 13.7611H9.75V11.7611H0V13.7611ZM8.75 12.7611V22.2121H10.75V12.7611H8.75ZM9.88735 21.2216L0.137353 19.8696L-0.137353 21.8507L9.61265 23.2027L9.88735 21.2216ZM1 20.8601V12.7611H-1V20.8601H1ZM23 12.7611V24.1611H25V12.7611H23ZM10.949 13.7611H11.2748V11.7611H10.949V13.7611ZM11.2748 13.7611H24V11.7611H11.2748V13.7611ZM24.1401 23.171L11.4149 21.37L11.1347 23.3503L23.8599 25.1513L24.1401 23.171ZM11.2748 21.3601H11.1V23.3601H11.2748V21.3601Z"
      mask="url(#mask)"
    />
  </svg>
);

export const WindowsOfferIcon = (props: Partial<CustomIconComponentProps>) => (
  <Icon component={WindowsOfferSvg} {...props} />
);
