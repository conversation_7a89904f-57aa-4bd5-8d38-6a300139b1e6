import { Modal as AntdModal, Flex, ModalProps } from 'antd';

import useStyles from '~/components/ui/Modal/Modal.styles';
import { Text, Title } from '~/components/ui/Typography/Typography';

interface IModal extends ModalProps {
  isPaymentMethod?: boolean;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
}

export const Modal = ({
  isPaymentMethod = false,
  title = undefined,
  subtitle = undefined,
  ...props
}: IModal) => {
  const { styles } = useStyles({ isPaymentMethod: isPaymentMethod });

  return (
    <AntdModal
      {...props}
      className={styles.modal}
      footer={null}
      centered
      width={600}
    >
      <Flex vertical gap="middle">
        {title && (
          <Title level={1} centered>
            {title}
          </Title>
        )}
        {subtitle && <Text centered>{subtitle}</Text>}
      </Flex>
      <Flex vertical className={styles.modal__content}>
        {props.children}
      </Flex>
    </AntdModal>
  );
};
