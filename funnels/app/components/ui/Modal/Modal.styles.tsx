import { createStyles } from 'antd-style';

export default createStyles(
  ({ token, css }, props: { isPaymentMethod: boolean }) => {
    return {
      modal: css`
        .ant-modal-content {
          border: 1px solid ${token.colorBorderSecondary};
          ${props.isPaymentMethod && `background: ${token.colorBgLayout};`}
          padding: ${token.paddingMD}px;

          @media (min-width: ${token.screenMD}px) {
            padding: ${token.paddingLG}px;
          }
        }

        .ant-modal-body {
          display: flex;
          gap: ${token.marginXL}px;
          flex-direction: column;
        }
      `,
      modal__content: css`
        // TODO: etienned do we keep that style from old shop ?
        /* width: 420px;
        margin: 0 auto; */
      `,
    };
  },
);
