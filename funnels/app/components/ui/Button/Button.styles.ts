// import { ButtonVariantType } from 'antd/lib/button';
import { SizeType } from 'antd/es/config-provider/SizeContext';
import { createStyles } from 'antd-style';

export default createStyles(
  (
    { token, css },
    props: {
      block: boolean;
      blockMobile: boolean;
      disabled?: boolean;
      justify: string;
      noWrap: boolean;
      size: SizeType;
    },
  ) => {
    return {
      button: css`
        // Default size
        &.ant-btn {
          // small button font weight and size
          ${props.size === 'small' &&
          `
            font-weight: 200;
          `}
        }

        white-space: ${props.noWrap ? 'nowrap' : 'normal'};
        display: flex;
        align-items: center;
        justify-content: ${props.justify};
        width: ${props.blockMobile ? '100%' : 'fit-content'};
        max-width: 100%;

        @media (min-width: ${token.screenLG}px) {
          width: fit-content;
        }

        &.ant-btn:disabled,
        &.ant-btn[disabled] {
          opacity: 0.5;
        }
      `,
    };
  },
);
