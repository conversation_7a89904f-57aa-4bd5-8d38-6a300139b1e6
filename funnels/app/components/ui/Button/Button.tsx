import { Button as AntdButton, ButtonProps, ConfigProvider } from 'antd';
import { theme as antdTheme } from 'antd';
import { forwardRef, ForwardedRef } from 'react';

import useStyles from '~/components/ui/Button/Button.styles';

interface IButton extends ButtonProps {
  'data-testid': string;
  block?: boolean;
  blockMobile?: boolean;
  justify?: 'left' | 'right' | 'center';
  noWrap?: boolean;
  secondary?: boolean;
  tertiary?: boolean;
  disabled?: boolean;
}

export const Button = forwardRef(
  (
    {
      block = false,
      blockMobile = false,
      justify = 'center',
      noWrap = false,
      secondary = false,
      tertiary = false,
      ...props
    }: IButton,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    const { styles } = useStyles({
      block: block,
      blockMobile: blockMobile,
      justify: justify,
      noWrap: noWrap,
      disabled: props.disabled,
      size: props.size,
    });
    const { useToken } = antdTheme;
    const { token: theme } = useToken();

    // const sizing = props.size === 'large' ? 'middle' : 'large';
    const classNames = [styles.button, props.className].join(' ');
    // const { useBreakpoint } = Grid;

    if (secondary) {
      return (
        <ConfigProvider
          theme={{
            components: {
              Button: {
                colorText: theme.colorPrimary,
                defaultBg: theme.colorBorderSecondary,
                colorBorder: theme.colorBorderSecondary,
                // hover
                defaultHoverBg: theme.colorPrimary,
                defaultHoverBorderColor: theme.colorPrimary,
                defaultHoverColor: theme.colorBgBase,
                // disabled
                colorTextDisabled: theme.colorPrimary,
                borderColorDisabled: theme.colorBorderSecondary,
                colorBgContainerDisabled: theme.colorBorderSecondary,
              },
            },
          }}
        >
          <AntdButton
            type="default"
            variant="solid"
            ref={ref}
            shape="round"
            block={block}
            {...props}
            onClick={
              !props.loading && !props.disabled && props.onClick
                ? props.onClick
                : () => {}
            }
            className={styles.button}
          />
        </ConfigProvider>
      );
    } else if (tertiary) {
      return (
        <ConfigProvider
          theme={{
            components: {
              Button: {
                fontWeight: '300',
                colorBorder: theme.colorBorderSecondary,
                colorText: theme.colorTextBase,
                // hover
                defaultHoverBorderColor: theme.colorPrimary,
                defaultHoverColor: theme.colorPrimary,
                //disabled
                colorTextDisabled: theme.colorTextBase,
                borderColorDisabled: theme.colorBorderSecondary,
                colorBgContainerDisabled: 'transparent',
              },
            },
          }}
        >
          <AntdButton
            type="default"
            ref={ref}
            shape="round"
            block={block}
            {...props}
            onClick={
              !props.loading && !props.disabled && props.onClick
                ? props.onClick
                : () => {}
            }
            className={classNames}
          />
        </ConfigProvider>
      );
    }

    return (
      <AntdButton
        type="primary"
        ref={ref}
        shape="round"
        block={block}
        {...props}
        onClick={
          !props.loading && !props.disabled && props.onClick
            ? props.onClick
            : () => {}
        }
        className={classNames}
        // size={screens.md ? props.size : sizing}
      />
    );
  },
);
