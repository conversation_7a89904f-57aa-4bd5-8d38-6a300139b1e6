import { createStyles } from 'antd-style';

export default createStyles(
  ({ token, css }, props: { hasBlackTitle: boolean; hoverable: boolean }) => {
    return {
      card__body: css`
        &.ant-card-body {
          padding: 24px 0;
          border-radius: ${token.borderRadius}px;
        }

        overflow: hidden;
        border-radius: ${token.borderRadius}px;
        box-shadow: 0px 0px 20px 0px rgba(54, 83, 204, 0.1) !important;
      `,

      card__content: css`
        flex: 1;
        padding: 0 24px;
      `,

      card__wrapper: css`
        height: 100%;
      `,

      card__footer: css`
        margin-top: 24px;
      `,

      card__title: css`
        &.ant-typography {
          color: ${props.hasBlackTitle
            ? token.colorTextBase
            : token.colorPrimary};
        }
      `,
    };
  },
);
