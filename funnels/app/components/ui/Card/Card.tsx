import { Card as AntCard, CardProps, Flex, Grid } from 'antd';
import { forwardRef, ForwardedRef, ReactNode } from 'react';

import useStyles from '~/components/ui/Card/Card.styles';
import { Title } from '~/components/ui/Typography/Typography';

interface Props extends CardProps {
  children: ReactNode;
  hasBlackTitle?: boolean;
  title?: ReactNode | string;
  footer?: ReactNode | string;
  hoverable?: boolean;
}

export const Card = forwardRef(
  (
    {
      children,
      hasBlackTitle = false,
      title,
      footer,
      hoverable = false,
      ...props
    }: Props,
    ref: ForwardedRef<HTMLDivElement>,
  ) => {
    const { styles } = useStyles({
      hasBlackTitle: hasBlackTitle,
      hoverable: hoverable,
    });
    const { useBreakpoint } = Grid;
    const screens = useBreakpoint();

    const classNames = [styles.card__body, props.className].join(' ');

    return (
      <AntCard
        classNames={{ body: [classNames].join(' ') }}
        ref={ref}
        {...props}
      >
        <Flex vertical justify="space-between" className={styles.card__wrapper}>
          <Flex
            vertical
            gap={screens.lg ? 'large' : 'middle'}
            className={styles.card__content}
          >
            {title && (
              <Title level={1} className={styles.card__title}>
                {title}
              </Title>
            )}
            {children}
          </Flex>
          {/* Footer is used for tech specs on product card */}
          <div className={styles.card__footer}>{footer}</div>
        </Flex>
      </AntCard>
    );
  },
);
