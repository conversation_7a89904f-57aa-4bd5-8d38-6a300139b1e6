import { createStyles } from 'antd-style';

export default createStyles(
  (
    { token, css },
    props: { centered: boolean; primary: boolean; small?: boolean },
  ) => {
    return {
      typography: css`
        &.ant-typography {
          ${props.primary && `color: ${token.colorPrimary};`}
          ${props.small &&
          `font-size: ${token.fontSizeSM}px;
          line-height: ${token.fontSizeSM}px;`};
          font-weight: ${props.small ? '200' : '300'};
          ${props.centered && 'text-align: center'};
        }
      `,
    };
  },
);
