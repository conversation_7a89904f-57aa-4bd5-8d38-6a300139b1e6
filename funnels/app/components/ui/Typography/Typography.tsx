import { Typography as TypographyAntd } from 'antd';
import { LinkProps } from 'antd/es/typography/Link';
import { ParagraphProps } from 'antd/es/typography/Paragraph';
import { TextProps } from 'antd/es/typography/Text';
import { TitleProps } from 'antd/es/typography/Title';
import { forwardRef, ForwardedRef, ReactNode } from 'react';

import useStyles from '~/components/ui/Typography/Typography.styles';

interface ILinkProps extends LinkProps {
  children: ReactNode;
  centered?: boolean;
  primary?: boolean;
  small?: boolean;
}

const Link = forwardRef(
  (
    { centered = false, primary = false, small = false, ...props }: ILinkProps,
    ref: ForwardedRef<HTMLElement>,
  ) => {
    const { styles } = useStyles({
      centered: centered,
      primary: primary,
      small: small,
    });

    return (
      <TypographyAntd.Link className={styles.typography} ref={ref} {...props} />
    );
  },
);

interface ITextProps extends TextProps {
  children: React.ReactNode;
  centered?: boolean;
  primary?: boolean;
  small?: boolean;
}

const Text = forwardRef(
  (
    { centered = false, primary = false, small = false, ...props }: ITextProps,
    ref: ForwardedRef<HTMLElement>,
  ) => {
    const { styles } = useStyles({
      centered: centered,
      primary: primary,
      small: small,
    });

    return (
      <TypographyAntd.Text className={styles.typography} ref={ref} {...props} />
    );
  },
);

interface IParagraphProps extends ParagraphProps {
  children: React.ReactNode;
  centered?: boolean;
  primary?: boolean;
  small?: boolean;
}

const Paragraph = forwardRef(
  (
    {
      centered = false,
      primary = false,
      small = false,
      ...props
    }: IParagraphProps,
    ref: ForwardedRef<HTMLElement>,
  ) => {
    const { styles } = useStyles({
      centered: centered,
      primary: primary,
      small: small,
    });

    return (
      <TypographyAntd.Paragraph
        className={styles.typography}
        ref={ref}
        {...props}
      />
    );
  },
);

interface ITitleProps extends TitleProps {
  children: React.ReactNode;
  centered?: boolean;
  primary?: boolean;
}

const Title = forwardRef(
  (
    { children, centered = false, primary = false, ...props }: ITitleProps,
    ref: ForwardedRef<HTMLElement>,
  ) => {
    const { styles } = useStyles({ centered: centered, primary: primary });

    return (
      <TypographyAntd.Title className={styles.typography} ref={ref} {...props}>
        {children}
      </TypographyAntd.Title>
    );
  },
);

export { Link, Paragraph, Text, Title };
