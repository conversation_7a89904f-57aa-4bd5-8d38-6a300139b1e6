import { createStyles } from 'antd-style';

export default createStyles(
  (
    { css },
    props: {
      color: string | undefined;
      isPathVisible: boolean;
      size: number | undefined;
      sizeStroke: number | undefined;
    },
  ) => {
    return {
      shadowLoader: css`
        svg {
          overflow: visible;
        }

        svg > g {
          clip-path: unset;
        }

        /* Hide the logo path */
        g:first-of-type > g:first-of-type {
          ${!props.isPathVisible && 'display: none;'}
        }

        path {
          ${props.color && `stroke: ${props.color};`}
          ${props.sizeStroke && `stroke-width: ${props.sizeStroke};`}
        }
        ${props.size && `width: ${props.size}px; height: ${props.size}px;`}
      `,
    };
  },
);
