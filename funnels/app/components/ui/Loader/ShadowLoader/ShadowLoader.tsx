import Lottie from 'lottie-react';

import useStyles from '~/components/ui/Loader/ShadowLoader/ShadowLoader.styles';
import globalLoaderAnimation from '~/components/ui/Loader/ShadowLoader/shadowLoaderAnimation.json';
import { useDelayBeforeDisplay } from '~/hooks/useDelayBeforeDisplay';

interface Props {
  color?: string;
  isPathVisible?: boolean;
  size?: number;
  sizeStroke?: number;
}

export const ShadowLoader = ({
  color = undefined,
  isPathVisible = true,
  size = undefined,
  sizeStroke = undefined,
}: Props) => {
  const { styles } = useStyles({
    color: color,
    isPathVisible: isPathVisible,
    size: size,
    sizeStroke: sizeStroke,
  });
  // When loaders are displayed directly the user perception of slowness is increased
  // We delay the display of the loader to avoid that
  const showLoader = useDelayBeforeDisplay();

  return showLoader ? (
    <div className={styles.shadowLoader}>
      <Lottie animationData={globalLoaderAnimation} loop={true} />
    </div>
  ) : null;
};
