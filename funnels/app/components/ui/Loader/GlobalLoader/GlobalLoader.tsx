import { Flex } from 'antd';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { Text } from '~/components/ui//Typography/Typography';
import useStyles from '~/components/ui/Loader/GlobalLoader/GlobalLoader.styles';
import { ShadowLoader } from '~/components/ui/Loader/ShadowLoader/ShadowLoader';
import { useDelayBeforeDisplay } from '~/hooks/useDelayBeforeDisplay';

interface Props {
  fullHeight?: boolean;
  text?: string | ReactNode;
}

export const GlobalLoader = ({ fullHeight = false, text }: Props) => {
  // When loaders are displayed directly the user perception of slowness is increased
  // We delay the display of the loader to avoid that
  const showLoader = useDelayBeforeDisplay();
  const { t } = useTranslation();
  const { styles } = useStyles({ fullHeight: fullHeight });

  return showLoader ? (
    <Flex
      vertical
      align="center"
      justify="center"
      className={styles.globalLoader}
      gap={24}
    >
      <ShadowLoader />
      <Text>{text ?? t('global.loading', 'Loading...')}</Text>
    </Flex>
  ) : null;
};
