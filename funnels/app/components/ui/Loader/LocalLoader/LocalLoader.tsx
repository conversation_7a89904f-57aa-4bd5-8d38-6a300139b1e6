import { Flex } from 'antd';

import useStyles from '~/components/ui/Loader/LocalLoader/LocalLoader.styles';
import { ShadowLoader } from '~/components/ui/Loader/ShadowLoader/ShadowLoader';

export const LocalLoader = ({ justify = 'center', width = 56, ...props }) => {
  const { styles } = useStyles();

  return (
    <Flex
      align="center"
      justify={justify}
      {...props}
      className={styles.localLoader}
    >
      <ShadowLoader size={width} sizeStroke={50} isPathVisible={false} />
    </Flex>
  );
};
