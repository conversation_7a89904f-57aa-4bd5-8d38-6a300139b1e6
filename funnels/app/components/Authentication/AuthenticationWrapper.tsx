import { ReactNode, useEffect, useState } from 'react';
import { logError } from 'utils';

import { GlobalLoader } from '~/components/ui/Loader';
import { useAuthentication } from '~/hooks/useAuthentication';

interface AuthenticationWrapperProps {
  children: ReactNode;
  redirectOnFailure?: boolean;
}

export const AuthenticationWrapper = ({
  children,
  redirectOnFailure = true,
}: AuthenticationWrapperProps) => {
  const {
    isAuthenticated,
    isLoading,
    error,
    signinRedirect,
    startSilentRenew,
  } = useAuthentication();

  const [isInitialized, setIsInitialized] = useState(false);
  const [authError, setAuthError] = useState<Error | null>(null);

  // Handle silent renew on mount
  useEffect(() => {
    const initAuth = () => {
      try {
        startSilentRenew();
      } catch (e) {
        logError('startSilentRenew failed', e);
      } finally {
        setIsInitialized(true);
      }
    };

    void initAuth();
  }, [startSilentRenew]);

  // Handle redirect when necessary
  useEffect(() => {
    const handleSigninRedirect = async () => {
      if (!isInitialized || !redirectOnFailure) {
        return;
      }
      // Only redirect if user is not authenticated and there are no errors
      if (!isLoading && !isAuthenticated && !error && !authError) {
        try {
          await signinRedirect();
        } catch (e) {
          logError('signinRedirect failed', e);
          setAuthError(
            e instanceof Error ? e : new Error('Authentication failed'),
          );
        }
      }
    };

    void handleSigninRedirect();
  }, [
    isInitialized,
    isLoading,
    isAuthenticated,
    error,
    authError,
    signinRedirect,
    redirectOnFailure,
  ]);

  // Show loader while initializing or loading
  if (!isInitialized || isLoading) {
    return <GlobalLoader fullHeight />;
  }

  // Show loader while not authenticated (waiting for redirect)
  if (!isAuthenticated && redirectOnFailure) {
    return <GlobalLoader fullHeight />;
  }

  // Show children when authenticated or when redirect is not required
  return children;
};
