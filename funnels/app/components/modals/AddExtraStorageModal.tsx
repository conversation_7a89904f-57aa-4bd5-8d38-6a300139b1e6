import { useTranslation } from 'react-i18next';
import { Currency, IAnyOffer, OfferPeriodicity } from 'types';

import { AddExtraStorageForm } from '~/components/Forms/AddExtraStorageForm';
import { Modal } from '~/components/ui/Modal/Modal';

export interface IAddExtraStorageModalProps {
  extraStorageAddonOffer: IAnyOffer;
  offer: IAnyOffer;
  currentQuantity: number;
  price: number;
  periodicity: OfferPeriodicity;
  currency: Currency;
  isOpened: boolean;
  onClose: () => void;
}

export const AddExtraStorageModal = ({
  extraStorageAddonOffer,
  price,
  periodicity,
  currency,
  currentQuantity,
  isOpened,
  offer,
  onClose,
}: IAddExtraStorageModalProps) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t('purchaseOverview.addStorageModal.title', 'Add extra storage')}
      subtitle={t('purchaseOverview.addStorageModal.subtitle')}
      open={isOpened}
      onCancel={onClose}
      destroyOnHidden
    >
      <AddExtraStorageForm
        extraStorageAddonOffer={extraStorageAddonOffer}
        price={price}
        periodicity={periodicity}
        currency={currency}
        currentQuantity={currentQuantity}
        offer={offer}
        onClose={onClose}
      />
    </Modal>
  );
};
