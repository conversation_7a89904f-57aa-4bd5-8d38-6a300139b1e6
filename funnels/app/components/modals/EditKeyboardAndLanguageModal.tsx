import { useTranslation } from 'react-i18next';
import { KeyboardISO } from 'types';

import { EditKeyboardAndLanguageForm } from '~/components/Forms/EditKeyboardAndLanguageForm';
import { Modal } from '~/components/ui/Modal/Modal';

interface Props {
  isOpened: boolean;
  currentKeyboard: KeyboardISO;
  currentLanguage: string;
  onClose: () => void;
}

export const EditKeyboardAndLanguageModal = ({
  isOpened,
  currentKeyboard,
  currentLanguage,
  onClose,
}: Props) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t(
        'editKeyboardAndLanguageModal.title',
        'Change you keyboard layout and language',
      )}
      subtitle={t(
        'editKeyboardAndLanguageModal.subtitle',
        'A subtitle to say that the user can update the keyboard layout and windows language',
      )}
      open={isOpened}
      onCancel={onClose}
      destroyOnHidden
    >
      <EditKeyboardAndLanguageForm
        currentKeyboard={currentKeyboard}
        currentLanguage={currentLanguage}
        onClose={onClose}
      />
    </Modal>
  );
};
