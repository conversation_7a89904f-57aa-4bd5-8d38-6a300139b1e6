import { useTranslation } from 'react-i18next';
import { IDatacenter } from 'types';

import { EditDatacenterForm } from '~/components/Forms/EditDatacenterForm';
import { Modal } from '~/components/ui/Modal/Modal';

interface IEditDatacenterModalProps {
  isOpened: boolean;
  datacenterList: IDatacenter[];
  currentDatacenterName: string;
  withoutCancelButton?: boolean;
  onClose: () => void;
}

export const EditDatacenterModal = ({
  isOpened,
  datacenterList,
  currentDatacenterName,
  onClose,
}: IEditDatacenterModalProps) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t('editDatacenterModal.title', 'Select your datacenter')}
      subtitle={t(
        'editDatacenterModal.subtitle',
        'Optimize your Shadow Experience by selecting the nearest datacenter.',
      )}
      open={isOpened}
      onCancel={onClose}
      destroyOnHidden
    >
      <EditDatacenterForm
        datacenterList={datacenterList}
        currentDatacenterName={currentDatacenterName}
        onClose={onClose}
        withoutCancelButton
      />
    </Modal>
  );
};
