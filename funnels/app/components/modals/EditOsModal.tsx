import { useTranslation } from 'react-i18next';
import { Currency, IAnyOffer, ICatalog, ILicenseTypeChoice } from 'types';

import { EditOsForm } from '~/components/Forms/EditOsForm';
import { Modal } from '~/components/ui/Modal/Modal';

interface Props {
  catalog: ICatalog;
  currency: Currency;
  isOpened: boolean;
  licenseTypeChoices: ILicenseTypeChoice[];
  onClose: () => void;
  offer: IAnyOffer;
  selectedLicense: string;
}

export const EditOsModal = ({
  catalog,
  currency,
  isOpened,
  licenseTypeChoices,
  onClose,
  offer,
  selectedLicense,
}: Props) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t(
        'purchaseOverview.editOsModal.title',
        'Choose your windows license',
      )}
      open={isOpened}
      onCancel={onClose}
      destroyOnHidden
    >
      <EditOsForm
        catalog={catalog}
        currency={currency}
        currentOsLicense={selectedLicense}
        licenseTypeChoices={licenseTypeChoices}
        offer={offer}
        onClose={onClose}
      />
    </Modal>
  );
};
