import { Flex } from 'antd';
import <PERSON><PERSON> from 'lottie-react';
import { useTranslation } from 'react-i18next';

import animation404 from '~/components/ui/lottie/404.json';
import { Text, Title } from '~/components/ui/Typography/Typography';

const RouteError = ({ error }: any) => {
  const { t } = useTranslation();

  return (
    <>
      {error.status === 404 ? (
        <>
          <Title level={1}>{t('notFound.title', '404 not found')}</Title>
          <Flex gap={40} vertical>
            <Text>
              {t(
                'notFound.description',
                'The page your are looking for does not exist.',
              )}
            </Text>
            <Lottie
              animationData={animation404}
              style={{ height: 200 }}
              loop={true}
            />
          </Flex>
        </>
      ) : (
        <>
          <Title level={1}>
            {error.status} {error.statusText}
          </Title>
          <Text>{error.data}</Text>
        </>
      )}
    </>
  );
};

export default RouteError;
