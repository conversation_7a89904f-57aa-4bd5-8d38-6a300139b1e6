import { ButtonProps } from 'antd';
import { useTranslation } from 'react-i18next';

import { Button } from '~/components/ui/Button/Button';

export const CartCheckoutButton = ({ ...props }: ButtonProps) => {
  const { t } = useTranslation();

  return (
    <Button
      data-testid="cart-checkout-button"
      id="cart-checkout-button"
      block
      size="large"
      {...props}
    >
      {t('cart.proceed', 'Proceed to checkout')}
    </Button>
  );
};
