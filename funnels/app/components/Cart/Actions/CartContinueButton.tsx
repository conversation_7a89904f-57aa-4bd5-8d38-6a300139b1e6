import { ButtonProps } from 'antd';
import { useTranslation } from 'react-i18next';

import { Button } from '~/components/ui/Button/Button';

export const CartContinueButton = ({ ...props }: ButtonProps) => {
  const { t } = useTranslation();

  return (
    <Button
      data-testid="cart-continue-button"
      id="cart-continue-button"
      block
      size={'large'}
      {...props}
    >
      {t('cart.continue', 'Continue')}
    </Button>
  );
};
