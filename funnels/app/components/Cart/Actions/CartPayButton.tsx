import { useIsMutating } from '@tanstack/react-query';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PaymentMethodStatus } from 'types';

import { Button } from '~/components/ui/Button/Button';
import { LockIcon } from '~/components/ui/Icon';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import { useBillingDetails } from '~/hooks/reactQuery/user/useUser';
import { useCurrency } from '~/hooks/useCurrency';
import { useMutationStatus } from '~/hooks/useMutation';
import useStore from '~/store';
import { PaymentIntentType } from '~/types/payment';
import { formatPrice } from '~/utils/price';

export interface ICartPayButtonProps {
  hasPaymentError: boolean;
  isButtonDisabled?: boolean;
  resetHasPaymentError: () => void;
  amountDue: number | null;
  isPayButtonLoading: boolean;
}

export const CartPayButton = ({
  isButtonDisabled,
  hasPaymentError,
  resetHasPaymentError,
  amountDue,
  isPayButtonLoading,
}: ICartPayButtonProps) => {
  const { t } = useTranslation();
  const { offersInCart } = useStore();
  const currency = useCurrency();
  const billingDetailsQuery = useBillingDetails();
  const { paymentInfo } = usePaymentContext();
  const isAddPaymentMethodMutating = !!useIsMutating([
    'addPaymentMethod',
    true,
  ]);
  const isAddSubscriptionsMutating = !!useIsMutating(['addSubscriptions']);
  const isAddPaymentIntentMutating = !!useIsMutating([
    `addPaymentIntent_${PaymentIntentType.CARD_PAYMENT}`,
  ]);
  const { isError: hasAddPaymentIntentError } = useMutationStatus([
    `addPaymentIntent_${PaymentIntentType.CARD_PAYMENT}`,
  ]);
  const { isError: hasAddSubscriptionError } = useMutationStatus([
    'addSubscription',
  ]);
  const [isLoading, setLoading] = useState(false);

  const isCardPaymentMethodSelected =
    paymentInfo?.choice !== PaymentChoice.PAYPAL;

  // Credit card case: when the user clicks the payment button, we trigger both AddPaymentIntent and AddSubscription,
  // so we show a loader as long as one of them is mutating.
  // Paypal case: AddPaymentIntent is triggered silently when the Paypal button is loaded (via Chargebee).
  // So we only need to show a loader on the button when AddSubscription is mutating.
  const subscriptionInProgress =
    (isCardPaymentMethodSelected && isAddPaymentIntentMutating) ||
    isAddSubscriptionsMutating ||
    isAddPaymentMethodMutating;

  // If user has submitted, we force loading state for payment button to avoid further clicks from user
  useEffect(() => {
    if (subscriptionInProgress) {
      message.destroy('card-payment-error');
      setLoading(true);
    }

    if (
      hasAddPaymentIntentError ||
      hasPaymentError ||
      hasAddSubscriptionError
    ) {
      setLoading(false);
      resetHasPaymentError();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    subscriptionInProgress,
    hasAddPaymentIntentError,
    hasPaymentError,
    hasAddSubscriptionError,
    resetHasPaymentError,
  ]);

  let isValidPayment = false;
  switch (paymentInfo?.choice) {
    case PaymentChoice.SAVED_CARD:
      isValidPayment = paymentInfo.info.status === PaymentMethodStatus.VALID;
      break;
    case PaymentChoice.NEW_CARD:
      isValidPayment = paymentInfo.info.hasValidCard;
      break;
    case PaymentChoice.PAYPAL:
    case PaymentChoice.APPLE_PAY:
    case PaymentChoice.GOOGLE_PAY:
      isValidPayment =
        paymentInfo.info.status === PaymentMethodStatus.VALID &&
        !!paymentInfo.paymentIntent;
      break;
  }

  const isButtonEnabled =
    !isButtonDisabled &&
    offersInCart.length > 0 &&
    billingDetailsQuery.isSuccess &&
    isValidPayment;

  return (
    <Button
      data-testid="cart-pay-button"
      id="cart-pay-button"
      block
      disabled={!isButtonEnabled}
      loading={isPayButtonLoading || isLoading}
      htmlType="submit"
      size="large"
      icon={<LockIcon style={{ fontSize: '22px' }} />}
    >
      {t('cart.buyNow', {
        defaultValue: 'Pay {{price}}',
        price: amountDue === null ? '' : formatPrice(amountDue, currency),
      })}
    </Button>
  );
};
