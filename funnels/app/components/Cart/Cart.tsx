import { Flex } from 'antd';
import { usePaginatedSubscriptions } from 'hooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IEstimation } from 'types';

import { CartSummary } from '~/components/Cart/CartSummary/CartSummary';
import { CartTotal } from '~/components/Cart/CartTotal/CartTotal';
import { mapOffersInCartToCartSummaryProduct } from '~/components/Cart/mapOffersInCartToCartSummaryProduct';
import { useAutoApplyCoupon } from '~/hooks/cart/useAutoApplyCoupon';
import { useGetEstimationValue } from '~/hooks/cart/useGetEstimationValue';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useCurrency } from '~/hooks/useCurrency';
import useStore from '~/store';
import { getIsCartTotalZero } from '~/utils/cart';

export const Cart = () => {
  const { t } = useTranslation();
  const currency = useCurrency();

  const [estimation, setEstimation] = useState<IEstimation>();
  const { offersInCart } = useStore();
  const catalogQuery = useCatalog();
  const subscriptionsQuery = usePaginatedSubscriptions();
  const hasFreeCart = getIsCartTotalZero(offersInCart);

  const cartSummaryProduct = mapOffersInCartToCartSummaryProduct(
    offersInCart,
    catalogQuery.data,
    currency,
    t,
    estimation,
    subscriptionsQuery.data?.items,
    1,
  );

  const displayAddCoupon = !hasFreeCart;

  const { hasFinishedAutoApplyingCoupon } =
    useAutoApplyCoupon(displayAddCoupon);

  const canGetEstimationValue =
    (displayAddCoupon && hasFinishedAutoApplyingCoupon) || !displayAddCoupon;

  useGetEstimationValue(setEstimation, canGetEstimationValue);

  return (
    <Flex vertical gap="middle">
      <CartSummary
        cartSummaryProduct={cartSummaryProduct}
        estimation={estimation}
        displayAddCoupon={displayAddCoupon}
      />
      <CartTotal
        estimation={estimation}
        cartSummaryProduct={cartSummaryProduct}
      />
    </Flex>
  );
};
