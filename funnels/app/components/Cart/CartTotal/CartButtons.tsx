import { useIsMutating } from '@tanstack/react-query';
import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';

import { CartCheckoutButton } from '~/components/Cart/Actions/CartCheckoutButton';
import { CartContinueButton } from '~/components/Cart/Actions/CartContinueButton';
import { PayForm } from '~/components/Cart/CartTotal/PayForm';
import { useFormContext } from '~/contexts/FormContext';
import { useAuthentication } from '~/hooks/useAuthentication';
import { useLocale } from '~/hooks/useLocale';
import { useMutationStatus } from '~/hooks/useMutation';
import useStore from '~/store';
import { getIsCartTotalZero } from '~/utils/cart';

interface Props {
  amountDue?: number;
}

export const CartButtons = ({ amountDue = 0 }: Props) => {
  const { locale } = useLocale();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthentication();
  const { offersInCart } = useStore();
  const formContext = useFormContext();
  const currentForm = formContext?.formId;
  const isBillingDetailsForm = currentForm === 'billing_details_form';
  const isTotalZero = getIsCartTotalZero(offersInCart);

  const [isLoading, setLoading] = useState(false);

  const isUpdateBillingDetailsMutating = useIsMutating([
    'updateBillingDetails',
  ]);
  const { isError: hasUpdateBillingError, isSuccess: hasUpdateBillingSucces } =
    useMutationStatus(['updateBillingDetails']);

  const { pathname } = useLocation();

  // If user has submitted, we force loading state for payment button to avoid further clicks from user
  useEffect(() => {
    if (isUpdateBillingDetailsMutating) {
      setLoading(true);
    }

    if (hasUpdateBillingError || hasUpdateBillingSucces) {
      setLoading(false);
    }
  }, [
    hasUpdateBillingError,
    hasUpdateBillingSucces,
    isUpdateBillingDetailsMutating,
  ]);

  const getButtons = () => {
    // Configurator
    if (pathname.includes('/buy/')) {
      if (isAuthenticated && !isTotalZero) {
        return (
          // Proceed to checkout button
          <CartCheckoutButton
            onClick={() => navigate(`/${locale}/purchase-overview`)}
            disabled={offersInCart.length === 0}
          />
        );
      }
      return (
        // Continue button
        <CartContinueButton
          onClick={() => navigate(`/${locale}/purchase-overview`)}
          disabled={offersInCart.length === 0}
        />
      );
    }
    // Purchase overview
    if (pathname.includes('/purchase-overview')) {
      if (isBillingDetailsForm) {
        return (
          // Continue button
          <CartContinueButton
            form={'billing_details_form'}
            htmlType="submit"
            loading={isLoading}
          />
        );
      }
      // Pay button
      return <PayForm amountDue={amountDue} />;
    }
  };

  return (
    <Flex vertical gap="middle">
      {getButtons()}
    </Flex>
  );
};
