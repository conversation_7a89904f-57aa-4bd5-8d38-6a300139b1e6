import { Flex, Tooltip } from 'antd';
import { capitalize } from 'lodash';

import { InfoIcon } from '~/components/ui/Icon';
import { Text, Title } from '~/components/ui/Typography/Typography';
import { useCurrency } from '~/hooks/useCurrency';
import { formatPrice } from '~/utils/price';

interface Props {
  amount: number;
  label: string;
  hasBigAmount?: boolean;
  hasBigLabel?: boolean;
  id?: string;
  labelTooltip?: string;
  sublineLeft?: string;
  sublineRight?: string;
}

export const DetailedPrice = ({
  label,
  amount,
  hasBigAmount = false,
  hasBigLabel = false,
  id,
  labelTooltip,
  sublineLeft,
  sublineRight,
}: Props) => {
  const currency = useCurrency();

  return (
    <Flex align="top" justify="space-between" gap="middle">
      {/* Left part */}
      <Flex vertical>
        <Flex gap="small">
          <Title level={hasBigLabel ? 4 : 5} primary>
            {capitalize(label)}
          </Title>
          {labelTooltip && (
            <Tooltip title={labelTooltip} arrow={true}>
              <InfoIcon
                data-testid="cart-total-tooltip"
                style={{
                  cursor: 'help',
                  color: 'var(--ant-color-primary)',
                  fontSize: '18px',
                }}
              />
            </Tooltip>
          )}
        </Flex>

        {sublineLeft && (
          <Text primary small>
            {sublineLeft}
          </Text>
        )}
      </Flex>

      {/* Right  part */}
      <Flex vertical align="end" flex={'none'}>
        <Title level={hasBigAmount ? 4 : 5} data-test-id={id} primary>
          {formatPrice(amount, currency)}
        </Title>
        {sublineRight && (
          <Text primary small>
            {sublineRight}
          </Text>
        )}
      </Flex>
    </Flex>
  );
};
