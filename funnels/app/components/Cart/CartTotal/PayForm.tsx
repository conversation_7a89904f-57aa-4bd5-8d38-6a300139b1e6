import { HitType, useFlagship } from '@flagship.io/react-sdk';
import { Form } from 'antd';
import { useDatacenterListForPlan } from 'hooks';
import { FormEventHandler, useState } from 'react';
import { useNavigate } from 'react-router';
import { ProductFamilyId } from 'types';

import {
  CartPayButton,
  ICartPayButtonProps,
} from '~/components/Cart/Actions/CartPayButton';
import { CreditCardConditions } from '~/components/Payment/CreditCardItem/CreditCardConditions';
import { LinkedPaymentConditions } from '~/components/Payment/LinkedPaymentConditions';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import useFormCardPayment from '~/hooks/forms/useFormCardPayment';
import useFormLinkedPayment from '~/hooks/forms/useFormLinkedPayment';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import { CheckoutPayload } from '~/types/purchase';
import { getPlanOfferFromCart } from '~/utils/cart';

interface Props {
  amountDue: ICartPayButtonProps['amountDue'];
}

export const PayForm = ({ amountDue }: Props) => {
  const [form] = Form.useForm();
  const [isPayButtonLoading, setIsPayButtonLoading] = useState(false);
  const [hasPaymentError, setHasPaymentError] = useState(false);
  const [areCheckboxesChecked, setAreCheckboxesChecked] = useState(false);
  const { country } = useLocale();
  const catalogQuery = useCatalog();
  const {
    datacenterName,
    offersInCart,
    setSubscriptionCreationDate,
    setTransactionId,
    zipcode,
    geolocResults,
  } = useStore();
  const { locale } = useLocale();
  const navigate = useNavigate();
  const { paymentInfo, paymentIntent, setPaymentInfo } = usePaymentContext();
  const flagship = useFlagship();

  const isLinkedPayment =
    paymentInfo?.choice === PaymentChoice.PAYPAL ||
    paymentInfo?.choice === PaymentChoice.GOOGLE_PAY ||
    paymentInfo?.choice === PaymentChoice.APPLE_PAY;

  const planOfferInCart = getPlanOfferFromCart(catalogQuery.data, offersInCart);
  const datacentersQuery = useDatacenterListForPlan({
    zipcode: zipcode ?? geolocResults?.zipcode ?? null,
    country,
    planId: planOfferInCart?.product_id,
  });
  const currentDatacenter = datacentersQuery.data?.find(
    datacenter => datacenter.name === datacenterName,
  );
  const isOfferOutOfStock =
    planOfferInCart?.itemFamilyId === ProductFamilyId.CLOUDPC &&
    (!currentDatacenter?.status || currentDatacenter.status === 'unavailable');

  const handlePaymentSuccess = (payload: CheckoutPayload) => {
    if (payload.subscriptionCreationDate && payload.transactionId) {
      setTransactionId(payload.transactionId);
      setSubscriptionCreationDate(payload.subscriptionCreationDate);

      // Send to flagship a hit for transaction purchase KPI
      flagship.hit.send({
        type: HitType.TRANSACTION,
        transactionId: payload.transactionId,
        affiliation: 'purchase',
      });
    }

    // We must wait after tracking hit is sent before redirecting to confirmation page
    navigate(`/${locale}/confirmation`);
  };

  const resetHasPaymentError = () => {
    setHasPaymentError(false);
  };

  const initialValues = {
    cguAccepted: false,
    withdrawalAccepted: false,
  };

  const { onSubmit: onSubmitCreditCardPayment } =
    useFormCardPayment(handlePaymentSuccess);

  const { onSubmit: onSubmitLinkedPayment } = useFormLinkedPayment(
    paymentIntent,
    handlePaymentSuccess,
  );

  const handleSubmitLinkedPayment = () => {
    onSubmitLinkedPayment()
      .then(() => {})
      .catch(() => {
        setHasPaymentError(true);
        setIsPayButtonLoading(false);
      });
  };

  const handleSubmitCreditCardPayment = () => {
    onSubmitCreditCardPayment().catch(() => {
      setHasPaymentError(true);
      setIsPayButtonLoading(false);
      if (paymentInfo?.choice === PaymentChoice.NEW_CARD) {
        setPaymentInfo(paymentInfoState =>
          paymentInfoState?.choice === PaymentChoice.NEW_CARD
            ? {
                ...paymentInfoState,
                info: {
                  ...paymentInfoState.info,
                  hasValidCard: false,
                },
              }
            : paymentInfoState,
        );
      }
    });
  };

  const handleSubmit: FormEventHandler = () => {
    setIsPayButtonLoading(true);
    if (isLinkedPayment) {
      handleSubmitLinkedPayment();
    } else {
      handleSubmitCreditCardPayment();
    }
  };

  const handleCheckboxesChange = (allChecked: boolean) => {
    setAreCheckboxesChecked(allChecked);
  };

  return (
    <Form form={form} onFinish={handleSubmit} initialValues={initialValues}>
      {isLinkedPayment ? (
        <LinkedPaymentConditions onCheckboxesChange={handleCheckboxesChange} />
      ) : (
        <CreditCardConditions onCheckboxesChange={handleCheckboxesChange} />
      )}
      <CartPayButton
        hasPaymentError={hasPaymentError}
        resetHasPaymentError={resetHasPaymentError}
        amountDue={amountDue}
        isButtonDisabled={isOfferOutOfStock || !areCheckboxesChecked}
        isPayButtonLoading={isPayButtonLoading}
      />
    </Form>
  );
};
