import { Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { IEstimationTax } from 'types';

import { Text } from '~/components/ui/Typography/Typography';
import { useCurrency } from '~/hooks/useCurrency';
import { formatPrice } from '~/utils/price';

interface ICartVAT {
  taxesList: IEstimationTax[];
  amountWithoutTaxes?: number;
}

export const CartVAT = ({ amountWithoutTaxes, taxesList }: ICartVAT) => {
  const { t } = useTranslation();
  const currency = useCurrency();

  return (
    <div>
      <Flex justify="space-between">
        <Text small>{t('cart.total.vat.withoutVAT', 'Without VAT')}</Text>
        <Text small data-test-id="payment-without-vat">
          {formatPrice(amountWithoutTaxes || 0 / 100, currency)}
        </Text>
      </Flex>
      {taxesList.map((tax, key) => (
        <Flex key={tax.name}>
          <Text small>{tax.description}</Text>
          <Text
            small
            // As we can have multiple tax items, we cannot have a common data-test-id here
            data-test-id={`payment-tax-${key}`}
          >
            {formatPrice(tax.amount / 100, currency)}
          </Text>
        </Flex>
      ))}
    </div>
  );
};
