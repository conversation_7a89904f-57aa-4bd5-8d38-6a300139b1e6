import { useIsMutating } from '@tanstack/react-query';
import { Flex } from 'antd';
import { isEmpty } from 'lodash';
import { useTranslation } from 'react-i18next';
import { ICartSummaryProduct, IEstimation } from 'types';
import { DATE_FORMAT_WITH_SLASH_BY_MARKET, formatDate } from 'utils';

import { CartButtons } from '~/components/Cart/CartTotal/CartButtons';
import { CartTotalSkeleton } from '~/components/Cart/CartTotal/CartTotalSkeleton';
import { DetailedPrice } from '~/components/Cart/CartTotal/DetailedPrice';
import { Card } from '~/components/ui/Card/Card';
import { Text } from '~/components/ui/Typography/Typography';
import { useCartTotalValues } from '~/hooks/cart/useCartTotalValues';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useBillingDetails } from '~/hooks/reactQuery/user/useUser';
import { useLocale } from '~/hooks/useLocale';

interface Props {
  estimation?: IEstimation;
  cartSummaryProduct?: ICartSummaryProduct;
}

export const CartTotal = ({ cartSummaryProduct, estimation }: Props) => {
  const { t } = useTranslation();
  const { country } = useLocale();
  const isEstimateMutating = useIsMutating({
    predicate: mutation =>
      !!['addUnloggedEstimate', 'addEstimate'].some(i =>
        mutation.options.mutationKey?.includes(i),
      ),
  });

  const billingDetailsQuery = useBillingDetails();
  const billingDetails = billingDetailsQuery.data;

  const hasNoBillingDetails = isEmpty(billingDetails);

  const catalogQuery = useCatalog();
  const catalog = catalogQuery.data;

  const cartTotalValues = useCartTotalValues(estimation, catalog);

  // TODO: etienned clean
  const {
    creditsAppliedAmount,

    // lineItems,
    // discountAmount,

    amountDue,
    // taxesList,
    // amountWithoutTaxes,

    isTaxInclusive,

    nextDiscountedPaymentsAmount,
    nextDiscountedPaymentsDuration,

    regularPaymentAmount,
    regularPaymentDateInMs,
  } = cartTotalValues;

  const taxesDetailsString = isTaxInclusive
    ? t('cart.total.priceIncludingTaxes', 'Incl. Taxes')
    : t('cart.total.priceExcludingTaxes', 'Excl. Taxes');

  const defaultPeriodicityNamespace = `${t(
    `periodicity.noun.${cartSummaryProduct?.periodUnit}`,
    {
      count: cartSummaryProduct?.period,
    },
  )}`;
  const futurePlanId =
    estimation?.future_estimate?.invoice_estimate?.line_items?.find(
      lineItem => lineItem.entity_type === 'plan_item_price',
    )?.entity_id ?? '';
  const futurePlanOffer = catalog?.offers?.byId[futurePlanId];
  const futurePeriodicityString = `/ ${t(
    `periodicity.noun.${futurePlanOffer?.period_unit}`,
    defaultPeriodicityNamespace,
    {
      count: futurePlanOffer?.period,
    },
  )}`;

  if (!cartSummaryProduct || isEstimateMutating) {
    return <CartTotalSkeleton />;
  }

  return (
    <Card>
      <Flex gap="large" vertical>
        {/* Promotional Credits and credits_applied from invoice estimate */}
        {!!creditsAppliedAmount && (
          <Flex gap="middle" justify="space-between">
            <Text small>
              {t('cart.total.creditsApplied', 'Credits applied')}
            </Text>
            <Text small>{creditsAppliedAmount}</Text>
          </Flex>
        )}

        {/* Invoice estimate amount due */}
        {/* When user has or no billing details*/}
        {hasNoBillingDetails ? (
          <DetailedPrice
            hasBigLabel={true}
            hasBigAmount={true}
            label={t('cart.estimate.total.label', 'Total')}
            amount={amountDue}
          />
        ) : (
          <DetailedPrice
            hasBigLabel={true}
            hasBigAmount={true}
            label={t('cart.total.firstPayment', 'First payment')}
            labelTooltip={t(
              'cart.total.tooltip',
              'Billing will start when you get access to your Shadow and immediately for Shadow Drive. If you want to use a coupon, you’ll be able to apply it on the next steps.',
            )}
            sublineLeft={t('cart.total.priceIncludingTaxes', 'Incl. Taxes')}
            amount={amountDue}
          />
        )}
        {/* Future price for X months if a coupon with a dicount for multiple months is applied */}
        {!!nextDiscountedPaymentsAmount && (
          <DetailedPrice
            label={t('cart.total.thenDiscount', {
              defaultValue: 'Then the next {{duration}} months',
              duration: nextDiscountedPaymentsDuration,
            })}
            amount={nextDiscountedPaymentsAmount}
            sublineLeft={taxesDetailsString}
            sublineRight={futurePeriodicityString}
          />
        )}

        {/* Regular amount to be paid after the first payment, and discounted payments (if any). */}
        {!!regularPaymentAmount && (
          <DetailedPrice
            id="next-payment"
            label={
              regularPaymentDateInMs
                ? t('cart.total.pricingFromDate', {
                    defaultValue: 'From {{date}}',
                    date: formatDate(
                      regularPaymentDateInMs,
                      DATE_FORMAT_WITH_SLASH_BY_MARKET[country],
                    ),
                  })
                : t('cart.total.pricingThen', 'Then')
            }
            amount={regularPaymentAmount}
            sublineLeft={taxesDetailsString}
            sublineRight={futurePeriodicityString}
          />
        )}
        <CartButtons amountDue={amountDue} />
      </Flex>
    </Card>
  );
};
