import { Flex, Skeleton } from 'antd';

import { Card } from '~/components/ui/Card/Card';

export const CartTotalSkeleton = () => (
  <Card>
    {/* Header */}
    <Flex justify="space-between" gap="middle">
      <Skeleton.Button active shape={'round'} style={{ width: '70px' }} />
      <Skeleton.Button active shape={'round'} style={{ width: '75px' }} />
    </Flex>

    {/* Body */}
    <Flex vertical>
      <Flex gap="small" align="center" justify="space-between">
        <Flex gap="small" vertical>
          <Skeleton.Button active shape={'round'} style={{ width: '150px' }} />
          <Skeleton.Button
            active
            size={'small'}
            shape={'round'}
            style={{ width: '80px' }}
          />
        </Flex>
        <Flex vertical gap="small" align="end">
          <Skeleton.Button active shape={'round'} style={{ width: '75px' }} />
          <Skeleton.Button
            active
            size={'small'}
            shape={'round'}
            style={{ width: '50px' }}
          />
        </Flex>
      </Flex>
    </Flex>

    {/* Button */}
    <Skeleton.Button active size="large" shape={'round'} block={true} />
  </Card>
);
