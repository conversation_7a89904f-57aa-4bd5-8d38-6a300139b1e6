import { Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { ICartSummaryProduct, IEstimation, ProductType } from 'types';
import { getProductFromOfferId } from 'utils';

import { CartSummaryProductDetail } from '~/components/Cart/CartSummary/CartSummaryDetail';
import { CartSummarySkeleton } from '~/components/Cart/CartSummary/CartSummarySkeleton';
import { EditDatacenter } from '~/components/Cart/CartSummary/EditDatacenter';
import { Coupon } from '~/components/Cart/Coupon/Coupon';
import { Card } from '~/components/ui/Card/Card';
import { Price } from '~/components/ui/Price/Price';
import { Title } from '~/components/ui/Typography/Typography';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { isUpdateCharge } from '~/utils/changePlan';

interface Props {
  cartSummaryProduct: ICartSummaryProduct | undefined;
  displayAddCoupon: boolean;
  estimation?: IEstimation;
}

export const CartSummary = ({
  cartSummaryProduct,
  displayAddCoupon,
  estimation,
}: Props) => {
  const { t } = useTranslation();
  const catalogQuery = useCatalog();
  const catalog = catalogQuery.data;

  if (!cartSummaryProduct) {
    return <CartSummarySkeleton />;
  }

  const { planOffer, name, basePrice, amountDue, offers } = cartSummaryProduct;

  const offersToDisplay = offers.filter(offer => {
    const productFromOffer = getProductFromOfferId(catalog, offer.id);

    if (!productFromOffer) {
      // TODO: remove this once the charges are available in the catalog (used for "cloudpc_update_charge")
      // revert back to this -> return false;
      return isUpdateCharge(offer);
    }

    return (
      productFromOffer.type === ProductType.PLAN ||
      (productFromOffer.type === ProductType.ADDON &&
        productFromOffer.meta_data?.showInCart)
    );
  });

  const shouldDisplayDetails = offersToDisplay.length > 1 || amountDue > 0;

  return (
    <Card>
      <Flex gap="middle" vertical>
        <Flex gap="middle" justify="space-between">
          <Title level={4} primary>
            {name}
          </Title>
          <Price
            id="offer-catalog-price"
            priceLine={basePrice}
            subLine={t(`periodicity.adjective.${planOffer.period_unit}`, {
              count: planOffer.period,
            })}
          />
        </Flex>
        <Flex component="ul" vertical>
          {/* Add datacenter edit before product info */}
          <EditDatacenter />
          {catalog &&
            shouldDisplayDetails &&
            offersToDisplay.map((offer, index) => {
              return (
                <CartSummaryProductDetail
                  key={`detail-${index}`}
                  cartSummaryProductOffer={offer}
                  catalog={catalog}
                  id={`offer-${offer.productId}-price`}
                  planOffer={planOffer}
                />
              );
            })}
        </Flex>
      </Flex>
      {displayAddCoupon && <Coupon estimation={estimation} />}
    </Card>
  );
};
