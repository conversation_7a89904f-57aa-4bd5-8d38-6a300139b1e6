import { Badge, Flex, message } from 'antd';
import { useDatacenterListForPlan } from 'hooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ProductType } from 'types';
import { getDatacenterNameTranslated } from 'utils';

import { EditDatacenterModal } from '~/components/modals/EditDatacenterModal';
import { Link, Text } from '~/components/ui/Typography/Typography';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';

export const EditDatacenter = () => {
  const { t } = useTranslation();
  const { zipcode, geolocResults, datacenterName, offersInCart } = useStore();
  const { country } = useLocale();

  const [isEditDCModalOpened, setIsEditDCModalOpened] = useState(false);

  const planInCart = offersInCart?.find(
    offerInCart => offerInCart.type === ProductType.PLAN,
  );

  const { data: datacenterData, isError } = useDatacenterListForPlan({
    zipcode: zipcode ?? geolocResults?.zipcode ?? null,
    country,
    planId: planInCart?.productId,
  });

  const currentDatacenter = datacenterData?.find(
    dcDetails => dcDetails.name === datacenterName,
  );

  if (isError) {
    message.error(t('errors.api.catalog'));
    return null;
  }

  const renderDatacenterText = () => {
    // If there is more than one datacenter, we display a link to change it
    if (datacenterData && datacenterData.length > 1) {
      return (
        <Link
          small
          underline
          data-testid="cart-changeDatacenter-button"
          onClick={() => setIsEditDCModalOpened(true)}
        >
          {t('cart.changeDatacenterLabel', 'Datacenter (change)')}
        </Link>
      );
    }
    return <Text small>{t('cart.datacenterLabel', 'Datacenter')}</Text>;
  };

  return (
    <>
      <Flex component="li" gap="small" justify="space-between">
        <Badge
          status="default"
          text={renderDatacenterText()}
          style={{ alignSelf: 'center' }}
        />
        {currentDatacenter && (
          <Text>
            {/* TODO: etienned fix as any */}
            {getDatacenterNameTranslated(t as any, currentDatacenter?.name)}
          </Text>
        )}
      </Flex>
      <EditDatacenterModal
        isOpened={isEditDCModalOpened}
        onClose={() => setIsEditDCModalOpened(false)}
        datacenterList={datacenterData ?? []}
        currentDatacenterName={datacenterName ?? ''}
      />
    </>
  );
};
