import { useTranslation } from 'react-i18next';
import {
  ICartSummaryProduct,
  ICatalog,
  IAnyOffer,
  ProductType,
  ChargeType,
} from 'types';
import { getActivationFeesOffer, getProductFromOfferId } from 'utils';

import { Text } from '~/components/ui/Typography/Typography';
import { formatPrice } from '~/utils/price';

interface ICartSummaryProductValueProps {
  id?: string;
  planOffer: IAnyOffer;
  cartSummaryProductOffer: ICartSummaryProduct['offers'][number];
  catalog: ICatalog;
}

export const CartSummaryProductValue = ({
  id,
  planOffer,
  cartSummaryProductOffer,
  catalog,
}: ICartSummaryProductValueProps) => {
  const { t } = useTranslation();

  const productFromOffer = getProductFromOfferId(
    catalog,
    cartSummaryProductOffer.id,
  );
  const isActivationFeeCharge =
    productFromOffer?.type === ProductType.CHARGE &&
    productFromOffer.meta_data?.type === ChargeType.ACCESSFEES;

  const planInCart = catalog.products.byId[planOffer.product_id];
  const isPlanComparisonBase =
    planInCart?.meta_data?.isPlanComparisonBase &&
    planOffer.periodicity === '1-month';

  const activationFeesOffer = getActivationFeesOffer(catalog);
  const hasNoApplicableActivationFeesOffer =
    cartSummaryProductOffer.amount > 0 ||
    !activationFeesOffer ||
    activationFeesOffer.price === 0;

  if (
    !isActivationFeeCharge ||
    isPlanComparisonBase ||
    hasNoApplicableActivationFeesOffer
  ) {
    return <Text data-test-id={id}>{cartSummaryProductOffer.price}</Text>;
  }

  const discountedPrice = activationFeesOffer?.price;
  const discountedPriceCurrency = activationFeesOffer?.currency_code;

  return (
    <>
      <Text type="success" data-test-id={id}>
        {t('cart.price.free', 'Free')}
      </Text>
      <Text
        type="success"
        data-test-id={id}
        style={{ textDecoration: 'line-through' }}
      >
        {formatPrice(discountedPrice, discountedPriceCurrency)}
      </Text>
    </>
  );
};
