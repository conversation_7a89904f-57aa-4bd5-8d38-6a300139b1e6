import { Badge, Flex, Space } from 'antd';
import { IAnyOffer, ICartSummaryProduct, ICatalog } from 'types';

import { CartSummaryProductLabel } from '~/components/Cart/CartSummary/CartSummaryLabel';
import { CartSummaryProductValue } from '~/components/Cart/CartSummary/CartSummaryValue';

interface ICartSummaryItemRow {
  id?: string;
  planOffer: IAnyOffer;
  cartSummaryProductOffer: ICartSummaryProduct['offers'][number];
  catalog: ICatalog;
  'data-test-id'?: string;
}

export const CartSummaryProductDetail = ({
  cartSummaryProductOffer,
  catalog,
  planOffer,
  ...props
}: ICartSummaryItemRow) => (
  <Flex
    component="li"
    gap="small"
    justify="space-between"
    data-test-id={props['data-test-id']}
  >
    <Space size="small">
      <Badge status="default" />
      <CartSummaryProductLabel
        cartSummaryProductOffer={cartSummaryProductOffer}
        catalog={catalog}
      />
    </Space>
    <Flex vertical align="end" justify="center">
      <CartSummaryProductValue
        id={`offer-${cartSummaryProductOffer.productId}-price`}
        planOffer={planOffer}
        cartSummaryProductOffer={cartSummaryProductOffer}
        catalog={catalog}
      />
    </Flex>
  </Flex>
);
