import { Flex, Skeleton } from 'antd';
import { isEmpty } from 'lodash';

import { CouponPreviewSkeleton } from '~/components/Cart/Coupon/CouponPreview/CouponPreviewSkeleton';
import { Card } from '~/components/ui/Card/Card';
import useStore from '~/store';

export const CartSummarySkeleton = () => {
  const { coupon } = useStore();

  return (
    <Card>
      <Flex justify="space-between" vertical gap="middle">
        {/* Header */}
        <Flex justify="space-between" gap="middle">
          <Skeleton.Button active shape={'round'} style={{ width: '130px' }} />
          <Flex vertical gap="small" align="end">
            <Skeleton.Button active shape={'round'} style={{ width: '75px' }} />
            <Skeleton.Button
              active
              size={'small'}
              shape={'round'}
              style={{ width: '60px' }}
            />
          </Flex>
        </Flex>

        {/* Body */}
        <Flex vertical>
          <Flex gap="small" align="center" justify="space-between">
            <Flex gap="small">
              <Skeleton.Avatar active size={'small'} />
              <Skeleton.Button
                active
                size={'small'}
                shape={'round'}
                style={{ width: '155px' }}
              />
            </Flex>
            <Skeleton.Button
              active
              size={'small'}
              shape={'round'}
              style={{ width: '50px' }}
            />
          </Flex>
          <Flex gap="small" align="center" justify="space-between">
            <Flex gap="small">
              <Skeleton.Avatar active size={'small'} />
              <Skeleton.Button
                active
                size={'small'}
                shape={'round'}
                style={{ width: '155px' }}
              />
            </Flex>
            <Skeleton.Button
              active
              size={'small'}
              shape={'round'}
              style={{ width: '50px' }}
            />
          </Flex>
        </Flex>
      </Flex>

      {/* Coupon */}
      {isEmpty(coupon) ? (
        <Skeleton.Button
          active
          size="small"
          shape={'round'}
          style={{ width: '200px' }}
        />
      ) : (
        <CouponPreviewSkeleton />
      )}
    </Card>
  );
};
