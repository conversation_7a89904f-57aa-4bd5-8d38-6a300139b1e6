import { Space } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  AddonType,
  ChargeType,
  ICartSummaryProduct,
  ICatalog,
  ProductType,
} from 'types';
import { STORAGE_SLICE_SIZE_GO, getProductFromOfferId } from 'utils';

import { AccessFeesDialog } from '~/components/AccessFees/AccessFeesDialog';
import { Text } from '~/components/ui/Typography/Typography';
import useStore from '~/store';

interface ICartSummaryProductLabelProps {
  cartSummaryProductOffer: ICartSummaryProduct['offers'][number];
  catalog: ICatalog;
}

export const CartSummaryProductLabel = ({
  cartSummaryProductOffer,
  catalog,
}: ICartSummaryProductLabelProps) => {
  const { t } = useTranslation();
  const { vmsCount } = useStore();

  const productFromOfferId = getProductFromOfferId(
    catalog,
    cartSummaryProductOffer.id,
  );

  const isAccessFees =
    productFromOfferId?.type === ProductType.CHARGE &&
    productFromOfferId.meta_data?.type === ChargeType.ACCESSFEES;

  const isExtraStorage =
    productFromOfferId?.type === ProductType.ADDON &&
    productFromOfferId.meta_data?.type === AddonType.STORAGE;

  const extraStorageLabel = t('cart.summaryExtraStorage.value', {
    defaultValue: '{{storageQuantity, storageUnit}}',
    storageQuantity: cartSummaryProductOffer.quantity * STORAGE_SLICE_SIZE_GO,
  });

  const isMultipleVmsOrder = !!vmsCount && vmsCount > 1;
  const isQuantityDisplayed = isMultipleVmsOrder;

  return (
    <>
      <Space size="small">
        {isQuantityDisplayed && (
          <Text
            data-test-id={`${cartSummaryProductOffer.productId}-quantity`}
            strong
            small
          >
            {`${
              isMultipleVmsOrder ? vmsCount : cartSummaryProductOffer.quantity
            }x`}
          </Text>
        )}
        <Text
          small
          data-test-id={`offer-${cartSummaryProductOffer.productId}-name`}
        >
          {t(`cart.summary.product.name.${cartSummaryProductOffer.productId}`)}
        </Text>
        {isExtraStorage && (
          <Text
            small
            data-test-id={`${cartSummaryProductOffer.productId}-storage-volume`}
          >
            ({extraStorageLabel})
          </Text>
        )}
      </Space>
      {isAccessFees && (
        <AccessFeesDialog cartSummaryProductOffer={cartSummaryProductOffer} />
      )}
    </>
  );
};
