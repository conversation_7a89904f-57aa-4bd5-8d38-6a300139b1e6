import { TFunction } from 'i18next';
import {
  Currency,
  ICartSummaryProduct,
  ICatalog,
  IPlan,
  IEstimation,
  ProductType,
  ISubscription,
} from 'types';
import {
  getOffer,
  getOfferFromId,
  getProductFamilyIncludedFreeProducts,
  getProductFromId,
} from 'utils';

import { OfferInCart } from '~/types/cart';
import {
  getPlanAndPlanOfferFromOffersInCart,
  getCartTotalAmount,
  getOfferInCartAlignedPrice,
} from '~/utils/cart';
import { isUpdateCharge } from '~/utils/changePlan';
import { formatPrice } from '~/utils/price';

export const mapOffersInCartToCartSummaryProduct = (
  offersInCart: OfferInCart[],
  catalog: ICatalog | undefined,
  currency: Currency,
  t: TFunction,
  estimation: IEstimation | undefined,
  subscriptions: ISubscription[] | undefined,
  vmsCount: number,
): ICartSummaryProduct | undefined => {
  const { plan, planOffer } = getPlanAndPlanOfferFromOffersInCart(
    catalog,
    offersInCart,
  );

  if (!planOffer || !plan || !estimation) {
    return;
  }

  const includedProducts = plan.meta_data?.target
    ? getProductFamilyIncludedFreeProducts(
        catalog,
        plan.meta_data?.target,
        subscriptions,
        plan.item_family_id,
      )
    : null;

  const cartFullPrice = getCartTotalAmount(
    catalog,
    vmsCount,
    offersInCart,
    estimation,
  );

  // We subtract setup fees and all charges from the 'Shadow PC' monthly total.
  // Ex: For games, it's a separate section in the cart and should not be included in the 'Shadow PC' total.

  const chargesInCart = offersInCart.filter(offerInCart => {
    return offerInCart?.type === ProductType.CHARGE;
  });

  const totalAmount = chargesInCart.reduce(
    (total, charge) => total + charge.price,
    0,
  );

  // This is the total for the main plan, not the amount due or the real cart total.
  const mainBasePrice = cartFullPrice - totalAmount;

  const offersWithAlignedPrices = offersInCart.reduce(
    (cartSummaryOffers, offerInCart) => {
      const offer = getOfferFromId(catalog, offerInCart.id);

      if (!offer || !estimation) {
        // TODO: remove this once the charges are available in the catalog (used for "cloudpc_update_charge")
        if (isUpdateCharge(offerInCart)) {
          cartSummaryOffers.push({
            id: offerInCart.id,
            productId: offerInCart.productId,
            name: offerInCart.name,
            price: formatPrice(offerInCart.price, currency),
            amount: offerInCart.price,
            quantity: offerInCart.quantity,
            type: offerInCart.type,
          });
        }
        return cartSummaryOffers;
      }
      const product = getProductFromId(catalog, offer.product_id);
      if (!product || product.meta_data?.showInCart === false) {
        return cartSummaryOffers;
      }

      let offerAmount: number;

      const offerFromFutureEstimate =
        estimation?.future_estimate?.next_invoice_estimate?.line_items?.find(
          lineItem => lineItem.entity_id === offerInCart.id,
        );

      if (offerFromFutureEstimate) {
        offerAmount = offerFromFutureEstimate.amount / 100;
      } else {
        const offerAlignedPrice = getOfferInCartAlignedPrice(
          catalog,
          vmsCount,
          offerInCart,
          offer,
          planOffer,
          plan as IPlan,
        );
        offerAmount = offerAlignedPrice;
      }

      cartSummaryOffers.push({
        id: offerInCart.id,
        productId: offerInCart.productId,
        name: offerInCart.name,
        price: formatPrice(offerAmount, currency),
        amount: offerAmount,
        quantity: offerInCart.quantity,
        type: offerInCart.type,
      });

      return cartSummaryOffers;
    },
    [] as ICartSummaryProduct['offers'],
  );

  if (includedProducts) {
    const { productFamilyIncludedFreeProductsIds } = includedProducts;
    for (const productId of productFamilyIncludedFreeProductsIds) {
      const product = catalog?.products.byId[productId];
      const productOffer = getOffer(
        catalog,
        productId,
        offer => offer.periodicity === planOffer.periodicity,
      );
      if (!product || !productOffer) {
        continue;
      }

      offersWithAlignedPrices.push({
        id: productOffer.id,
        amount: 0,
        productId,
        name: product.name,
        price: formatPrice(0, currency),
        quantity: 1,
        type: product.type,
      });
    }
  }

  return {
    planOffer,
    name: t(`cart.header.family.${planOffer.itemFamilyId}`),
    period: planOffer.period,
    periodUnit: planOffer.period_unit,
    basePrice: formatPrice(mainBasePrice, currency),
    amountDue: cartFullPrice,
    offers: offersWithAlignedPrices,
  };
};
