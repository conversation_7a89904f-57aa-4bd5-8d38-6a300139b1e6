import { PlusOutlined } from '@ant-design/icons';
import { Form, Input, Space } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';

import useStyles from '~/components/Cart/Coupon/Coupon.styles';
import { Button } from '~/components/ui/Button/Button';
import {
  usePublicVerifyCoupon,
  useVerifyCoupon,
} from '~/hooks/reactQuery/user/useUser';
import useStore from '~/store';
import { IVerifyCoupon } from '~/types/api';

interface Props {
  setCouponStep: (step: 'label' | 'input' | 'preview') => void;
}

export const CouponForm = ({ setCouponStep }: Props) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const [form] = Form.useForm();
  const { user } = useAuth();
  const authToken = user?.access_token;

  const { coupon, setCoupon } = useStore();
  const [isBadCoupon, setIsBadCoupon] = useState(false);

  const {
    mutateAsync: verifyCouponMutateAsync,
    isLoading: isVerifyCouponLoading,
  } = useVerifyCoupon();

  const {
    mutateAsync: publicVerifyCouponMutateAsync,
    isLoading: isPublicVerifyCouponLoading,
  } = usePublicVerifyCoupon();

  const handleSubmit = (values: IVerifyCoupon) => {
    (authToken
      ? verifyCouponMutateAsync({
          coupon: values.coupon,
        })
      : publicVerifyCouponMutateAsync({
          coupon: values.coupon,
        })
    )
      .then(response => {
        if (response) {
          setCoupon(values.coupon);
          setIsBadCoupon(false);
          setCouponStep('preview');
          form.resetFields();
        } else {
          setIsBadCoupon(true);
          form.setFields([
            {
              name: 'coupon',
              errors: [
                t(
                  'cart.addCoupon.invalid',
                  `Sorry, but this coupon is invalid`,
                ),
              ],
            },
          ]);
        }
      })
      .catch(() => {
        // No message.error here, since we already show the error in the form
        setIsBadCoupon(true);
      })
      .finally();
  };

  return (
    <Form
      form={form}
      onFinish={handleSubmit}
      initialValues={{
        coupon: coupon,
      }}
    >
      <Space.Compact className={styles.promoCode__form__space}>
        <Form.Item
          name="coupon"
          className={styles.promoCode__form__input}
          rules={[
            {
              required: true,
              message: t('cart.addCoupon.needed', 'Please enter a coupon code'),
            },
          ]}
        >
          <Input
            data-testid="cart-addCoupon-input"
            status={isBadCoupon ? 'error' : undefined}
            placeholder={t(
              'cart.addCoupon.placeholder',
              'Add your coupon here',
            )}
            value={coupon}
            onChange={() => setIsBadCoupon(false)}
          />
        </Form.Item>
        <Button
          data-testid="cart-addCoupon-button"
          htmlType="submit"
          loading={isVerifyCouponLoading || isPublicVerifyCouponLoading}
          icon={<PlusOutlined />}
        />
      </Space.Compact>
    </Form>
  );
};
