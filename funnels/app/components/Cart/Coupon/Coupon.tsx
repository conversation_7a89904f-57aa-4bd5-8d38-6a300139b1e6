import { isEmpty } from 'lodash';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IEstimation } from 'types';

import { CouponForm } from '~/components/Cart/Coupon/CouponForm';
import { CouponPreview } from '~/components/Cart/Coupon/CouponPreview/CouponPreview';
import { Link } from '~/components/ui/Typography/Typography';
import useStore from '~/store';

interface Props {
  estimation?: IEstimation;
}

export const Coupon = ({ estimation }: Props) => {
  const { t } = useTranslation();
  const { coupon, setCoupon } = useStore();

  const getCouponStep = (couponToTest: string) => {
    if (isEmpty(couponToTest)) {
      return 'label';
    }
    return 'preview';
  };

  const [couponStep, setCouponStep] = useState<'label' | 'input' | 'preview'>(
    getCouponStep(coupon),
  );

  const removeCoupon = () => {
    setCoupon('');
    setCouponStep('input');
  };

  return (
    <>
      {couponStep === 'label' && (
        <Link
          data-testid="cart-showCoupon-button"
          onClick={() => setCouponStep('input')}
        >
          {t('cart.addCoupon.title', 'Add coupon')}
        </Link>
      )}
      {couponStep === 'input' && <CouponForm setCouponStep={setCouponStep} />}
      {couponStep === 'preview' && (
        <CouponPreview
          coupon={coupon}
          estimation={estimation}
          onRemoveCoupon={removeCoupon}
        />
      )}
    </>
  );
};
