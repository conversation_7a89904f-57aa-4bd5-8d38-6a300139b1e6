import { DeleteOutlined } from '@ant-design/icons';
import { useIsMutating } from '@tanstack/react-query';
import { Flex, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { DiscountDurationType, IEstimation } from 'types';

import { CouponPreviewSkeleton } from '~/components/Cart/Coupon/CouponPreview/CouponPreviewSkeleton';
import { Link, Text, Title } from '~/components/ui/Typography/Typography';
import { useCurrency } from '~/hooks/useCurrency';
import {
  getCurrentCouponFromEstimation,
  getDiscountValue,
} from '~/utils/estimation';
import { formatPrice } from '~/utils/price';

interface ICouponProps {
  coupon: string;
  estimation?: IEstimation;
  onRemoveCoupon: () => void;
}

export const CouponPreview = ({
  coupon,
  estimation,
  onRemoveCoupon,
}: ICouponProps) => {
  const { t } = useTranslation();
  const currency = useCurrency();

  const attachedCoupon = getCurrentCouponFromEstimation(estimation, coupon);
  const discountValue = getDiscountValue(estimation, coupon);

  const isAddEstimateMutating = useIsMutating(['addEstimate']);
  const isAddUnloggedEstimateMutating = useIsMutating(['addUnloggedEstimate']);
  const isGetSubscriptionModificationEstimationMutating = useIsMutating([
    'getSubscriptionModificationEstimation',
  ]);

  const isEstimateMutating =
    isAddEstimateMutating ||
    isAddUnloggedEstimateMutating ||
    isGetSubscriptionModificationEstimationMutating;

  if (isEstimateMutating) {
    return <CouponPreviewSkeleton />;
  }

  if (!attachedCoupon || !discountValue) {
    return null;
  }

  let discountLabel = '';

  const valueString = attachedCoupon.discount_percentage
    ? `${attachedCoupon.discount_percentage}%`
    : formatPrice((attachedCoupon.discount_amount || 0) / 100, currency);

  if (attachedCoupon.duration_type === DiscountDurationType.FOREVER) {
    discountLabel = t('cart.coupon.description.forever', {
      defaultValue: '{{value}} off forever',
      value: valueString,
    });
  } else if (
    attachedCoupon.duration_type === DiscountDurationType.LIMITED_PERIOD &&
    attachedCoupon.duration_month > 1
  ) {
    discountLabel = t('cart.coupon.description.limitedPeriod', {
      defaultValue: '{{value}} off for {{duration}} months',
      value: valueString,
      duration: attachedCoupon.duration_month,
    });
  } else {
    discountLabel = t('cart.coupon.description.oneTime', {
      defaultValue: '{{value}} off on the first payment',
      value: valueString,
    });
  }

  return (
    <Flex vertical gap="small">
      <Flex gap="middle" justify="space-between">
        <Title level={4} primary>
          {t('cart.coupon.title', 'Discount')}
        </Title>
        <Title level={4} data-test-id="cart-summary-coupon-amount" primary>
          {`- ${formatPrice(discountValue, currency)}`}
        </Title>
      </Flex>
      <Flex gap="middle" justify="space-between" align="center">
        <Text small style={{ flex: 1 }}>
          {discountLabel}
        </Text>
        <Link
          small
          data-test-id="cart-removeCoupon-button"
          onClick={() => onRemoveCoupon()}
        >
          <Space size="small">
            <DeleteOutlined />
            {t('cart.coupon.remove.label', 'Remove')}
          </Space>
        </Link>
      </Flex>
    </Flex>
  );
};
