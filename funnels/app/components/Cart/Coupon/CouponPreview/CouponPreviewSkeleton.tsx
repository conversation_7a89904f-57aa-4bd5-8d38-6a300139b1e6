import { Flex, Skeleton } from 'antd';

export const CouponPreviewSkeleton = () => (
  <Flex justify="space-between" vertical gap="middle">
    <Flex justify="space-between" gap="middle">
      <Flex style={{ width: '90px' }} gap="small" vertical>
        <Skeleton.Button active shape={'round'} block={true} />
        <span style={{ width: '175px' }}>
          <Skeleton.Button active size={'small'} shape={'round'} block={true} />
        </span>
      </Flex>
      <Flex style={{ width: '90px' }} vertical gap="small" align="right">
        <Skeleton.Button active shape={'round'} block={true} />
        <Flex style={{ alignSelf: 'flex-end', width: '65px' }} gap="small">
          <Skeleton.Avatar active size={'small'} />
          <Skeleton.Button active size={'small'} shape={'round'} block={true} />
        </Flex>
      </Flex>
    </Flex>
  </Flex>
);
