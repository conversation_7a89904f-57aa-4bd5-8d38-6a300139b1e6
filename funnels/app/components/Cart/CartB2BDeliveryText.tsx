import { useTranslation } from 'react-i18next';

import { Text } from '~/components/ui/Typography/Typography';

export const CartB2BDeliveryText = () => {
  const { t } = useTranslation();

  // TODO: check etienne
  const isB2BVmOnDemandSubscriptionFunnel = false;

  const b2BDeliveryText = isB2BVmOnDemandSubscriptionFunnel
    ? t(
        'b2b.cart.vmOnDemandDescription',
        "These fees are applied to setup your API access and credentials. However, you'll get $50 of Spot Computing credits as a starting gift.",
      )
    : t(
        'b2b.cart.description',
        'The Shadow PC will be available at the end of the configuration (up to one hour), an email will be sent to the assigned user when his Shadow PC is ready.',
      );

  return <Text small>{b2BDeliveryText}</Text>;
};
