import { Collapse, CollapseProps } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from '~/components/Confirmation/FaqCard/FaqCard.style';
import { Link, Title } from '~/components/ui/Typography/Typography';
import { useUrl } from '~/hooks/useUrl';
import { CLOUDPC_FAQ_URL } from '~/utils/constants';

interface IFaqCardProps {
  isDriveProduct: boolean;
}

export const FaqCard = ({ isDriveProduct }: IFaqCardProps) => {
  const { t } = useTranslation();
  const { styles } = useStyles();

  const { getGlobalFaqUrl } = useUrl();

  const cloudPCFaqContent: CollapseProps['items'] = [
    {
      id: 'faqQuestion1',
      label: t('confirmation.shadowPc.faq.q1', 'What do I need to use Shadow?'),
      children: (
        <Trans i18nKey="confirmation.shadowPc.faq.a1">
          Two things are needed to connect to <PERSON>: a stable Internet
          connection and a device compatible with our app. Check our{' '}
          <Link href={CLOUDPC_FAQ_URL} title="Requirements" target="_blank">
            Requirement page
          </Link>{' '}
          to be sure.
        </Trans>
      ),
    },
    {
      id: 'faqQuestion2',
      label: t(
        'confirmation.shadowPc.faq.q2',
        'What Internet connexion is required to use Shadow?',
      ),
      children: t(
        'confirmation.shadowPc.faq.a2',
        'Shadow automatically adapts to the available bandwidth: the higher the download speed, the better the video quality can be. 15 Mbps of download speed and less than 30 ms of ping are recommended for fast response time.',
      ),
    },
    {
      id: 'faqQuestion3',
      label: t(
        'confirmation.shadowPc.faq.q3',
        'Once subscribed, when can I use Shadow?',
      ),
      children: t(
        'confirmation.shadowPc.faq.a3',
        "For the vast majority of users, Shadow is accessible in less than an hour after subscription. After that, your Shadow PC only takes a few minutes to set up. Enjoy your first Shadow session as soon as it's done!",
      ),
    },
  ];

  return (
    <div className={styles.main__wrapper}>
      <div className={styles.main__background} />
      <Title level={1} className={styles.main__title}>
        {t('confirmation.faq.title', 'Frequently asked questions')}
      </Title>
      <Link href={getGlobalFaqUrl(isDriveProduct)}>
        {t('confirmation.faq.link', 'View all frequently asked questions')}
      </Link>
      <Collapse
        className={styles.faq__wrapper}
        items={cloudPCFaqContent}
        expandIconPosition="end"
        bordered={false}
      />
    </div>
  );
};
