import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    main__wrapper: css`
      background: transparent;
      position: relative;
      padding: 40px 16px 0 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 24px;
      z-index: 0;

      @media (min-width: ${token.screenMD}px) {
        padding: 64px;
      }
    `,

    main__title: css`
      &.ant-typography {
        color: ${token.colorPrimary};
        font-size: 32px;
        text-align: center;
      }
    `,

    main__background: css`
      position: absolute;
      z-index: -1;
      left: 0;
      top: 0;
      background: linear-gradient(
        131.73deg,
        rgba(255, 255, 255, 0.8) -36.04%,
        rgba(215, 224, 255, 0.8) 52.66%,
        rgba(238, 241, 255, 1) 139.54%
      );
      border-radius: 16px;
      width: 100%;
      height: 300px;

      ::before,
      ::after {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: -1;
        left: 0;
        top: 0;
        background: ${token.colorBgBase};
        border-radius: 16px;
        inset: 0;
        padding: 1px;
        background: linear-gradient(
            29deg,
            rgba(59, 104, 255, 0.02) 73.18%,
            #3653cc 85.85%
          )
          border-box;
        mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
        mask-composite: exclude;
      }
      ::after {
        background: linear-gradient(
            231deg,
            rgba(59, 104, 255, 0.02) 78.66%,
            #3653cc 93.22%
          )
          border-box;
      }
    `,

    faq__wrapper: css`
      display: flex;
      flex-direction: column;
      background: ${token.colorBgBase};
      width: 100%;
      gap: 24px;
      padding: 32px;

      @media (min-width: ${token.screenMD}px) {
        width: 90%;
      }

      .ant-collapse-item,
      .ant-collapse-item:last-child {
        border-radius: 8px;
        border: none;
      }

      .ant-collapse-header-text {
        font-weight: 400;
      }

      .ant-collapse-item-active {
        background: ${token.colorPrimary};

        .ant-collapse-header-text,
        .ant-collapse-content,
        .ant-collapse-expand-icon {
          color: ${token.colorWhite};
        }
      }

      a.ant-typography {
        color: ${token.colorWhite};
        text-decoration: underline;
        &:hover {
          text-decoration: none;
        }
      }
    `,
  };
});
