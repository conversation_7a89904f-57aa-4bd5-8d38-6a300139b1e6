import { PlayCircleFilled } from '@ant-design/icons';

import useStyles from '~/components/Confirmation/VideoCard/VideoCard.style';
import { Card } from '~/components/ui/Card/Card';
import { useUrl } from '~/hooks/useUrl';

interface IVideoCardProps {
  setIsVideoModalOpen: (value: boolean) => void;
  isDriveProduct: boolean;
}

export const VideoCard = ({
  setIsVideoModalOpen,
  isDriveProduct,
}: IVideoCardProps) => {
  const { styles } = useStyles();

  const { getConfirmationVideoPlaceholder } = useUrl();

  return (
    // <ClickTracker
    //   eventPayload={{
    //     action: 'click',
    //     parameters: {
    //       event_category: 'funnel_step3',
    //       event_label: 'youtube_tutorial',
    //     },
    //   }}
    // >
    <Card
      hoverable
      aria-label="video-tutorial"
      classNames={{
        body: styles.video__body,
      }}
      data-test-id="video-tutorial"
      id="video-tutorial"
      onClick={() => setIsVideoModalOpen(true)}
    >
      <img
        className={styles.video__img}
        src={getConfirmationVideoPlaceholder(isDriveProduct)}
      />
      <PlayCircleFilled className={styles.video__play} />
    </Card>
    // </ClickTracker>
  );
};
