import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    video__body: css`
      button {
        background: none;
        border: none;
        box-shadow: none;
        padding: 0;
        cursor: pointer;
        overflow: hidden;
      }

      &.ant-card-body {
        position: relative;
        width: 100%;
        max-width: 1248px;
        aspect-ratio: 1248 / 500;
        padding: 0;

        &:hover span {
          color: ${token.colorPrimaryHover};
        }
      }
    `,

    video__img: css`
      width: 100%;
      aspect-ratio: 1248 / 500;
      position: absolute;
      left: 0;
      top: 0;
      cursor: pointer;
    `,

    video__play: css`
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      font-size: 75px;
      align-items: center;
      justify-content: center;
      transform: translate(-50%, -50%);
      color: ${token.colorPrimary};
      transition: color 0.3s;
      z-index: 1;

      @media (min-width: ${token.screenMD}px) {
        width: 64px;
        height: 64px;
      }

      @media (min-width: ${token.screenLG}px) {
        width: 78px;
        height: 78px;
      }

      :hover {
        color: ${token.colorPrimaryHover};
      }
    `,

    modal__content: css`
      &.ant-modal-content {
        padding: 0;
        background: transparent;
        box-shadow: none;
      }
    `,
  };
});
