import { Grid, Modal } from 'antd';
import { useEffect, useState } from 'react';
import YouTube, { YouTubeProps } from 'react-youtube';

import useStyles from '~/components/Confirmation/VideoCard/VideoCard.style';
import { useWindowSize } from '~/hooks/useWindowSize';

const MODAL_PADDING_MOBILE = 24;

interface Props {
  videoId?: string;
  isVideoModalOpen: boolean;
  setIsVideoModalOpen: (value: boolean) => void;
}

export const VideoModal = ({
  videoId,
  isVideoModalOpen,
  setIsVideoModalOpen,
}: Props) => {
  const { styles } = useStyles();

  const { width: screenWidth } = useWindowSize(true);
  const [videoSize, setVideoSize] = useState<{
    width: string | number;
    height: string | number;
  }>({ width: 1024, height: 576 });
  const screenWidthHasValue = screenWidth !== 0;

  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  useEffect(() => {
    if (!isVideoModalOpen || !screenWidthHasValue) {
      return;
    }

    if (screens.lg) {
      setVideoSize({ width: 1024, height: 576 });
    } else {
      setVideoSize({
        width: screenWidth - MODAL_PADDING_MOBILE * 2,
        height: ((screenWidth - MODAL_PADDING_MOBILE * 2) * 576) / 1024,
      });
    }
  }, [screenWidth, screenWidthHasValue, isVideoModalOpen, screens.lg]);

  const youtubeOpts: YouTubeProps['opts'] = {
    height: videoSize.height,
    width: videoSize.width,
    playerVars: {
      autoplay: 1,
    },
  };

  return (
    <Modal
      destroyOnHidden
      open={isVideoModalOpen}
      onCancel={() => setIsVideoModalOpen(false)}
      footer={null}
      closable={false}
      width={videoSize.width}
      classNames={{
        content: styles.modal__content,
      }}
      centered
    >
      <YouTube videoId={videoId} opts={youtubeOpts} />
    </Modal>
  );
};
