import { Flex } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from '~/components/Confirmation/ThankingCard/ThankingCard.style';
import { Button } from '~/components/ui/Button/Button';
import { Card } from '~/components/ui/Card/Card';
import {
  CircleQuestionIcon,
  DiscordIcon,
  UserIcon,
} from '~/components/ui/Icon';
import { Text } from '~/components/ui/Typography/Typography';
import {
  useBillingDetails,
  useCurrentMember,
} from '~/hooks/reactQuery/user/useUser';
import { useUrl } from '~/hooks/useUrl';
import {
  ACCOUNT_DRIVE_URL,
  MANAGER_URL,
  WEB_SUPPORT_URL,
} from '~/utils/constants';

interface Props {
  isDriveProduct: boolean;
}

export const ThankingCard = ({ isDriveProduct, ...props }: Props) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const { getDiscordUrl } = useUrl();
  const currentMemberQuery = useCurrentMember();
  const billingDetailsQuery = useBillingDetails();

  const userFirstName = billingDetailsQuery.data?.first_name;
  const userEmail = currentMemberQuery.data?.user?.email;

  return (
    <Card
      {...props}
      title={
        <Trans
          i18nKey="confirmation.thanking.title"
          defaults="Thank you {{userFirstName}},"
          values={{ userFirstName }}
        />
      }
    >
      <Flex vertical gap="middle">
        <Text>
          <Trans
            i18nKey="confirmation.thanking.deliverOrder.label"
            defaults="Your order is being prepared. We make every effort to deliver it as soon as possible"
            components={{ bold: <strong /> }}
          />
          {isDriveProduct ? (
            '.'
          ) : (
            <>
              {' '}
              <Trans
                i18nKey="confirmation.thanking.deliverOrder.time"
                defaults="<bold>(less than 60 min)</bold>."
                components={{ bold: <strong /> }}
              />
            </>
          )}
        </Text>
        <Text>
          <Trans
            i18nKey="confirmation.thanking.confirmationEmail"
            defaults="A confirmation email has been sent to <bold>{{userEmail}}</bold>. As soon as your order is available, you will receive a new email notification with all practical information for getting started."
            values={{ userEmail }}
            components={{ bold: <strong /> }}
          />
        </Text>
      </Flex>
      <div className={styles.small__card__list}>
        <div className={styles.small__card__item}>
          <Text>
            {t(
              'confirmation.thanking.customerSpace.title',
              'Follow your order',
            )}
          </Text>
          {/* <ClickTracker
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'funnel_step3',
                event_label: 'customer_space',
              },
            }}
          > */}
          <Button
            data-testid="customer-space-button"
            id="customer-space-button"
            href={isDriveProduct ? ACCOUNT_DRIVE_URL : MANAGER_URL}
            target="_blank"
            secondary
            size="small"
            icon={<UserIcon />}
          >
            {t('confirmation.thanking.customerSpace.link', 'Customer Space')}
          </Button>
          {/* </ClickTracker> */}
        </div>
        <div className={styles.small__card__item}>
          <Text>
            {t('confirmation.thanking.discord.title', 'Discuss with others')}
          </Text>
          {/* <ClickTracker
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'funnel_step3',
                event_label: 'discord',
              },
            }}
          > */}
          <Button
            data-testid="discord-button"
            id="discord-button"
            href={getDiscordUrl(isDriveProduct)}
            target="_blank"
            secondary
            size="small"
            icon={<DiscordIcon />}
          >
            {t('confirmation.thanking.discord.link', 'Discord')}
          </Button>
          {/* </ClickTracker> */}
        </div>
        <div className={styles.small__card__item}>
          <Text>
            {t('confirmation.thanking.helpCenter.title', 'Find answers')}
          </Text>
          {/* <ClickTracker
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'funnel_step3',
                event_label: 'help_center',
              },
            }}
          > */}
          <Button
            data-testid="help-center-button"
            id="help-center-button"
            href={WEB_SUPPORT_URL}
            target="_blank"
            secondary
            size="small"
            icon={<CircleQuestionIcon />}
          >
            {t('confirmation.thanking.helpCenter.link', 'Help center')}
          </Button>
          {/* </ClickTracker> */}
        </div>
      </div>
    </Card>
  );
};
