import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    small__card__list: css`
      display: grid;
      grid-auto-flow: row;
      gap: ${token.sizeXL}px;

      @media (min-width: ${token.screenLG}px) {
        grid-template-columns: repeat(3, 1fr);
        grid-auto-flow: column;
      }

      @media (min-width: ${token.screenXL}px) {
        margin: 0 auto;
        grid-template-columns: repeat(3, 1fr);
        grid-auto-flow: column;
      }
    `,

    small__card__item: css`
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: ${token.sizeSM}px;
      padding: ${token.paddingMD}px;
      background-color: ${token.colorBgBase};
      border: 1px solid ${token.colorBorder};
      border-radius: ${token.borderRadiusLG}px;
      width: auto;
    `,
  };
});
