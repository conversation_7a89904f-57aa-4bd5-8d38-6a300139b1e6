import { Flex } from 'antd';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from '~/components/Confirmation/SetupStepsCard/SetupStepsCard.style';
import { Card } from '~/components/ui/Card/Card';
import { Link, Text } from '~/components/ui/Typography/Typography';
import { APPS_URL, DRIVE_URL } from '~/utils/constants';

interface ISetupStepsCardProps {
  setIsVideoModalOpen: (value: boolean) => void;
  isDriveProduct: boolean;
}

export const SetupStepsCard = ({
  setIsVideoModalOpen,
  isDriveProduct,
  ...props
}: ISetupStepsCardProps) => {
  const { t } = useTranslation();
  const { styles } = useStyles();

  const getTitle = () => {
    return isDriveProduct
      ? t(
          'confirmation.setupSteps.title.drive',
          'Your first steps with Shadow Drive:',
        )
      : t(
          'confirmation.setupSteps.title.cloudpc',
          'To anticipate the handling of your Shadow PC:',
        );
  };

  return (
    <Card {...props} title={getTitle()} hasBlackTitle>
      <Flex vertical gap="large">
        <ul className={styles.list}>
          {isDriveProduct ? (
            <>
              <ol key="text-item-bullet1" className={styles.item}>
                <Text>
                  <Trans i18nKey="confirmation.setupSteps.bullet1.drive">
                    Watch the explanatory{' '}
                    {/* <ClickTrackerLink
                      eventPayload={{
                        action: 'click',
                        parameters: {
                          event_category: 'funnel_step3',
                          event_label: 'youtube_tutorial',
                        },
                      }}
                      data-test-id="shadow-drive-video-link"
                      id="shadow-drive-video-link"
                      component="a"
                      color="#3653cc"
                      onClick={() => setIsVideoModalOpen(true)}
                      variant="body-sm-link"
                    >
                      video
                    </ClickTrackerLink> */}
                    <Link
                      data-test-id="shadow-drive-video-link"
                      id="shadow-drive-video-link"
                      onClick={() => setIsVideoModalOpen(true)}
                    >
                      video
                    </Link>
                    of Shadow Drive below.
                  </Trans>
                </Text>
              </ol>
              <ol key="text-item-bullet2" className={styles.item}>
                <Text>
                  <Trans i18nKey="confirmation.setupSteps.bullet2.drive">
                    Remember to validate the creation of your account via the
                    validation email you received
                  </Trans>
                </Text>
              </ol>
              <ol key="text-item-bullet3" className={styles.item}>
                <Text>
                  <Trans i18nKey="confirmation.setupSteps.bullet3.drive">
                    Once your delivery email is received, access your{' '}
                    {/* <ClickTrackerLink
                      eventPayload={{
                        action: 'click',
                        parameters: {
                          event_category: 'funnel_step3',
                          event_label: 'access_drive',
                        },
                      }}
                      data-test-id="shadow-drive-access-link"
                      id="shadow-drive-access-link"
                      component="a"
                      color="#3653cc"
                      href={DRIVE_URL}
                      target="_blank"
                      variant="body-sm-link"
                    >
                      Shadow Drive
                    </ClickTrackerLink> */}
                    <Link
                      data-test-id="shadow-drive-access-link"
                      id="shadow-drive-access-link"
                      href={DRIVE_URL}
                      target="_blank"
                    >
                      Shadow Drive
                    </Link>
                  </Trans>
                </Text>
              </ol>
              <ol key="text-item-bullet4" className={styles.item}>
                <Text>
                  <Trans i18nKey="confirmation.setupSteps.bullet4.drive">
                    You can also{' '}
                    {/* <ClickTrackerLink
                      eventPayload={{
                        action: 'click',
                        parameters: {
                          event_category: 'funnel_step3',
                          event_label: 'download_drive',
                        },
                      }}
                      data-test-id="shadow-drive-download-link"
                      id="shadow-drive-download-link"
                      component="a"
                      color="#3653cc"
                      target="_blank"
                      href={APPS_URL}
                      variant="body-sm-link"
                    >
                      download
                    </ClickTrackerLink> */}
                    <Link
                      data-test-id="shadow-drive-download-link"
                      id="shadow-drive-download-link"
                      target="_blank"
                      href={APPS_URL}
                    >
                      download
                    </Link>
                    our Shadow Drive App on Android, iOS and Desktop to sync
                    your data to all your media.
                  </Trans>
                </Text>
              </ol>
            </>
          ) : (
            <>
              <ol key="text-item-bullet1" className={styles.item}>
                <Text>
                  <Trans i18nKey="confirmation.setupSteps.bullet2.cloudpc">
                    {/* <ClickTrackerLink
                      eventPayload={{
                        action: 'click',
                        parameters: {
                          event_category: 'funnel_step3',
                          event_label: 'download_shadow',
                        },
                      }}
                      data-test-id="shadow-pc-download-link"
                      id="shadow-pc-download-link"
                      component="a"
                      color="#3653cc"
                      target="_blank"
                      href={APPS_URL}
                      variant="body-sm-link"
                    >
                      Download Shadow
                    </ClickTrackerLink> */}
                    <Link
                      data-test-id="shadow-pc-download-link"
                      id="shadow-pc-download-link"
                      target="_blank"
                      href={APPS_URL}
                    >
                      Download Shadow
                    </Link>
                    on all your devices for easy access from anywhere to your
                    Shadow PC (thanks to the identifier and password of your
                    customer account)
                  </Trans>
                </Text>
              </ol>
              <ol key="text-item-bullet2" className={styles.item}>
                <Text>
                  <Trans i18nKey="confirmation.setupSteps.bullet3.cloudpc">
                    Watch the{' '}
                    {/* <ClickTrackerLink
                      eventPayload={{
                        action: 'click',
                        parameters: {
                          event_category: 'funnel_step3',
                          event_label: 'youtube_tutorial',
                        },
                      }}
                      data-test-id="shadow-pc-video-link"
                      id="shadow-pc-video-link"
                      component="a"
                      color="#3653cc"
                      onClick={() => setIsVideoModalOpen(true)}
                      variant="body-sm-link"
                    >
                      video
                    </ClickTrackerLink> */}
                    <Link
                      data-test-id="shadow-pc-video-link"
                      id="shadow-pc-video-link"
                      onClick={() => setIsVideoModalOpen(true)}
                    >
                      video
                    </Link>
                    below that guides your first steps with Shadow PC
                  </Trans>
                </Text>
              </ol>
            </>
          )}
        </ul>
      </Flex>
    </Card>
  );
};
