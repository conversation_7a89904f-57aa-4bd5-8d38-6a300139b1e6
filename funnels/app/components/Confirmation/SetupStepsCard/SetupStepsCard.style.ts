import { createStyles } from 'antd-style';

export default createStyles(({ token, css }) => {
  return {
    list: css`
      display: grid;
      gap: ${token.marginSM}px;
      width: 100%;
      counter-reset: li;
      list-style: none;
      list-style: decimal;
      padding: 0;
    `,

    item: css`
      padding: 0;
      display: flex;
      align-items: center;
      position: relative;
      gap: ${token.marginXS}px;

      ::before {
        display: flex;
        content: counter(li);
        width: ${token.marginSM}px;
        height: ${token.marginSM}px;
        align-items: center;
        background: ${token.colorBorderSecondary};
        border-radius: ${token.borderRadiusLG}px;
        counter-increment: li;
        flex-shrink: 0;
        justify-content: center;
        font-size: 12px;
        line-height: 12px;
      }
    `,
  };
});
