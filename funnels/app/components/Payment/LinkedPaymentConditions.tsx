import { Checkbox, Flex, Form } from 'antd';
import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import useStyles from '~/components/Payment/Payment.styles';
import { Link, Text } from '~/components/ui/Typography/Typography';
import { useCurrentMember } from '~/hooks/reactQuery/user/useUser';
import { useUrl } from '~/hooks/useUrl';

interface LinkedPaymentConditionsProps {
  onCheckboxesChange?: (allChecked: boolean) => void;
}

export const LinkedPaymentConditions = ({
  onCheckboxesChange,
}: LinkedPaymentConditionsProps) => {
  const { t } = useTranslation();
  useStyles(); // Keep the hook call without destructuring to avoid unused variable
  const currentMemberQuery = useCurrentMember();

  const isB2bUser =
    !!currentMemberQuery.data?.user?.b2b &&
    (!!currentMemberQuery.data?.user?.first_name ||
      !!currentMemberQuery.data?.user?.last_name);

  const { getCguUrl } = useUrl();

  const [isCguAccepted, setIsCguAccepted] = useState(false);
  const [isWithdrawalAccepted, setIsWithdrawalAccepted] = useState(false);

  useEffect(() => {
    if (onCheckboxesChange) {
      onCheckboxesChange(isCguAccepted && isWithdrawalAccepted);
    }
  }, [isCguAccepted, isWithdrawalAccepted, onCheckboxesChange]);

  return (
    <Form.Item name="checkboxes" valuePropName="checked">
      <Checkbox.Group>
        <Flex vertical gap="small">
          <Checkbox
            value="cguAccepted"
            onChange={() => setIsCguAccepted(!isCguAccepted)}
          >
            <Text small>
              <Trans i18nKey="payment.linked.cguCheck.label">
                I have read and agree to the{' '}
                <Link
                  small
                  underline
                  data-test-id="payment-terms-link"
                  href={getCguUrl(isB2bUser)}
                  target="_blank"
                >
                  Terms of Use
                </Link>
                .
              </Trans>
            </Text>
          </Checkbox>
          <Checkbox
            value="withdrawalAccepted"
            onChange={() => setIsWithdrawalAccepted(!isWithdrawalAccepted)}
          >
            <Text small>
              {t(
                'payment.linked.withdrawal.label',
                'I acknowledge that by subscribing to the Shadow service, I expressly waive my right of withdrawal.',
              )}
            </Text>
          </Checkbox>
        </Flex>
      </Checkbox.Group>
    </Form.Item>
  );
};
