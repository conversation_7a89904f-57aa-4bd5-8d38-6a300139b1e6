import { Flex, Grid } from 'antd';

import { Text } from '~/components/ui/Typography/Typography';

interface Props {
  title: string;
  icon: any;
  information: string;
  expirationDate?: string;
}

export const PaymentMethodInformation = ({
  title,
  icon,
  information,
  expirationDate,
  ...props
}: Props) => {
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  return (
    <Flex {...props} align="center" gap="large">
      {title && screens.lg && <Text>{title}</Text>}
      <Flex gap="middle" align="center">
        {icon && icon}
        {information && <Text small={!screens.lg}>{information}</Text>}
        {expirationDate && (
          <Text
            style={!screens.lg ? { flex: 1, textAlign: 'right' } : undefined}
            small={!screens.lg}
          >
            {expirationDate}
          </Text>
        )}
      </Flex>
    </Flex>
  );
};
