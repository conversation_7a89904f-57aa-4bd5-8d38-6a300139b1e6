import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { IPaymentMethod, PaymentMethodType } from 'types';

import { AddCreditCard } from '~/components/Payment/CreditCardItem/AddCreditCard';
import { SelectCreditCardList } from '~/components/Payment/CreditCardItem/SelectCreditCardList/SelectCreditCardList';
import { LocalLoader } from '~/components/ui/Loader';
import { Text } from '~/components/ui/Typography/Typography';
import { CardChoice } from '~/contexts/PaymentContext';
import { usePaymentMethods } from '~/hooks/reactQuery/user/useUser';

interface ICreditCardItemProps {
  chargebeeInstance: any;
  displaySavedCards?: boolean;
  displaySaveCardButton?: boolean;
  onAddNewCardSuccess?: () => void;
}

const isCard = (paymentMethod: IPaymentMethod) => {
  return paymentMethod.type === PaymentMethodType.CARD;
};

export const CreditCardItem = ({
  chargebeeInstance,
  displaySavedCards,
  displaySaveCardButton,
  onAddNewCardSuccess,
}: ICreditCardItemProps) => {
  const paymentMethodsQuery = usePaymentMethods();
  const savedCard = paymentMethodsQuery.data?.find(isCard);
  const [selectedChoice, setSelectedChoice] = useState<CardChoice | null>(null);

  useEffect(() => {
    if (savedCard && !selectedChoice) {
      setSelectedChoice(CardChoice.SAVED_CARDS);
    }
  }, [savedCard, selectedChoice]);

  const componentsToDisplay = {
    [CardChoice.NEW_CARD]: (
      <AddCreditCard
        chargebeeInstance={chargebeeInstance}
        displaySaveCardButton={displaySaveCardButton}
        onAddNewCardSuccess={onAddNewCardSuccess}
      />
    ),
    [CardChoice.SAVED_CARDS]: paymentMethodsQuery.isSuccess ? (
      <SelectCreditCardList cards={paymentMethodsQuery.data} />
    ) : paymentMethodsQuery.isError ? (
      <Text type="danger">{paymentMethodsQuery.error.message}</Text>
    ) : (
      <></>
    ),
  };

  if (paymentMethodsQuery.isLoading) {
    return <LocalLoader />;
  }

  return savedCard && displaySavedCards ? (
    <Flex gap="small" vertical>
      {paymentMethodsQuery.isSuccess ? (
        <SelectCreditCardList cards={paymentMethodsQuery.data} />
      ) : paymentMethodsQuery.isError ? (
        <Text type="danger">{paymentMethodsQuery.error.message}</Text>
      ) : (
        <></>
      )}
    </Flex>
  ) : (
    // eslint-disable-next-line prettier/prettier
    (componentsToDisplay[CardChoice.NEW_CARD] ?? null)
  );
};
