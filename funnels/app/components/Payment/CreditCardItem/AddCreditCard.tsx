import ChargebeeComponents from '@chargebee/chargebee-js-react-wrapper/dist/components/ComponentGroup';
import { Flex, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ApiErrorCodes, IPaymentIntentPayload, PaymentMethodType } from 'types';
import { APIError, logError } from 'utils';

import { AddCreditCardInput } from '~/components/Payment/CreditCardItem/AddCreditCardInput';
import { Button } from '~/components/ui/Button/Button';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import { useCreatePaymentIntent } from '~/hooks/payment/useCreatePaymentIntent';
import {
  useAddPaymentMethod,
  usePaymentMethods,
} from '~/hooks/reactQuery/user/useUser';
import { useCurrency } from '~/hooks/useCurrency';
import { PaymentIntentType } from '~/types/payment';

interface Props {
  chargebeeInstance: any;
  displaySaveCardButton?: boolean;
  onAddNewCardSuccess?: () => void;
}

export interface ICardPaymentErrors {
  cardNumber: boolean;
  cvv: boolean;
  expiry: boolean;
}

export const AddCreditCard = ({
  chargebeeInstance,
  displaySaveCardButton,
  onAddNewCardSuccess = () => {},
  ...props
}: Props) => {
  const { t } = useTranslation();
  const { paymentInfo, setPaymentInfo } = usePaymentContext();
  const createPaymentIntent = useCreatePaymentIntent(
    PaymentIntentType.ADD_CREDIT_CARD,
  );
  const addPaymentMethod = useAddPaymentMethod();
  const paymentMethodsQuery = usePaymentMethods();
  const currency = useCurrency();

  const cardRef = useRef<ChargebeeComponents>(null);
  const [cardHolder, setCardHolder] = useState<string>('');
  const [cardErrors, setCardErrors] = useState<ICardPaymentErrors>({
    cardNumber: true,
    expiry: true,
    cvv: true,
  });
  const [isSubmittingCreditCard, setIsSubmittingCreditCard] =
    useState<boolean>();

  const onSubmit = async () => {
    if (!cardRef || !cardRef.current) {
      return;
    }

    setIsSubmittingCreditCard(true);
    try {
      if (!chargebeeInstance) {
        throw 'Error when loading ChargeBee instance';
      }

      const paymentIntentOptions: IPaymentIntentPayload = {
        currency_code: currency,
        payment_method_type: PaymentMethodType.CARD,
      };

      const paymentIntent = await createPaymentIntent(paymentIntentOptions);

      if (!paymentIntent) {
        throw 'Missing payment intent';
      }

      // We need to trigger a 3DS verification before adding a credit card
      const threeDSHandler = await chargebeeInstance.load3DSHandler();
      threeDSHandler.setPaymentIntent(paymentIntent, {
        stripe: {},
        adyen: {},
        braintree: {},
      });

      const tokenizedCard = await cardRef.current.tokenize({
        customer: { firstName: cardHolder },
      });

      // Create an authorized payment intent to add a verified credit card
      const authorizedPaymentIntent = await threeDSHandler.handleCardPayment(
        {
          cbToken: tokenizedCard.token,
        },
        {},
      );

      addPaymentMethod.mutate(
        { paymentIntent: authorizedPaymentIntent },
        {
          onSuccess: async () => {
            await paymentMethodsQuery.refetch();
            setIsSubmittingCreditCard(false);
            onAddNewCardSuccess();
          },
          onError: (error: APIError) => {
            throw error;
          },
        },
      );
    } catch (error: any) {
      if (error.code !== ApiErrorCodes.PAYMENT_METHOD_VERIFICATION_FAILED) {
        logError('Error on add payment method:', error.message);
      }

      message.error(
        t(
          'errors.api.addPaymentMethod',
          'An error occurred adding your new credit card, please try again later.',
        ),
      );
      setIsSubmittingCreditCard(false);
    }
  };

  useEffect(() => {
    if (
      (paymentInfo?.choice === PaymentChoice.NEW_CARD &&
        !displaySaveCardButton) ||
      !!displaySaveCardButton
    ) {
      return;
    }
    setPaymentInfo({
      choice: PaymentChoice.NEW_CARD,
      info: {
        cardHolder: '',
        cardRef,
        hasValidCard: false,
      },
    });
  }, [paymentInfo, setPaymentInfo, displaySaveCardButton]);

  useEffect(() => {
    if (
      paymentInfo?.choice !== PaymentChoice.NEW_CARD &&
      !displaySaveCardButton
    ) {
      return;
    }

    const hasValidCard = !(
      cardErrors.cardNumber ||
      cardErrors.cvv ||
      cardErrors.expiry
    );

    setPaymentInfo({
      choice: PaymentChoice.NEW_CARD,
      info: {
        cardHolder,
        cardRef,
        hasValidCard,
      },
    });
  }, [
    cardErrors,
    cardHolder,
    paymentInfo?.choice,
    setPaymentInfo,
    displaySaveCardButton,
  ]);

  const setError = (name: string, value: boolean) => {
    setCardErrors(currentState => {
      return { ...currentState, [name]: value };
    });
  };

  return (
    <Flex vertical gap="middle">
      <div {...props}>
        <AddCreditCardInput
          cardRef={cardRef}
          setCardHolder={setCardHolder}
          setError={setError}
          chargebeeInstance={chargebeeInstance}
        />
      </div>
      {displaySaveCardButton && (
        <Button
          data-testid="payment-saveCard-button"
          htmlType="submit"
          onClick={onSubmit}
          loading={isSubmittingCreditCard}
        >
          {t('payment.addCreditCard.button', 'Save')}
        </Button>
      )}
    </Flex>
  );
};
