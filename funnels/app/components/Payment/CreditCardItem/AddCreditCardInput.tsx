import {
  Card<PERSON>omponent,
  CardCVV,
  CardExpiry,
  CardNumber,
  Provider,
} from '@chargebee/chargebee-js-react-wrapper';
import ChargebeeComponents from '@chargebee/chargebee-js-react-wrapper/dist/components/ComponentGroup';
import { Col, Flex, Row, Tooltip } from 'antd';
import { RefObject, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ChargebeeInput } from '~/components/Forms/ChargbeeInput/ChargebeeInput';
import type { ICardPaymentErrors } from '~/components/Payment/CreditCardItem/AddCreditCard';
import {
  CalendarIcon,
  CreditCardIcon,
  InfoIcon,
  LockIcon,
  UserIcon,
} from '~/components/ui/Icon';
import { LocalLoader } from '~/components/ui/Loader';
import { Text } from '~/components/ui/Typography/Typography';

interface IAddCreditCard {
  cardRef: RefObject<ChargebeeComponents>;
  setCardHolder: (value: string) => void;
  setError?: (name: keyof ICardPaymentErrors, value: boolean) => void;
  chargebeeInstance: any;
}

export const AddCreditCardInput = ({
  cardRef,
  setCardHolder,
  setError,
  chargebeeInstance,
}: IAddCreditCard) => {
  const { t } = useTranslation();

  const placeholder = useMemo(
    () => ({
      number: 'XXXX XXXX XXXX XXXX',
      expiry: t('payment.addCreditCard.expiry', 'MM / YY'),
      cvv: 'XXX',
    }),
    [t],
  );

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return (
    <Provider cbInstance={chargebeeInstance}>
      <Flex vertical gap="middle">
        <CardComponent ref={cardRef} locale="en" placeholder={placeholder}>
          <Row gutter={24}>
            <Col xs={24} sm={24} md={12}>
              <ChargebeeInput
                data-test-id="payment-cardNumber-input"
                label={t(
                  'payment.addCreditCard.cardNumber.label',
                  'Card number',
                )}
                customErrorMessage={t(
                  'payment.addCreditCard.cardNumber.error',
                  'Invalid card number',
                )}
                startIcon={<CreditCardIcon style={{ fontSize: '20px' }} />}
                hideIcon
                onChange={hasError => {
                  setError?.('cardNumber', hasError);
                }}
              >
                <CardNumber />
              </ChargebeeInput>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <ChargebeeInput
                data-test-id="payment-cardExpiration-input"
                label={t(
                  'payment.addCreditCard.expiration.label',
                  'Expiry date',
                )}
                customErrorMessage={t(
                  'payment.addCreditCard.expiration.error',
                  'Invalid expiration',
                )}
                startIcon={<CalendarIcon style={{ fontSize: '20px' }} />}
                onChange={hasError => {
                  setError?.('expiry', hasError);
                }}
              >
                <CardExpiry className="chargebeeInput" />
              </ChargebeeInput>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col xs={24} sm={24} md={12}>
              <ChargebeeInput
                data-test-id="payment-cardCvc-input"
                label={t('payment.addCreditCard.cvc.label', 'CVC Number')}
                customErrorMessage={t(
                  'payment.addCreditCard.cvc.error',
                  'Invalid CVV',
                )}
                startIcon={<LockIcon style={{ fontSize: '20px' }} />}
                endIcon={
                  <Tooltip
                    title={t(
                      'payment.addCreditCard.cvc.tooltip',
                      'The Card Verification Code (CVC) is a 3 or 4 digit security code found on the back of your card.',
                    )}
                    arrow={true}
                  >
                    <InfoIcon
                      style={{
                        cursor: 'help',
                        color: 'var(--ant-color-primary)',
                        fontSize: '18px',
                      }}
                    />
                  </Tooltip>
                }
                onChange={hasError => {
                  setError?.('cvv', hasError);
                }}
              >
                <CardCVV className="chargebeeInput" />
              </ChargebeeInput>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <ChargebeeInput
                data-test-id="payment-cardName-input"
                label={t(
                  'payment.addCreditCard.name.label',
                  'Name of the card owner',
                )}
                customErrorMessage={t(
                  'payment.addCreditCard.name.error',
                  'Invalid name',
                )}
                startIcon={<UserIcon style={{ fontSize: '20px' }} />}
                onChange={(_, value) => {
                  setCardHolder(value as string);
                }}
              >
                <input
                  placeholder={t(
                    'payment.addCreditCard.name.placeholder',
                    'Card owner name here',
                  )}
                />
              </ChargebeeInput>
            </Col>
          </Row>
        </CardComponent>

        <Text small>
          {t(
            'payment.bank.3DSecure',
            'To offer maximum security with credit card payments on our site, we opted for the 3D SECURE system. This system involves an additional level of security: validation by your own banking institution that requires an additional authentication code.',
          )}
        </Text>
      </Flex>
    </Provider>
  );
};
