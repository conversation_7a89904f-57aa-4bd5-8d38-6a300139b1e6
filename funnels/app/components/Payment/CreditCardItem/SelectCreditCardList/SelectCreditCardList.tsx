import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { IPaymentMethod, PaymentMethodStatus } from 'types';

import { SelectCreditCardButton } from '~/components/Payment/CreditCardItem/SelectCreditCardList/SelectCreditCardButton';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';

interface Props {
  cards: IPaymentMethod[];
}

export const SelectCreditCardList = ({ cards }: Props) => {
  const creditCardList = cards.filter(paymentMethod => !!paymentMethod.card);
  const { paymentInfo, setPaymentInfo } = usePaymentContext();

  const [selectedCardId, setSelectedCardId] = useState<string>('');

  const handleChangePaymentMethod = (paymentMethod: IPaymentMethod) => {
    if (!paymentMethod) {
      return;
    }

    setSelectedCardId(paymentMethod.id);
    setPaymentInfo({
      choice: PaymentChoice.SAVED_CARD,
      info: paymentMethod,
    });
  };

  useEffect(() => {
    if (paymentInfo?.choice === PaymentChoice.SAVED_CARD) {
      return;
    }

    const primaryCreditCard = cards.find(
      (paymentMethod: IPaymentMethod) =>
        paymentMethod.type === 'card' && paymentMethod.is_primary,
    );

    // If there is a Credit Card set as primary, we display it first
    // Else, we display the last credit card added
    const defaultCreditCard = primaryCreditCard;

    if (!defaultCreditCard) {
      return;
    }

    setSelectedCardId(defaultCreditCard.id);
    setPaymentInfo({
      choice: PaymentChoice.SAVED_CARD,
      info: defaultCreditCard,
    });
  }, [cards, paymentInfo, setPaymentInfo]);

  return (
    <Flex vertical gap="middle">
      {creditCardList.map((paymentMethod: IPaymentMethod, key) => {
        const selected = paymentMethod.id === selectedCardId;
        const expired = [
          PaymentMethodStatus.EXPIRED,
          PaymentMethodStatus.EXPIRING,
        ].includes(paymentMethod.status);
        return (
          <SelectCreditCardButton
            expired={expired}
            handleChangePaymentMethod={handleChangePaymentMethod}
            key={key}
            paymentMethod={paymentMethod}
            selected={selected}
          />
        );
      })}
    </Flex>
  );
};
