import { Radio } from 'antd';
import { useTranslation } from 'react-i18next';
import { IPaymentMethod } from 'types';
import { CARD_PATH } from 'utils';

import useStyles from '~/components/Payment/CreditCardItem/SelectCreditCardList/SelectCreditCardButton.styles';
import { PaymentMethodInformation } from '~/components/Payment/PaymentMethodInformation';

interface Props {
  expired: boolean;
  handleChangePaymentMethod: (paymentMethod: IPaymentMethod) => void;
  key: number;
  paymentMethod: IPaymentMethod;
  selected: boolean;
}

export const SelectCreditCardButton = ({
  expired,
  handleChangePaymentMethod,
  key,
  paymentMethod,
  selected,
}: Props) => {
  const { t } = useTranslation();
  const { styles } = useStyles({ selected, expired });

  return (
    <button
      data-test-id={`payment-card-select-${key}`}
      className={styles.creditCard__wrapper}
      key={key}
      onClick={() => handleChangePaymentMethod(paymentMethod)}
    >
      <Radio
        id={`payment-card-select-${key}-choice-radio-button`}
        data-test-id={`payment-card-select-${key}-choice-radio-button`}
        checked={selected}
      >
        <PaymentMethodInformation
          title={t('payment.selectCreditCard.select.label', 'Credit card')}
          icon={
            <img
              src={`${CARD_PATH}${
                paymentMethod.is_primary
                  ? `${paymentMethod.card?.brand}-white`
                  : paymentMethod.card?.brand
              }.svg`}
              width={30}
              height={24}
            />
          }
          information={paymentMethod.card?.masked_number as string}
          expirationDate={
            expired
              ? t('payment.selectCreditCard.select.expired', 'Expired')
              : `${t(
                  'payment.selectCreditCard.select.expiration',
                  'Expiration :',
                )} ${paymentMethod.card?.expiry_month}/${
                  paymentMethod.card?.expiry_year
                }`
          }
        />
      </Radio>
    </button>
  );
};
