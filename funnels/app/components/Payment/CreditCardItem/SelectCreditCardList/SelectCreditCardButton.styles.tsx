import { createStyles } from 'antd-style';

export default createStyles(
  (
    { token, css },
    props: {
      selected: boolean;
      expired: boolean;
    },
  ) => {
    return {
      creditCard__wrapper: css`
        & .ant-radio-wrapper {
          align-items: center;
        }

        cursor: pointer;
        align-items: center;
        background: ${token.colorBgContainer};
        border: ${token.lineWidth}px solid rgba(155, 169, 230, 0.1);
        border-radius: ${token.borderRadius}px;
        display: flex;
        flex: 1;
        justify-content: space-between;
        padding: ${token.paddingXS}px;
        transition: background linear 150ms, border linear 150ms;
        padding: 8px 16px;

        :hover {
          background-color: ${token.colorSplit};
          border: ${token.lineWidth}px solid ${token.colorPrimary};
        }

        ${props.selected &&
        `
          background-color: ${token.colorBgContainer};
          border-color: ${token.colorSplit};
          color: ${token.colorPrimary};  

          :hover {
            background-color: ${token.colorBgContainer};
            border-color: ${token.colorSplit};
          }
      `}

        ${props.expired &&
        `
          background-color: rgba(255, 193, 25, 0.1);
          border-color: ${token.colorWarning};
          color: ${token.colorPrimary};  
        `}
      `,
    };
  },
);
