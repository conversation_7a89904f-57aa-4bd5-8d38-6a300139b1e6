import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useEffect } from 'react';

import { LocalLoader } from '~/components/ui/Loader';
import { useApplePay } from '~/hooks/payment/useApplePay';

interface IApplePayPaymentButtonProps {
  chargebeeInstance: any;
  setApplePayButtonMustUnmount: (isApplePayButtonMustUnmount: boolean) => void;
  setPaymentIntent?: (paymentIntent: PaymentIntent) => void;
}

export const ApplePayPaymentButton = ({
  chargebeeInstance,
  setPaymentIntent,
  setApplePayButtonMustUnmount,
}: IApplePayPaymentButtonProps) => {
  const { initApplePayButton, paymentIntent } = useApplePay(
    setApplePayButtonMustUnmount,
  );

  useEffect(() => {
    if (paymentIntent && setPaymentIntent) {
      setPaymentIntent(paymentIntent);
    }
  }, [paymentIntent, setPaymentIntent]);

  useEffect(
    () => {
      initApplePayButton(chargebeeInstance);
    },
    // Do not add initApplePayButton here, can trigger infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chargebeeInstance],
  );

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return <div id="apple-pay-button" />;
};
