import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useQueryClient } from '@tanstack/react-query';
import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { PaymentMethodType } from 'types';

import { ApplePayPaymentButton } from '~/components/Payment/ApplePayPaymentItem/ApplePayPaymentButton';
import { LocalLoader } from '~/components/ui/Loader';
import { Link, Text } from '~/components/ui/Typography/Typography';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import { usePaymentMethods } from '~/hooks/reactQuery/user/useUser';

interface IApplePayPaymentItemProps {
  chargebeeInstance: any;
}

export const ApplePayPaymentItem = ({
  chargebeeInstance,
}: IApplePayPaymentItemProps) => {
  const { t } = useTranslation();
  const paymentMethodsQuery = usePaymentMethods();
  const { setPaymentInfo, paymentIntent, setPaymentIntent } =
    usePaymentContext();
  const queryClient = useQueryClient();

  const [isApplePayButtonMustUnmount, setApplePayButtonMustUnmount] =
    useState(false);

  const existingApplePayPaymentMethod = paymentMethodsQuery.data?.find(
    paymentMethod => {
      return paymentMethod.type === PaymentMethodType.APPLE_PAY;
    },
  );

  useEffect(() => {
    if (existingApplePayPaymentMethod && paymentIntent) {
      setPaymentInfo({
        choice: PaymentChoice.APPLE_PAY,
        info: existingApplePayPaymentMethod,
        paymentIntent: paymentIntent,
      });
    } else {
      setPaymentInfo(null);
    }
  }, [existingApplePayPaymentMethod, paymentIntent, setPaymentInfo]);

  const displayApplePayButton = !paymentIntent;
  const displayUserEmail = !!paymentIntent;

  const onSetPaymentIntent = (applePayPaymentIntent: PaymentIntent) => {
    setPaymentIntent(applePayPaymentIntent);
    queryClient.invalidateQueries(['paymentMethods']);
  };

  const resetPaymentIntent = () => {
    setPaymentIntent(null);
  };

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return (
    <Flex gap="middle" vertical>
      {displayApplePayButton && (
        <Flex gap="middle" vertical>
          <Text>
            {t(
              'payment.applePay.paymentSelectionLabel',
              'To choose Apple Pay as your payment method, click below :',
            )}
          </Text>
          {!isApplePayButtonMustUnmount && (
            <ApplePayPaymentButton
              chargebeeInstance={chargebeeInstance}
              setPaymentIntent={onSetPaymentIntent}
              setApplePayButtonMustUnmount={setApplePayButtonMustUnmount}
            />
          )}
        </Flex>
      )}
      {displayUserEmail && (
        <Text>
          <Trans i18nKey="payment.applePay.connectedUser">
            Connected with your ApplePay account
            {' ('}
            <Link onClick={resetPaymentIntent}>Log out</Link>
            {')'}
          </Trans>
        </Text>
      )}
    </Flex>
  );
};
