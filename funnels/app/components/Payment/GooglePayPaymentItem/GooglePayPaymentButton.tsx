import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useEffect } from 'react';

import { LocalLoader } from '~/components/ui/Loader';
import { useGooglePay } from '~/hooks/payment/useGooglePay';

interface Props {
  chargebeeInstance: any;
  setGooglePayButtonMustUnmount: (
    isGooglePayButtonMustUnmount: boolean,
  ) => void;
  setPaymentIntent?: (paymentIntent: PaymentIntent) => void;
}

export const GooglePayPaymentButton = ({
  chargebeeInstance,
  setPaymentIntent,
  setGooglePayButtonMustUnmount,
}: Props) => {
  const { initGooglePayButton, paymentIntent } = useGooglePay(
    setGooglePayButtonMustUnmount,
  );

  useEffect(() => {
    if (paymentIntent && setPaymentIntent) {
      setPaymentIntent(paymentIntent);
    }
  }, [paymentIntent, setPaymentIntent]);

  useEffect(
    () => {
      initGooglePayButton(chargebeeInstance);
    },
    // Do not add initGooglePayButton here, can trigger infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chargebeeInstance],
  );

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return <div id="google-pay-button" />;
};
