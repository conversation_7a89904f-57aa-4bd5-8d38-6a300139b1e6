import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useQueryClient } from '@tanstack/react-query';
import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { PaymentMethodType } from 'types';

import { GooglePayPaymentButton } from '~/components/Payment/GooglePayPaymentItem/GooglePayPaymentButton';
import { LocalLoader } from '~/components/ui/Loader';
import { Link, Text } from '~/components/ui/Typography/Typography';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import { usePaymentMethods } from '~/hooks/reactQuery/user/useUser';

interface Props {
  chargebeeInstance: any;
}

export const GooglePayPaymentItem = ({ chargebeeInstance }: Props) => {
  const { t } = useTranslation();
  const paymentMethodsQuery = usePaymentMethods();
  const { setPaymentInfo, paymentIntent, setPaymentIntent } =
    usePaymentContext();
  const queryClient = useQueryClient();

  const [isGooglePayButtonMustUnmount, setGooglePayButtonMustUnmount] =
    useState(false);

  const existingGooglePayPaymentMethod = paymentMethodsQuery.data?.find(
    paymentMethod => {
      return (
        paymentMethod.type === PaymentMethodType.GOOGLE_PAY &&
        paymentMethod.google_pay?.email ===
          paymentIntent?.payer_info?.customer?.email
      );
    },
  );

  useEffect(() => {
    if (existingGooglePayPaymentMethod && paymentIntent) {
      setPaymentInfo({
        choice: PaymentChoice.GOOGLE_PAY,
        info: existingGooglePayPaymentMethod,
        paymentIntent: paymentIntent,
      });
    } else {
      setPaymentInfo(null);
    }
  }, [existingGooglePayPaymentMethod, paymentIntent, setPaymentInfo]);

  const displayGooglePayButton = !paymentIntent;
  const displayUserEmail = !!paymentIntent;

  const onSetPaymentIntent = (googlePayPaymentIntent: PaymentIntent) => {
    setPaymentIntent(googlePayPaymentIntent);
    queryClient.invalidateQueries(['paymentMethods']);
  };

  const resetPaymentIntent = () => {
    setPaymentIntent(null);
  };

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return (
    <Flex gap="middle" vertical>
      {displayGooglePayButton && (
        <>
          <Text>
            {t(
              'payment.googlePay.paymentSelectionLabel',
              'To choose Google Pay as your payment method, click below :',
            )}
          </Text>
          {!isGooglePayButtonMustUnmount && (
            <GooglePayPaymentButton
              chargebeeInstance={chargebeeInstance}
              setPaymentIntent={onSetPaymentIntent}
              setGooglePayButtonMustUnmount={setGooglePayButtonMustUnmount}
            />
          )}
        </>
      )}
      {displayUserEmail && (
        <Text>
          <Trans i18nKey="payment.googlePay.connectedUser">
            Connected with your GooglePay account
            {' ('}
            <Link onClick={resetPaymentIntent}>Log out</Link>
            {')'}
          </Trans>
        </Text>
      )}
    </Flex>
  );
};
