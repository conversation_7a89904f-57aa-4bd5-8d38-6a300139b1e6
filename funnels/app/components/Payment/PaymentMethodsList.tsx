import { Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { PaymentMethodStatus, PaymentMethodType, PaymentMethods } from 'types';
import { CARD_PATH, SVG_PATH } from 'utils';

import { ApplePayPaymentItem } from '~/components/Payment/ApplePayPaymentItem/ApplePayPaymentItem';
import { CreditCardItem } from '~/components/Payment/CreditCardItem/CreditCardItem';
import { GooglePayPaymentItem } from '~/components/Payment/GooglePayPaymentItem/GooglePayPaymentItem';
import { PaymentListItem } from '~/components/Payment/PaymentListItem/PaymentListItem';
import { PaypalPaymentItem } from '~/components/Payment/PaypalPaymentItem/PaypalPaymentItem';
import { usePaymentMethods } from '~/hooks/reactQuery/user/useUser';

interface IPaymentMethodsListProps {
  chargebeeInstance: any;
  shouldDisplaySavedCard?: boolean;
  shouldDisplayNewCardInput?: boolean;
  shouldDisplayPaypal: boolean;
  shouldDisplayGooglePay: boolean;
  shouldDisplayApplePay: boolean;
  methodSelected: string;
  toggleMethodSelected: (id: PaymentMethods) => void;
  displaySaveCardButton?: boolean;
  onAddNewCardSuccess?: () => void;
}

export const PaymentMethodsList = ({
  chargebeeInstance,
  shouldDisplaySavedCard,
  shouldDisplayNewCardInput,
  shouldDisplayPaypal,
  shouldDisplayGooglePay,
  shouldDisplayApplePay,
  methodSelected,
  toggleMethodSelected,
  displaySaveCardButton,
  onAddNewCardSuccess,
}: IPaymentMethodsListProps) => {
  const { t } = useTranslation();
  const paymentMethodsQuery = usePaymentMethods();

  const renderCardPaymentMethodItem = (
    paymentMethod: PaymentMethods,
    isPrimaryExpired?: boolean,
  ) => (
    <PaymentListItem
      data-test-id="payment-creditCard-button"
      id={paymentMethod}
      methodSelected={methodSelected}
      onClick={toggleMethodSelected}
      label={t('payment.creditCard.label', 'Credit or debit card')}
      isPrimaryExpired={isPrimaryExpired}
      defaultIcons={
        // @todo : Factorize icons
        <>
          <img
            src={`${CARD_PATH}mastercard.svg`}
            alt="mastercard"
            width={40}
            height={24}
          />
          <img src={`${CARD_PATH}visa.svg`} alt="visa" width={40} height={24} />
          <img
            src={`${CARD_PATH}american_express.svg`}
            alt="american express"
            width={40}
            height={24}
          />
        </>
      }
      openedIcons={
        <>
          <img
            src={`${CARD_PATH}mastercard-white.svg`}
            alt="mastercard"
            width={40}
            height={24}
          />
          <img
            src={`${CARD_PATH}visa-white.svg`}
            alt="visa"
            width={40}
            height={24}
          />
          <img
            src={`${CARD_PATH}american_express.svg`}
            alt="american express"
            width={40}
            height={24}
          />
        </>
      }
    >
      <CreditCardItem
        chargebeeInstance={chargebeeInstance}
        displaySavedCards={shouldDisplaySavedCard}
        displaySaveCardButton={displaySaveCardButton}
        onAddNewCardSuccess={onAddNewCardSuccess}
      />
    </PaymentListItem>
  );

  const isPrimaryCardExpired = !!paymentMethodsQuery.data?.find(
    paymentMethod =>
      paymentMethod.is_primary &&
      paymentMethod.type === PaymentMethodType.CARD &&
      paymentMethod.status === PaymentMethodStatus.EXPIRED,
  );

  return (
    <nav aria-label="payment methods">
      <Flex vertical gap="middle">
        {shouldDisplaySavedCard &&
          renderCardPaymentMethodItem(
            PaymentMethods.SAVED_CREDIT_CARD,
            isPrimaryCardExpired,
          )}
        {shouldDisplayNewCardInput &&
          renderCardPaymentMethodItem(PaymentMethods.CREDIT_CARD)}
        {shouldDisplayPaypal && (
          <PaymentListItem
            data-test-id="payment-paypal-button"
            id={PaymentMethods.PAYPAL}
            methodSelected={methodSelected}
            onClick={() => toggleMethodSelected(PaymentMethods.PAYPAL)}
            label={t('payment.paypal.label', 'PayPal')}
            defaultIcons={
              <img
                src={`${SVG_PATH}banks/paypal.svg`}
                alt="Paypal"
                width={90}
                height={24}
              />
            }
          >
            <PaypalPaymentItem chargebeeInstance={chargebeeInstance} />
          </PaymentListItem>
        )}
        {shouldDisplayGooglePay && (
          <PaymentListItem
            data-test-id="payment-googlePay-button"
            id={PaymentMethods.GOOGLE_PAY}
            methodSelected={methodSelected}
            onClick={() => toggleMethodSelected(PaymentMethods.GOOGLE_PAY)}
            label={t('payment.googlePay.label', 'Google Pay')}
            defaultIcons={
              <img
                src={`${SVG_PATH}banks/google-pay.png`}
                alt="Google Pay"
                width={55}
                height={26}
              />
            }
          >
            <GooglePayPaymentItem chargebeeInstance={chargebeeInstance} />
          </PaymentListItem>
        )}
        {shouldDisplayApplePay && (
          <PaymentListItem
            data-test-id="payment-applePay-button"
            id={PaymentMethods.APPLE_PAY}
            methodSelected={methodSelected}
            onClick={() => toggleMethodSelected(PaymentMethods.APPLE_PAY)}
            label={t('payment.applePay.label', 'Apple Pay')}
            defaultIcons={
              <img
                src={`${SVG_PATH}banks/apple-pay.png`}
                alt="Apple Pay"
                width={63}
                height={26}
              />
            }
          >
            <ApplePayPaymentItem chargebeeInstance={chargebeeInstance} />
          </PaymentListItem>
        )}
      </Flex>
    </nav>
  );
};
