import { Flex } from 'antd';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Market, PaymentMethodType, PaymentMethods } from 'types';

import { PaymentMethodsList } from '~/components/Payment/PaymentMethodsList';
import { LocalLoader } from '~/components/ui/Loader';
import { Title } from '~/components/ui/Typography/Typography';
import { usePaymentContext } from '~/contexts/PaymentContext';
import { usePaymentMethods } from '~/hooks/reactQuery/user/useUser';
import { getChargebeeInstance } from '~/utils/chargebee';

interface Props {
  isUnfolded: boolean;
  shouldDisplayPaypal: boolean;
  shouldDisplayGooglePay: boolean;
  shouldDisplayApplePay: boolean;
  country: Market;
}

export const PaymentMethodsBlock = ({
  isUnfolded,
  shouldDisplayPaypal,
  shouldDisplayGooglePay,
  shouldDisplayApplePay,
  country,
}: Props) => {
  const { t } = useTranslation();
  const [methodSelected, setMethodSelected] = useState<string>('');
  const { setPaymentInfo, setPaymentIntent } = usePaymentContext();
  const chargebeeInstance = getChargebeeInstance(country);

  const paymentMethodsQuery = usePaymentMethods();

  const toggleMethodSelected = (id: PaymentMethods) => {
    // if same method is selected, we deselect it
    if (id === methodSelected) {
      setPaymentInfo(null);
      setPaymentIntent(null);
      setMethodSelected('');
      return;
    }
    setMethodSelected(id);
  };

  const doesUserHavePaymentMethod = (paymentMethod: PaymentMethodType) => {
    return !!paymentMethodsQuery.data?.find(
      userPaymentMethod => userPaymentMethod.type === paymentMethod,
    );
  };

  useEffect(() => {
    const primaryPaymentMethod = paymentMethodsQuery.data?.find(
      userPaymentMethod => userPaymentMethod.is_primary,
    );

    switch (primaryPaymentMethod?.type) {
      case PaymentMethodType.APPLE_PAY:
        toggleMethodSelected(PaymentMethods.APPLE_PAY);
        break;
      case PaymentMethodType.GOOGLE_PAY:
        toggleMethodSelected(PaymentMethods.GOOGLE_PAY);
        break;
      case PaymentMethodType.CARD:
        toggleMethodSelected(PaymentMethods.SAVED_CREDIT_CARD);
        break;
      default:
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentMethodsQuery.data]);

  if (paymentMethodsQuery.isLoading) {
    return <LocalLoader />;
  }

  const displaySavedPaymentMethodBlock = !!paymentMethodsQuery.data?.length;

  if (!isUnfolded) {
    return (
      <Title level={4} primary style={{ opacity: 0.5, cursor: 'not-allowed' }}>
        {t('purchaseOverview.paymentMethods.step.title', 'Payment Methods')}
      </Title>
    );
  }

  return (
    <Flex vertical gap="large">
      {displaySavedPaymentMethodBlock && (
        <>
          <Title level={4} primary>
            {t(
              'purchaseOverview.savedPaymentMethods.title',
              'Your saved payment methods',
            )}
          </Title>
          <PaymentMethodsList
            chargebeeInstance={chargebeeInstance}
            shouldDisplaySavedCard={doesUserHavePaymentMethod(
              PaymentMethodType.CARD,
            )}
            shouldDisplayPaypal={
              doesUserHavePaymentMethod(PaymentMethodType.PAYPAL) &&
              shouldDisplayPaypal
            }
            shouldDisplayGooglePay={
              doesUserHavePaymentMethod(PaymentMethodType.GOOGLE_PAY) &&
              shouldDisplayGooglePay
            }
            shouldDisplayApplePay={
              doesUserHavePaymentMethod(PaymentMethodType.APPLE_PAY) &&
              shouldDisplayApplePay
            }
            methodSelected={methodSelected}
            toggleMethodSelected={toggleMethodSelected}
          />
        </>
      )}

      <Flex vertical gap="middle">
        <Title level={5} primary>
          {t(
            'purchaseOverview.addPaymentMethods.title',
            'Add a payment method',
          )}
        </Title>
        <PaymentMethodsList
          chargebeeInstance={chargebeeInstance}
          shouldDisplayNewCardInput
          shouldDisplayPaypal={
            !doesUserHavePaymentMethod(PaymentMethodType.PAYPAL) &&
            shouldDisplayPaypal
          }
          shouldDisplayGooglePay={
            !doesUserHavePaymentMethod(PaymentMethodType.GOOGLE_PAY) &&
            shouldDisplayGooglePay
          }
          shouldDisplayApplePay={
            !doesUserHavePaymentMethod(PaymentMethodType.APPLE_PAY) &&
            shouldDisplayApplePay
          }
          methodSelected={methodSelected}
          toggleMethodSelected={toggleMethodSelected}
        />
      </Flex>
    </Flex>
  );
};
