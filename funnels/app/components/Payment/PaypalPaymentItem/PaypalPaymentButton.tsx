import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useEffect } from 'react';

import { LocalLoader } from '~/components/ui/Loader';
import { usePaypal } from '~/hooks/payment/usePaypal';

interface Props {
  chargebeeInstance: any;
  setPaypalButtonMustUnmount: (isPaypalButtonMustUnmount: boolean) => void;
  setPaymentIntent?: (paymentIntent: PaymentIntent) => void;
}

export const PaypalPaymentButton = ({
  chargebeeInstance,
  setPaymentIntent,
  setPaypalButtonMustUnmount,
}: Props) => {
  const { initPaypalButton, paymentIntent } = usePaypal(
    setPaypalButtonMustUnmount,
  );

  useEffect(() => {
    if (paymentIntent && setPaymentIntent) {
      setPaymentIntent(paymentIntent);
    }
  }, [paymentIntent, setPaymentIntent]);

  useEffect(
    () => {
      initPaypalButton(chargebeeInstance);
    },
    // Do not add initPaypalButton here, can trigger infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chargebeeInstance],
  );

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return <div id="paypal-button" />;
};
