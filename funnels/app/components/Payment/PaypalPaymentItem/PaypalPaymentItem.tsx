import { PaymentIntent } from '@chargebee/chargebee-js-types';
import { useQueryClient } from '@tanstack/react-query';
import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { PaymentMethodType } from 'types';

import { PaypalPaymentButton } from '~/components/Payment/PaypalPaymentItem/PaypalPaymentButton';
import { LocalLoader } from '~/components/ui/Loader';
import { Link, Text } from '~/components/ui/Typography/Typography';
import { PaymentChoice, usePaymentContext } from '~/contexts/PaymentContext';
import { usePaymentMethods } from '~/hooks/reactQuery/user/useUser';

interface Props {
  chargebeeInstance: any;
}

export const PaypalPaymentItem = ({ chargebeeInstance }: Props) => {
  const { t } = useTranslation();
  const paymentMethodsQuery = usePaymentMethods();
  const { setPaymentInfo, paymentIntent, setPaymentIntent } =
    usePaymentContext();
  const queryClient = useQueryClient();

  const [isPaypalButtonMustUnmount, setPaypalButtonMustUnmount] =
    useState(false);

  const existingPaypalPaymentMethod = paymentMethodsQuery.data?.find(
    paymentMethod => {
      return (
        paymentMethod.type === PaymentMethodType.PAYPAL &&
        paymentMethod.paypal?.email ===
          paymentIntent?.payer_info?.customer?.email
      );
    },
  );

  useEffect(() => {
    if (existingPaypalPaymentMethod && paymentIntent) {
      setPaymentInfo({
        choice: PaymentChoice.PAYPAL,
        info: existingPaypalPaymentMethod,
        paymentIntent: paymentIntent,
      });
    } else {
      setPaymentInfo(null);
    }
  }, [existingPaypalPaymentMethod, paymentIntent, setPaymentInfo]);

  const displayPaypalButton = !paymentIntent;
  const displayUserEmail = !!paymentIntent;

  const onSetPaymentIntent = (paypalPaymentIntent: PaymentIntent) => {
    setPaymentIntent(paypalPaymentIntent);
    queryClient.invalidateQueries(['paymentMethods']);
  };

  const resetPaymentIntent = () => {
    setPaymentIntent(null);
  };

  if (!chargebeeInstance) {
    return <LocalLoader />;
  }

  return (
    <Flex gap="middle" vertical>
      {displayPaypalButton && (
        <>
          <Text>
            {t(
              'payment.paypal.paymentSelectionLabel',
              'To choose PayPal as your payment method, click below :',
            )}
          </Text>
          {!isPaypalButtonMustUnmount && (
            <PaypalPaymentButton
              chargebeeInstance={chargebeeInstance}
              setPaymentIntent={onSetPaymentIntent}
              setPaypalButtonMustUnmount={setPaypalButtonMustUnmount}
            />
          )}
        </>
      )}
      {displayUserEmail && (
        <Text>
          <Trans i18nKey="payment.paypal.connectedUser">
            Connected with your Paypal account
            {' ('}
            <Link onClick={resetPaymentIntent}>Log out</Link>
            {')'}
          </Trans>
        </Text>
      )}
    </Flex>
  );
};
