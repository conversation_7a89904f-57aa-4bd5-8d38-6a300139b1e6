import { createStyles } from 'antd-style';

export default createStyles(
  ({ token, css }, props: { open: boolean; isPrimaryExpired: boolean }) => {
    return {
      collapse: css`
        overflow: hidden;

        &.ant-collapse .ant-collapse-item > .ant-collapse-header {
          border-radius: 0;
          background: ${props.isPrimaryExpired
            ? token.colorWarning
            : props.open
            ? token.colorPrimary
            : token.colorBorderSecondary};
          color: ${props.open ? token.colorWhite : token.colorText};
        }

        & .ant-collapse-content {
          /* background: ${token.colorBorderSecondary}; */
          background: ${token.colorBgBase};
        }
      `,
    };
  },
);
