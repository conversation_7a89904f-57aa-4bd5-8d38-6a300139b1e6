import { Collapse, CollapseProps, Flex, Grid } from 'antd';
import { useEffect, useState, ReactNode } from 'react';
import { PaymentMethods } from 'types';

import useStyles from '~/components/Payment/PaymentListItem/PaymentListItem.styles';

interface Props {
  children: ReactNode;
  id: PaymentMethods;
  label: string;
  methodSelected: string;
  onClick: (id: PaymentMethods) => void;
  defaultIcons?: ReactNode;
  isPrimaryExpired?: boolean;
  openedIcons?: ReactNode;
}

export const PaymentListItem = ({
  children,
  id,
  label,
  methodSelected,
  onClick,
  defaultIcons,
  isPrimaryExpired = false,
  openedIcons,
}: Props) => {
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const [open, setOpen] = useState(methodSelected === id);

  const { styles } = useStyles({
    open: open,
    isPrimaryExpired: isPrimaryExpired,
  });

  const onChangeCollapse = () => {
    setOpen(!open);
    onClick(id);
  };

  useEffect(() => {
    setOpen(id === methodSelected);
  }, [id, methodSelected]);

  const items: CollapseProps['items'] = [
    {
      key: id,
      label: (
        <Flex component="span" gap="middle" vertical={screens.xs}>
          {label}
          <Flex component="span">
            {open && openedIcons ? openedIcons : defaultIcons}
          </Flex>
        </Flex>
      ),
      children: children,
    },
  ];

  return (
    <Collapse
      data-test-id={`payment-${id}-tab`}
      onChange={onChangeCollapse}
      items={items}
      activeKey={open ? id : undefined}
      expandIconPosition="end"
      className={styles.collapse}
      destroyOnHidden
    />
  );
};
