import { Market } from 'types';

import { PaymentMethodsBlock } from '~/components/Payment/PaymentMethodBlock';
import { Card } from '~/components/ui/Card/Card';
import { useIsPaymentMeanAvailableOnDevice } from '~/hooks/payment/useIsPaymentMeanAvailableOnDevice';

interface Props {
  isUnfolded: boolean;
  country: Market;
  hasEmptyBillingDetails: boolean;
}

export const PaymentMethodsCard = ({
  isUnfolded,
  country,
  hasEmptyBillingDetails,
}: Props) => {
  const { isApplePayAvailableOnDevice, isGooglePayAvailableOnDevice } =
    useIsPaymentMeanAvailableOnDevice();

  return (
    <Card aria-readonly={hasEmptyBillingDetails}>
      <PaymentMethodsBlock
        isUnfolded={isUnfolded}
        shouldDisplayPaypal
        shouldDisplayGooglePay={
          isGooglePayAvailableOnDevice && !isApplePayAvailableOnDevice
        }
        shouldDisplayApplePay={isApplePayAvailableOnDevice}
        country={country}
      />
    </Card>
  );
};
