import { Flex, Grid } from 'antd';
import * as Flags from 'country-flag-icons/react/3x2';
import { useTranslation } from 'react-i18next';
import { IBillingDetails, Market } from 'types';

import { Button } from '~/components/ui/Button/Button';
import { Flag } from '~/components/ui/Flag';
import { EditIcon, UserIcon } from '~/components/ui/Icon';
import { Text } from '~/components/ui/Typography/Typography';
import { MARKETS } from '~/utils/constants';

interface IBillingDetailsInfoProps {
  billingDetails: IBillingDetails;
  onEdit: () => void;
}

export const BillingDetailsInfo = ({
  billingDetails,
  onEdit,
}: IBillingDetailsInfoProps) => {
  const { t } = useTranslation();
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  const { first_name, last_name, address1, city, zipcode, country } =
    billingDetails;
  const fullName = `${first_name} ${last_name}`;
  const fullAddress = `${address1}, ${city} ${zipcode}`;
  const label = `${fullName}${fullAddress ? ` - ${fullAddress}` : ''}${
    country ? ` - ${MARKETS[country.toLowerCase() as Market]}` : ''
  }`;

  return (
    <Flex
      align="center"
      justify="space-between"
      gap="large"
      vertical={!screens.sm}
    >
      <Flex align="center" gap="small">
        <UserIcon />
        <Text>{label}</Text>
        {billingDetails.country ? (
          <Flag
            flag={billingDetails.country.toUpperCase() as keyof typeof Flags}
          />
        ) : null}
      </Flex>
      <Button
        data-testid="edit_billing_details-button"
        size="small"
        secondary
        block={!screens.sm}
        icon={<EditIcon />}
        onClick={onEdit}
      >
        {t('billingDetailsInfo.editButtonLabel', 'Edit')}
      </Button>
    </Flex>
  );
};
