import { useTranslation } from 'react-i18next';
import { IBillingDetails } from 'types';

import { BillingDetailsForm } from '~/components/Forms/BillingDetailsForm';
import { BillingDetailsInfo } from '~/components/PurchaseOverview/BillingDetailsInfo';
import { Card } from '~/components/ui/Card/Card';
import { Title } from '~/components/ui/Typography/Typography';
import { useBillingDetails } from '~/hooks/reactQuery/user/useUser';
import { CheckoutPayload } from '~/types/purchase';

interface Props {
  isUnfolded: boolean;
  onSaveBillingDetailsSuccess: (payload?: Partial<CheckoutPayload>) => void;
  onEdit: () => void;
}

export const BillingDetailsCard = ({
  isUnfolded,
  onSaveBillingDetailsSuccess,
  onEdit,
}: Props) => {
  const { t } = useTranslation();
  const billingDetailsQuery = useBillingDetails();
  const billingDetails = billingDetailsQuery.data;

  const hasBillingDetails =
    billingDetails?.address1 && billingDetails?.zipcode && billingDetails?.city;

  return (
    <Card>
      <Title level={4} primary>
        {t('form.billingDetails.title', 'Billing details')}
      </Title>
      {hasBillingDetails && !isUnfolded ? (
        <BillingDetailsInfo
          billingDetails={billingDetails as IBillingDetails}
          onEdit={onEdit}
        />
      ) : (
        <BillingDetailsForm onSuccess={onSaveBillingDetailsSuccess} />
      )}
    </Card>
  );
};
