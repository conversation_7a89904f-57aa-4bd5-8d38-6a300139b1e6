import { DidomiSDK, IDidomiObject, ConsentStatus } from '@didomi/react';
import { theme as antdTheme } from 'antd';
import { useTranslation } from 'react-i18next';
import { log } from 'utils';

import {
  generalModalStyle,
  cookiesModalStyle,
  customCookiesModalStyle,
  partnerModalStyle,
} from '~/components/ConsentBanner/constants';
import { useTracking } from '~/contexts/TrackingContext';
import { useAuthentication } from '~/hooks/useAuthentication';
import { useLocale } from '~/hooks/useLocale';
import {
  DIDOMI_DEFAULT_NOTICE_ID,
  DIDOMI_PUBLIC_API_KEY,
  PRIVACY_POLICY_URL,
} from '~/utils/constants';

interface IVendorStatus {
  google: ConsentStatus;
  livechat: ConsentStatus;
  hotjar: ConsentStatus;
}

const ConsentBanner = () => {
  const { t } = useTranslation();
  const { useToken } = antdTheme;
  const { token: theme } = useToken();
  const { language: didomiLanguage } = useLocale();
  const { isLoading: authIsLoading } = useAuthentication();
  const { onTrackingEnabled, onTrackingDisabled } = useTracking();

  const getDidomiConfig = () => ({
    app: {
      name: 'Shadow',
      country: 'FR',
      apiKey: DIDOMI_PUBLIC_API_KEY,
      privacyPolicyURL: PRIVACY_POLICY_URL,
    },
    vendors: {
      didomi: ['google'],
      iab: {
        version: 2,
      },
    },
    tagManager: {
      provider: 'gtm',
    },
    notice: {
      position: 'popup',
      denyOptions: {
        link: true,
        button: 'none',
        cross: false,
      },
    },
    theme: {
      color: theme.colorPrimary,
      linkColor: theme.colorPrimary,
      font: 'Nexa Text',
      css: `
        ${generalModalStyle(theme)}
        ${cookiesModalStyle(theme)}
        ${customCookiesModalStyle(theme)}
        ${partnerModalStyle(theme)}
      `,
      buttons: {
        regularButtons: {
          backgroundColor: theme.colorWhite,
          borderRadius: '100px',
          borderWidth: 0,
          textColor: theme.colorPrimary,
        },
        highlightButtons: {
          borderRadius: '100px',
          borderWidth: 0,
        },
      },
    },
    preferences: {
      content: {
        title: t('consentBanner.title', 'Shadow consent banner'),
      },
    },
    languages: {
      enabled: [didomiLanguage], // disable browser language detection by forcing user's language
      default: 'en',
    },
  });

  const getVendorStatus = (Didomi: IDidomiObject): IVendorStatus => {
    return {
      google: Didomi.getUserConsentStatusForVendor('google'),
      livechat: Didomi.getUserConsentStatusForVendor('c:livechat'),
      hotjar: Didomi.getUserConsentStatusForVendor('c:hotjar'),
    };
  };

  const toggleTracking = function (vendorStatus: IVendorStatus) {
    if (vendorStatus.google) {
      onTrackingEnabled();
      log('DataLayer pageView activated', 'success');
    } else {
      onTrackingDisabled();
      log('DataLayer pageView deactivated', 'warn');
    }
  };

  const onReady = function (Didomi: IDidomiObject) {
    toggleTracking(getVendorStatus(Didomi));

    Didomi.on('consent.changed', () => {
      toggleTracking(getVendorStatus(Didomi));
    });
  };

  if (!didomiLanguage || authIsLoading) {
    return <></>;
  }

  return (
    <DidomiSDK
      config={getDidomiConfig()}
      noticeId={DIDOMI_DEFAULT_NOTICE_ID}
      gdprAppliesGlobally={true}
      onReady={onReady}
    />
  );
};

export default ConsentBanner;
