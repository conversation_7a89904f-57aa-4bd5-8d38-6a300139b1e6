import { FullToken } from 'antd-style';

export const generalModalStyle = (token: FullToken) => `
  #didomi-host {
    color: ${token.colorTextBase};
    font-family: "Nexa text";
    font-size: ${token.fontSize}px;
    font-weight: 200;
  }

  #didomi-host .didomi-popup-notice .didomi-popup-notice-text p {
    font-size: ${token.fontSize}px;
  }

  #didomi-host p, #didomi-host a {
    font-size: ${token.fontSize}px;
    font-weight: 200;
  }

   #didomi-host .didomi-consent-popup-title span {
    font-size: ${token.fontSizeHeading2}px;
    font-weight: ${token.fontWeightStrong};
    line-height: ${token.lineHeightHeading2}px;
  }

  #didomi-host button > * {
    font-size: ${token.fontSizeLG}px;
    font-weight: 400;
  }

  #didomi-host .didomi-popup-backdrop{
    background-color: rgba(30, 37, 43, 0.6);
  }

  #didomi-host .didomi-exterior-border{
    padding: 0;
  }

  #didomi-host .didomi-exterior-border.didomi-popup__exterior-border,
  #didomi-host .didomi-exterior-border .didomi-popup-container {
    position: relative;
    border-color: transparent;
    border-radius: 8px;
  }

  #didomi-host .didomi-exterior-border .didomi-popup-container {
    padding: 40px;
    width: auto;
    max-width: 730px;
    overflow-y: auto;
    background: ${token.colorBorderSecondary};
    border: none;
  }

  .didomi-popup-notice::before {
    display: none;
  }

  #didomi-host .didomi-components-button {
    font-size: ${token.fontSizeLG}px;
    font-weight: ${token.fontWeightStrong};
  }

  #didomi-host button {
    font-family: 'Nexa Text';
  }

  #didomi-host a.didomi-no-link-style.didomi-consent-popup-header-close, 
  #didomi-host #didomi-notice-x-button.didomi-x-button {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 40px;
    height: 40px;
    border-radius: 50% 50%;
    color: ${token.colorBgBase};
    opacity: 1;
    text-shadow: none;
  }

  #didomi-host a.didomi-no-link-style.didomi-consent-popup-header-close::before, 
  #didomi-host #didomi-notice-x-button.didomi-x-button::before {
    position: absolute;
    content:'';
    width: 70%;
    height: 5%;
    top: 47%;
    left: 15%;
    background: #888;
    transform: rotate(45deg);
    z-index: 2;
  }

  #didomi-host a.didomi-no-link-style.didomi-consent-popup-header-close::after,
  #didomi-host #didomi-notice-x-button.didomi-x-button::after {
    position: absolute;
    content:'';
    width: 5%;
    height: 70%;
    left: 47%;
    top: 15%;
    background: #888;
    transform: rotate(45deg);
    z-index: 2;
  } 

  #didomi-host :focus{
    outline: none;
  }

  #didomi-host .didomi-components-accordion .label-click .trigger-icon{
    display: inline-flex;
    margin: 0 8px 0 0;
    width: 20px;
    height: 20px;
    background: ${token.colorBorderSecondary};
    border-radius: 50%;
    color: ${token.colorTextBase};
    font-weight: 300;
    line-height: 19px;
    justify-content: center;
  }

  @media (max-width: ${token.screenMD}px) {  
    max-height: 600px;
  }
`;

export const cookiesModalStyle = (token: FullToken) => `
  .didomi-popup-notice {
    position: relative;
  }

  .didomi-popup-notice::before {
    content: '';
    position: absolute;
    top: 16px;
    right: 16px;
    width: 40px;
    height: 40px;
    background: #fff;
    pointer-events: none;
    z-index: 1;
  }

  #didomi-host .didomi-popup-notice .didomi-popup-notice-logo {
    margin: 24px 0 40px;
  }

  #didomi-host .didomi-popup-notice .didomi-popup-notice-text {
    max-width: inherit;
  }

  .didomi-continue-without-agreeing {
    display: flex;
    justify-content: flex-end;
  }

  #didomi-host a.didomi-no-link-style.didomi-consent-popup-header-close {
    color: ${token.colorBorderSecondary};
  }

  #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons {
    margin-top: 40px;
    gap: 24px;
  }

  #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons .didomi-disagree-button {
    margin-right: 24px;
  }

  #buttons button#didomi-notice-disagree-button, 
  #buttons button#didomi-notice-agree-button {
    position: relative;
    overflow: hidden;
  }

  #didomi-notice-disagree-button span,
  #didomi-notice-agree-button span {
    position: relative;
    z-index: 2;
  }
  
  #didomi-notice-disagree-button:hover span {
    color: #fff;
  }
  
  #didomi-notice-agree-button::after, 
  #didomi-notice-disagree-button::after {
    content:'';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%; 
    height: 100%;
    border-radius: 100px;
  }

  #didomi-notice-agree-button::after {
    background: ${token.colorPrimary};
    transform: translateY(100%);
    transition: transform ease-out .3s;
    z-index: 1;
  }

  #didomi-notice-agree-button:hover::after {
    transform: translateY(0);
  }

  #didomi-notice-disagree-button::after {
    background: ${token.colorBorderSecondary};
    transition: .3s background;
    z-index: 0;
  }

  #didomi-notice-disagree-button:hover::after {
    background: ${token.colorPrimary};
  }

  #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons {
    flex-wrap: wrap;
  }

  #didomi-host .didomi-components-radio__option:first-child, [dir=rtl] #didomi-host .didomi-components-radio__option:last-child{
    margin-right: 12px;
  }

  @media (max-width: ${token.screenMD}px) {  
    #didomi-notice-disagree-button {
      order: 2;
    }
    #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons {
      max-width: 100%;
      flex-direction: column;
    }
    #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons .didomi-button,
    #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons .didomi-disagree-button {
      margin-right: 0;
    }
    #didomi-host .didomi-popup-notice .didomi-popup-notice-buttons .didomi-disagree-button {
      margin-top: 10px;
    }
  }

`;

export const customCookiesModalStyle = (token: FullToken) => `

  #didomi-host .didomi-popup-header {
    padding: 24px 0 0;
    font-family: 'Nexa Text';
    font-size: ${token.fontSizeHeading2}px;
    font-weight: ${token.fontWeightStrong};
    justify-content: center;
    line-height: ${token.fontSizeHeading2}px;
  }

  #didomi-host .didomi-consent-popup-body {
    padding: 32px 0 0;
  }

  #didomi-host .didomi-popup-body .didomi-popup-body-section {
    margin-bottom: 0;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-category {
    margin-top: 5px;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-category__name span,
  #didomi-host .didomi-consent-popup-data-processing__purpose > span > span  {
    font-weight: ${token.fontWeightStrong};
  }

  #didomi-host .didomi-consent-popup-body .didomi-consent-popup-body__title span {
    padding-top: 16px;
    font-size: ${token.fontSizeLG}px;
    font-weight: ${token.fontWeightStrong};
    text-transform: none;
  }

  #didomi-host .didomi-components-radio__option {
    background: ${token.colorBorderSecondary};
    border-color: ${token.colorBorderSecondary};
    border-radius: 100px;
    box-shadow: none;
    color: ${token.colorPrimary};
  }

  #didomi-host button.didomi-components-radio__option span {
    font-size: ${token.fontSizeSM}px;
    font-weight: ${token.fontWeightStrong};
    line-height: ${token.lineHeightSM}px;
  }

  #didomi-host .didomi-components-radio__option:hover {
    background: ${token.colorPrimary};
    border-color: ${token.colorPrimary};
    color: ${token.colorBgBase};
  }

  #didomi-host .didomi-components-radio__option:focus {
    outline: none;
  }

  #didomi-host .didomi-components-radio__option.didomi-components-radio__option--disagree,
  #didomi-host .didomi-consent-popup-container-click-all .didomi-components-radio__option.didomi-components-radio__option--disagree {
    border-color:  ${token.colorError};
    background-color:  ${token.colorError};
  }



  #didomi-host .didomi-components-radio__option.didomi-components-radio__option--agree,
  #didomi-host .didomi-consent-popup-container-click-all .didomi-components-radio__option.didomi-components-radio__option--agree  {
    border-color:  ${token.colorSuccess};
    background-color:  ${token.colorSuccess};
  }


  #didomi-host .didomi-consent-popup-preferences-purposes-features p {
    margin-bottom: 8px;
  }
  
  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-data-processing__purpose_actions {
    background: none;
  }

  #didomi-host .didomi-consent-popup-preferences .didomi-consent-popup-data-processing__buttons_tcf_v2 {
    align-self: inherit;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes-features [dataTooltip]:focus:after{
    background: ${token.colorBgBase};
    padding: 8px;
    box-shadow: 0 5px 5px rgba(0,0,0,0.2)
  }


  @media (max-width: ${token.screenMD}px) {
    #didomi-host .didomi-popup-header {
      padding: 24px 12px 0;
      text-align: center;
    }
    #didomi-host .didomi-components-radio {
      width: 100%;
    }
    #didomi-host .didomi-components-radio__option.didomi-components-radio__option--agree, 
    #didomi-host .didomi-components-radio__option.didomi-components-radio__option--disagree {
      padding: 0 20px;
    }
  }
`;

export const partnerModalStyle = (token: FullToken) => `
  #didomi-host .didomi-consent-popup-preferences-vendors .didomi-consent-popup-body {
    padding: 32px 0;
  }

  .didomi-consent-popup-body__section{
    border-radius: 8px;
  }

  #didomi-host #didomi-popup-vendors-all{
    font-size: ${token.fontSizeLG}px;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-view-vendors-list {
    margin-bottom: 32px;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-view-vendors-list .didomi-consent-popup-view-vendors-list-link {
    display: flex;
    padding: 0 16px;
    align-items: center;
    background: none;
    border: 0;
    box-shadow: none;
    color: ${token.colorPrimary};
    font-size: ${token.fontSizeSM}px;
    line-height: ${token.fontSizeSM}px;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-view-vendors-list .didomi-consent-popup-view-vendors-list-link:hover {
    background: none;
  }

  #didomi-host .didomi-first-letter-vendors-list,
  #didomi-host .didomi-consent-popup-vendor__name {
    font-weight: ${token.fontWeightStrong};
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-view-vendors-list .didomi-consent-popup-view-vendors-list-link:focus {
    background: none;
    outline: none;
  }

  #didomi-host .didomi-consent-popup-preferences-purposes .didomi-consent-popup-view-vendors-list .didomi-consent-popup-view-vendors-list-link::after {
      content: "→";
      padding-left: 8px;
  }

  #didomi-host .didomi-consent-popup-preferences-vendors .didomi-consent-popup-container-click-all {
    padding: 16px;
    background-color: ${token.colorBorderSecondary};
    border-color: ${token.colorBorderSecondary};
    border-radius: 8px 8px 0 0;
    font-size: ${token.fontSize}px;
    font-weight: ${token.fontWeightStrong};
    line-height: ${token.fontSize}px};
  }

  #didomi-host .didomi-consent-popup-container-click-all .didomi-components-radio__option {
    background-color: ${token.colorPrimary};
    color: ${token.colorBgBase};
  }

  #didomi-host .didomi-consent-popup-preferences-vendors .didomi-consent-popup-body_vendors-list {
    padding: 0;
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 0 0 8px 8px;
  }
  .didomi-consent-popup-body_vendors-list>div>div {
    padding-left: 16px;
    padding-right: 16px;
  }

  .didomi-consent-popup-body_vendors-list>div>div:not(.didomi-consent-popup-vendor) {
    padding-top: 8px;
  }

  .didomi-consent-popup-body_vendors-list>div>div:not(.didomi-consent-popup-vendor):not(:first-child) {
    border-top: 1px solid ${token.colorBorderSecondary};
  }

  #didomi-host .didomi-consent-popup-preferences .didomi-consent-popup-vendor {
    margin-bottom: 5px;
  }

  #didomi-host .didomi-consent-popup-preferences .didomi-consent-popup-vendor .didomi-consent-popup-vendor__buttons {
    margin-left: 0;
  }

  #didomi-host .didomi-consent-popup-vendor__description [dataTooltip]{
    font-size: 13px;
  }

  #didomi-host .didomi-consent-popup-vendor__description [dataTooltip]:focus:after {
    padding-bottom: 8px;
    max-height: inherit;
    background: none;
    border: 0;
    font-size: 11px;
    line-height: 16px;
  }

  #didomi-host .didomi-consent-popup-preferences-vendors .didomi-user-information-trigger {
    display: flex;
    padding-top: 16px;
    align-items: center;
    color: ${token.colorPrimary};
    font-size: ${token.fontSize}px;
    line-height: ${token.fontSize}px;
  }
  .didomi-components-accordion-label-container {
    padding: 8px 0;
  }

  #didomi-host .didomi-mobile .didomi-components-accordion{
    width: inherit;
    flex: 1;
  }

  #didomi-host .didomi-consent-popup-category .didomi-components-accordion .didomi-components-accordion-label-container,
  #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor {
    flex-direction: row;
    justify-content: space-between;
  }
  
  #didomi-host .didomi-arrow-back-popup-preferences-vendors {
    font-size: 0;
    position: absolute;
    display: block;
    margin: 30px auto;
    width: 14px;
    height: 14px;
    border-top: 2px solid #888;
    border-left: 2px solid #888;
    transform: rotate(-45deg);
    font-size: 0;
    top: -1px;
    left: 28px;
  }

  .didomi-arrow-back-popup-preferences-vendors::after {
    content: "";
    position: absolute;
    top: 3px;
    left: -3px;
    display: block;
    width: 2px;
    height: 32px;
    background-color: #888;
    transform: rotate(-45deg) translate(15px, 4px);
  }

  #didomi-host .didomi-consent-popup-preferences .didomi-consent-popup-vendor .didomi-consent-popup-vendor__right_aligned_buttons{
    padding: 0 0 12px;
  }

  #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor {
    align-items: center;
  }

  #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor.didomi-consent-popup-container-click-all .didomi-consent-popup-vendor__buttons {
    margin-top: 0;
  }

  #didomi-host .didomi-consent-popup-preferences-vendors .didomi-user-information-container{
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
  }

  @media (min-width: ${token.screenMD}px) {
    #didomi-host p.didomi-consent-popup-body__explanation {
      margin-bottom: 32px;
    }
  }

  @media (max-width: ${token.screenMD}px) {
    #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor {
      padding: 16px;
    }
    #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-data-processing__buttons {
      margin-top: 8px;
      width: 100%;
    }
    #didomi-host .didomi-components-radio__option {
      width: 50%;
      height: 36px;
      line-height: 36px;
    }
    #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor .didomi-consent-popup-vendor__buttons {
      width: 100%;
    }

    #didomi-host .didomi-mobile .didomi-components-accordion {
      width: 100%;
      flex: 1 auto;
    }

    #didomi-host .didomi-consent-popup-category .didomi-components-accordion .didomi-components-accordion-label-container,
    #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor{
      flex-direction: column;
      justify-content: flex-start;
    }

    #didomi-host .didomi-mobile #didomi-consent-popup .didomi-consent-popup-preferences .didomi-consent-popup-vendor.didomi-consent-popup-container-click-all .didomi-consent-popup-vendor__buttons {
      margin-top: 10px;
    }
  }

`;
