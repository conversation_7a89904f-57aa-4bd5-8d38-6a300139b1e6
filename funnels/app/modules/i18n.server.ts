import { createCookie } from '@remix-run/node';
import Backend from 'i18next-fs-backend';
import { resolve } from 'node:path';
import { RemixI18Next } from 'remix-i18next/server';

import i18n from '~/config/i18n';

import { DEFAULT_LANGUAGE } from '../utils/constants';

export const localeCookie = createCookie('lng', {
  path: '/',
  sameSite: 'lax',
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
});

export default new RemixI18Next({
  detection: {
    supportedLanguages: i18n.supportedLngs,
    fallbackLanguage: DEFAULT_LANGUAGE,
    cookie: localeCookie,
  },
  // This is the configuration for i18next used
  // when translating messages server-side only
  i18next: {
    ...i18n,
    ns: i18n.ns,
    backend: {
      loadPath: resolve('./funnel/public/locales/{{ns}}-{{lng}}.json'),
    },
  },
  plugins: [Backend],
});
