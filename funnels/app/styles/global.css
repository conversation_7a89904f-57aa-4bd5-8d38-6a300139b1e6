@font-face {
    font-family: 'Nexa Text';
    font-style: normal;
    font-weight: 200;
    src: url('../fonts/nexa-text-light.woff2') format('woff2');
  }
  @font-face {
    font-family: 'Nexa Text';
    font-style: normal;
    font-weight: 300;
    src: url('../fonts/nexa-text-book.woff2') format('woff2');
  }
  @font-face {
    font-family: 'Nexa Text';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/nexa-text-regular.woff2') format('woff2');
  }
  @font-face {
    font-family: 'Nexa Text';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/nexa-text-bold.woff2') format('woff2');
  }

   /* not using for the moment
  .ant-input:not(:placeholder-shown) {
    background: var(--shade-colors-onsurface-border-primary);
  }  */

  html { font-weight: 200; }
  
  .ant-select-focused .ant-select-selector,
  .ant-select-selector:focus,
  .ant-select-selector:active,
  .ant-select-open .ant-select-selector {
    box-shadow: none !important;
  }


  /* .ant-row {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  
  .ant-col:first-child {
      padding-left: 0 !important;
  }
  
  .ant-col:last-child {
    padding-right: 0 !important;
  } */

  /* reset list style */
  body ol, body ul {
    list-style: none;
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
  }

