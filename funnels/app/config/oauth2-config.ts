import { WebStorageStateStore } from 'oidc-client-ts';
import { IS_CLIENT_SIDE } from 'utils';

import {
  BASE_URL,
  MANAGER_URL,
  CLIENT_ID,
  OAUTH2_AUTHORITY,
} from '~/utils/constants';

const selectUserStore = () => {
  if (IS_CLIENT_SIDE) {
    return new WebStorageStateStore({ store: localStorage });
  }
};

const onSigninCallback = (): void => {
  window.history.replaceState({}, document.title, window.location.pathname);
};

export const oidcConfig = {
  authority: OAUTH2_AUTHORITY,
  client_id: CLIENT_ID,
  redirect_uri: `${BASE_URL}login-success`,
  post_logout_redirect_uri: MANAGER_URL,
  response_type: 'code',
  scope: 'openid profile email',
  onSigninCallback: onSigninCallback,
  userStore: selectUserStore(),
};
