import { AdvancedSettingsConfigurationList, KeyboardISO } from 'types';

import { CartState } from '~/types/stores';
import { createPersistentStore } from '~/utils/stores';

export const useCart: () => CartState = createPersistentStore<CartState>(
  set => ({
    estimation: undefined,
    setEstimation: (estimation: number | undefined) =>
      set(() => ({
        estimation,
      })),
    advancedSettingsConfiguration: AdvancedSettingsConfigurationList.AUTO,
    setAdvancedSettingsConfiguration: (
      advancedSettingsConfiguration: AdvancedSettingsConfigurationList,
    ) =>
      set(() => ({
        advancedSettingsConfiguration,
      })),
    advancedSettingsLanguage: '',
    setAdvancedSettingsLanguage: (advancedSettingsLanguage: string) =>
      set(() => ({
        advancedSettingsLanguage,
      })),
    advancedSettingsKeyboard: KeyboardISO.EN_US,
    setAdvancedSettingsKeyboard: (advancedSettingsKeyboard: KeyboardISO) =>
      set(() => ({
        advancedSettingsKeyboard,
      })),
    setKeyboardAndLanguage: (keyboard, language) =>
      set(() => ({
        advancedSettingsKeyboard: keyboard,
        advancedSettingsLanguage: language,
      })),
  }),
  'funnels.cart',
);
