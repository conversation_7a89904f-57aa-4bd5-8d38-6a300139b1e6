import { useTranslation } from 'react-i18next';
import { IApiStock, ProductFamilyId, ICatalog, IAnyOffer } from 'types';
import {
  formatDate,
  DATE_FORMAT_WITH_SLASH_BY_MARKET,
  getPlans,
  getOfferFromId,
  hasPlanMatchingUserTarget,
  isOfferEnabledForSale,
} from 'utils';

import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';

/**
 * Get stock information about an offer.
 * @param stock - IApiStock | null | undefined - can be null based on API response, or undefined if the offer is possibly undefined.
 * @returns isOutOfStock: boolean
 * @returns restockDate: market-formatted available_at date
 * @returns outOfStockText: either 'Out of stock' or 'Available from {{date}}'
 * @example const { isOutOfStock, restockDate, outOfStockText } = useProductStock(planOffer?.stock);
 */
export const useProductStock = (stock: IApiStock | null | undefined) => {
  const { t } = useTranslation();
  const { country } = useLocale();

  const isOutOfStock = stock ? !stock.in_stock : false;

  const restockDate = stock?.available_at
    ? formatDate(
        stock.available_at * 1000,
        DATE_FORMAT_WITH_SLASH_BY_MARKET[country],
      )
    : null;

  const outOfStockText = restockDate
    ? t('catalog.item.restockWithDate', 'Available from {{date}}', {
        date: restockDate,
      })
    : t('catalog.item.outOfStock', 'Out of stock');

  return {
    isOutOfStock,
    restockDate,
    outOfStockText,
  };
};

export const useProductFamilyStock = (
  catalog: ICatalog,
  productFamilyId: ProductFamilyId,
) => {
  const { userTarget } = useStore();

  const plansInProductFamily = getPlans(catalog, plan => {
    return (
      plan.item_family_id === productFamilyId &&
      // CloudPC products are currently the only ones to have a user target that we want to filter on.
      // Without this condition, we would check all cloudpc products from catalog, including all user targets.
      (productFamilyId !== ProductFamilyId.CLOUDPC ||
        hasPlanMatchingUserTarget(plan, userTarget!))
    );
  });

  const isProductFamilyOutOfStock = plansInProductFamily
    .flatMap(product => product.offers)
    .map(offerId => getOfferFromId(catalog, offerId))
    .filter((offer): offer is IAnyOffer => offer !== undefined)
    .every(offer => !isOfferEnabledForSale({ offer, withCatalogStock: true }));

  return {
    isProductFamilyOutOfStock,
  };
};
