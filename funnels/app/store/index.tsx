import { ProductFamilyId } from 'types';

import { OfferInCart } from '~/types/cart';
import { ProductTarget } from '~/types/product';
import { createPersistentStore } from '~/utils/stores';

interface IState {
  coupon: string | '';
  setCoupon: (coupon: string) => void;
  datacenterName: string | null;
  setDatacenterName: (datacenterName: string) => void;
  zipcode: string | null;
  setZipcode: (zipcode: string) => void;
  userTarget: ProductTarget | null;
  setUserTarget: (userTarget: ProductTarget) => void;
  offersInCart: OfferInCart[];
  productFamilyId: ProductFamilyId | null;
  addOfferToCart: (newOfferInCart: OfferInCart[]) => void;
  setOfferToCart: (newOfferInCart: OfferInCart) => void;
  removeOfferFromCart: (offerId: string) => void;
  resetCartAndAddOfferToCart: (newOfferInCart: OfferInCart[]) => void;
  geolocResults: { zipcode: string; ip?: string; geolocalized: boolean } | null;
  setGeolocResults: (
    geolocalized: boolean,
    zipcode: string,
    ip?: string,
  ) => void;
  productType: string | null;
  referralCode: string | null;
  setReferralCode: (referralCode: string) => void;
  setProductType: (productType: string) => void;
  subscriptionCreationDate: number;
  setSubscriptionCreationDate: (subscriptionCreationDate: number) => void;
  subscriptionId: string | null;
  setSubscriptionId: (subscriptionId: string) => void;
  transactionId: string;
  setTransactionId: (transactionId: string) => void;
  vmsCount: number;
  setVmsCount: (vmsCount: number) => void;
  resetState: () => void;
  userIp: string | null;
  setUserIp: (userIp: string) => void;
}

const useStore: () => IState = createPersistentStore<IState>(
  set => ({
    coupon: '',
    setCoupon: (coupon: string) =>
      set(() => ({
        coupon,
      })),
    datacenterName: null,
    setDatacenterName: (datacenterName: string) =>
      set(() => ({
        datacenterName,
      })),
    zipcode: null,
    setZipcode: (zipcode: string) =>
      set(() => ({
        zipcode,
      })),
    userTarget: null,
    setUserTarget: (userTarget: ProductTarget) =>
      set(() => ({
        userTarget,
      })),
    offersInCart: [],
    productFamilyId: null,
    addOfferToCart: (newOfferInCart: OfferInCart[]) =>
      set(state => ({
        offersInCart: [...state.offersInCart, ...newOfferInCart],
      })),
    // remplace si déjà existant
    setOfferToCart: (newOfferInCart: OfferInCart) =>
      set(state => ({
        offersInCart: [
          ...state.offersInCart.filter(offer => offer.id !== newOfferInCart.id),

          newOfferInCart,
        ],
      })),
    // supprime si déjà existant
    removeOfferFromCart: (offerId: string) =>
      set(state => ({
        offersInCart: [
          ...state.offersInCart.filter(offer => offer.id !== offerId),
        ],
      })),

    resetCartAndAddOfferToCart: (newOfferInCart: OfferInCart[]) =>
      set(() => ({
        offersInCart: [...newOfferInCart],
      })),
    geolocResults: null,
    setGeolocResults: (geolocalized: boolean, zipcode: string, ip?: string) =>
      set(() => ({ geolocResults: { zipcode, ip, geolocalized } })),
    productType: null,
    referralCode: null,
    setReferralCode: (referralCode: string) =>
      set(() => ({
        referralCode,
      })),
    setProductType: (productType: string) =>
      set(() => ({
        productType,
      })),
    subscriptionCreationDate: 0,
    setSubscriptionCreationDate: (subscriptionCreationDate: number) =>
      set(() => ({
        subscriptionCreationDate,
      })),
    subscriptionId: null,
    setSubscriptionId: (subscriptionId: string) =>
      set(() => ({
        subscriptionId,
      })),
    transactionId: '',
    setTransactionId: (transactionId: string) =>
      set(() => ({
        transactionId,
      })),
    vmsCount: 0,
    setVmsCount: (vmsCount: number) =>
      set(() => ({
        vmsCount,
      })),
    userIp: null,
    setUserIp: (userIp: string) =>
      set(() => ({
        userIp,
      })),

    resetState: () =>
      set(() => ({
        coupon: '',
        datacenterName: null,
        offersInCart: [],
        productType: null,
        productFamilyId: null,
        referralCode: null,
        subscriptionId: null,
        transactionId: '',
        vmsCount: 0,
        zipcode: null,
        userIp: null,
      })),
  }),
  'funnels.global',
);

export default useStore;
