import { createPersistentStore } from '~/utils/stores';

interface RedirectAfterLoginSuccessState {
  loginRedirectUrl?: string;
  setLoginRedirectUrl: (url: string) => void;
  clearLoginRedirectUrl: () => void;
}

export const useRedirectAfterLoginSuccess: () => RedirectAfterLoginSuccessState =
  createPersistentStore<RedirectAfterLoginSuccessState>(
    set => ({
      loginRedirectUrl: undefined,
      setLoginRedirectUrl(url: string) {
        set(() => ({ loginRedirectUrl: url }));
      },
      clearLoginRedirectUrl() {
        set(() => ({ loginRedirectUrl: undefined }));
      },
    }),
    'funnels.redirectAfterLoginSuccess',
  );
