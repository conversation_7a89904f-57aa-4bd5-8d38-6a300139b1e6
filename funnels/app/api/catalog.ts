import { ICatalog, IApiProduct, MaybeAuthToken, Market } from 'types';
import { fetchSpectrApi } from 'utils';

import { formatCatalog } from '~/utils/catalog';
import { API_URL } from '~/utils/constants';

const fetchApiCatalog = async (
  authToken: MaybeAuthToken,
  params: {
    currency: string;
    region: string;
    market: Market;
    zipcode: string | null;
    datacenter: string | null;
    accessCode?: string;
    subscriptionId?: string;
    withStock: boolean;
  },
): Promise<IApiProduct[]> => {
  const useDatacenter = !!params.datacenter;
  const apiResult: IApiProduct[] = await fetchSpectrApi(
    authToken ? `Bearer ${authToken}` : '', // Used for server fetch and non co user fetch
    `${API_URL}/catalog`,
    {
      method: 'GET',
      body: {
        currency_code: params.currency.toUpperCase(),
        region: params.region.toUpperCase(),
        country: params.market,
        ...(params.zipcode && !useDatacenter && { zipcode: params.zipcode }),
        ...(params.datacenter && { datacenter: params.datacenter }),
        ...(params.accessCode && { access_code: params.accessCode }),
        with_stock: params.withStock,
        ...(params.subscriptionId && {
          subscription_id: params.subscriptionId,
        }),
      },
    },
  );

  return apiResult;
};

export const fetchCatalog = async (
  authToken: MaybeAuthToken,
  params: {
    currency: string;
    region: string;
    market: Market;
    zipcode: string | null;
    datacenter: string | null;
    accessCode?: string;
    subscriptionId?: string;
    withStock: boolean;
  },
): Promise<ICatalog> => {
  const apiResult = await fetchApiCatalog(authToken, params);

  return formatCatalog(apiResult);
};
