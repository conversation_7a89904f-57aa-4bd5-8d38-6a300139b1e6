import { PaymentIntent } from '@chargebee/chargebee-js-types';
import {
  IAnyBillingDetails,
  ICouponPayload,
  IMember,
  IPaymentIntentPayload,
  IPaymentMethod,
  ISubscription,
  ISubscriptionPayload,
  IUpdateBillingDetailsPayload,
  IUserDetails,
  MaybeAuthToken,
  Zone,
} from 'types';
import { fetchSpectrApi, fetchSpectrApiPublic } from 'utils';

import { API_URL } from '~/utils/constants';

export const fetchUserDetails = async (
  authToken: string,
): Promise<IUserDetails | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/`);
};

export const fetchCurrentMember = async (
  authToken: string,
): Promise<IMember | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/members/me`);
};

export const fetchBillingDetails = async (
  authToken: MaybeAuthToken,
): Promise<IAnyBillingDetails> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/billing`);
};

export const fetchPaymentMethods = async (
  authToken: MaybeAuthToken,
): Promise<IPaymentMethod[]> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/payment`);
};

export const updateBillingDetails = async (
  authToken: string,
  billingDetails:
    | IUpdateBillingDetailsPayload
    | Partial<IUpdateBillingDetailsPayload>,
): Promise<string | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/billing`, {
    method: 'POST',
    body: {
      ...billingDetails,
    },
  });
};

export const addPaymentIntent = async (
  authToken: MaybeAuthToken,
  paymentIntentPayload: IPaymentIntentPayload,
): Promise<PaymentIntent | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/payment/intent`,
    {
      method: 'POST',
      body: paymentIntentPayload,
    },
  );
};

export const addPaymentMethod = async (
  authToken: MaybeAuthToken,
  chargeBeeToken?: string,
  paymentIntent?: PaymentIntent,
): Promise<IPaymentMethod> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/payment`, {
    method: 'POST',
    body: {
      primary: true,
      ...(chargeBeeToken ? { token: chargeBeeToken } : {}),
      ...(paymentIntent
        ? {
            payment_intent: {
              id: paymentIntent.id,
            },
          }
        : {}),
    },
  });
};

export const publicVerifyCoupon = async (
  { coupon }: ICouponPayload,
  zone: Zone,
): Promise<boolean | undefined> => {
  return fetchSpectrApiPublic(
    `${API_URL}/catalog/coupon/${coupon}?region=${zone}`,
  );
};

export const verifyCoupon = async (
  authToken: MaybeAuthToken,
  couponPayload: ICouponPayload,
  zone: Zone,
): Promise<boolean | undefined> => {
  const { coupon } = couponPayload;

  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/coupon/${coupon}`,
    {
      method: 'GET',
      body: {
        region: zone,
      },
    },
  );
};

// TODO: etienned keep subscription endoint in user file ?
export const addSubscription = async (
  authToken: MaybeAuthToken,
  subscriptionPayload: ISubscriptionPayload,
  vmsCount: number,
): Promise<ISubscription> => {
  const params = vmsCount > 1 ? `&vms_count=${vmsCount}` : '';
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription?version=v1.5${params}`,
    {
      method: 'POST',
      body: {
        ...subscriptionPayload,
      },
    },
  );
};
