import {
  IEstimation,
  IEstimationPayload,
  INotLoggedEstimationPayload,
  MaybeAuthToken,
} from 'types';
import { API_URL, fetchSpectrApi, fetchSpectrApiPublic } from 'utils';

export const fetchNotLoggedEstimation = async (
  estimatePayload: INotLoggedEstimationPayload,
): Promise<IEstimation | undefined> => {
  return fetchSpectrApiPublic(`${API_URL}/subscription/estimate`, {
    method: 'POST',
    body: {
      ...estimatePayload,
    },
  });
};

export const fetchEstimation = async (
  authToken: MaybeAuthToken,
  estimatePayload: IEstimationPayload,
  vmsCount: number,
): Promise<IEstimation | undefined> => {
  const params = vmsCount > 1 ? `?vms_count=${vmsCount}` : '';

  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/estimate${params}`,
    {
      method: 'POST',
      body: {
        ...estimatePayload,
      },
    },
  );
};

export const fetchSubscriptionModificationEstimation = async (
  authToken: MaybeAuthToken,
  estimatePayload: IEstimationPayload,
  subscriptionIdToModify: string,
): Promise<IEstimation | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/estimate/${subscriptionIdToModify}`,
    {
      method: 'POST',
      body: {
        ...estimatePayload,
      },
    },
  );
};
