import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { AuthenticationWrapper } from '~/components/Authentication/AuthenticationWrapper';
import { GlobalLoader } from '~/components/ui/Loader';
import { useAuthentication } from '~/hooks/useAuthentication';
import { useRedirectAfterLoginSuccess } from '~/store/useRedirectAfterLoginSuccess';

const LoginSuccess = () => {
  const { loginRedirectUrl, clearLoginRedirectUrl } =
    useRedirectAfterLoginSuccess();
  const { isAuthenticated } = useAuthentication();
  const navigate = useNavigate();

  useEffect(
    () => {
      if (isAuthenticated && loginRedirectUrl) {
        clearLoginRedirectUrl();
        navigate(loginRedirectUrl, { replace: true });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isAuthenticated],
  );

  return <GlobalLoader fullHeight />;
};

const LoginSuccessRoute = () => {
  return (
    <AuthenticationWrapper>
      <LoginSuccess />
    </AuthenticationWrapper>
  );
};

export default LoginSuccessRoute;
