import { Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { NotificationState } from 'types';

import { Configurator } from '~/components/Configurator/Configurator';
import { GlobalLoader } from '~/components/ui/Loader';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';

const ConfiguratorRoute = () => {
  const { t } = useTranslation();

  const catalogQuery = useCatalog();

  if (catalogQuery.isLoading || catalogQuery.isFetching) {
    return <GlobalLoader fullHeight />;
  }

  if (catalogQuery.isError) {
    return (
      <Alert
        type={NotificationState.ERROR}
        description={t('errors.api.catalog')}
      />
    );
  }

  return <Configurator catalog={catalogQuery.data} />;
};

export default ConfiguratorRoute;
