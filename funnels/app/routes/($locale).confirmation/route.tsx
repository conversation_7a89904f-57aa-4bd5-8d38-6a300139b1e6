import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import { ProductFamilyId } from 'types';

import { AuthenticationWrapper } from '~/components/Authentication/AuthenticationWrapper';
import { FaqCard } from '~/components/Confirmation/FaqCard/FaqCard';
import { SetupStepsCard } from '~/components/Confirmation/SetupStepsCard/SetupStepsCard';
import { ThankingCard } from '~/components/Confirmation/ThankingCard/ThankingCard';
import { VideoCard } from '~/components/Confirmation/VideoCard/VideoCard';
import { VideoModal } from '~/components/Confirmation/VideoCard/VideoModal';
import { LocalLoader } from '~/components/ui/Loader';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useUrl } from '~/hooks/useUrl';
import useStore from '~/store';
import { OfferInCart } from '~/types/cart';

const Confirmation = () => {
  const catalogQuery = useCatalog();
  const { offersInCart } = useStore();
  const { getShadowVideoUrl } = useUrl();
  const [subscribedProduct, setSubscribedProduct] = useState<OfferInCart>();
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const isDriveProduct =
    subscribedProduct?.productFamilyId === ProductFamilyId.SHADOWDRIVE;

  useEffect(() => {
    if (offersInCart?.length > 0) {
      const productInCart = offersInCart.find(
        offerInCart => !!offerInCart.productFamilyId,
      );
      setSubscribedProduct(productInCart);
    }
  }, [offersInCart]);

  if (catalogQuery.isLoading) {
    return <LocalLoader />;
  }

  // if (!catalogQuery.data || !subscribedProduct?.productFamilyId) {
  //   return <></>;
  // }

  return (
    <Flex vertical gap="large">
      <ThankingCard isDriveProduct={isDriveProduct} />
      <SetupStepsCard
        setIsVideoModalOpen={setIsVideoModalOpen}
        isDriveProduct={isDriveProduct}
      />
      <VideoCard
        setIsVideoModalOpen={setIsVideoModalOpen}
        isDriveProduct={isDriveProduct}
      />
      <FaqCard isDriveProduct={isDriveProduct} />
      <VideoModal
        videoId={getShadowVideoUrl(isDriveProduct)}
        isVideoModalOpen={isVideoModalOpen}
        setIsVideoModalOpen={setIsVideoModalOpen}
      />
    </Flex>
  );
};

const ConfirmationRoute = () => {
  return (
    <AuthenticationWrapper>
      <Confirmation />
    </AuthenticationWrapper>
  );
};

export default ConfirmationRoute;
