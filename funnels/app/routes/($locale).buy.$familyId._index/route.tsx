import { Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { NotificationState } from 'types';

import { ProductSelection } from '~/components/ProductSelection/ProductSelection';
import { GlobalLoader } from '~/components/ui/Loader';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';

// import { ProductsList } from '@/components/product/productsList/productsList';

const ProductSelectorRoute = () => {
  const { t } = useTranslation();
  const catalogQuery = useCatalog({ withStock: true });

  if (catalogQuery.isLoading || catalogQuery.isFetching) {
    return <GlobalLoader fullHeight />;
  }

  if (catalogQuery.isError) {
    return (
      <Alert type={NotificationState.ERROR} message={t('errors.api.catalog')} />
    );
  }

  return <ProductSelection />;
};

export default ProductSelectorRoute;
