import { <PERSON><PERSON>, Col, Flex, Row } from 'antd';
import { Assert, QueryManager } from 'common-components';
import { useDatacenterListForPlan } from 'hooks';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  IAnyOffer,
  IAnyProduct,
  ICatalog,
  NotificationState,
  ProductFamilyId,
} from 'types';

import { AuthenticationWrapper } from '~/components/Authentication/AuthenticationWrapper';
import { Cart } from '~/components/Cart/Cart';
import { OutOfStockAlert } from '~/components/OutOfStockAlert/OutOfStockAlert';
import { CloudpcOverviewCard } from '~/components/OverviewCards/Cloudpc/Card';
import { BillingDetailsCard } from '~/components/PurchaseOverview/BillingDetailsCard';
import { PaymentMethodsCard } from '~/components/PurchaseOverview/PaymentMethodsCard';
import { GlobalLoader, LocalLoader } from '~/components/ui/Loader';
import { FormContextProvider } from '~/contexts/FormContext';
import { PaymentProvider } from '~/contexts/PaymentContext';
import { useCatalog } from '~/hooks/reactQuery/catalog/useCatalog';
import { useBillingDetails } from '~/hooks/reactQuery/user/useUser';
import { useLocale } from '~/hooks/useLocale';
import useStore from '~/store';
import {
  getIsCartTotalZero,
  getPlanAndPlanOfferFromOffersInCart,
  getPlanOfferFromCart,
} from '~/utils/cart';

interface IPurchaseOverviewContentProps {
  catalog: ICatalog;
  plan: IAnyProduct;
  planOffer: IAnyOffer;
}

type PurchaseOverviewForm = 'billingDetails' | 'payment';

const PurchaseOverview = () => {
  const { t } = useTranslation();
  const catalogQuery = useCatalog();
  const { offersInCart } = useStore();

  return (
    <PaymentProvider>
      <FormContextProvider>
        <Row gutter={[40, 16]}>
          <Col
            xs={{ flex: '100%' }}
            sm={{ flex: '100%' }}
            md={{ flex: '100%' }}
            lg={{ flex: '100%' }}
            xl={{ flex: '68%' }}
          >
            <Flex vertical gap="middle">
              <QueryManager
                queries={{ catalog: catalogQuery }}
                whenLoading={<GlobalLoader />}
                whenError={() => (
                  <Alert
                    type={NotificationState.ERROR}
                    description={t('errors.api.catalog')}
                  />
                )}
              >
                {({ catalog }) => {
                  const { plan: maybePlan, planOffer: maybePlanOffer } =
                    getPlanAndPlanOfferFromOffersInCart(catalog, offersInCart);

                  return (
                    <Assert
                      values={{ plan: maybePlan, planOffer: maybePlanOffer }}
                      whenError={() => <>Error ?</>}
                    >
                      {({ plan, planOffer }) => {
                        return (
                          <PurchaseOverviewWithCatalog
                            catalog={catalog}
                            plan={plan}
                            planOffer={planOffer}
                          />
                        );
                      }}
                    </Assert>
                  );
                }}
              </QueryManager>
              <PurchaseOverviewForms />
            </Flex>
          </Col>
          <Col
            xs={{ flex: '100%' }}
            sm={{ flex: '100%' }}
            md={{ flex: '100%' }}
            lg={{ flex: '100%' }}
            xl={{ flex: '32%' }}
          >
            <Cart />
          </Col>
        </Row>
      </FormContextProvider>
    </PaymentProvider>
  );
};

const PurchaseOverviewWithCatalog = ({
  catalog,
  plan,
  planOffer,
}: IPurchaseOverviewContentProps) => {
  const { t } = useTranslation();
  const { zipcode, geolocResults, offersInCart, datacenterName } = useStore();
  const { country } = useLocale();
  const planOfferInCart = getPlanOfferFromCart(catalog, offersInCart);
  const datacentersQuery = useDatacenterListForPlan({
    zipcode: zipcode ?? geolocResults?.zipcode ?? null,
    country,
    planId: planOfferInCart?.product_id,
  });

  return (
    <>
      <QueryManager
        queries={{ datacenters: datacentersQuery }}
        whenLoading={<></>}
        whenError={() => (
          <Alert
            type={NotificationState.ERROR}
            description={t('errors.api.datacenters')}
          />
        )}
      >
        {({ datacenters }) => {
          const currentDatacenter = datacenters.find(
            datacenter => datacenter.name === datacenterName,
          );

          if (
            (!currentDatacenter ||
              currentDatacenter.status === 'unavailable') &&
            planOfferInCart?.itemFamilyId === ProductFamilyId.CLOUDPC
          ) {
            return (
              <Alert
                type={NotificationState.ERROR}
                description={<OutOfStockAlert />}
              />
            );
          }

          return null;
        }}
      </QueryManager>
      <CloudpcOverviewCard
        catalog={catalog}
        plan={plan}
        offer={planOffer}
        offersInCart={offersInCart}
      />
    </>
  );
};

const PurchaseOverviewForms = () => {
  const { t } = useTranslation();
  const { offersInCart } = useStore();
  const billingDetailsQuery = useBillingDetails();
  const { country } = useLocale();
  const isInitializedRef = useRef(false);
  const [currentForm, setCurrentForm] =
    useState<PurchaseOverviewForm>('billingDetails');

  const hasFreeCart = getIsCartTotalZero(offersInCart);

  const onBillingDetailsEdit = () => {
    setCurrentForm('billingDetails');
  };

  return (
    <>
      <BillingDetailsCard
        isUnfolded={currentForm === 'billingDetails'}
        onEdit={onBillingDetailsEdit}
        onSaveBillingDetailsSuccess={() => {
          setCurrentForm('payment');
        }}
      />
      {!hasFreeCart && (
        <QueryManager
          queries={{ billingDetails: billingDetailsQuery }}
          whenLoading={<LocalLoader />}
          whenError={() => (
            <Alert
              type={NotificationState.ERROR}
              description={t('errors.api.billingDetails')}
            />
          )}
        >
          {({ billingDetails }) => {
            const hasEmptyBillingDetails = hasFreeCart
              ? isEmpty(billingDetails)
              : !(
                  billingDetails?.address1 &&
                  billingDetails?.zipcode &&
                  billingDetails?.city
                );

            if (!isInitializedRef.current) {
              if (!hasFreeCart) {
                setCurrentForm(
                  hasEmptyBillingDetails ? 'billingDetails' : 'payment',
                );
              }

              isInitializedRef.current = true;
            }

            return (
              <PaymentMethodsCard
                country={country}
                isUnfolded={currentForm === 'payment'}
                hasEmptyBillingDetails={hasEmptyBillingDetails}
              />
            );
          }}
        </QueryManager>
      )}
    </>
  );
};

const PurchaseOverviewRoute = () => {
  return (
    <AuthenticationWrapper>
      <PurchaseOverview />
    </AuthenticationWrapper>
  );
};

export default PurchaseOverviewRoute;
