import {
  Cur<PERSON>cy,
  CurrencyPerLocale,
  KeyboardISO,
  Language,
  Locale,
  LocalePerCurrency,
  Market,
  SelectOption,
} from 'types';
import { IMAGE_PATH } from 'utils';

import { IChargebeeConfiguration } from '~/types/chargebee';

export const REDIRECT_KEY = 'shadowNavigationRedirect';
export const MAX_RETRY = 1;

// Have to make this complex array because Next doesn't allow
// destructuring env variables so process.env[VAR_NAME] returns undefined
const requiredEnvVars = [
  {
    key: 'VITE_PUBLIC_API_URL',
    value: import.meta.env.VITE_PUBLIC_API_URL,
  },
  {
    key: 'VITE_PUBLIC_BASE_URL',
    value: import.meta.env.VITE_PUBLIC_BASE_URL,
  },
  {
    key: 'VITE_PUBLIC_CLIENT_ID',
    value: import.meta.env.VITE_PUBLIC_CLIENT_ID,
  },
  {
    key: 'VITE_PUBLIC_OAUTH2_AUTHORITY',
    value: import.meta.env.VITE_PUBLIC_OAUTH2_AUTHORITY,
  },
  {
    key: 'VITE_PUBLIC_SHOP_URL',
    value: import.meta.env.VITE_PUBLIC_SHOP_URL,
  },
  {
    key: 'VITE_PUBLIC_MANAGER_URL',
    value: import.meta.env.VITE_PUBLIC_SHOP_URL,
  },
  {
    key: 'VITE_PUBLIC_ACCOUNT_DRIVE_URL',
    value: import.meta.env.VITE_PUBLIC_ACCOUNT_DRIVE_URL,
  },
  {
    key: 'VITE_PUBLIC_DRIVE_URL',
    value: import.meta.env.VITE_PUBLIC_DRIVE_URL,
  },
  {
    key: 'VITE_PUBLIC_LANDING_URL',
    value: import.meta.env.VITE_PUBLIC_LANDING_URL,
  },
  {
    key: 'VITE_PUBLIC_AUTH_URL',
    value: import.meta.env.VITE_PUBLIC_AUTH_URL,
  },
  {
    key: 'VITE_PUBLIC_SENTRY_DSN',
    value: import.meta.env.VITE_PUBLIC_SENTRY_DSN,
  },
  {
    key: 'VITE_PUBLIC_SENTRY_ENV',
    value: import.meta.env.VITE_PUBLIC_SENTRY_ENV,
  },
  {
    key: 'VITE_PUBLIC_SPRINKLR_APP_ID',
    value: import.meta.env.VITE_PUBLIC_SPRINKLR_APP_ID,
  },
  {
    key: 'VITE_PUBLIC_FLAGSHIP_API_KEY',
    value: import.meta.env.VITE_PUBLIC_FLAGSHIP_API_KEY,
  },
  {
    key: 'VITE_PUBLIC_FLAGSHIP_ENV_ID',
    value: import.meta.env.VITE_PUBLIC_FLAGSHIP_ENV_ID,
  },
  {
    key: 'VITE_PUBLIC_GTM_URL',
    value: import.meta.env.VITE_PUBLIC_GTM_URL,
  },
];

const computeErrorText = (name: string) =>
  `Required env variable ${name} is undefined`;

requiredEnvVars.map(requiredEnvVar => {
  if (!requiredEnvVar.value) {
    throw new Error(computeErrorText(requiredEnvVar.key));
  }
});

// Base urls
export const API_URL = import.meta.env.VITE_PUBLIC_API_URL as string;
export const BASE_URL = import.meta.env.VITE_PUBLIC_BASE_URL as string;
export const AUTH_URL = import.meta.env.VITE_PUBLIC_AUTH_URL as string;

export const OAUTH2_AUTHORITY = import.meta.env
  .VITE_PUBLIC_OAUTH2_AUTHORITY as string;
export const CLIENT_ID = import.meta.env.VITE_PUBLIC_CLIENT_ID as string;

export const SHOP_URL = import.meta.env.VITE_PUBLIC_SHOP_URL;
export const MANAGER_URL = import.meta.env.VITE_PUBLIC_MANAGER_URL;
export const ACCOUNT_URL = import.meta.env.VITE_PUBLIC_ACCOUNT_URL;
export const LANDING_URL = import.meta.env.VITE_PUBLIC_LANDING_URL;
export const DRIVE_URL = import.meta.env.VITE_PUBLIC_DRIVE_URL;
export const COOKIE_DOMAIN = process.env.NEXT_PUBLIC_COOKIE_DOMAIN;

export const DIDOMI_PUBLIC_API_KEY = import.meta.env
  .VITE_PUBLIC_DIDOMI_PUBLIC_API_KEY;
export const DIDOMI_DEFAULT_NOTICE_ID = import.meta.env
  .VITE_PUBLIC_DIDOMI_DEFAULT_NOTICE_ID;
export const SENTRY_DSN = import.meta.env.VITE_PUBLIC_SENTRY_DSN;
export const SENTRY_ENV = import.meta.env.VITE_PUBLIC_SENTRY_ENV;

export const FLAGSHIP_API_KEY = import.meta.env
  .VITE_PUBLIC_FLAGSHIP_API_KEY as string;
export const FLAGSHIP_ENV_ID = import.meta.env
  .VITE_PUBLIC_FLAGSHIP_ENV_ID as string;

export const DEFAULT_MARKET = Market.US;
export const DEFAULT_LANGUAGE = Language.EN;
export const DEFAULT_CURRENCY = Currency.USD;
export const DEFAULT_LOCALE = Locale.EN_US;
export const FALLBACK_LOCALE = 'en';

export const PRIVACY_POLICY_URL = `${LANDING_URL}privacy-policy`;
export const LEGAL_URL = `${LANDING_URL}legal-notice`;
export const COOKIES_URL = `${LANDING_URL}cookies`;

export const TOOLTIP_LEAVE_TOUCH_DELAY = 3;
export const TOOLTIP_ENTER_TOUCH_DELAY = 0;

export const CURRENCY_PER_LOCALE: CurrencyPerLocale = {
  en: Currency.EUR,
  'en-AT': Currency.EUR,
  'en-BE': Currency.EUR,
  'en-CA': Currency.CAD,
  'en-CH': Currency.CHF,
  'en-DE': Currency.EUR,
  'en-DK': Currency.DKK,
  'en-ES': Currency.EUR,
  'en-FR': Currency.EUR,
  'en-GB': Currency.GBP,
  'en-IT': Currency.EUR,
  'en-LU': Currency.EUR,
  'en-NL': Currency.EUR,
  'en-SE': Currency.SEK,
  'en-US': Currency.USD,
  'fr-BE': Currency.EUR,
  'fr-CA': Currency.CAD,
  'fr-CH': Currency.CHF,
  'fr-FR': Currency.EUR,
  'fr-LU': Currency.EUR,
  'de-AT': Currency.EUR,
  'de-CH': Currency.CHF,
  'de-DE': Currency.EUR,
  'es-ES': Currency.EUR,
  'it-IT': Currency.EUR,
  'sv-SE': Currency.SEK,
  'da-DK': Currency.DKK,
  'en-PL': Currency.EUR,
  'en-CZ': Currency.EUR,
  'en-HU': Currency.EUR,
  'en-IE': Currency.EUR,
  'en-SK': Currency.EUR,
  'en-HR': Currency.EUR,
  'en-SI': Currency.EUR,
  'en-PT': Currency.EUR,
};

export const LOCALE_PER_CURRENCY: LocalePerCurrency = {
  [Currency.EUR]: Locale.FR_FR,
  [Currency.CAD]: Locale.EN_CA,
  [Currency.CHF]: Locale.FR_CH,
  [Currency.DKK]: Locale.DA_DK,
  [Currency.SEK]: Locale.SV_SE,
  [Currency.USD]: Locale.EN_US,
  [Currency.GBP]: Locale.EN_GB,
};

export const LANGUAGES_PER_MARKET: Record<Market, Language[]> = {
  [Market.US]: [Language.EN],
  [Market.GB]: [Language.EN],
  [Market.FR]: [Language.FR, Language.EN],
  [Market.BE]: [Language.FR, Language.EN],
  [Market.CH]: [Language.FR, Language.DE, Language.EN],
  [Market.LU]: [Language.FR, Language.EN],
  [Market.DE]: [Language.DE, Language.EN],
  [Market.NL]: [Language.EN],
  [Market.CA]: [Language.FR, Language.EN],
  [Market.AT]: [Language.DE, Language.EN],
  [Market.ES]: [Language.ES, Language.EN],
  [Market.IT]: [Language.IT, Language.EN],
  [Market.SE]: [Language.EN],
  [Market.DK]: [Language.EN],
  [Market.PL]: [Language.EN],
  [Market.CZ]: [Language.EN],
  [Market.HU]: [Language.EN],
  [Market.IE]: [Language.EN],
  [Market.SK]: [Language.EN],
  [Market.HR]: [Language.EN],
  [Market.SI]: [Language.EN],
  [Market.PT]: [Language.EN],
};

export const LANGUAGES_WITH_FLAG_OPTIONS: SelectOption[] = [
  { value: Language.EN, label: 'English', flag: 'gb' },
  { value: Language.FR, label: 'Français', flag: 'fr' },
  { value: Language.DE, label: 'Deutsche', flag: 'de' },
  { value: Language.ES, label: 'Español', flag: 'es' },
  { value: Language.IT, label: 'Italiano', flag: 'it' },
];

export const LANGUAGES_OPTIONS: SelectOption[] = [
  { value: Language.EN, label: 'English' },
  { value: Language.FR, label: 'Français' },
  { value: Language.DE, label: 'Deutsch' },
  { value: Language.NL, label: 'Nederlandse' },
  { value: Language.ES, label: 'Español' },
  { value: Language.IT, label: 'Italiano' },
];

export const MARKETS: Record<Market, string> = {
  [Market.US]: 'United States',
  [Market.GB]: 'United Kingdom',
  [Market.FR]: 'France',
  [Market.BE]: 'Belgique',
  [Market.CH]: 'Switzerland',
  [Market.LU]: 'Luxembourg',
  [Market.DE]: 'Deutschland',
  [Market.NL]: 'Nederland',
  [Market.CA]: 'Canada',
  [Market.AT]: 'Österreich',
  [Market.ES]: 'España',
  [Market.IT]: 'Italia',
  [Market.SE]: 'Sweden',
  [Market.DK]: 'Denmark',
  [Market.PL]: 'Poland',
  [Market.CZ]: 'Czech Republic',
  [Market.HU]: 'Hungary',
  [Market.IE]: 'Ireland',
  [Market.SK]: 'Slovakia',
  [Market.HR]: 'Croatia',
  [Market.SI]: 'Slovenia',
  [Market.PT]: 'Portugal',
};

export const MARKETS_OPTIONS = (Object.keys(MARKETS) as Market[]).map(key => ({
  value: key,
  label: MARKETS[key],
}));

export const APPS_URL = `${LANDING_URL}download`;
export const WEB_SUPPORT_URL = 'https://shdw.me/HC-B2C';
export const CLOUDPC_FAQ_URL = `${LANDING_URL}faq`;
export const DRIVE_FAQ_URLS: Record<Language, string> = {
  [Language.FR]:
    'https://support.shadow.tech/fr/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.DE]:
    'https://support.shadow.tech/de/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.NL]:
    'https://support.shadow.tech/en_US/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.EN]:
    'https://support.shadow.tech/en_US/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.ES]:
    'https://support.shadow.tech/es/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.IT]:
    'https://support.shadow.tech/it/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.SV]:
    'https://support.shadow.tech/en_US/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
  [Language.DA]:
    'https://support.shadow.tech/en_US/articles/introduction-with-shadow-drive/frequently-asked-questions-faq/64e3101204a01d0067a5ef1e',
};

export const SHADOW_YOUTUBE_URLS: Record<
  Market,
  string | Partial<Record<Language, string>>
> = {
  [Market.US]: 'ZXKngN2qV7Y',
  [Market.GB]: 'ZXKngN2qV7Y',
  [Market.FR]: 'gZSDXkBLCS8',
  [Market.BE]: 'gZSDXkBLCS8',
  [Market.CH]: {
    [Language.FR]: 'gZSDXkBLCS8',
    [Language.DE]: 'ZXKngN2qV7Y',
  },
  [Market.LU]: 'ZXKngN2qV7Y',
  [Market.DE]: 'ZXKngN2qV7Y',
  [Market.NL]: 'ZXKngN2qV7Y',
  [Market.CA]: {
    [Language.FR]: 'gZSDXkBLCS8',
    [Language.EN]: 'ZXKngN2qV7Y',
  },
  [Market.AT]: 'ZXKngN2qV7Y',
  [Market.ES]: 'ZXKngN2qV7Y',
  [Market.IT]: '5XE1PdkvJEM',
  [Market.SE]: 'ZXKngN2qV7Y',
  [Market.DK]: 'ZXKngN2qV7Y',
  [Market.PL]: 'ZXKngN2qV7Y',
  [Market.CZ]: 'ZXKngN2qV7Y',
  [Market.HU]: 'ZXKngN2qV7Y',
  [Market.IE]: 'ZXKngN2qV7Y',
  [Market.SK]: 'ZXKngN2qV7Y',
  [Market.HR]: 'ZXKngN2qV7Y',
  [Market.SI]: 'ZXKngN2qV7Y',
  [Market.PT]: 'ZXKngN2qV7Y',
};

export const DRIVE_YOUTUBE_URL: Record<
  Market,
  string | Partial<Record<Language, string>>
> = {
  [Market.US]: '5_k6M216j9g',
  [Market.GB]: '5_k6M216j9g',
  [Market.FR]: '5_k6M216j9g',
  [Market.BE]: '5_k6M216j9g',
  [Market.CH]: {
    [Language.FR]: '5_k6M216j9g',
    [Language.DE]: '5_k6M216j9g',
  },
  [Market.LU]: '5_k6M216j9g',
  [Market.DE]: '5_k6M216j9g',
  [Market.NL]: '5_k6M216j9g',
  [Market.CA]: {
    [Language.FR]: '5_k6M216j9g',
    [Language.EN]: '5_k6M216j9g',
  },
  [Market.AT]: '5_k6M216j9g',
  [Market.ES]: '5_k6M216j9g',
  [Market.IT]: '5_k6M216j9g',
  [Market.SE]: '5_k6M216j9g',
  [Market.DK]: '5_k6M216j9g',
  [Market.PL]: '5_k6M216j9g',
  [Market.CZ]: '5_k6M216j9g',
  [Market.HU]: '5_k6M216j9g',
  [Market.IE]: '5_k6M216j9g',
  [Market.SK]: '5_k6M216j9g',
  [Market.HR]: '5_k6M216j9g',
  [Market.SI]: '5_k6M216j9g',
  [Market.PT]: '5_k6M216j9g',
};

export const ACCOUNT_DRIVE_URL = `${ACCOUNT_URL}drive`;

export const CONFIRMATION_SUPPORT_URL =
  'https://support.shadow.tech/fr/categories/getting-started-with-shadow-pc-gaming/64e30ffcedbce432b4c449f3';
export const CONFIRMATION_DRIVE_SUPPORT_URL =
  'https://support.shadow.tech/fr/categories/getting-started-with-shadow-drive/64e31011edbce432b4c44c3b';

export const DISCORD_URLS: Record<Language, string> = {
  [Language.FR]: 'https://discord.com/invite/shadowfr',
  [Language.DE]: 'https://discord.com/invite/shadowde',
  [Language.NL]: 'https://discord.com/invite/shadow',
  [Language.EN]: 'https://discord.com/invite/shadow',
  [Language.ES]: 'https://discord.com/invite/shadow',
  [Language.IT]: 'https://discord.com/invite/shadow',
  [Language.SV]: 'https://discord.com/invite/shadow',
  [Language.DA]: 'https://discord.com/invite/shadow',
};

export const DISCORD_DRIVE_URLS: Record<Language, string> = {
  [Language.FR]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.DE]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.NL]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.EN]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.ES]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.IT]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.SV]: 'https://shdw.me/Shadow-Drive-Discord',
  [Language.DA]: 'https://shdw.me/Shadow-Drive-Discord',
};

export const COUPON_ERROR_CODES = [
  'coupon_not_applicable',
  'max_redemptions_reached',
  'coupon_expired',
];

export const PLACEHOLDER_DRIVE_CONFIRMATION_VIDEO_URLS: Record<
  Language,
  string
> = {
  [Language.FR]: `${IMAGE_PATH}confirmation-video-placeholder-drive-fr.jpg`,
  [Language.DE]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
  [Language.NL]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
  [Language.EN]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
  [Language.ES]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
  [Language.IT]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
  [Language.SV]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
  [Language.DA]: `${IMAGE_PATH}confirmation-video-placeholder-drive-en.jpg`,
};

export const ADVANCED_SETTINGS_LOCALE_PER_MARKET: Record<Market, KeyboardISO> =
  {
    [Market.US]: KeyboardISO.EN_US,
    [Market.GB]: KeyboardISO.EN_GB,
    [Market.FR]: KeyboardISO.FR_FR,
    [Market.DE]: KeyboardISO.DE_DE,
    [Market.NL]: KeyboardISO.EN_GB,
    [Market.BE]: KeyboardISO.EN_US,
    [Market.CH]: KeyboardISO.EN_US,
    [Market.LU]: KeyboardISO.EN_US,
    [Market.CA]: KeyboardISO.EN_US,
    [Market.AT]: KeyboardISO.DE_DE,
    [Market.ES]: KeyboardISO.ES_ES,
    [Market.IT]: KeyboardISO.IT_IT,
    [Market.SE]: KeyboardISO.EN_US,
    [Market.DK]: KeyboardISO.EN_US,
    [Market.PL]: KeyboardISO.EN_US,
    [Market.CZ]: KeyboardISO.EN_US,
    [Market.HU]: KeyboardISO.EN_US,
    [Market.IE]: KeyboardISO.EN_US,
    [Market.SK]: KeyboardISO.EN_US,
    [Market.HR]: KeyboardISO.EN_US,
    [Market.SI]: KeyboardISO.EN_US,
    [Market.PT]: KeyboardISO.EN_US,
  };

export const ADVANCED_SETTINGS_KEYBOARDS = {
  [KeyboardISO.EN_US]: 'QWERTY - English (US)',
  [KeyboardISO.EN_GB]: 'QWERTY - English (GB)',
  [KeyboardISO.FR_FR]: 'AZERTY - Français',
  [KeyboardISO.DE_DE]: 'QWERTZ - Deutsch',
  [KeyboardISO.IT_IT]: 'QWERTY - Italian',
  [KeyboardISO.ES_ES]: 'QWERTY - Español',
};

export const ADVANCED_SETTINGS_KEYBOARD_CONFIGURATIONS = (
  Object.keys(ADVANCED_SETTINGS_KEYBOARDS) as KeyboardISO[]
).map(key => ({ value: key, label: ADVANCED_SETTINGS_KEYBOARDS[key] }));

export const ADVANCED_SETTINGS_LANGUAGES = {
  'en-US': 'English (US)',
  'en-GB': 'English (GB)',
  'fr-FR': 'Français',
  'de-DE': 'Deutsch',
  'es-ES': 'Español',
  'it-IT': 'Italiano',
};

export const ADVANCED_SETTINGS_LANGUAGES_OPTIONS = (
  Object.keys(
    ADVANCED_SETTINGS_LANGUAGES,
  ) as (keyof typeof ADVANCED_SETTINGS_LANGUAGES)[]
).map(key => ({ value: key, label: ADVANCED_SETTINGS_LANGUAGES[key] }));

export const CHARGEBEE_NA_CONFIGURATION: IChargebeeConfiguration = {
  site: import.meta.env.VITE_PUBLIC_CHARGEBEE_SITE_US,
  publishableKey: import.meta.env.VITE_PUBLIC_CHARGEBEE_KEY_US,
};

export const CHARGEBEE_EU_CONFIGURATION: IChargebeeConfiguration = {
  site: import.meta.env.VITE_PUBLIC_CHARGEBEE_SITE_EU,
  publishableKey: import.meta.env.VITE_PUBLIC_CHARGEBEE_KEY_EU,
};

export const CHARGEBEE_B2B_EU_CONFIGURATION: IChargebeeConfiguration = {
  site: import.meta.env.VITE_PUBLIC_CHARGEBEE_B2B_SITE_EU,
  publishableKey: import.meta.env.VITE_PUBLIC_CHARGEBEE_B2B_KEY_EU,
};

export const CHARGEBEE_CONFIGURATION_PER_MARKET: Record<
  Market,
  IChargebeeConfiguration
> = {
  [Market.US]: CHARGEBEE_NA_CONFIGURATION,
  [Market.GB]: CHARGEBEE_EU_CONFIGURATION,
  [Market.FR]: CHARGEBEE_EU_CONFIGURATION,
  [Market.BE]: CHARGEBEE_EU_CONFIGURATION,
  [Market.CH]: CHARGEBEE_EU_CONFIGURATION,
  [Market.LU]: CHARGEBEE_EU_CONFIGURATION,
  [Market.DE]: CHARGEBEE_EU_CONFIGURATION,
  [Market.NL]: CHARGEBEE_EU_CONFIGURATION,
  [Market.CA]: CHARGEBEE_NA_CONFIGURATION,
  [Market.AT]: CHARGEBEE_EU_CONFIGURATION,
  [Market.ES]: CHARGEBEE_EU_CONFIGURATION,
  [Market.IT]: CHARGEBEE_EU_CONFIGURATION,
  [Market.SE]: CHARGEBEE_EU_CONFIGURATION,
  [Market.DK]: CHARGEBEE_EU_CONFIGURATION,
  [Market.PL]: CHARGEBEE_EU_CONFIGURATION,
  [Market.CZ]: CHARGEBEE_EU_CONFIGURATION,
  [Market.HU]: CHARGEBEE_EU_CONFIGURATION,
  [Market.IE]: CHARGEBEE_EU_CONFIGURATION,
  [Market.SK]: CHARGEBEE_EU_CONFIGURATION,
  [Market.HR]: CHARGEBEE_EU_CONFIGURATION,
  [Market.SI]: CHARGEBEE_EU_CONFIGURATION,
  [Market.PT]: CHARGEBEE_EU_CONFIGURATION,
};

export const CHARGEBEE_CONFIGURATION_B2B_PER_MARKET: Record<
  Market,
  IChargebeeConfiguration
> = {
  [Market.US]: CHARGEBEE_NA_CONFIGURATION,
  [Market.GB]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.FR]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.BE]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.CH]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.LU]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.DE]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.NL]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.CA]: CHARGEBEE_NA_CONFIGURATION,
  [Market.AT]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.ES]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.IT]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.SE]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.DK]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.PL]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.CZ]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.HU]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.IE]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.SK]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.HR]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.SI]: CHARGEBEE_B2B_EU_CONFIGURATION,
  [Market.PT]: CHARGEBEE_B2B_EU_CONFIGURATION,
};

export const ZIPCODE_PER_MARKET: Record<Market, string> = {
  at: '1010',
  be: '1000',
  ca: 'K0A',
  ch: '3001',
  de: '10115',
  dk: '1050',
  es: '28022',
  fr: '75001',
  gb: 'SW1A 2WH',
  it: '00042',
  lu: 'L-1111',
  nl: '1034',
  us: '10001',
  se: '103 16',
  pl: '00-001',
  cz: '100 00',
  hu: '1007',
  ie: 'D01',
  sk: '811 02',
  hr: '10000',
  si: '1000',
  pt: '1000',
};

export const TOP_BANNER_COOKIE_NAME = 'top_banner_disabled';
