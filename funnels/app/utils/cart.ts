import {
  ICatalog,
  IAnyOffer,
  IPlan,
  ProductType,
  IEstimation,
  B2cProductTarget,
  AddonType,
  ISubscription,
} from 'types';
import {
  computeOfferPriceAlignedWithPeriod,
  getOfferWithDifferentialPricing,
  getChargesOffers,
  getMandatoryPrimaryStorageAddon,
  getProductFromId,
  getOffer,
  getProductFromOfferId,
  getOfferFromId,
} from 'utils';

import { OfferInCart } from '~/types/cart';
import { isUpdateCharge } from '~/utils/changePlan';

export const getOfferInCart = (
  offer: IAnyOffer,
  forPlanOfferId: string = offer.id,
  quantity = 1,
): OfferInCart => {
  return {
    id: offer.id,
    quantity,
    offerId: forPlanOfferId,
    name: offer.name,
    price: offer.price,
    productId: offer.product_id,
    productFamilyId: offer.itemFamilyId,
    type: offer.item_type,
  };
};

export const getOffersInCartToAddFromPlanOffer = (
  catalog: ICatalog,
  planOffer: IAnyOffer,
  withCharges = true,
): OfferInCart[] => {
  const planOfferInCart = getOfferInCart(planOffer);
  const plan = getProductFromId(catalog, planOfferInCart.productId ?? '');

  if (!plan) {
    return [];
  }

  // Include mandatory primary storage addon
  const addonOfferInCart: OfferInCart[] = [];
  const addonInfo = getMandatoryPrimaryStorageAddon(catalog, plan);
  if (addonInfo) {
    const addonOffer = getOffer(catalog, addonInfo.addon.id, offer => {
      return offer.periodicity === planOffer.periodicity;
    });
    if (addonOffer) {
      addonOfferInCart.push(
        getOfferInCart(addonOffer, planOffer.id, addonInfo.quantity),
      );
    }
  }

  // Only include charges cart items if we want it
  const chargesOffersInCart = withCharges
    ? (
        getChargesOffers(
          catalog,
          planOffer.product_id,
          planOffer.periodicity,
          // Only retrieve accessFees charges
          charge => charge.meta_data?.type === 'accessFees',
        ) ?? []
      ).map(offer => getOfferInCart(offer, planOffer.id, 1))
    : [];

  return [planOfferInCart, ...addonOfferInCart, ...chargesOffersInCart];
};

/**
 * Extracts addon items from a subscription and converts them to cart format
 * for the specified target plan offer. Used in the change plan funnel to get
 * the addons from the current subscription to be added to the cart.
 *
 * @param catalog - The catalog containing all offers
 * @param subscription - The current subscription containing items
 * @param targetPlanOfferId - The ID of the plan offer these addons will be associated with
 * @returns Array of addon offers formatted for cart
 */
export const getAddonsFromSubscriptionAsCartItems = (
  catalog: ICatalog,
  subscription: ISubscription,
  targetPlanOfferId: string,
): OfferInCart[] => {
  return subscription.items
    .map(item => {
      const isAddon = item.item_type === ProductType.ADDON;
      const offer = getOfferFromId(catalog, item.id);
      if (isAddon && offer) {
        return getOfferInCart(offer, targetPlanOfferId, item.quantity);
      }
      return undefined;
    })
    .filter((item): item is OfferInCart => item !== undefined);
};

export const getPlanOfferFromCart = (
  catalog: ICatalog | undefined,
  offersInCart: OfferInCart[],
  predicate?: (plan: IAnyOffer) => boolean,
) => {
  if (!catalog) {
    return undefined;
  }

  for (const offerInCart of offersInCart) {
    const offer = catalog.offers.byId[offerInCart.id];
    if (
      offer &&
      offer.item_type === ProductType.PLAN &&
      (!predicate || predicate(offer))
    ) {
      return offer;
    }
  }
  return undefined;
};

export const getIsCartTotalZero = (offersInCart: OfferInCart[]) => {
  const offersInCartTotals = offersInCart.reduce((cartSum, offerInCart) => {
    return cartSum + offerInCart.price * offerInCart.quantity;
  }, 0);

  return offersInCartTotals === 0;
};

export const getCartTotalAmount = (
  catalog: ICatalog | undefined,
  vmsCount: number,
  offersInCart: OfferInCart[],
  estimation?: IEstimation,
) => {
  const { plan, planOffer } = getPlanAndPlanOfferFromOffersInCart(
    catalog,
    offersInCart,
  );

  if (!planOffer || !plan) {
    return 0;
  }

  return offersInCart.reduce((cartSum, offerInCart) => {
    const offer = catalog?.offers.byId[offerInCart.id];
    if (!offer) {
      // TODO: remove this once the charges are available in the catalog (used for "cloudpc_update_charge")
      if (isUpdateCharge(offerInCart)) {
        return cartSum + offerInCart.price;
      }
      return cartSum;
    }
    let offerAmount: number;

    const offerFromFutureEstimate =
      estimation?.future_estimate?.next_invoice_estimate?.line_items?.find(
        lineItem => lineItem.entity_id === offerInCart.id,
      );

    if (offerFromFutureEstimate) {
      offerAmount = offerFromFutureEstimate.amount / 100;
    } else {
      const offerAlignedPrice = getOfferInCartAlignedPrice(
        catalog,
        vmsCount,
        offerInCart,
        offer,
        planOffer,
        plan as IPlan,
      );
      offerAmount = offerAlignedPrice;
    }
    return cartSum + offerAmount;
  }, 0);
};

export const getOfferInCartAlignedPrice = (
  catalog: ICatalog | undefined,
  vmsCount: number,
  offerInCart: OfferInCart,
  offer: IAnyOffer,
  planOffer: IAnyOffer,
  plan: IPlan,
) => {
  // TODO : rework all differential pricing logic to get a cleaner version
  // (get separated logic between Addons and Charges as they have different structure from chargebee)
  if (offerInCart.type === ProductType.CHARGE) {
    return offerInCart.price * offerInCart.quantity;
  }

  const offerWithDifferentialPricing = getOfferWithDifferentialPricing(
    plan as IPlan,
    offer,
  );

  // Offers in cart can now have a periodicity which is not the same than the offer
  // of the plan. To display prices on the cart summary the prices are aligned with
  // the period of the plan.
  // ie: addon 1 month for plan 6 months -> addon price display for 6 months
  const offerAlignedUnitPrice =
    offer.period && offer.period_unit
      ? computeOfferPriceAlignedWithPeriod(
          offerWithDifferentialPricing,
          planOffer.period,
          planOffer.period_unit,
        )
      : offerWithDifferentialPricing.price;

  const isStorageAddon =
    getProductFromOfferId(catalog, offerInCart.id)?.meta_data?.type ===
    AddonType.STORAGE;

  const offerAlignedPrice =
    offerAlignedUnitPrice *
    // big error with linter and autoformat here
    // eslint-disable-next-line prettier/prettier
    (isStorageAddon ? offerInCart.quantity * (vmsCount ?? 1) : (vmsCount ?? 1));

  return offerAlignedPrice;
};

export const getPlanAndPlanOfferFromOffersInCart = (
  catalog: ICatalog | undefined,
  offersInCart: OfferInCart[],
) => {
  const planOfferInCart = offersInCart.find(
    offerInCart => offerInCart.type === ProductType.PLAN,
  );

  const chargeOfferInCart = offersInCart.find(
    offerInCart => offerInCart.type === ProductType.CHARGE,
  );
  const planOffer = Object.values(catalog?.offers.byId ?? {}).find(offer => {
    return offer.id === planOfferInCart?.id;
  });

  const plan = catalog?.products.byId[planOffer?.product_id ?? ''];

  return { planOffer, plan, chargeOfferInCart };
};

export const hasPlanWithMatchingTargetInCart = (
  catalog: ICatalog | undefined,
  offersInCart: OfferInCart[],
  productTarget: B2cProductTarget,
) => {
  const planOfferInCart = getPlanOfferFromCart(catalog, offersInCart);
  const planProductInCart =
    catalog?.products.byId[planOfferInCart?.product_id as string];
  const isMatchingTargetProductInCart =
    planProductInCart?.meta_data?.target === productTarget;

  return isMatchingTargetProductInCart;
};
