import { ChargebeeInstance } from '@chargebee/chargebee-js-types';
import { Market } from 'types';
import { logError } from 'utils';

import { CHARGEBEE_CONFIGURATION_PER_MARKET } from '~/utils/constants';

const chargebeeInstanceExist = () => {
  try {
    return !!window.Chargebee.getInstance();
  } catch (error) {
    return !error;
  }
};

export function getChargebeeConfig(market: Market) {
  const { site, publishableKey } = CHARGEBEE_CONFIGURATION_PER_MARKET[market];

  return {
    site,
    publishableKey,
  };
}

export const getChargebeeInstance = (
  market: Market,
): ChargebeeInstance | null => {
  if (chargebeeInstanceExist()) {
    return window.Chargebee.getInstance();
  }
  try {
    const { site, publishableKey } = getChargebeeConfig(market);

    return window.Chargebee.init({
      site,
      publishableKey,
    });
  } catch (error) {
    logError(error as string);
    return null;
  }
};
