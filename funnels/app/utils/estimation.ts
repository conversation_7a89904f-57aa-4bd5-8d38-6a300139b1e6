import { IAttachedCoupon, DiscountType, IEstimation } from 'types';

// TODO: the handling of coupons with apply_on === CouponApplyOn.EACH_SPECIFIED_ITEM is not perfect in the following function.
// We should match the applicable items in the invoice and in the coupon meta data and compute the results.
// A better solution would be to get the result directly from chargebee...
export const computeAmountWithDiscount = (
  amountInCents: number,
  currentAttachedCoupon: IAttachedCoupon,
) => {
  const { discount_type, discount_percentage, discount_amount } =
    currentAttachedCoupon;

  let discountedAmountInCents = amountInCents;

  switch (discount_type) {
    case DiscountType.FIXED_AMOUNT:
      discountedAmountInCents = amountInCents - discount_amount;
      break;
    case DiscountType.PERCENTAGE:
      discountedAmountInCents =
        amountInCents - (amountInCents * discount_percentage) / 100;
      break;
  }

  return discountedAmountInCents;
};

/**
 *
 * @param estimation - IEstimation
 * @param coupon - string
 * @returns IAttachedCoupon
 */

export const getCurrentCouponFromEstimation = (
  estimation: IEstimation | undefined,
  coupon: string | undefined,
) => {
  const currentAttachedCoupon = estimation?.attached_coupons?.find(
    discount =>
      discount.id?.toUpperCase() === coupon?.toUpperCase() ||
      discount.id?.toUpperCase().startsWith('REFPUB_'), // Referral code
  );

  return currentAttachedCoupon;
};

/**
 *
 * @param estimation - IEstimation
 * @param coupon - string
 * @returns the discount amount in the invoice of a given coupon.
 * If the coupon does not match, but we have a `REFPUB_` discount in the invoice,
 * we then assume the given coupon is a referral coupon, and return the `REFPUB_` discount value.
 */

// We can have several discounts on an invoice, but we only allow one that the user can enter.
// This function allows to display the value of a discount code entered and validated by the user.
// We can have 2 cases:
// 1) if it's a "regular" discount code, we will have a correspondence between the code entered by the user,
// and the ID of the discount in the invoice.
// 2) if it's a "referral" promo code, the code entered by the user and the one in the invoice will be different.
// The ID of the discount in the invoice will start with REFPUB_, whereas the coupon entered by the user will not.
export const getDiscountValue = (
  estimation: IEstimation | undefined,
  coupon: string | undefined,
) => {
  const currentDiscount = estimation?.invoice_estimate?.discounts?.find(
    discount =>
      // when entity_type: "promotional_credits", entity_id is null.
      discount.entity_id?.toUpperCase() === coupon?.toUpperCase() ||
      discount.entity_id?.toUpperCase().startsWith('REFPUB_'), // Referral code
  );

  return currentDiscount ? currentDiscount.amount / 100 : 0;
};
