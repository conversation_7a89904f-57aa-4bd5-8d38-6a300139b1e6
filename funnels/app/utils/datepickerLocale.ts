import deDE from 'antd/locale/de_DE';
import enGB from 'antd/locale/en_GB';
import enUS from 'antd/locale/en_US';
import esES from 'antd/locale/es_ES';
import frFR from 'antd/locale/fr_FR';
import itIT from 'antd/locale/it_IT';

export const setDatepickerLocale = (locale: string) => {
  switch (locale.split('-')[0]) {
    case 'fr':
      return setDatepickerLocaleFR();
    case 'de':
      return deDE;
    case 'es':
      return esES;
    case 'it':
      return setDatepickerLocaleIT();
    default:
      if (locale.split('-')[1] === 'GB') {
        return enGB;
      } else {
        return enUS;
      }
  }
};

export const setDatepickerLocaleFR = () => {
  if (!frFR.DatePicker) {
    return frFR;
  }

  return {
    ...frFR,
    DatePicker: {
      ...frFR.DatePicker,
      lang: {
        ...frFR.DatePicker.lang,
        shortWeekDays: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
        shortMonths: [
          'Jan',
          'Fév',
          'Mar',
          'Avr',
          'Mai',
          'Juin',
          'Juil',
          'Août',
          'Sep',
          'Oct',
          'Nov',
          'Déc',
        ],
      },
    },
  };
};

export const setDatepickerLocaleIT = () => {
  if (!itIT.DatePicker) {
    return itIT;
  }

  return {
    ...itIT,
    DatePicker: {
      ...itIT.DatePicker,
      lang: {
        ...itIT.DatePicker.lang,
        shortWeekDays: ['Dom', 'Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab'],
        shortMonths: [
          'Gen',
          'Feb',
          'Mar',
          'Apr',
          'Mag',
          'Giu',
          'Lug',
          'Ago',
          'Set',
          'Ott',
          'Nov',
          'Dic',
        ],
      },
    },
  };
};
