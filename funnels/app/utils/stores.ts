import { produce, Draft } from 'immer';
import { areWeTestingWithJest } from 'utils';
import create, {
  State,
  StateCreator,
  SetState,
  GetState,
  StoreApi,
} from 'zustand';
import { persist, devtools, StoreApiWithPersist } from 'zustand/middleware';

const withImmer =
  <T extends State, CustomSetState extends SetState<T>>(
    // eslint-disable-next-line no-unused-vars
    config: StateCreator<T, (fn: (draft: Draft<T>) => void) => void>,
  ): StateCreator<T, CustomSetState, GetState<T>, StoreApi<T>> =>
  (set, get, api) =>
    config(fn => set(produce<T>(fn)), get, api);

const withPersist = <
  T extends State,
  CustomSetState extends SetState<T>,
  CustomStoreApi extends StoreApi<T>,
>(
  config: StateCreator<T>,
  name: string,
) =>
  persist<T, CustomSetState, GetState<T>, CustomStoreApi>(config, {
    name,
  });

// TODO: devtools triggers a warning when testing with jest.
// We disable it when running tests, but this areWeTestingWithJest() condition can be removed
// when we upgrade zustand to v4+, because the "enabled" option was added in v4.0.0:
// https://github.com/pmndrs/zustand/pull/880
const withDevTools = <
  T extends State,
  CustomSetState extends SetState<T>,
  CustomStoreApi extends StoreApi<T>,
>(
  config: StateCreator<T>,
) =>
  areWeTestingWithJest()
    ? config
    : devtools<T, CustomSetState, GetState<T>, CustomStoreApi>(config, {
        name: 'shadow-account',
      });

export const createStore = <T extends State>(
  // eslint-disable-next-line no-unused-vars
  stateCreator: StateCreator<T, (fn: (draft: Draft<T>) => void) => void>,
) => {
  return create(withDevTools(withImmer(stateCreator)));
};

export const createPersistentStore = <T extends State>(
  // eslint-disable-next-line no-unused-vars
  stateCreator: StateCreator<T, (fn: (draft: Draft<T>) => void) => void>,
  name: string,
) => {
  return create<T, SetState<T>, GetState<T>, StoreApiWithPersist<T>>(
    withPersist(withDevTools(withImmer(stateCreator)), name),
  );
};
