/**
 * Utility functions for handling hydration issues
 */

/**
 * Check if we're in a browser environment
 */
export const isBrowser = typeof window !== 'undefined';

/**
 * Check if the DOM is ready for hydration
 */
export const isDOMReady = () => {
  if (!isBrowser) {
    return false;
  }
  return (
    document.readyState === 'complete' || document.readyState === 'interactive'
  );
};

/**
 * Wait for DOM to be ready
 */
export const waitForDOM = (): Promise<void> => {
  return new Promise(resolve => {
    if (isDOMReady()) {
      resolve();
      return;
    }

    const handleReady = () => {
      if (isDOMReady()) {
        document.removeEventListener('DOMContentLoaded', handleReady);
        document.removeEventListener('readystatechange', handleReady);
        resolve();
      }
    };

    document.addEventListener('DOMContentLoaded', handleReady);
    document.addEventListener('readystatechange', handleReady);
  });
};

/**
 * Check if hydration is likely to succeed by comparing basic DOM structure
 */
export const canHydrate = (): boolean => {
  if (!isBrowser) {
    return false;
  }

  try {
    // Check if the root element exists and has content
    const root = document.querySelector('body');
    return !!(root && root.children.length > 0);
  } catch {
    return false;
  }
};
