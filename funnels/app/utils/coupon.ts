import { ICatalog } from 'types';
import { logError } from 'utils';

import { OfferInCart } from '~/types/cart';

/**
 * Automatically check and apply a referral coupon or a promo coupon from plan metadata, when state is viewingPaymentMethods.
 *
 * @param planOfferInCart OfferInCart with type === 'plan'
 * @param catalog catalog
 * @returns an arbitrarily taken first coupon from meta_data, or undefined if none is present.
 */
export const getCouponsFromPlanOfferInCartMetadata = (
  planOfferInCart: OfferInCart,
  catalog?: ICatalog,
) => {
  const couponsFromProductMetaData =
    catalog?.products.byId[planOfferInCart.productId]?.meta_data
      ?.coupons_to_apply;

  if (!couponsFromProductMetaData?.[0]) {
    return undefined;
  }

  // Currently the frontend only accepts one coupon. We'll log an error to Sentry if the back sends more, because at this stage the need hasn't been expressed.
  const arbitrarilyTakenCouponToApply = couponsFromProductMetaData[0];

  if (couponsFromProductMetaData.length > 1) {
    logError(
      `There are multiple coupons in ${planOfferInCart.productId} metadata. Frontend currently handles only one coupon and ignores the others. If we want to accept more coupons we need to update the code.`,
    );
  }

  return arbitrarilyTakenCouponToApply.id;
};
