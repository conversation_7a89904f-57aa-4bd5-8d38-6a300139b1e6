import {
  CouponType,
  ICatalog,
  IAnyOffer,
  IPlanParameter,
  IPlan,
  ProductType,
  IAddon,
  OfferPeriodUnit,
  OfferPeriodicity,
  IApiProduct,
  IAnyProduct,
  ProductFamilyId,
} from 'types';
import { FORM_VALIDATION_REGEX, isOfferEnabledForSale } from 'utils';

export const isPowerCoupon = (coupon: string | undefined) => {
  const powerRegex = new RegExp(FORM_VALIDATION_REGEX[CouponType.POWER], 'g');
  return !!coupon && powerRegex.test(coupon);
};

export const getPeriodicities = ({
  catalog,
  plans,
  excludePeriodUnit,
}: {
  catalog: ICatalog | undefined;
  plans: IPlan[] | undefined;
  excludePeriodUnit?: OfferPeriodUnit;
}) => {
  if (!catalog || !plans) {
    return [];
  }

  const periodicitiesSet = plans.reduce((periodicities, plan) => {
    const planPeriodicities = plan.offers.map(offerId => {
      const offer = catalog.offers.byId[offerId];
      if (!offer || !isOfferEnabledForSale({ offer, withCatalogStock: true })) {
        return;
      }
      if (excludePeriodUnit && offer.period_unit === excludePeriodUnit) {
        return;
      }
      return offer.periodicity;
    });

    for (const periodicity of planPeriodicities) {
      if (periodicity) {
        periodicities.add(periodicity);
      }
    }

    return periodicities;
  }, new Set<OfferPeriodicity>());

  return [...periodicitiesSet];
};

export const getInfoFromPeriodicity = (
  periodicity: OfferPeriodicity,
): [number, OfferPeriodUnit] => {
  const [period, periodUnit] = periodicity.split('-');

  if (
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    !Object.values(OfferPeriodUnit).includes(periodUnit! as OfferPeriodUnit)
  ) {
    throw new Error('try parsing wrong periodicity format');
  }

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return [parseInt(period!, 10), periodUnit as OfferPeriodUnit];
};

export const getPlanAddonParameter = (
  catalog: ICatalog,
  plan: IPlan,
  predicate: (
    planParameter: IPlanParameter,
    parameterProduct: IAddon,
  ) => boolean,
) => {
  return plan.addons_and_charges?.find(planParameter => {
    const parameterProduct = catalog.products.byId[planParameter.id];
    return (
      parameterProduct &&
      planParameter.item_type === ProductType.ADDON &&
      predicate(planParameter, parameterProduct as IAddon)
    );
  });
};

export const getPlanAddonsCatalogOffers = (
  plan: IPlan,
  catalog: ICatalog,
  periodicity: OfferPeriodUnit,
) => {
  const catalogOffers = Object.values(catalog.offers.byId);
  return plan.addons_and_charges.reduce((addonsOffers, parameter) => {
    if (parameter.item_type !== ProductType.ADDON) {
      return addonsOffers;
    }
    const parameterOffers = catalogOffers.filter(
      offer => offer.product_id === parameter.id,
    );
    const offerWithCorrectPeriodicity = parameterOffers.find(
      offer => offer.period_unit === periodicity,
    );
    if (!offerWithCorrectPeriodicity) {
      return addonsOffers;
    }

    addonsOffers.push(offerWithCorrectPeriodicity);

    return addonsOffers;
  }, [] as IAnyOffer[]);
};

export const getAddonAssociationTypeForPlan = (
  catalog: ICatalog,
  plan: IPlan,
  addon: IAnyOffer | IAddon,
  predicate: (addonParameterProduct: IAddon) => boolean,
) => {
  const addonProductId = 'product_id' in addon ? addon.product_id : addon.id;
  const addonParameter = getPlanAddonParameter(catalog, plan, (_, product) => {
    return product.id === addonProductId && predicate(product);
  });
  return addonParameter?.type;
};

// Format the catalog from API result
export const formatCatalog = (apiProductList: IApiProduct[]): ICatalog => {
  const familyIds = Object.values(ProductFamilyId);
  const productTypes = Object.values(ProductType);
  return {
    products: {
      byId: apiProductList.reduce<Record<string, IAnyProduct>>(
        (acc, apiProduct) => {
          const hasValidFamilyId = (familyIds as string[]).includes(
            apiProduct.item_family_id,
          );
          const hasValidProductType = (productTypes as string[]).includes(
            apiProduct.type,
          );

          if (!hasValidFamilyId || !hasValidProductType) {
            return acc;
          }

          acc[apiProduct.id] = {
            ...apiProduct,
            offers: apiProduct.offers?.map(offer => offer.id),
          } as IAnyProduct;

          return acc;
        },
        {},
      ),

      allIds: apiProductList.map(product => product.id),
    },
    offers: {
      byId: apiProductList.reduce<Record<string, IAnyOffer>>(
        (acc, productRaw) => {
          productRaw.offers?.forEach(offer => {
            acc[offer.id] = {
              ...offer,
              itemFamilyId: productRaw.item_family_id as ProductFamilyId,
              periodicity: `${offer.period}-${offer.period_unit}`,
            };
          });
          return acc;
        },
        {},
      ),

      allIds: apiProductList.reduce<Array<string>>((acc, product) => {
        product.offers?.forEach(offer => {
          acc.push(offer.id);
        });
        return acc;
      }, []),
    },
  };
};

export const mergeCatalogs = (...catalogs: ICatalog[]): ICatalog => {
  return {
    products: {
      byId: Object.assign(
        {},
        ...catalogs.map(catalog => catalog.products.byId),
      ),
      allIds: [
        ...new Set(catalogs.flatMap(catalog => catalog.products.allIds)),
      ],
    },
    offers: {
      byId: Object.assign({}, ...catalogs.map(catalog => catalog.offers.byId)),
      allIds: [...new Set(catalogs.flatMap(catalog => catalog.offers.allIds))],
    },
  };
};
