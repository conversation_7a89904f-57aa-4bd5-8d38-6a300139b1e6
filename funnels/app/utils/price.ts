import { Currency } from 'types';

import { LOCALE_PER_CURRENCY } from '~/utils/constants';

export const formatPrice = (
  amount: number,
  currency: Currency | Uppercase<Currency>,
  minimumFractionDigits = 2,
): string => {
  const locale = LOCALE_PER_CURRENCY[currency.toLowerCase() as Currency];

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits,
  }).format(amount);
};
