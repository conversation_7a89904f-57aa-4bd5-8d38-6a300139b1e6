import {
  ICatalog,
  ISubscriptionItem,
  ISubscription,
  DriveProduct,
  PowerProduct,
  OldProduct,
  ProductFamilyId,
} from 'types';
import { DRIVE_OFFER_REGEX, findSubscription } from 'utils';

import { OfferInCart } from '~/types/cart';

export const getSubscriptionIdToModify = (
  subscriptions: ISubscription[] | undefined,
  queryProduct: string,
) => {
  let subscriptionToModify: ISubscription | undefined;

  switch (queryProduct) {
    case DriveProduct.DRIVE_PREMIUM:
      subscriptionToModify = subscriptions?.find(subscription =>
        subscription.items.find(subscriptionItem =>
          subscriptionItem.id.match(DRIVE_OFFER_REGEX),
        ),
      );
      break;
    // For an update, we need to get the existing cloudPC subscription_id to modify the subscription
    case PowerProduct.POWER:
    case PowerProduct.POWER_EARLY_ACCESS:
    case PowerProduct.POWER_PLUS:
    case OldProduct.INFINITE:
    case OldProduct.ULTRA:
      subscriptionToModify = findSubscription(
        subscriptions,
        ProductFamilyId.CLOUDPC,
      );
      break;
  }

  if (subscriptionToModify) {
    return subscriptionToModify.id;
  }

  return '';
};

export const computeSubscriptionItemPricesFromOffersInCart = (
  catalog: ICatalog | undefined,
  offersInCart: OfferInCart[],
) => {
  const itemPricesPayload = offersInCart
    .filter(offerInCart => {
      return offerInCart.quantity > 0;
    })
    .map(offerInCart => {
      return {
        id: offerInCart.id,
        quantity: offerInCart.quantity,
        item_type: offerInCart.type,
      };
    }) as ISubscriptionItem[];

  return itemPricesPayload;
};
