/**
 * Appends query parameters to a URL.
 * @param baseUrl - The base URL to append the query parameters to.
 * @param queryParams - The query parameters to append to the URL.
 * @returns The URL with the appended query parameters.
 */

export const appendQueryParamsToUrl = (
  baseUrl: URL,
  queryParams: Record<string, string | string[] | undefined>,
) => {
  const url = new URL(baseUrl);

  Object.entries(queryParams).forEach(([key, value]) => {
    if (value) {
      if (Array.isArray(value)) {
        value.forEach(val => url.searchParams.append(key, val));
      } else {
        url.searchParams.append(key, value.toString());
      }
    }
  });

  return url;
};
