import { UserRole } from 'types';
/**
 * Interface representing rules and configurations for virtual machines (VMs).
 */
export interface IVmRules {
  vmDrawer: IVmDrawer;
  vmConfiguration: IVmConfiguration;
  vmTagManager: boolean;
  vmMemberAssign: boolean;
  canCreateVm: boolean;
  canSearchVm: boolean;
  pushNewOffer: boolean;
  promoBanner: boolean;
  promoAlert: boolean;
  scheduleChange: boolean;
  canCloneVm: boolean;
  canResetVm: boolean;
  canDeleteVm: boolean;
  canReactivateVm: boolean;
}

export interface IVmDrawer {
  drawer: boolean;
  footer: boolean;
  title: boolean;
}

export interface IVmConfiguration {
  VmConfigurationPlan: boolean;
  VmConfigurationStorage: boolean;
  VmConfigurationDatacenter: boolean;
  VmConfigurationAlwaysOn: boolean;
  VmConfigurationPeriodicity: boolean;
}

/**
 * Interface representing navigation rules and user drawer settings.
 */
export interface IUseNavigationRules {
  useNavigation: {
    shadowPC: boolean;
    shadowPCUsers: boolean;
    shadowDrive: boolean;
    account: boolean;
    accountSecurity: boolean;
    billing: boolean;
    billingDetails: boolean;
    billingMethod: boolean;
    billingInvoices: boolean;
    gameStore: boolean;
    support: boolean;
    downloads: boolean;
  };
  userDrawer: {
    drawer: boolean;
    footer: boolean;
    tagManager: boolean;
  };
}

/**
 * Interface representing rules for managing user roles and permissions.
 */
export interface IRules {
  setRole: {
    [key in UserRole]: boolean;
  };
  canBecome: {
    [key in UserRole]: boolean;
  };
  canDelete: {
    [key in UserRole]: boolean;
  };
  canSetRoles: boolean;
  canBeDeleted: boolean;
  canResendInvitation: boolean;
  canSearchUsers: boolean;
  canInviteUsers: boolean;
}
