/**
 * Do not edit directly
 * Generated on Thu May 12 2022 14:20:09 GMT+0000 (Coordinated Universal Time)
 */

export default {
  'heading-h1': {
    fontSize: '80px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -2.4,
    lineHeight: '80px',
  },
  'heading-h2': {
    fontSize: '64px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -1.92,
    lineHeight: '64px',
  },
  'heading-h3': {
    fontSize: '48px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -1.44,
    lineHeight: '48px',
  },
  'heading-h4': {
    fontSize: '32px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -0.96,
    lineHeight: '32px',
  },
  'heading-h4-light': {
    fontSize: '32px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -0.96,
    lineHeight: '32px',
  },
  'heading-h5': {
    fontSize: '24px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -0.72,
    lineHeight: '24px',
  },
  'heading-h6': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: -0.6,
    lineHeight: '20px',
  },
  'body-xs': {
    fontSize: '12px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'body-xs-regular': {
    fontSize: '12px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'body-xs-italic': {
    fontSize: '12px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'italic',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'body-xs-link': {
    fontSize: '12px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'body-xs-regular-link': {
    fontSize: '12px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'body-sm': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '21px',
  },
  'body-sm-light': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 200,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '21px',
  },
  'body-sm-link': {
    fontSize: '14px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '21px',
  },
  'body-sm-regular': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '21px',
  },
  'body-sm-regular-link': {
    fontSize: '14px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '21px',
  },
  'body-md': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '24px',
  },
  'body-md-link': {
    fontSize: '16px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '24px',
  },
  'body-md-light-italic': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'italic',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '24px',
  },
  'body-md-regular': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '24px',
  },
  'body-md-regular-link': {
    fontSize: '16px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '24px',
  },
  'body-lg': {
    fontSize: '18px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '27px',
  },
  'body-lg-regular': {
    fontSize: '18px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '27px',
  },
  'body-xl': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '30px',
  },
  'body-xl-regular': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '30px',
  },
  'label-xs': {
    fontSize: '12px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '12px',
  },
  'label-xs-regular': {
    fontSize: '12px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '12px',
  },
  'label-xs-light': {
    fontSize: '12px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 200,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '12px',
  },
  'label-sm': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
  },
  'label-sm-link': {
    fontSize: '14px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
  },
  'label-sm-light': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 200,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
  },
  'label-sm-regular': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
  },
  'label-sm-regular-underline': {
    fontSize: '14px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
  },
  'label-sm-bold': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 700,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
  },
  'label-sm-caps': {
    fontSize: '14px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 700,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '14px',
    textTransform: 'uppercase',
  },
  'label-md': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '16px',
  },
  'label-md-light': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 200,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '16px',
  },
  'label-md-regular': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '16px',
  },
  'label-md-regular-link': {
    fontSize: '16px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '16px',
  },
  'label-md-bold': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 700,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '16px',
  },
  'label-md-caps': {
    fontSize: '16px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 700,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '16px',
    textTransform: 'uppercase',
  },
  'label-lg': {
    fontSize: '18px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'label-lg-regular': {
    fontSize: '18px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'label-lg-light': {
    fontSize: '18px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 200,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'label-xl': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 300,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '20px',
  },
  'label-xl-light': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 200,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '20px',
  },
  'label-xl-regular': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '20px',
  },
  'label-xl-caps': {
    fontSize: '20px',
    textDecoration: 'none',
    fontFamily: 'Nexa Text',
    fontWeight: 700,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '20px',
    textTransform: 'uppercase',
  },
  'label-lg-link': {
    fontSize: '18px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '18px',
  },
  'label-xs-link': {
    fontSize: '12px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '12px',
  },
  'label-xl-link': {
    fontSize: '20px',
    textDecoration: 'underline',
    fontFamily: 'Nexa Text',
    fontWeight: 400,
    fontStyle: 'normal',
    fontStretch: 'normal',
    letterSpacing: 0,
    lineHeight: '20px',
  },
};
