{"name": "hooks", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest"}, "peerDependencies": {"next-i18next": "^10.5.0", "notistack": "^2.0.4", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@tanstack/react-query": "^4.0.10", "@tanstack/react-query-devtools": "^4.0.10", "@types/jest": "^29.2.3", "@types/node": "17.0.21", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "@types/uuid": "^9.0.7", "eslint": "8.10.0", "eslint-config-prettier": "^8.4.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.3", "eslint-plugin-test-id": "^2.1.0", "jest": "^29.3.1", "notistack": "^2.0.4", "prettier": "^2.5.1", "react": "18.2.0", "react-dom": "18.2.0", "react-oidc-context": "^2.1.0", "ts-jest": "^29.0.3", "tsconfig-paths-webpack-plugin": "^4.0.0", "typescript": "^5.3.3"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"uuid": "^9.0.1"}}