import { useMutation, useQuery } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { IAccessCodeInformation, ICouponDetails } from 'types';
import {
  APIError,
  fetchAccessCodeInformation,
  fetchCouponDetails,
} from 'utils';

export const useAccessCodeInformation = (accessCode: string | undefined) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IAccessCodeInformation, APIError>(
    ['accessCodeInformation', accessCode],
    () => fetchAccessCodeInformation(authToken as string, accessCode!),
    {
      enabled: !!accessCode,
    },
  );
};

export const useGetAccessCodeInformation = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useMutation<IAccessCodeInformation, APIError, string>(
    ['getAccessCodeInformation'],
    (accessCode: string) =>
      fetchAccessCodeInformation(authToken as string, accessCode),
  );
};

export const useCouponDetails = (coupon?: string | undefined) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<ICouponDetails, APIError>(
    ['couponDetails', coupon],
    () => fetchCouponDetails(authToken as string, coupon),
    { enabled: !!coupon },
  );
};
