import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { IDatacenter, IDetailedDatacenter, Market } from 'types';
import {
  augmentBestDatacentersForPlanResults,
  fetchBestDatacentersForPlan,
  fetchDatacenterList,
  fetchOptimalDatacenter,
  rawFetchBestDatacentersForPlan,
} from 'utils';

import { MAX_RETRY } from './constants';

export function useDatacenterList() {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IDatacenter[], Error>(
    ['datacenters'],
    () => fetchDatacenterList(),
    {
      enabled: !!authToken,
      retry: MAX_RETRY,
    },
  );
}

export function useOptimalDatacenter({
  zipcode,
  country,
  skip = false,
  shouldBeLoggedIn = true,
}: {
  zipcode: string | null;
  country: string | undefined;
  skip?: boolean;
  shouldBeLoggedIn?: boolean;
}) {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IDatacenter, Error>(
    ['datacenters', zipcode, country],
    () => fetchOptimalDatacenter(zipcode!, country!),
    {
      enabled:
        (!!authToken || !shouldBeLoggedIn) && !skip && !!(zipcode && country),
      retry: MAX_RETRY,
    },
  );
}

export function useOptimalDatacenterMutation() {
  return useMutation<IDatacenter, Error, { zipcode: string; country: Market }>(
    ['optimalDatacenter'],
    {
      mutationFn: ({ zipcode, country }) => {
        return fetchOptimalDatacenter(zipcode, country);
      },
    },
  );
}

interface DatacenterListForPlanParams {
  zipcode: string | null;
  country: Market;
  planId: string | undefined;
}

export function useDatacenterListForPlan(
  { zipcode, country, planId }: DatacenterListForPlanParams,
  skip = false,
) {
  return useQuery<IDetailedDatacenter[], Error>(
    [
      'datacentersForPlan',
      {
        zipcode,
        country,
        planId,
      },
    ],
    () => fetchBestDatacentersForPlan(zipcode!, country, planId!),
    {
      enabled: !skip && !!(zipcode && country && planId),
      retry: MAX_RETRY,
    },
  );
}

export function useDatacenterListForPlanMutation() {
  const queryClient = useQueryClient();

  return useMutation<
    {
      rawResults: Omit<IDetailedDatacenter, 'status'>[];
      augmentedResults: IDetailedDatacenter[];
    },
    Error,
    DatacenterListForPlanParams
  >(['datacentersForPlanMutation'], {
    mutationFn: async ({ zipcode, country, planId }) => {
      const rawResults = await rawFetchBestDatacentersForPlan(
        zipcode!,
        country,
        planId!,
      );
      const augmentedResults = augmentBestDatacentersForPlanResults(
        rawResults,
        country,
      );
      return { rawResults, augmentedResults };
    },

    onSuccess: (data, variables) => {
      // Update the query cache with the mutation result
      queryClient.setQueryData(
        [
          'datacentersForPlan',
          {
            zipcode: variables.zipcode,
            country: variables.country,
            planId: variables.planId,
          },
        ],
        data.augmentedResults,
      );
    },

    retry: MAX_RETRY,
  });
}
