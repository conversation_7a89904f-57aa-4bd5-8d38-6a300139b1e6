import { useState, useEffect } from 'react';

export function useDelayedDisplay(value: boolean, delay: number): boolean {
  const [delayedValue, setDelayedValue] = useState<boolean>(value);

  useEffect(() => {
    if (value) {
      // Show immediately
      setDelayedValue(true);
    } else {
      // Hide after delay
      const timer = setTimeout(() => {
        setDelayedValue(false);
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [value, delay]);

  return delayedValue;
}

export default useDelayedDisplay;
