import { v4 as uuidv4 } from 'uuid';

// Don't change this key as users may lose their feature flags variation
// This name is actually not mentionning Flagship/AB Tasty nor visitor id to avoid
// ad blockers blocking this cookie or else
const VISITOR_ID_STORAGE_KEY = 'ff_shadow_user_uniq_id';

const getStoredVisitorId = () => {
  try {
    return localStorage.getItem(VISITOR_ID_STORAGE_KEY);
  } catch {
    // Do nothing
  }

  return undefined;
};

const storeVisitorId = (visitorId: string) => {
  try {
    localStorage.setItem(VISITOR_ID_STORAGE_KEY, visitorId);
  } catch {
    // Do nothing
  }
};

export function useVisitorId() {
  // We don't do server side ab testing for the moment and the visitor id is only
  // accessible from the browser
  if (typeof window === 'undefined') {
    return undefined;
  }

  const storedVisitorId = getStoredVisitorId();

  if (storedVisitorId) {
    return storedVisitorId;
  }

  const visitorId = uuidv4();
  storeVisitorId(visitorId);

  return visitorId;
}
