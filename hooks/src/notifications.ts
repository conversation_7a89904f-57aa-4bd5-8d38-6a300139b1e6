import type { SnackbarMessage, OptionsObject } from 'notistack';
import { useSnackbar } from 'notistack';
import { useCallback } from 'react';
import { NotificationState } from 'types';

export const useNotifications = () => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const notifySuccess = useCallback(
    (successMessage: SnackbarMessage, options?: OptionsObject) => {
      enqueueSnackbar(successMessage, {
        variant: NotificationState.SUCCESS,
        ...options,
      });
    },
    [enqueueSnackbar],
  );

  const notifyWarning = useCallback(
    (warningMessage: SnackbarMessage, options?: OptionsObject) => {
      enqueueSnackbar(warningMessage, {
        variant: NotificationState.WARNING,
        ...options,
      });
    },
    [enqueueSnackbar],
  );

  const notifyError = useCallback(
    (errorMessage: SnackbarMessage, options?: OptionsObject) => {
      enqueueSnackbar(errorMessage, {
        variant: NotificationState.ERROR,
        autoHideDuration: 10000,
        ...options,
      });
    },
    [enqueueSnackbar],
  );

  return { notifySuccess, notifyWarning, notifyError, closeSnackbar };
};
