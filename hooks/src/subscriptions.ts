import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import {
  IApiProduct,
  IClonableVm,
  IPaginatedSubscriptions,
  IRenewalEstimate,
  ISubscription,
  ISubscriptionPayload,
  OrderColumns,
  ProductFamilyId,
  SortableColumns,
} from 'types';
import {
  APIError,
  fetchPaginatedSubscriptions,
  fetchRenewalEstimate,
  fetchScheduledChangesSubscriptions,
  fetchSubscriptionAvailableUpdates,
  fetchClonableSubscription,
  modifySubscription,
  fetchSubscriptionDetails,
  fetchAllSubscriptions,
} from 'utils';

import { DEFAULT_SUBSCRIPTIONS_PAGE_SIZE } from './constants';

const useGetAllSubscriptions = (
  isMember: boolean,
  productFamily: ProductFamilyId,
  orderBy: OrderColumns,
  sortBy: SortableColumns,
  enabled: boolean = true,
) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  const subscriptionsParams: {
    orderBy?: OrderColumns;
    role?: string;
    sortBy?: SortableColumns;
  } = {
    ...(sortBy && { sort_by: sortBy }),
    ...(orderBy && { order_by: orderBy }),
    ...(isMember && { role: 'member' }),
    ...(productFamily && { product_family: productFamily }),
    ...{ from_chargebee: false }, // 'false' makes the response time faster
  };

  return useQuery<ISubscription[], Error>(
    ['subscriptions', subscriptionsParams],
    () => fetchAllSubscriptions(authToken as string, subscriptionsParams),
    {
      enabled: !!authToken && enabled,
      retry: false,
    },
  );
};

const usePaginatedSubscriptions = (
  isMember?: boolean,
  subscriptionsListPageNumber = 0,
  subscriptionsListPageSize = DEFAULT_SUBSCRIPTIONS_PAGE_SIZE,
  tags?: string[],
  productFamily?: ProductFamilyId,
  orderBy: OrderColumns = OrderColumns.DESCEND,
  sortBy: SortableColumns = SortableColumns.SUB_NAME,
  search?: string,
) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  const subscriptionsParams: {
    orderBy?: OrderColumns;
    page?: string;
    pageSize?: string;
    role?: string;
    sortBy?: SortableColumns;
    tags?: string;
    search?: string;
  } = {
    ...(sortBy && { sort_by: sortBy }),
    ...(orderBy && { order_by: orderBy }),
    ...(isMember && { role: 'member' }),
    ...(subscriptionsListPageNumber >= 0 && {
      page: subscriptionsListPageNumber.toString(),
    }),
    ...(subscriptionsListPageSize >= 0 && {
      page_size: subscriptionsListPageSize.toString(),
    }),
    ...(tags && tags.length > 0 && { tags: tags.join() }),
    ...(productFamily && { product_family: productFamily }),
    ...(search && { search }),
    ...{ from_chargebee: false }, // 'false' makes the response time faster
  };

  return useQuery<IPaginatedSubscriptions, Error>(
    ['subscriptions', subscriptionsParams],
    () => fetchPaginatedSubscriptions(authToken as string, subscriptionsParams),
    {
      enabled: !!authToken,
      retry: false,
    },
  );
};

const useScheduledChangesSubscription = (
  subscriptionId: string,
  hasScheduledChanges = false,
) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<ISubscription | undefined, Error>(
    ['scheduledChangesSubscriptions', subscriptionId],
    () =>
      fetchScheduledChangesSubscriptions(authToken as string, subscriptionId),
    {
      // As the API is throwing a 400 if there is no scheduled changes,
      // we do not want to trigger a call in this case
      enabled: !!authToken && !!hasScheduledChanges && !!subscriptionId,
      retry: false,
    },
  );
};

const useGetPossibleSubscriptionUpdates = (subscriptionId: string) => {
  const { user } = useAuth();

  return useQuery<IApiProduct[] | undefined, Error>(
    ['subscriptionUpgrades', subscriptionId],
    () =>
      fetchSubscriptionAvailableUpdates(
        user?.access_token as string,
        subscriptionId,
      ).then(res => {
        if (Array.isArray(res)) {
          return res;
        }
        throw new Error('Failed getting possible updates');
      }),
    {
      enabled: !!subscriptionId,
    },
  );
};

const useModifySubscription = (subscriptionId: string) => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<ISubscription, APIError, ISubscriptionPayload>(
    ['modifySubscription', subscriptionId],
    (subscriptionPayload: ISubscriptionPayload) =>
      modifySubscription(authToken, subscriptionPayload, subscriptionId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useClonableSubscription = () => {
  const { user } = useAuth();

  return useQuery<IClonableVm[] | undefined, Error>(
    ['clonableSubscription'],
    () => fetchClonableSubscription(user?.access_token as string),
  );
};

const useGetSubscription = (subscriptionId: string) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<ISubscription, APIError>(
    ['getSubscriptionDetails', { subscriptionId }],
    () => fetchSubscriptionDetails(authToken, subscriptionId),
    {
      enabled: !!subscriptionId,
    },
  );
};

export {
  usePaginatedSubscriptions,
  useGetAllSubscriptions,
  useScheduledChangesSubscription,
  useGetPossibleSubscriptionUpdates,
  useClonableSubscription,
  useModifySubscription,
  useGetSubscription,
};

export function useRenewalEstimate(subscriptionId: string) {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IRenewalEstimate, APIError>(
    ['getRenewalEstimate', { subscriptionId }],
    () => {
      return fetchRenewalEstimate(subscriptionId, authToken as string);
    },
    {
      enabled: !!authToken && !!subscriptionId,
      retry: false,
    },
  );
}
