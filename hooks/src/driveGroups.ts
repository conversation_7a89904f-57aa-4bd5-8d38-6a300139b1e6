import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import {
  ICreateDriveGroupInvitePayload,
  ICreateDriveGroupPayload,
  IDriveGroup,
  IPaginatedDriveGroups,
  IUpdateDriveGroupPayload,
  IUpgradeDriveGroupPayload,
} from 'types';
import {
  APIError,
  createDriveGroup,
  createDriveGroupInvite,
  deleteDriveGroupMember,
  getDriveGroup,
  getDriveGroups,
  rejectDriveGroupInvite,
  acceptDriveGroupInvite,
  deleteDriveGroup,
  updateDriveGroup,
  upgradeDriveGroup,
} from 'utils';

export const useGetDriveGroup = (driveGroupId?: string) => {
  const { user } = useAuth();

  return useQuery<IDriveGroup, APIError>(
    ['driveGroup', { driveGroupId }],
    () => {
      return getDriveGroup(user?.access_token as string, driveGroupId || '');
    },
    {
      enabled: !!user?.access_token && !!driveGroupId,
    },
  );
};

export const useGetDriveGroups = () => {
  const { user } = useAuth();

  return useQuery<IPaginatedDriveGroups, APIError>(
    ['driveGroups'],
    () => {
      return getDriveGroups(user?.access_token as string);
    },
    {
      enabled: !!user?.access_token,
    },
  );
};

export const useCreateDriveGroup = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<IDriveGroup, APIError, ICreateDriveGroupPayload>(
    ['createDriveGroup'],
    (payload: ICreateDriveGroupPayload) =>
      createDriveGroup(user?.access_token as string, payload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups']);
      },
    },
  );
};

export const useDeleteDriveGroup = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    ['deleteDriveGroup'],
    (driveGroupId: string) =>
      deleteDriveGroup(user?.access_token as string, driveGroupId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups']);
      },
    },
  );
};

export const useUpdateDriveGroup = (driveGroupId: string) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<IDriveGroup, APIError, IUpdateDriveGroupPayload>(
    ['updateDriveGroup'],
    (payload: IUpdateDriveGroupPayload) =>
      updateDriveGroup(user?.access_token as string, driveGroupId, payload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups']);
      },
    },
  );
};

export const useUpgradeDriveGroup = (driveGroupId: string) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string, APIError, IUpgradeDriveGroupPayload>(
    ['upgradeDriveGroup'],
    (payload: IUpgradeDriveGroupPayload) =>
      upgradeDriveGroup(user?.access_token as string, driveGroupId, payload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups', 'driveGroup']);
      },
    },
  );
};

export const useCreateDriveGroupInvite = (driveGroupId: string) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<
    string | undefined,
    APIError,
    ICreateDriveGroupInvitePayload
  >(
    ['createDriveGroupInvite'],
    (payload: ICreateDriveGroupInvitePayload) =>
      createDriveGroupInvite(
        user?.access_token as string,
        driveGroupId,
        payload,
      ),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups']);
      },
    },
  );
};

export const useDeleteDriveGroupMember = (driveGroupId: string) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    ['deleteDriveGroupMember'],
    (membershipId: string) =>
      deleteDriveGroupMember(
        user?.access_token as string,
        driveGroupId,
        membershipId,
      ),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups']);
      },
    },
  );
};

export const useAcceptDriveGroupInvite = () => {
  const { user } = useAuth();

  return useMutation<string | undefined, APIError, string>(
    ['acceptDriveGroupInvite'],
    (inviteToken: string) =>
      acceptDriveGroupInvite(user?.access_token as string, inviteToken),
    {},
  );
};

export const useRejectDriveGroupInvite = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    ['rejectDriveGroupInvite'],
    (inviteToken: string) =>
      rejectDriveGroupInvite(user?.access_token as string, inviteToken),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['driveGroups']);
      },
    },
  );
};
