<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,400;0,600;1,400;1,600&family=Roboto+Mono&display=swap" rel="stylesheet">
  <style>
    body {
      margin: 0;
      padding: 0;
      display: grid;
      place-items: center;
      height: 100vh;
    }

    body * {
      box-sizing: border-box;
    }

    body, input, select, button {
      font-family: 'Open Sans', sans-serif;
      font-size: 14px;
      color: #333;
    }

    h1 {
      margin: 0;
    }

    .container {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
    }

    .error {
      padding: 16px;
      border-radius: 8px;
      background-color: rgb(242, 182, 182);
      color: rgb(218, 50, 50);
    }

    code {
      font-family: 'Roboto Mono', monospace;
      display: inline-flex;
      padding: 2px 8px;
      background-color: #eee;
      border-radius: 4px;
      color: rgb(218, 50, 50);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Multi-env Feature Select</h1>
    <div class="error">
      <%= message %>
    </div>
  </div>
</body>
</html>