const Cookies = require('cookies');
const ejs = require('ejs');
const http = require('http');
const path = require('path');
const url = require('url');

const appPort = process.env.PORT ?? 3003;

const isUndefined = v => typeof v === 'undefined';

const MESSAGES = {
  NO_SELECT_VALUE: 'You must add a value to select query param',
};

/**
 *
 * @param {http.ServerResponse} res
 * @param {string} errorCode
 */
const sendErrorPage = (res, errorCode) => {
  ejs.renderFile(
    path.resolve(__dirname, './error.ejs'),
    {
      message: MESSAGES[errorCode] ?? errorCode,
    },
    (err, output) => {
      if (err) {
        throw err;
      }

      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(output);
    },
  );
};

http
  .createServer((req, res) => {
    const cookies = new Cookies(req, res);
    const {
      pathname,
      query: { select: selectValue, deselect: deselectValue, app },
    } = url.parse(req.url, true);

    if (pathname !== '/' && pathname !== '/feature') {
      res.writeHead(404);
      res.end('Not found');
      return;
    }

    if (selectValue === '') {
      sendErrorPage(res, 'NO_SELECT_VALUE');
      return;
    }

    const status = !isUndefined(selectValue)
      ? 'selection'
      : !isUndefined(deselectValue)
      ? 'deselection'
      : 'idle';

    const featureName =
      status === 'selection' ? selectValue : cookies.get('shadow_feature');

    if (status !== 'idle') {
      const cookieMaxAge = status === 'deselection' ? 0 : undefined;
      cookies.set(
        'shadow_feature',
        status === 'deselection' ? '' : featureName,
        {
          maxAge: cookieMaxAge,
          overwrite: true,
        },
      );
    }

    if (!!app && ['shop', 'account'].includes(app)) {
      const redirectUrl = `https://eu.spectr.be/${app}`;
      res.writeHead(302, { Location: redirectUrl });
      res.end();
      return;
    }

    ejs.renderFile(
      path.resolve(__dirname, './index.ejs'),
      { status, featureName },
      (err, output) => {
        if (err) {
          throw err;
        }

        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(output);
      },
    );
  })
  .listen(appPort, () => {
    // eslint-disable-next-line no-console
    console.log(`Serveur démarré sur http://localhost:${appPort}`);
  });
