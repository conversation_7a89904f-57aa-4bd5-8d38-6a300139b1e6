{"name": "shadow-cbp-frontend-features-select", "version": "1.0.0", "description": "Pour rediriger vers l'instance d'une feature spécifique", "main": "app.js", "scripts": {"lint": "echo 'no linting for this project'", "start": "node app.js", "tsc": "echo 'no typescript on  this app'"}, "author": "Julien CARTIGNY", "dependencies": {"cookies": "^0.8.0", "ejs": "^3.1.9", "url": "^0.11.0"}, "devDependencies": {"eslint": "8.10.0", "eslint-config-next": "12.1.0", "eslint-config-prettier": "^8.4.0", "eslint-config-react-app": "^7.0.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.27.4", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-test-id": "^2.1.0", "eslint-webpack-plugin": "^3.2.0"}}