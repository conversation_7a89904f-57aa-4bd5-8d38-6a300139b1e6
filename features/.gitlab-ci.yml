features_test:
  stage: prebuild
  script:
    - echo "no test job"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - features/**/*

features_build:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:v1.9.1-debug
    entrypoint: [""]
  script:
    - >-
      echo
      "{\"auths\":{\"registry.gitlab.com\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}"
      > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --context ${CI_PROJECT_DIR}/features/ --dockerfile ${CI_PROJECT_DIR}/features/Dockerfile
      --destination ${CI_REGISTRY_IMAGE}/features:latest
      --cache=true --cache-copy-layers=true --cache-ttl=24h
  needs:
    - job: features_test
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - features/**/*