import { DidomiSDK, IDidomiObject } from '@didomi/react';
import { useTranslation } from 'next-i18next';
import { useCallback } from 'react';
import { theme } from 'shared-components';

import {
  generalModalStyle,
  cookiesModalStyle,
  customCookiesModalStyle,
  partnerModalStyle,
} from '@/components/consentBanner/constants';
import { useConfig } from '@/hooks/store/useConfig';
import { useSocial } from '@/hooks/store/useSocial';
import { useAuthentication } from '@/hooks/user/useAuthentication';
import { IPurposeStatus } from '@/types/tracking';
import {
  DIDOMI_DEFAULT_NOTICE_ID,
  DIDOMI_PUBLIC_API_KEY,
  PRIVACY_POLICY_URL,
} from '@/utils/constants';
import { getPurposeStatus } from '@/utils/tracking';

const ConsentBanner = () => {
  const { t } = useTranslation();
  const { didomiLanguage } = useConfig();
  const { setCanLoadLiveChat } = useSocial();
  const { isAuthenticated } = useAuthentication();

  const getDidomiConfig = useCallback(
    () => ({
      app: {
        name: 'Shadow',
        country: 'FR',
        apiKey: DIDOMI_PUBLIC_API_KEY,
        privacyPolicyURL: PRIVACY_POLICY_URL,
      },
      tagManager: {
        provider: 'gtm',
      },
      notice: {
        position: 'popup',
        denyOptions: {
          link: true,
          button: 'none',
          cross: false,
        },
      },
      theme: {
        color: theme.palette.primary.main,
        linkColor: theme.palette.primary.main,
        font: 'Nexa Text',
        css: `
          ${generalModalStyle}
          ${cookiesModalStyle}
          ${customCookiesModalStyle}
          ${partnerModalStyle}
        `,
        buttons: {
          regularButtons: {
            backgroundColor: theme.palette.white.main,
            borderRadius: '100px',
            borderWidth: 0,
            textColor: theme.palette.primary.main,
          },
          highlightButtons: {
            borderRadius: '100px',
            borderWidth: 0,
          },
        },
      },
      preferences: {
        content: {
          title: t('consentBanner.title', 'Shadow consent banner'),
        },
      },
      languages: {
        enabled: [didomiLanguage], // disable browser language detection by forcing user's language
        default: 'en',
      },
    }),
    [didomiLanguage], // eslint-disable-line react-hooks/exhaustive-deps
  );

  const toggleLiveChat = function (status: IPurposeStatus) {
    setCanLoadLiveChat(status.preferences ?? false);
  };

  const onReady = function (Didomi: IDidomiObject) {
    toggleLiveChat(getPurposeStatus(Didomi));

    Didomi.on('consent.changed', () => {
      toggleLiveChat(getPurposeStatus(Didomi));
    });
  };

  if (!didomiLanguage || !isAuthenticated) {
    return <></>;
  }

  return (
    <DidomiSDK
      iabVersion={2}
      config={getDidomiConfig()}
      noticeId={DIDOMI_DEFAULT_NOTICE_ID}
      gdprAppliesGlobally={true}
      onReady={onReady}
      onNoticeClickAgree={() => {
        // Disabled for now, as the new sprinklr chat does not add cookies
        // setCanLoadLiveChat(true);
      }}
    />
  );
};

export default ConsentBanner;
