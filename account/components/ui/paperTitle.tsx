import styled from '@emotion/styled';
import type { ReactNode } from 'react';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';

const Title = styled(Typography)`
  padding-bottom: 24px;

  ${theme.breakpoints.up('md')} {
    padding-bottom: 40px;
  }
`;

interface IPaperTitleProps {
  children: ReactNode;
}

const PaperTitle = ({ children }: IPaperTitleProps) => {
  const { isSM } = useThemeMediaQueries();

  return (
    <Title variant={isSM ? 'body-lg-regular' : 'heading-h5'}>{children}</Title>
  );
};

export default PaperTitle;
