import { Tooltip as Mu<PERSON><PERSON>ooltip, TooltipProps, styled } from '@mui/material';
import { Icon, theme, Typography } from 'shared-components';
import { Icons } from 'types';

import {
  TOOLTIP_LEAVE_TOUCH_DELAY,
  TOOLTIP_ENTER_TOUCH_DELAY,
} from '@/utils/constants';

interface ITooltipProps extends Omit<TooltipProps, 'children'> {
  fontSize?: 'small' | 'medium' | 'large';
  color?:
    | 'inherit'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'error'
    | 'info'
    | 'warning';
  iconWidth?: number;
}

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => {
  return <MuiTooltip {...props} classes={{ popper: className }} />;
})`
  & .MuiTooltip-tooltip {
    color: ${theme.palette.black.main};
    background-color: ${theme.palette.primary.main10};
    padding: 16px;
    max-width: 357px;
    cursor: help;
  }
  & .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom {
    margin-top: 2px;
  }
  font-size: 24px;
`;

const IconInfo = styled(Icon)`
  margin-left: 8px;
  cursor: help;
`;

export const Tooltip = ({
  title,
  placement,
  iconWidth,
  ...props
}: ITooltipProps) => {
  return (
    <>
      <StyledTooltip
        title={<Typography variant="body-sm">{title}</Typography>}
        placement={placement}
        enterTouchDelay={TOOLTIP_ENTER_TOUCH_DELAY}
        leaveTouchDelay={TOOLTIP_LEAVE_TOUCH_DELAY}
        {...props}
      >
        {/* We have to keep a div as parent otherwise tooltip doesn't work  */}
        <div>
          <IconInfo name={Icons.INFO} width={iconWidth} />
        </div>
      </StyledTooltip>
    </>
  );
};

export default Tooltip;
