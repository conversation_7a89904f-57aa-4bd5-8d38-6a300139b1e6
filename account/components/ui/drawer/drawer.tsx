import styled from '@emotion/styled';
import { ReactNode, useEffect } from 'react';
import { Icon, theme } from 'shared-components';
import { Icons } from 'types';

import { Keycode } from '@/types/keycode';

const Container = styled.div<Pick<IDrawerProps, 'open' | 'keepHeaderVisible'>>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  transition: visibility 0.3s cubic-bezier(0, 0, 0.2, 1);
  visibility: ${({ open }) => (open ? 'visible' : 'hidden')};
  z-index: 200;
  ${({ keepHeaderVisible }) => keepHeaderVisible && 'top: 64px;'}
  height: ${({ keepHeaderVisible }) =>
    keepHeaderVisible ? 'calc(100vh - 64px)' : '100vh'};
`;

const Overlay = styled.div<Pick<IDrawerProps, 'open'>>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: ${({ open }) => (open ? '1' : '0')};
  transition: opacity 0.3s cubic-bezier(0, 0, 0.2, 1);
`;

const Wrapper = styled.div<
  Pick<IDrawerProps, 'anchor' | 'open' | 'keepHeaderVisible'>
>`
  position: fixed;
  left: ${({ anchor }) => (anchor === 'left' ? '0' : 'unset')};
  right: ${({ anchor }) => (anchor === 'right' ? '0' : 'unset')};
  width: 100%;
  height: ${({ keepHeaderVisible }) =>
    keepHeaderVisible ? 'calc(100% - 64px)' : '100vh'};
  background: ${theme.palette.white.main};
  box-sizing: border-box;
  overflow: auto;
  transform: ${({ open }) => !open && 'translateX(100%)'};
  transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1);

  ${theme.breakpoints.up('lg')} {
    padding: 64px;
    border-radius: ${({ anchor }) =>
      anchor === 'right' ? '16px 0 0 16px' : '0 16px 16px 0'};
    max-width: 720px;
  }
`;

const CloseButton = styled.button`
  align-items: center;
  border-radius: 8px;
  color: ${theme.palette.secondary.light50};
  display: flex;
  justify-content: center;
  left: 16px;
  padding: 8px;
  position: absolute;
  top: 16px;
  transition: all 0.1s ease-in-out;

  :hover {
    color: ${theme.palette.primary.main};
    background-color: ${theme.palette.primary.main10};
  }

  svg {
    display: block;
  }
`;

interface IDrawerProps {
  anchor: 'left' | 'right';
  children: ReactNode;
  handleClose: () => void;
  open: boolean;
  keepHeaderVisible?: boolean;
  showCloseIcon?: boolean;
}
const Drawer = ({
  anchor,
  children,
  handleClose,
  open = false,
  keepHeaderVisible = false,
  showCloseIcon = false,
  ...props
}: IDrawerProps) => {
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.code === Keycode.Escape) {
        handleClose();
      }
    };
    document.addEventListener('keydown', handleEscapeKey);
    return () => document.removeEventListener('keydown', handleEscapeKey);
  }, [handleClose]);

  useEffect(() => {
    document.body.style.overflow = open ? 'hidden' : 'unset';
  }, [open]);

  return (
    <Container open={open} keepHeaderVisible={keepHeaderVisible}>
      <Overlay onClick={handleClose} open={open}></Overlay>
      <Wrapper
        anchor={anchor}
        open={open}
        {...props}
        keepHeaderVisible={keepHeaderVisible}
      >
        {showCloseIcon && (
          <CloseButton onClick={handleClose}>
            <Icon name={Icons.CROSS} width={16} height={16} />
          </CloseButton>
        )}
        {children}
      </Wrapper>
    </Container>
  );
};

export default Drawer;
