import { ListItem, ListItemProps } from '@mui/material';
import { styled } from '@mui/material/styles';
import { SubscriptionStatus } from 'types';

import { STATUS_COLORS } from '@/utils/constants';

interface IStyledBulletListItemProps extends ListItemProps {
  status?: SubscriptionStatus;
}

const BulletListItem = styled(ListItem, {
  shouldForwardProp: prop => prop !== 'status',
})<IStyledBulletListItemProps>(({ status }) => ({
  '&::before': {
    content: '"\u2022"', // •
    color: STATUS_COLORS[status || SubscriptionStatus.ACTIVE],
    fontSize: '24px',
    width: '16px',
    marginLeft: '-16px',
  },
}));

export { BulletListItem };
