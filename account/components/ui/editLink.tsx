import { Icon, theme, useThemeMediaQueries } from 'shared-components';
import { Icons } from 'types';

import Link from '@/components/ui/link';

interface IEditLinkProps {
  label: string;
  onClick?: () => void;
  href?: string;
  target?: string;
}

const EditLink = ({
  label,
  href,
  onClick,
  target,
  ...props
}: IEditLinkProps) => {
  const { isSM } = useThemeMediaQueries();

  return (
    <Link
      color={theme.palette.black.main}
      target={target}
      href={href}
      onClick={onClick}
      variant="label-xs-link"
      component="a"
      {...props}
    >
      {isSM ? (
        <Icon
          name={Icons.EDIT}
          color={theme.palette.black.main75}
          width={16}
          height={16}
        />
      ) : (
        label
      )}
    </Link>
  );
};

export default EditLink;
