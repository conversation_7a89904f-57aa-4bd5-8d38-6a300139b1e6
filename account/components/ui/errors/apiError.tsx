import { useTranslation } from 'next-i18next';
import { Alert } from 'shared-components';
import { NotificationState } from 'types';

import Paper from '@/components/ui/paper';

const ApiError = () => {
  const { t } = useTranslation();

  return (
    <Paper>
      <Alert
        type={NotificationState.ERROR}
        title={t('api.error.title', 'Something went wrong...', {
          ns: 'common',
        })}
      >
        {t(
          'api.error.description',
          'An error has occurred fetching your data. Reload the page or contact support',
          { ns: 'common' },
        )}
      </Alert>
    </Paper>
  );
};

export default ApiError;
