import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Alert } from 'shared-components';
import { theme } from 'shared-components';
import { NotificationState } from 'types';

import Paper from '@/components/ui/paper';

const Container = styled.div`
  display: flex;
  width: 100%;
  height: 100vh;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const PaperStyled = styled(Paper)`
  max-width: 600px;
  padding-bottom: 0;

  ${theme.breakpoints.up('md')} {
    padding-bottom: 40px;
  }
`;

const GlobalError = () => {
  const { t } = useTranslation();

  return (
    <Container>
      <PaperStyled>
        <Alert
          type={NotificationState.ERROR}
          title={t('login.error.title', 'Oops, something is broken', {
            ns: 'common',
          })}
        >
          {t(
            'login.error.description',
            'An error occurred when attempting to logged you in. Please try again later or contact support if the problem persists.',
            { ns: 'common' },
          )}
        </Alert>
      </PaperStyled>
    </Container>
  );
};

export default GlobalError;
