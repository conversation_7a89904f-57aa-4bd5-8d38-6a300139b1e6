import styled from '@emotion/styled';
import { Pagination, PaginationItem } from '@mui/material';
import {
  gridPageCountSelector,
  gridPageSelector,
  useGridApiContext,
  useGridSelector,
} from '@mui/x-data-grid';
import { Icon, theme } from 'shared-components';
import { Icons } from 'types';

const Container = styled(Pagination)`
  .MuiPaginationItem-page {
    width: 39px;
    border: 1px solid transparent;
    border-radius: ${theme.shape.borderRadius}px;
    margin: 4px;
    font-size: ${theme.typography['label-md'].fontSize};
    font-weight: ${theme.typography['label-md'].fontWeight};
    line-height: ${theme.typography['label-md'].lineHeight};
    &:hover,
    &.Mui-selected,
    &.Mui-selected:hover {
      background: ${theme.palette.white.main};
      border-color: ${theme.palette.primary.main10};
      color: ${theme.palette.primary.main};
    }
  }
`;

const DataGridPagination = () => {
  const apiRef = useGridApiContext();
  const page = useGridSelector(apiRef, gridPageSelector);
  const pageCount = useGridSelector(apiRef, gridPageCountSelector);

  return (
    <Container
      count={pageCount}
      page={page + 1}
      onChange={(event, value) => apiRef.current.setPage(value - 1)}
      siblingCount={1}
      boundaryCount={1}
      renderItem={item => (
        <PaginationItem
          components={{
            previous: () => (
              <Icon
                name={Icons.CHEVRON_LEFT}
                color={theme.palette.primary.main25}
                width={6}
                height={10}
              />
            ),
            next: () => (
              <Icon
                name={Icons.CHEVRON_RIGHT}
                color={theme.palette.primary.main25}
                width={6}
                height={10}
              />
            ),
          }}
          {...item}
        />
      )}
    />
  );
};

export default DataGridPagination;
