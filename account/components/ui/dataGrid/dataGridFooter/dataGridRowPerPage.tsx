import styled from '@emotion/styled';
import { MenuItem, SelectChangeEvent } from '@mui/material';
import { useTranslation } from 'next-i18next';
import { theme, Typography } from 'shared-components';

import Select from '@/components/ui/select';
import { LIST_MANAGER_PAGE_OPTIONS } from '@/utils/constants';

const Container = styled.div`
  color: ${theme.palette.black.main75};
  display: flex;
  align-items: center;
`;

const SelectStyled = styled(Select)`
  margin-left: 8px;
  .MuiSelect-select {
    padding-top: 8px;
    padding-bottom: 8px;
  }
`;

interface IDataGridRowPerPageProps {
  handlePageSizeChange: (event: SelectChangeEvent<unknown>) => void;
  pageSize: number;
}

const DataGridRowPerPage = ({
  handlePageSizeChange,
  pageSize,
}: IDataGridRowPerPageProps) => {
  const { t } = useTranslation();

  return (
    <Container>
      <Typography variant="label-sm">
        {t('businessManager.managerList.footer.rowPerPage', 'Row per page')}
      </Typography>
      <SelectStyled
        placeholder={pageSize.toString()}
        value={pageSize}
        onChange={handlePageSizeChange}
      >
        {LIST_MANAGER_PAGE_OPTIONS.map(item => (
          <MenuItem key={item.value} value={item.value}>
            {item.value}
          </MenuItem>
        ))}
      </SelectStyled>
    </Container>
  );
};

export default DataGridRowPerPage;
