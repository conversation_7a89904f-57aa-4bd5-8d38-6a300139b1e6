import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { theme, Typography } from 'shared-components';

interface IDataGridTotal {
  total: number;
  type: string;
}

const Container = styled.div`
  display: flex;
  align-items: center;
  color: ${theme.palette.black.main75};
`;

const TotalValue = styled(Typography)`
  color: ${theme.palette.black.main};
  margin-left: 8px;
`;

const DataGridRowsTotal = ({ total, type }: IDataGridTotal) => {
  const { t } = useTranslation();

  return (
    <Container>
      <Typography variant="label-sm">
        {t('businessManager.managerList.footer.total', 'Total:')}
      </Typography>
      <TotalValue variant="label-md-regular">
        {total} {t(`businessManager.managerList.footer.${type}`)}
      </TotalValue>
    </Container>
  );
};

export default DataGridRowsTotal;
