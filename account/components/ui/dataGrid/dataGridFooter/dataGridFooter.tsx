import styled from '@emotion/styled';
import { ReactNode } from 'react';
import { theme } from 'shared-components';

interface IDataGridFooter {
  pagination: ReactNode;
  rowPerPage: ReactNode;
  rowsTotal: ReactNode;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 24px 0;
  gap: 16px;

  ${theme.breakpoints.up('md')} {
    flex-direction: row;
    justify-content: space-between;
  }
`;

const Left = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
`;

const DataGridFooter = ({
  pagination,
  rowPerPage,
  rowsTotal,
}: IDataGridFooter) => {
  return (
    <Container>
      <Left>
        {rowPerPage}
        {rowsTotal}
      </Left>
      {pagination}
    </Container>
  );
};

export default DataGridFooter;
