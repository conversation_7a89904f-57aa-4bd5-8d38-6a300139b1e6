import styled from '@emotion/styled';
import { SelectChangeEvent } from '@mui/material';
import {
  DataGrid as MuiDataGrid,
  GridColDef,
  GridEventListener,
  GridFeatureMode,
} from '@mui/x-data-grid';
import { useState } from 'react';
import { Icon, theme } from 'shared-components';
import { Icons } from 'types';

import DataGridFooter from '@/components/ui/dataGrid/dataGridFooter/dataGridFooter';
import DataGridPagination from '@/components/ui/dataGrid/dataGridFooter/dataGridPagination';
import DataGridRowPerPage from '@/components/ui/dataGrid/dataGridFooter/dataGridRowPerPage';
import DataGridRowsTotal from '@/components/ui/dataGrid/dataGridFooter/dataGridRowsTotal';
import {
  IVmManager,
  IUserManager,
  IDriveGroupsManager,
} from '@/types/adminManagers';
import {
  LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
  ManagerDataType,
} from '@/utils/constants';

interface IDataGrid {
  columns: GridColDef[];
  rows: IVmManager[] | IUserManager[] | IDriveGroupsManager[];
  type: ManagerDataType;
  disableSelectionOnClick?: boolean;
  onRowClick?: GridEventListener<'rowClick'>;
  onPageNumberChange?: (pageNumber: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  paginationMode?: GridFeatureMode;
  rowCount?: number;
  loading?: boolean;
  disablePagination?: boolean;
  columnVisibilityModel?: { [k: string]: boolean };
}

const Container = styled(MuiDataGrid)`
  &.MuiDataGrid-root {
    border: none;
  }
  // set height to 0 to place <empty/> component
  .MuiDataGrid-virtualScroller {
    background: ${theme.palette.white.main};
    ${({ rows }) => rows.length === 0 && 'max-height: 0;'}
  }

  // header
  .MuiDataGrid-columnHeaders {
    padding: 0 16px;
    background: ${theme.palette.primary.main10};
    border-radius: ${theme.shape.borderRadius}px;
    border: none;

    .MuiDataGrid-columnHeadersInner {
      width: 100%;
      flex: 1;
    }

    .MuiDataGrid-columnHeader {
      padding: 0 8px;

      // disable outline focus on header cells
      &:focus,
      &:focus-within {
        outline: none;
      }
    }
    .MuiDataGrid-columnHeader:not(.MuiDataGrid-columnHeader--sortable) {
      font-size: ${theme.typography['label-md-regular'].fontSize};
      font-weight: ${theme.typography['label-md-regular'].fontWeight};
    }
    .MuiDataGrid-columnHeader--sortable {
      & .MuiDataGrid-columnHeaderTitle {
        font-size: ${theme.typography['label-md-regular-link'].fontSize};
        font-weight: ${theme.typography['label-md-regular-link'].fontWeight};
        text-decoration: ${theme.typography['label-md-regular-link']
          .textDecoration};
      }
      &:hover,
      &.MuiDataGrid-columnHeader--sorted {
        color: ${theme.palette.primary.main};
      }
    }
  }
  .MuiDataGrid-columnSeparator {
    display: none;
  }
  .MuiDataGrid-row {
    padding: 0 16px;
    border-bottom: 1px solid ${theme.palette.secondary.main25};
    color: ${theme.palette.black.main75};
    &:hover {
      ${({ disableSelectionOnClick }) =>
        disableSelectionOnClick
          ? `background: none;`
          : `background: ${theme.palette.secondary.main10}; cursor: pointer;`}
    }
    &:last-child {
      border: none;
    }
  }
  .MuiDataGrid-cell {
    border: none;
    padding: 0 8px;
    font-size: ${theme.typography['body-sm'].fontSize};
    font-weight: ${theme.typography['body-sm'].fontWeight};
    line-height: ${theme.typography['body-sm'].lineHeight};
    &:focus-within {
      outline: none;
    }
  }
  // disable outline focus on cells
  &.MuiDataGrid-root .MuiDataGrid-cell:focus {
    outline: none;
  }
`;

const DataGrid = ({
  columns,
  rows,
  type,
  disableSelectionOnClick = false,
  onRowClick,
  onPageNumberChange,
  onPageSizeChange,
  paginationMode,
  rowCount = 0,
  loading,
  disablePagination,
  columnVisibilityModel,
}: IDataGrid) => {
  const [pageSize, setPageSize] = useState<number>(
    LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
  );

  const handlePageSizeChange = (event: SelectChangeEvent<unknown>) => {
    const newPageSize = Number(event.target.value as number);

    setPageSize(newPageSize);
    onPageSizeChange && onPageSizeChange(newPageSize);
  };

  function renderIcon(icon: Icons, color: string | undefined) {
    return <Icon name={icon} color={color} width={16} height={16} />;
  }

  return (
    <Container
      // @TODO action on click on line ? need to add prop
      onRowClick={onRowClick}
      disableSelectionOnClick={disableSelectionOnClick}
      autoHeight
      pageSize={pageSize}
      onPageSizeChange={newPageSize => setPageSize(newPageSize)}
      onPageChange={pageNumber =>
        onPageNumberChange && onPageNumberChange(pageNumber)
      }
      rows={rows}
      columns={columns}
      disableColumnSelector
      disableColumnMenu
      headerHeight={48}
      rowHeight={type === ManagerDataType.DRIVE_GROUPS ? undefined : 72}
      getRowHeight={() =>
        type === ManagerDataType.DRIVE_GROUPS ? 'auto' : undefined
      }
      getEstimatedRowHeight={() => 72}
      paginationMode={paginationMode}
      columnVisibilityModel={columnVisibilityModel}
      rowCount={rowCount}
      loading={loading}
      initialState={{
        sorting: {
          sortModel: [{ field: 'createdAt', sort: 'desc' }],
        },
      }}
      components={{
        ColumnUnsortedIcon: () =>
          renderIcon(Icons.ARROW_UP, theme.palette.primary.main25),
        ColumnSortedAscendingIcon: () =>
          renderIcon(Icons.ARROW_UP, theme.palette.primary.main),
        ColumnSortedDescendingIcon: () =>
          renderIcon(Icons.ARROW_DOWN, theme.palette.primary.main),
        Footer: () =>
          disablePagination ? null : (
            <DataGridFooter
              rowPerPage={
                <DataGridRowPerPage
                  handlePageSizeChange={handlePageSizeChange}
                  pageSize={pageSize}
                />
              }
              rowsTotal={<DataGridRowsTotal total={rowCount} type={type} />}
              pagination={<DataGridPagination />}
            />
          ),
      }}
    ></Container>
  );
};

export default DataGrid;
