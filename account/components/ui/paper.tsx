import styled from '@emotion/styled';
import type { ReactNode } from 'react';
import { theme } from 'shared-components';
interface IPaperProps {
  children: ReactNode;
  id?: string;
}

const Container = styled.div`
  padding: 24px;
  margin-bottom: 40px;
  background: ${theme.palette.white.main};
  border-radius: ${theme.shape.borderRadius}px;

  ${theme.breakpoints.up('md')} {
    padding: 40px;
    border: 1px solid ${theme.palette.primary.main10};
  }

  ${theme.breakpoints.up('lg')} {
    padding: 64px;
  }
`;

const Paper = ({ children, ...props }: IPaperProps) => (
  <Container {...props}>{children}</Container>
);

export default Paper;
