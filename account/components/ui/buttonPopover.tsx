import styled from '@emotion/styled';
import Popover from '@mui/material/Popover';
import type { ReactNode } from 'react';
import { MouseEvent, useEffect, useState } from 'react';
import { Icon, theme } from 'shared-components';
import { Icons } from 'types';

interface IButtonPopoverProps {
  children: ReactNode;
  icon?: ReactNode;
  dataTestId: string;
  name: string;
  label?: string;
  isOpen: boolean;
  hasBackground?: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

// TODO: Remove the span hack (#l20-l22) when the button component is fixed @EtienneDenys
const StyledButton = styled.button<
  Pick<IButtonPopoverProps, 'hasBackground'> & {
    isActive: boolean;
  }
>`
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.4s, color 0.4s ease-out;

  &:hover {
    background-color: ${theme.palette.primary.main};
    color: ${theme.palette.white.main};
  }

  ${({ hasBackground }) =>
    hasBackground &&
    `
      background-color: ${theme.palette.primary.main10};
      color: ${theme.palette.primary.main};
    `}

  ${({ isActive }) =>
    isActive &&
    `
      background-color: ${theme.palette.primary.main};
      color: ${theme.palette.white.main};
    `}
`;

const StyledPopover = styled(Popover)`
  .MuiPopover-paper {
    margin-left: -8px;
    border: 1px solid ${theme.palette.primary.main10};
    box-shadow: 0 0 20px rgba(54, 83, 204, 0.1);
    padding: 24px 32px;
  }
`;

const PopoverInner = styled.div`
  display: flex;
  flex-direction: column;
  justify-items: center;
  gap: 32px;
`;

const ButtonPopover = ({
  children,
  hasBackground,
  dataTestId,
  icon,
  name,
  label,
  isOpen,
  setIsOpen,
}: IButtonPopoverProps) => {
  const [displayedPopoverElement, setDisplayedPopoverElement] =
    useState<HTMLButtonElement | null>(null);

  const handleButtonClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
    setDisplayedPopoverElement(isOpen ? null : e.currentTarget);
  };

  const handlePopoverClose = () => {
    setIsOpen(false);
    setDisplayedPopoverElement(null);
  };

  // Automatically close popover on mount.
  useEffect(() => {
    if (!isOpen) {
      handlePopoverClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const ids = isOpen ? name : undefined;

  return (
    <>
      <StyledButton
        data-test-id={dataTestId}
        isActive={isOpen}
        aria-describedby={ids}
        hasBackground={hasBackground}
        onClick={handleButtonClick}
      >
        {!icon && !label && (
          <Icon name={Icons.MENU_DOTS_VERTICAL} width={16} height={16} />
        )}
        {icon}
        {label ?? null}
      </StyledButton>
      <StyledPopover
        id={ids}
        open={isOpen}
        anchorEl={displayedPopoverElement}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <PopoverInner>{children}</PopoverInner>
      </StyledPopover>
    </>
  );
};

export default ButtonPopover;
