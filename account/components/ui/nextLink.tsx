import { default as NextLinkComponent } from 'next/link';
import { ILinkProps } from 'types';

import Link from '@/components/ui/link';

interface INextLinkProps extends Partial<ILinkProps> {
  href: string;
}

const NextLink = ({
  href,
  children,
  startIcon,
  endIcon,
  variant = 'label-md',
}: INextLinkProps) => (
  <NextLinkComponent href={href} passHref>
    <Link
      startIcon={startIcon}
      endIcon={endIcon}
      component="a"
      variant={variant}
    >
      {children}
    </Link>
  </NextLinkComponent>
);

export default NextLink;
