import styled from '@emotion/styled';
import { Select as SelectMui, SelectProps } from '@mui/material';
import type { ReactNode } from 'react';
import { Icon, theme, Typography } from 'shared-components';
import { Icons } from 'types';

interface ISelectProps extends SelectProps {
  children: ReactNode;
  isValue?: boolean;
  placeholder: string;
}

const ICON_HEIGHT = 7;

const SelectStyled = styled(SelectMui)`
  background: ${theme.palette.white.main};
  color: ${theme.palette.black.main};
  &.Mui-focused {
    background: ${theme.palette.secondary.main10};

    fieldset.MuiOutlinedInput-notchedOutline {
      border-color: transparent;
    }
  }
  .MuiMenuItem-root:hover {
    background: ${theme.palette.secondary.main10};
  }
`;

const SelectIcon = styled(Icon)`
  position: absolute;
  top: calc(50% - ${ICON_HEIGHT} / 2);
  right: 16px;
`;

const Placeholder = styled(Typography)`
  color: ${theme.palette.black.main75};
`;

const Select = ({ children, isValue, placeholder, ...props }: ISelectProps) => (
  <SelectStyled
    {...props}
    IconComponent={() => (
      <SelectIcon name={Icons.CHEVRON_DOWN} width={12} height={ICON_HEIGHT} />
    )}
    renderValue={
      isValue
        ? undefined
        : () => (
            <Placeholder variant="body-md-regular">{placeholder}</Placeholder>
          )
    }
  >
    {children}
  </SelectStyled>
);

export default Select;
