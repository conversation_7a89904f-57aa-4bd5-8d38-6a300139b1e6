import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Modal, ModalButton, ModalLink } from 'shared-components';
import { logError } from 'utils';

import { useCancelScheduledChanges } from '@/hooks/subscription/useSubscription';

interface IModalCancelScheduledChangeProps {
  isOpen: boolean;
  onClose: () => void;
  subscriptionId: string;
  onScheduledChangeCancelSuccess?: () => void;
}

const ModalCancelScheduledChange = ({
  isOpen,
  onClose,
  subscriptionId,
  onScheduledChangeCancelSuccess,
}: IModalCancelScheduledChangeProps) => {
  const { t } = useTranslation();
  const cancelScheduledChange = useCancelScheduledChanges();
  const { notifySuccess, notifyError } = useNotifications();
  const [isLoading, setIsLoading] = useState(false);

  const handleCancelScheduledChange = async () => {
    setIsLoading(true);
    try {
      await cancelScheduledChange.mutateAsync(subscriptionId);
      notifySuccess(
        t(
          'scheduledChange.update.success.title',
          'Your change was canceled successfully',
        ),
      );
      onScheduledChangeCancelSuccess?.();
      onClose();
    } catch (error) {
      logError('Error canceling scheduled change', error);
      notifyError(
        t(
          'scheduledChange.update.error.title',
          `An error occurred when updating your scheduled change. Please try again later.`,
        ),
      );
      setIsLoading(false);
    }
  };

  return (
    <>
      <Modal
        open={isOpen}
        onClose={onClose}
        disableEscapeKeyDown={isLoading}
        title={t(
          'subscription.details.cancelChangesModal.Title',
          'Cancel modification',
        )}
      >
        <>
          {t(
            'subscription.details.cancelChangesModal.Text',
            `A modification to your subscription was scheduled. If you proceed with the cancellation, your subscription will remain unchanged as it is today. Are you sure you want to proceed with this cancellation?`,
          )}
          <ModalButton
            onClick={handleCancelScheduledChange}
            loading={isLoading}
            disabled={isLoading}
          >
            {t('global.confirm', 'Confirm', { ns: 'common' })}
          </ModalButton>
          <ModalLink onClick={onClose} disabled={isLoading}>
            {t('global.cancel', 'Cancel', { ns: 'common' })}
          </ModalLink>
        </>
      </Modal>
    </>
  );
};

export default ModalCancelScheduledChange;
