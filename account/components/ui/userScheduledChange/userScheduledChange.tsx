import styled from '@emotion/styled';
import { useScheduledChangesSubscription } from 'hooks';
import { Trans, useTranslation } from 'next-i18next';
import { useState } from 'react';
import { FC } from 'react';
import { Link, Typography, theme } from 'shared-components';
import { ISubscription } from 'types';

import ModalCancelScheduledChange from './modalCancelScheduledChange';
import { useScheduledChangesReasons } from './useScheduledChangesReasons';

import { useDate } from '@/hooks/useDate';

interface IUserScheduledChangeProps {
  subscription: ISubscription;
  onScheduledChangeCancelSuccess?: () => void;
}

const Container = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
  padding: 8px 16px;
  background-color: ${theme.palette.secondary.main10};
`;

const ButtonModalCancel = styled(Link)`
  padding: 12px 24px;
  white-space: nowrap;
`;

const UserScheduledChange: FC<IUserScheduledChangeProps> = ({
  subscription,
  onScheduledChangeCancelSuccess,
}) => {
  const { getSubscriptionScheduledChangesApplicationDate } = useDate();
  const { t } = useTranslation();

  const [openModal, setOpenModal] = useState<boolean>(false);
  const scheduledChangesSubscriptionQuery = useScheduledChangesSubscription(
    subscription.id,
    subscription.has_scheduled_changes,
  );

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const reasons = useScheduledChangesReasons(subscription);

  if (
    !scheduledChangesSubscriptionQuery ||
    !scheduledChangesSubscriptionQuery.data
  ) {
    return null;
  }

  return (
    <Container>
      <Typography variant="body-sm">
        <Trans
          i18nKey="subscription.details.scheduledChanges"
          defaults="A change ({{reasons}}) is being made to your {{productFamily}} subscription that will take effect on <bold>{{scheduledChangesApplicationDate}}</bold>."
          values={{
            scheduledChangesApplicationDate:
              getSubscriptionScheduledChangesApplicationDate(
                scheduledChangesSubscriptionQuery.data,
              ),
            reasons: reasons,
            productFamily: t(
              `subscription.details.scheduledChangesProductFamily.${subscription.product_family_id}`,
            ),
          }}
          components={{ bold: <strong /> }}
        />
      </Typography>
      <ButtonModalCancel
        data-test-id="cancel-scheduled-changes-button"
        color="primary"
        variant="label-xs-link"
        onClick={() => setOpenModal(true)}
      >
        {t(
          'subscription.details.cancelChangesModal.Title',
          'Cancel modification',
        )}
      </ButtonModalCancel>
      <ModalCancelScheduledChange
        isOpen={openModal}
        onClose={handleCloseModal}
        subscriptionId={subscription.id}
        onScheduledChangeCancelSuccess={onScheduledChangeCancelSuccess}
      />
    </Container>
  );
};

export default UserScheduledChange;
