import { useScheduledChangesSubscription } from 'hooks';
import { useTranslation } from 'next-i18next';
import type { TFunction } from 'next-i18next';
import { ISubscription, ProductType } from 'types';
import {
  STORAGE_SLICE_SIZE_GO,
  findSubscriptionItemByType,
  getOfferFromId,
  logError,
} from 'utils';

import { useCatalog } from '@/hooks/useCatalog';

function addReasonWithCondition(
  t: TFunction,
  condition: boolean,
  reasonArray: string[],
  translationKey: string,
  defaultValue: string,
  params?: Record<string, unknown>,
) {
  if (condition) {
    reasonArray.push(t(translationKey, { defaultValue, ...params }));
  }
}

export const useScheduledChangesReasons = (
  currentSubscription: ISubscription,
): string => {
  const { t } = useTranslation();
  const reasonArray: string[] = [];
  const catalogQuery = useCatalog();
  const scheduledChangesSubscriptionQuery = useScheduledChangesSubscription(
    currentSubscription.id,
    currentSubscription.has_scheduled_changes,
  );

  const scheduledChangesSubscription = scheduledChangesSubscriptionQuery.data;

  if (!scheduledChangesSubscription) {
    if (!scheduledChangesSubscriptionQuery.isLoading) {
      logError("Can't find scheduled changes subscription");
    }
    return '';
  }

  const currentStorageQuantity =
    currentSubscription.items.find(
      item => item.item_type === ProductType.ADDON && !!item.price,
    )?.quantity ?? 0;

  const scheduledChangesStorageQuantity =
    scheduledChangesSubscription.items.find(
      item => item.item_type === ProductType.ADDON && !!item.price,
    )?.quantity ?? 0;

  const currentPlan = findSubscriptionItemByType(
    currentSubscription,
    ProductType.PLAN,
  );

  const scheduledPlan = findSubscriptionItemByType(
    scheduledChangesSubscription,
    ProductType.PLAN,
  );

  if (!currentPlan) {
    logError("Can't find current plan");
    return '';
  }

  if (!scheduledPlan) {
    logError("Can't find scheduled plan");
    return '';
  }

  const currentOffer = getOfferFromId(catalogQuery.data, currentPlan.id);
  const scheduledOffer = getOfferFromId(catalogQuery.data, scheduledPlan.id);

  if (!currentOffer || !scheduledOffer) {
    logError("Can't find current/scheduled offer");
    return '';
  }

  const isPeriodicityChange =
    currentOffer &&
    scheduledOffer &&
    currentOffer.period !== scheduledOffer.period;
  const isPlanChange = currentPlan.name !== scheduledPlan.name;
  const isStorageChange =
    currentStorageQuantity !== scheduledChangesStorageQuantity;

  addReasonWithCondition(
    t,
    isPeriodicityChange,
    reasonArray,
    'subscription.details.scheduledChangesReasons.periodicity',
    `<bold>Change periodicity</bold>`,
  );

  addReasonWithCondition(
    t,
    isPlanChange,
    reasonArray,
    'subscription.details.scheduledChangesReasons.offers',
    `Switch to offer <bold>{{planOfferName}}</bold>`,
    {
      planOfferName: t(`subscription.details.name.${scheduledPlan.name}`),
    },
  );

  addReasonWithCondition(
    t,
    isStorageChange,
    reasonArray,
    'subscription.details.scheduledChangesReasons.storage',
    `Reduce extra storage (<bold>{{from currentStorageQty, storageUnit}} to {{storageQty, storageUnit}}</bold>)`,
    {
      currentStorageQty: currentStorageQuantity * STORAGE_SLICE_SIZE_GO,
      storageQty: scheduledChangesStorageQuantity * STORAGE_SLICE_SIZE_GO,
    },
  );

  return reasonArray.join(', ');
};
