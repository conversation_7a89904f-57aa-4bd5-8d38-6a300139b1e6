import styled from '@emotion/styled';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import { isEmpty } from 'lodash';
import { Controller } from 'react-hook-form';
import { theme, Typography } from 'shared-components';
import type { IFormInputProps } from 'types';

const StyledInput = styled(TextField)`
  .MuiOutlinedInput-root {
    input {
      padding: 16px;
      flex: 1;
      font-size: ${theme.typography['body-sm'].fontSize};
      font-weight: ${theme.typography['body-sm'].fontWeight};

      ${theme.breakpoints.up('md')} {
        font-size: ${theme.typography['body-md'].fontSize};
        font-weight: ${theme.typography['body-md'].fontWeight};
      }

      &[type='date']::-webkit-inner-spin-button,
      &[type='date']::-webkit-calendar-picker-indicator {
        cursor: pointer;
      }
    }

    :hover:not(.Mui-focused).Mui-disabled .MuiOutlinedInput-notchedOutline {
      border-color: transparent;
    }

    &:not(.Mui-disabled) {
      background: ${theme.palette.white.main};
      .MuiOutlinedInput-notchedOutline {
        ${({ ...field }) =>
          !isEmpty(field.value) &&
          `background:${theme.palette.secondary.main10};`}
      }
    }
    &.Mui-disabled {
      background: ${theme.palette.background.default};
      border-radius: ${theme.shape.borderRadius}px;
      input {
        -webkit-text-fill-color: ${theme.palette.black.main75};
      }
      .MuiOutlinedInput-notchedOutline {
        border-color: transparent;
      }
    }
  }

  // prevent autofill background color for chrome
  input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 100px #fff inset;
  }

  legend .MuiTypography-root {
    font-size: 12px;
  }
`;

const Label = styled.label<Pick<IFormInputProps, 'disabled'>>`
  display: block;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  ${({ disabled }) =>
    !disabled
      ? `  
      :not(:disabled) {
          cursor: pointer;
      }`
      : ''}
`;

const InputText = ({
  control,
  name,
  label,
  required = false,
  type = 'text',
  onChange,
  multiline,
  placeholder,
  rows,
  disabled,
  InputProps,
  ...props
}: IFormInputProps & TextFieldProps) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <>
          {label && (
            <Label disabled={disabled} htmlFor={name}>
              <Typography variant="body-sm">{label}</Typography>
            </Label>
          )}
          <StyledInput
            {...field}
            required={required}
            disabled={disabled}
            fullWidth
            onChange={e => {
              field.onChange(e);
              onChange && onChange(e.target.value);
            }}
            type={type}
            helperText={error?.message}
            error={!!error}
            InputProps={InputProps}
            multiline={multiline}
            placeholder={placeholder}
            rows={rows}
            {...props}
          />
        </>
      )}
    />
  );
};

export default InputText;
