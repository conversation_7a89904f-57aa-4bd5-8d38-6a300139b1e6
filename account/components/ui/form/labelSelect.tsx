import styled from '@emotion/styled';
import InputLabel from '@mui/material/InputLabel';
import type { ReactNode } from 'react';
import { theme, Typography } from 'shared-components';

import Tooltip from '@/components/ui/tooltip';
interface IFormInputProps {
  id: string;
  text: string;
  tooltipTitle?: ReactNode;
}

const Container = styled(InputLabel)`
  margin-bottom: 8px;
  color: ${theme.palette.black.main};
  display: flex;
  align-items: center;
  overflow: visible;
`;

const ToolTipCSS = styled(Tooltip)`
  margin-left: 4px;
`;

const LabelSelect = ({ id, text, tooltipTitle }: IFormInputProps) => (
  <Container id={id}>
    <Typography variant="label-xs">{text}</Typography>
    {!!tooltipTitle && (
      <ToolTipCSS title={tooltipTitle} placement="right" iconWidth={14} />
    )}
  </Container>
);

export default LabelSelect;
