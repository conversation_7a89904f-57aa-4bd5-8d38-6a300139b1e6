import styled from '@emotion/styled';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { Controller } from 'react-hook-form';
import { Icon, useThemeMediaQueries, Typography } from 'shared-components';
import { Icons } from 'types';
import { FLAG_PATH } from 'utils';

import type { IFormSelectProps } from '@/types/forms';

const ICON_HEIGHT = 7;

const SelectStyled = styled(Select)`
  display: flex;
  height: 55px;

  .MuiSelect-select.MuiSelect-outlined {
    display: flex;
    padding: 0 40px 0 16px;
    align-items: center;
  }
  &.Mui-focused fieldset.MuiOutlinedInput-notchedOutline {
    background: transparent;
  }
`;

const SelectIcon = styled(Icon)`
  position: absolute;
  top: calc(50% - ${ICON_HEIGHT} / 2);
  right: 16px;
`;

const SelectFlag = styled.div`
  display: flex;
  margin: 0 12px 0 0;
  align-items: center;
`;

const InputSelect = ({
  control,
  name,
  label,
  items,
  required = false,
  disabled,
  onChange = () => undefined,
  ...props
}: IFormSelectProps) => {
  const { t } = useTranslation();
  const { isMD } = useThemeMediaQueries();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <SelectStyled
          {...field}
          labelId={label}
          disabled={disabled}
          error={!!error}
          required={required}
          onChange={e => {
            field.onChange(e);
            onChange(e.target.value as string);
          }}
          IconComponent={() => (
            <SelectIcon
              name={Icons.CHEVRON_DOWN}
              width={12}
              height={ICON_HEIGHT}
            />
          )}
          {...props}
        >
          {items.map(item => (
            <MenuItem key={item.value} value={item.value}>
              {item.flag ? (
                <SelectFlag>
                  <Image
                    src={`${FLAG_PATH}${item.flag}.png`}
                    width={36}
                    height={36}
                  />
                </SelectFlag>
              ) : null}
              <Typography variant={isMD ? 'label-md' : 'label-sm'}>
                {item.label ??
                  t(`form.select.${item.value}.label`, { ns: 'common' })}
              </Typography>
            </MenuItem>
          ))}
        </SelectStyled>
      )}
    />
  );
};

export default InputSelect;
