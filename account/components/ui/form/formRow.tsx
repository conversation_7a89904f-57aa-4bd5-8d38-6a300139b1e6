import styled from '@emotion/styled';
import type { ReactNode } from 'react';
import { theme } from 'shared-components';
export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;

  ${theme.breakpoints.up('md')} {
    align-items: flex-start;
    flex-direction: row;
    gap: 24px;
    margin-bottom: 24px;
    flex: 1;
    justify-content: space-between;
  }
`;

interface IFormRowProps {
  children: ReactNode;
}

const FormRow = ({ children }: IFormRowProps) => {
  return <Container>{children}</Container>;
};

export default FormRow;
