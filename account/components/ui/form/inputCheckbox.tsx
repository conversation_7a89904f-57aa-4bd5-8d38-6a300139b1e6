import styled from '@emotion/styled';
import { ChangeEvent, useState } from 'react';
import { Controller } from 'react-hook-form';
import { Checkbox, theme } from 'shared-components';
import { ICheckboxProps } from 'types';

interface IInputCheckboxProps extends Omit<ICheckboxProps, 'data-test-id'> {
  control: any;
  dataTestId: string;
  name: string;
  required?: boolean;
  hasBackground?: boolean;
}

const Wrapper = styled.div<
  Pick<IInputCheckboxProps, 'hasBackground'> & { isChecked?: boolean }
>`
  padding: 8px 0;

  ${({ hasBackground, isChecked }) =>
    hasBackground &&
    `
      padding: 8px 16px;
      background: ${theme.palette.secondary.main10};
      border: 1px solid transparent;
      border-radius: 6px;

      ${
        isChecked &&
        `
          background: ${theme.palette.secondary.main25}; 
          border-color: ${theme.palette.secondary.main25};
        `
      }
    `}
`;

const InputCheckbox = ({
  control,
  id,
  dataTestId,
  name,
  checkboxSize,
  disabled,
  hasBackground,
  label,
}: IInputCheckboxProps) => {
  const [isChecked, setIsChecked] = useState(false);
  const labelColor =
    hasBackground && !isChecked
      ? theme.palette.black.main75
      : theme.palette.black.main;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Wrapper hasBackground={hasBackground} isChecked={isChecked}>
          <Checkbox
            id={id}
            data-test-id={dataTestId}
            value={isChecked}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              field.onChange(e);
              setIsChecked(!isChecked);
            }}
            checkboxSize={checkboxSize ?? 'large'}
            disabled={disabled}
            label={label}
            labelColor={labelColor}
            labelPosition="left"
          />
        </Wrapper>
      )}
    />
  );
};

export default InputCheckbox;
