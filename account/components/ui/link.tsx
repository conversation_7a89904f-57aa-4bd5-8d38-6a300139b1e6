import styled from '@emotion/styled';
import { forwardRef } from 'react';
import { theme, Typography } from 'shared-components';
import { ILinkProps, TypographyRef } from 'types';

const {
  palette: { primary, black, white },
} = theme;

const handleHoverColor = (color?: string) => {
  switch (color) {
    case primary.main:
      return black.main;
    case white.main:
      return white.main;
    default:
      return primary.main;
  }
};

const LinkStyled = styled(Typography)<ILinkProps>`
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  gap: 12px;
  :hover,
  :hover:visited {
    color: ${({ color }) => handleHoverColor(color)};
  }
  :disabled {
    cursor: not-allowed;
    color: ${theme.palette.black.main25};
  }
`;

const Link = forwardRef(
  (
    {
      color,
      variant,
      children,
      component = 'button',
      disabled,
      href,
      target,
      startIcon,
      endIcon,
      ...props
    }: ILinkProps,
    ref,
  ) => (
    <LinkStyled
      component={component}
      disabled={disabled}
      color={color}
      variant={variant}
      href={href}
      target={target}
      ref={ref as TypographyRef}
      {...props}
    >
      {startIcon}
      {children}
      {endIcon}
    </LinkStyled>
  ),
);

Link.displayName = 'Link';

export default Link;
