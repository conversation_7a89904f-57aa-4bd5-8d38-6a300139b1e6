import styled from '@emotion/styled';
import { useRenewalEstimate } from 'hooks';
import { Trans } from 'next-i18next';
import { Typography, theme } from 'shared-components';
import { Currency } from 'types';

import { usePrice } from '@/hooks/usePrice';

const Container = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  border-radius: 4px;
  margin-top: 8px;
  padding: 8px 16px;
  background-color: ${theme.palette.secondary.main10};
`;

interface ISubscriptionDiscountProps {
  subscriptionId: string;
}

const SubscriptionDiscount = ({
  subscriptionId,
}: ISubscriptionDiscountProps) => {
  const { formatPrice } = usePrice();

  const renewalEstimateQuery = useRenewalEstimate(subscriptionId);
  const subscriptionRenewalEstimateData = renewalEstimateQuery.data;

  if (
    renewalEstimateQuery.isLoading ||
    !subscriptionRenewalEstimateData?.invoice_estimate?.discounts
  ) {
    return null;
  }

  const subscriptionDiscounts =
    subscriptionRenewalEstimateData.invoice_estimate.discounts;

  const subscriptionDiscountsAmount = subscriptionDiscounts.reduce(
    (acc, discount) => {
      return acc + discount.amount;
    },
    0,
  );

  const subscriptionAccountCreditsAmount = formatPrice(
    subscriptionDiscountsAmount,
    subscriptionRenewalEstimateData.invoice_estimate.currency_code as Currency,
    0,
  );

  return (
    <Container data-test-id="account-credits-message">
      <Typography variant="body-sm">
        <Trans
          i18nKey={'account.details.creditsMessage'}
          defaults={
            'Following the purchase of your game, a <bold>{{ amount }}</bold> discount will be applied to your next Shadow PC payment.'
          }
          values={{ amount: subscriptionAccountCreditsAmount }}
          components={{ bold: <strong /> }}
        />
      </Typography>
    </Container>
  );
};

export default SubscriptionDiscount;
