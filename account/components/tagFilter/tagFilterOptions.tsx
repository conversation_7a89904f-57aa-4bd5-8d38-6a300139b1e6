import styled from '@emotion/styled';
import React, { SyntheticEvent } from 'react';
import { Checkbox, theme } from 'shared-components';

import { ITagFilterSelect } from './tagFilterSelect';

const Container = styled.div<{ isOpen: boolean }>`
  position: absolute;
  left: 0;
  width: 100%;
  background: ${theme.palette.white.main};
  border: 2px solid ${theme.palette.secondary.main25};
  border-radius: 8px;
  box-sizing: border-box;
  opacity: ${({ isOpen }) => (isOpen ? `1` : '0')};
  scale: ${({ isOpen }) => (isOpen ? `1` : '0')};
  visibility: ${({ isOpen }) => (isOpen ? `visible` : 'hidden')};
  transform-origin: center top;
  transition: opacity 0.2s, scale 0.2s ease-in-out, visibility 0.2s;
  overflow: hidden;
  z-index: 1;
`;

const Wrapper = styled.div`
  max-height: 180px;
  overflow-y: auto;
`;

const Option = styled.div<{ isSelected: boolean }>`
  display: flex;
  padding: 12px 16px;
  align-items: center;
  cursor: pointer;

  :hover {
    background: ${theme.palette.primary.main10};
  }

  ${({ isSelected }) =>
    isSelected && `background: ${theme.palette.secondary.main10}`};
`;

const CheckboxStyled = styled(Checkbox)`
  overflow: hidden;
`;

interface ITagFilterOptions extends ITagFilterSelect {
  isOpen: boolean;
}

const TagFilterOptions = ({
  filterName,
  isOpen,
  options,
  selectedOptions,
  setSelectedOptions,
}: ITagFilterOptions) => {
  const toggleOption = (id: string) => {
    const newSelectedOptions = [...selectedOptions];
    if (newSelectedOptions.includes(id)) {
      setSelectedOptions(newSelectedOptions.filter(item => item !== id));
    } else {
      newSelectedOptions.push(id);
      setSelectedOptions(newSelectedOptions);
    }
  };

  const handleOptionClick = (e: SyntheticEvent, optionId: string) => {
    e.preventDefault();
    toggleOption(optionId);
  };

  return (
    <Container isOpen={isOpen}>
      <Wrapper>
        {options.map((option, index) => {
          return (
            <Option
              key={index}
              onClick={e => handleOptionClick(e, option.id)}
              isSelected={selectedOptions.includes(option.id)}
            >
              <CheckboxStyled
                id={`tag-filter-${filterName}-${option.name}`}
                data-test-id={`tag-filter-${filterName}-${option.name}`}
                label={option.name}
                checked={selectedOptions.includes(option.id)}
                readOnly
              />
            </Option>
          );
        })}
      </Wrapper>
    </Container>
  );
};

export default TagFilterOptions;
