import styled from '@emotion/styled';
import { isEmpty } from 'lodash';
import { useTranslation } from 'next-i18next';
import { Dispatch, SyntheticEvent, useState } from 'react';
import { ClickAwayListener, Icon, theme, Typography } from 'shared-components';
import { Icons, ITag } from 'types';

import TagFilterOptions from './tagFilterOptions';

const Container = styled(ClickAwayListener)`
  position: relative;
  width: 150px;
`;

const Select = styled.button<{
  onClick: () => void;
  hasSelected: boolean;
  isOpen: boolean;
}>`
  display: flex;
  width: 100%;
  box-sizing: border-box;
  padding: 0 16px;
  gap: 8px;
  height: 56px;
  align-items: center;
  background: ${theme.palette.white.main};
  border: 2px solid ${theme.palette.primary.main10};
  border-radius: 8px;
  justify-content: space-between;
  transition: background 0.2s, border 0.2s ease-in-out;

  ${({ hasSelected, isOpen }) =>
    (hasSelected || isOpen) && `background: ${theme.palette.secondary.main10};`}

  ${({ isOpen }) => isOpen && `border-color: ${theme.palette.primary.main};`}

  :hover {
    background: ${theme.palette.secondary.main10};
    border-color: ${theme.palette.primary.main};
  }
`;

const Label = styled(Typography)`
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: space-between;
`;

const DeleteChoices = styled.button`
  padding-inline: 8px;
  :hover {
    color: ${theme.palette.primary.main};
  }
`;

const ChevronDown = styled(Icon, {
  shouldForwardProp: prop => prop !== 'isOpen',
})<{ isOpen: boolean }>`
  ${({ isOpen }) => isOpen && `transform: rotate(180deg)`};
`;

export interface ITagFilterSelect {
  filterName: string;
  options: ITag[];
  selectedOptions: string[];
  setSelectedOptions: Dispatch<string[]>;
}

const TagFilterSelect = ({
  filterName,
  options,
  selectedOptions,
  setSelectedOptions,
}: ITagFilterSelect) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const resetFilter = (e: SyntheticEvent) => {
    e.stopPropagation();
    setSelectedOptions([]);
  };

  return (
    <Container onClickAway={() => setIsOpen(false)}>
      <Select
        data-test-id={`vm-manager-filter-${filterName}`}
        isOpen={isOpen}
        hasSelected={!isEmpty(selectedOptions)}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Label variant="label-md" component="span">
          {t('global.tags', 'Tags', { ns: 'common' })}
          {selectedOptions.length > 0 && (
            <span>
              <Typography variant="label-md" color="primary" component="span">
                ({selectedOptions.length})
              </Typography>
              <DeleteChoices onClick={resetFilter}>
                <Icon name={Icons.CROSS} width={8} />
              </DeleteChoices>
            </span>
          )}
        </Label>
        <ChevronDown name={Icons.CHEVRON_DOWN} width={8} isOpen={isOpen} />
      </Select>
      <TagFilterOptions
        filterName={filterName}
        isOpen={isOpen}
        options={options}
        selectedOptions={selectedOptions}
        setSelectedOptions={setSelectedOptions}
      />
    </Container>
  );
};

export default TagFilterSelect;
