import styled from '@emotion/styled';
import { FC, useState } from 'react';
import { theme } from 'shared-components';
import { NotificationState } from 'types';

import RetryPayment from '@/components/retryPayment/retryPayment';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { useHasUnpaidInvoice } from '@/hooks/user/useUser';
import {
  INFO_BANNER_BACKGROUND_COLORS,
  INFO_BANNER_BORDER_COLORS,
} from '@/utils/constants';

interface IStyledContainerProps {
  status?: NotificationState;
  isLoading?: boolean;
}

const Container = styled('div', {
  shouldForwardProp: prop => !['isLoading', 'status'].includes(prop),
})<IStyledContainerProps>(({ status, isLoading }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: isLoading ? 'center' : 'flex-start',
  marginBottom: '40px',
  padding: '24px',
  background: INFO_BANNER_BACKGROUND_COLORS[status || NotificationState.INFO],
  border: `2px solid ${
    INFO_BANNER_BORDER_COLORS[status || NotificationState.INFO]
  }`,
  borderRadius: theme.shape.borderRadius,
}));

interface IInfoBannerProps {
  isB2b: boolean;
}

const InfoBanner: FC<IInfoBannerProps> = ({ isB2b }) => {
  const paymentDueInfo = useHasUnpaidInvoice();

  const { isCloudPcSubscriptionOnHold, isDriveSubscriptionOnHold } =
    useIsSubscriptionOnHold();
  const [isRetryingPayment, setIsRetryingPayment] = useState(false);
  const [isRetryPaymentSuccess, setIsRetryPaymentSuccess] = useState(false);

  if (
    isCloudPcSubscriptionOnHold ||
    isDriveSubscriptionOnHold ||
    paymentDueInfo.hasUnpaidInvoice ||
    isRetryPaymentSuccess
  ) {
    return (
      <Container
        status={
          isRetryPaymentSuccess
            ? NotificationState.SUCCESS
            : NotificationState.ERROR
        }
        isLoading={isRetryingPayment}
      >
        <RetryPayment
          paymentDueInfo={paymentDueInfo}
          onLoading={(loading: boolean) => setIsRetryingPayment(loading)}
          onShowRetryPaymentSuccess={() => setIsRetryPaymentSuccess(true)}
          isB2b={isB2b}
        />
      </Container>
    );
  }

  return <></>;
};

export default InfoBanner;
