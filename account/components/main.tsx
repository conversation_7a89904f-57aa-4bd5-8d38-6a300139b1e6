import { omit } from 'lodash';
import { useRouter } from 'next/router';
import { ReactNode, useEffect, useState } from 'react';
import { GlobalLoader } from 'shared-components';
import { IMemberDetails, Language, Locale, Market } from 'types';
import { logError, setDatefnsLocaleByLanguage } from 'utils';

import GlobalError from '@/components/ui/errors/globalError';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useSprinklrChatScript } from '@/hooks/sprinklrChat/useSprinklrChatScript';
import { useConfig } from '@/hooks/store/useConfig';
import { useAuthentication } from '@/hooks/user/useAuthentication';
import {
  DEFAULT_LANGUAGE,
  DEFAULT_LOCALE,
  DEFAULT_MARKET,
  ROUTES_PATH,
  UNPROTECTED_ROUTES,
  REDIRECT_KEY,
  MANAGER_URL,
} from '@/utils/constants';
import {
  getFromLocalStorage,
  removeFromLocalStorage,
  saveToLocalStorage,
} from '@/utils/localStorage';

interface IMainProps {
  children: ReactNode;
}
const Main = ({ children }: IMainProps) => {
  const { setLocaleData } = useConfig();
  const router = useRouter();
  const { pathname, route, query, asPath } = router;
  const {
    isAuthenticated,
    isLoading,
    error,
    signinRedirect,
    startSilentRenew,
  } = useAuthentication();
  const [canAuth, setCanAuth] = useState<boolean>(true);
  const [hasAuthError, setHasAuthError] = useState<boolean>(false);
  const [hasSetLocaleData, setHasSetLocaleData] = useState<boolean>(false);
  const [hasCompletedRouteChange, setHasCompletedRouteChange] =
    useState<boolean>(false);
  const currentMemberQuery = useCurrentMember();
  const isUnprotectedRoute = UNPROTECTED_ROUTES.includes(route);

  // Load Sprinklr Chat script, and remove it on dismount
  useSprinklrChatScript();

  useEffect(() => {
    void startSilentRenew();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (
      !isLoading &&
      !isAuthenticated &&
      !error &&
      canAuth &&
      !isUnprotectedRoute
    ) {
      try {
        setCanAuth(false); // prevent infinite loop when auth service is down
        void signinRedirect();
      } catch (e) {
        logError('signinRedirect', e);
        setHasAuthError(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canAuth, error, isAuthenticated, isLoading, signinRedirect]);

  useEffect(() => {
    // saveToStorage on all account url and not IAM
    if (pathname !== '/') {
      saveToLocalStorage(REDIRECT_KEY, {
        asPath: asPath,
        pathname: pathname,
      });
    }
    if (!currentMemberQuery.isLoading) {
      if (currentMemberQuery.isSuccess) {
        const { country, language } = currentMemberQuery.data
          ?.user as IMemberDetails;
        const userCountry = country
          ? (country.toLowerCase() as Market)
          : DEFAULT_MARKET;
        const userLanguage = language
          ? (language as Language)
          : DEFAULT_LANGUAGE;

        const localeKey = `${userLanguage.toUpperCase()}_${userCountry.toUpperCase()}`;
        const userLocale =
          Locale[localeKey as keyof typeof Locale] ?? DEFAULT_LOCALE;

        const storedRoute = getFromLocalStorage(REDIRECT_KEY);
        const hashIndex = storedRoute?.asPath?.indexOf('#');
        const hash =
          hashIndex > -1 ? storedRoute?.asPath?.substring(hashIndex + 1) : null;

        // We update the router if locale has changed
        if (!!country && !!language) {
          const queryWithoutIamParams = omit(query, ['code', 'scope', 'state']);

          void router.replace(
            {
              pathname: storedRoute?.pathname || pathname,
              query: queryWithoutIamParams,
              hash,
            },
            undefined,
            {
              locale: userLocale,
            },
          );
        } else if (storedRoute) {
          void router.replace({
            pathname: storedRoute.pathname || pathname,
            hash: hash,
          });
        }

        removeFromLocalStorage(REDIRECT_KEY);
        setLocaleData(userLocale);
        setDatefnsLocaleByLanguage(userLanguage);
        setHasSetLocaleData(true);

        // Redirections to new manager
        if (!Object.values(ROUTES_PATH).includes(route)) {
          window.location.href = `${MANAGER_URL}`;

          // Cannot use router.push anymore with new manager url : missmatch between account urls and relative redirection
          // TODO : find a way to use this (or not, I don't care, account is going to be deleted)
          // router
          //   .replace(MANAGER_URL as string)
          //   .then(() => setHasCompletedRouteChange(true));
        } else {
          if (![ROUTES_PATH.DRIVE, ROUTES_PATH.DRIVE_GROUPS].includes(route)) {
            let routeToRedirect = `${MANAGER_URL}`;

            switch (route) {
              case ROUTES_PATH.VM_MANAGER:
                routeToRedirect = `${MANAGER_URL}vm/list`;
                break;
              case ROUTES_PATH.MY_PC:
                routeToRedirect = `${MANAGER_URL}vm/list`;
                break;
              case ROUTES_PATH.USER_MANAGER:
                routeToRedirect = `${MANAGER_URL}user/list`;
                break;
              case ROUTES_PATH.ACCOUNT:
                routeToRedirect = `${MANAGER_URL}account/personal`;
                break;
              default:
                routeToRedirect = `${MANAGER_URL}`;
                break;
            }

            window.location.href = routeToRedirect;
            // Cannot use router.push anymore with new manager url : missmatch between account urls and relative redirection
            // TODO : find a way to use this (or not, I don't care, account is going to be deleted)
            // router
            //   .replace(routeToRedirect as string)
            //   .then(() => setHasCompletedRouteChange(true));
          } else {
            setHasCompletedRouteChange(true);
          }
        }
      }
    }
  }, [currentMemberQuery.isLoading, currentMemberQuery.data]);

  if (isUnprotectedRoute) {
    return (
      <>
        <GlobalLoader fullHeight />
        {children}
      </>
    );
  }

  if (
    ((isLoading || error || !isAuthenticated) && canAuth) ||
    (!canAuth && !hasAuthError) ||
    !hasSetLocaleData ||
    !hasCompletedRouteChange
  ) {
    return <GlobalLoader fullHeight />;
  }

  if (!canAuth && hasAuthError) {
    return <GlobalError />;
  }

  return <>{children}</>;
};

export default Main;
