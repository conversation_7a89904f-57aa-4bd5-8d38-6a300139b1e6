import styled from '@emotion/styled';
import Image from 'next/image';
import { ReactNode } from 'react';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';
import { IMAGE_PATH } from 'utils';

const Title = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  padding: 0 32px;

  ${theme.breakpoints.up('lg')} {
    justify-content: flex-start;
  }
`;

interface INavigationTitleProps {
  children: ReactNode;
  icon?: string;
}

const NavigationTitle = ({ children, icon }: INavigationTitleProps) => {
  const { isMD } = useThemeMediaQueries();

  return (
    <>
      <Title>
        {icon && (
          <Image src={`${IMAGE_PATH}${icon}`} width="32px" height="32px" />
        )}
        <Typography
          variant={isMD ? 'heading-h5' : 'label-xl-regular'}
          component="h2"
        >
          {children}
        </Typography>
      </Title>
    </>
  );
};

export default NavigationTitle;
