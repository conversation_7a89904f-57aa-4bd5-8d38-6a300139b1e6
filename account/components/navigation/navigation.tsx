import styled from '@emotion/styled';
import { useRouter } from 'next/router';
import { Fragment, useEffect, useState } from 'react';
import { theme } from 'shared-components';
import { Market, UserRole } from 'types';

import NavigationTitle from './navigationTitle';

import NavigationItem from '@/components/navigation/navigationItem';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useNavigation } from '@/hooks/useNavigation';
import { DEFAULT_MARKET } from '@/utils/constants';

interface INavigationProps {
  closeDrawer?: () => void;
  burger?: boolean;
}

const NavigationElement = styled.div`
  display: grid;
  overflow: hidden;
  gap: 24px;
  padding-top: 24px;
  background: ${theme.palette.white.main};
  border: 1px solid ${theme.palette.primary.main10};
  border-radius: 8px;

  ${theme.breakpoints.up('lg')} {
    gap: 32px;
    padding-top: 32px;
  }
`;

const Container = styled.div`
  display: grid;
  gap: 16px;
`;

const Navigation = ({ closeDrawer }: INavigationProps) => {
  const router = useRouter();
  const currentMemberQuery = useCurrentMember();

  const { country } = currentMemberQuery.data?.user ?? {};
  const userCountry = (
    country ? country.toLowerCase() : DEFAULT_MARKET
  ) as Market;

  const hasAdminRights = [UserRole.ADMIN, UserRole.OWNER].includes(
    currentMemberQuery.data?.role as UserRole,
  );
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  const navigationList = useNavigation({
    hasAdminRights,
    isB2b,
    userCountry,
  });

  const [selectedItemHref, setSelectedItemHref] = useState<string | null>(null);

  useEffect(() => {
    setSelectedItemHref(router.pathname);
  }, [router]);

  const onMenuItemClick = (route: string) => {
    // Cannot use router.push anymore with new manager url : missmatch between account urls and relative redirection
    // TODO : find a way to use this (or not, I don't care, account is going to be deleted)
    // router.push(route);
    window.location.href = route;
    if (closeDrawer) {
      closeDrawer();
    }
  };

  return (
    <NavigationElement>
      {navigationList.map(
        section =>
          section.enabled && (
            <Fragment key={section.id}>
              <Container id={section.id}>
                {section.title && (
                  <NavigationTitle icon={section.icon}>
                    {section.title}
                  </NavigationTitle>
                )}
                <nav aria-label={section.title}>
                  <ul>
                    {section.menuItem.map(
                      item =>
                        item.enabled && (
                          <NavigationItem
                            id={item.id}
                            key={item.id}
                            colorItem={isB2b ? 'black' : 'primary'}
                            label={item.label}
                            selected={selectedItemHref === item.route}
                            onClick={() => {
                              if (item.route) {
                                onMenuItemClick(item.route);
                              }
                            }}
                            href={item.href}
                            icon={item.icon}
                            isExternal={item.external}
                          />
                        ),
                    )}
                  </ul>
                </nav>
              </Container>
            </Fragment>
          ),
      )}
    </NavigationElement>
  );
};

export default Navigation;
