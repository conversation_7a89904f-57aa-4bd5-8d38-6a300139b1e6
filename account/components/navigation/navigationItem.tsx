import styled from '@emotion/styled';
import { Icon, theme, useThemeMediaQueries } from 'shared-components';
import { Icons } from 'types';

import Link from '@/components/ui/link';
import { NAVIGATION_COLORS } from '@/utils/navigation';

interface INavigationItemProps {
  id: string;
  label: string;
  selected: boolean;
  href?: string;
  icon?: Icons;
  colorItem: 'primary' | 'black';
  onClick?: () => void;
  isExternal?: boolean;
}

const Container = styled(Link, {
  shouldForwardProp: prop => prop !== 'colorItem',
})<Pick<INavigationItemProps, 'colorItem' | 'selected'>>`
  ${({ colorItem, selected }) => {
    const { background, textColor } = NAVIGATION_COLORS[colorItem];
    return `
      background: ${background.main};
      color: ${textColor.main};
      
      :hover, :hover:visited {
        background: ${background.hover};
        color: ${textColor.hover};
        svg * {
          stroke: ${textColor.hover};
        }
      }
      
      ${
        selected
          ? `
          background: ${background.selected};
          color: ${textColor.selected};
          svg * {
            stroke: ${textColor.hover};
          }
        `
          : ''
      }
    `;
  }}
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  justify-content: end;
  text-decoration: none;

  ${theme.breakpoints.up('lg')} {
    justify-content: start;
    padding: 16px 40px;
  }

  ${theme.breakpoints.down('lg')} {
    &#DOWNLOAD {
      display: none;
    }
  }
`;

const NavigationItem = ({
  id,
  label,
  selected,
  href,
  icon,
  colorItem,
  onClick,
  isExternal,
}: INavigationItemProps) => {
  const { isLG } = useThemeMediaQueries();

  return (
    <Container
      id={id}
      data-test-id={`navigation_${id}_button`}
      colorItem={colorItem}
      component={href ? 'a' : 'button'}
      endIcon={icon && <Icon name={icon} width={12} height={12} />}
      href={href}
      onClick={onClick}
      target={isExternal ? '_blank' : '_self'}
      selected={selected}
      variant={isLG ? 'body-md' : 'label-sm'}
    >
      {label}
    </Container>
  );
};

export default NavigationItem;
