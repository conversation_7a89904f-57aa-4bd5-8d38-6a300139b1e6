import styled from '@emotion/styled';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  Alert,
  Typography,
  theme,
  useThemeMediaQueries,
} from 'shared-components';
import {
  IMemberDetails,
  NotificationState,
  ProductFamilyId,
  SubscriptionStatus,
} from 'types';
import { computeErrorMessage } from 'utils';

import MemberAssignButton from '@/components/vmDrawer/memberAssign/memberAssignButton';
import MemberAssignInput from '@/components/vmDrawer/memberAssign/memberAssignInput';
import MemberInformation from '@/components/vmDrawer/memberAssign/memberInformation';
import MemberRevokeModal from '@/components/vmDrawer/memberAssign/memberRevokeModal';
import { useAssignMemberToSubscription } from '@/hooks/subscription/useSubscription';
import { IUser } from '@/types/adminManagers';
import type { ApiError } from '@/types/api';

const Heading = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40px 0;
`;

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  gap: 24px;

  ${theme.breakpoints.up('md')} {
    flex-direction: row;
    align-items: center;
    gap: 60px;
  }
`;

interface MemberAssignProps {
  closeDrawer: () => void;
  isVmRunning: boolean;
  subscriptionFamilyId: ProductFamilyId;
  subscriptionId: string;
  subscriptionStatus: SubscriptionStatus;
  member?: IMemberDetails;
}

const MemberAssign = ({
  closeDrawer,
  isVmRunning,
  member,
  subscriptionFamilyId,
  subscriptionId,
  subscriptionStatus,
}: MemberAssignProps) => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();
  const assignMemberToSubscription = useAssignMemberToSubscription();
  const { notifyError, notifySuccess } = useNotifications();

  const [showAssigningInput, setShowAssigningInput] = useState(false);
  const [isAssigningMember, setIsAssigningMember] = useState(false);
  const isAssigningOrRevokingDisabled =
    isVmRunning || subscriptionStatus === SubscriptionStatus.FUTURE;

  const onAssignMemberSuccess = () => {
    notifySuccess(
      `${t(
        'businessManager.vmDetails.user.actions.assignMember.notification.success',
        {
          email: member?.email,
        },
      )}`,
    );
  };

  const onAssignMemberError = (error: string) => {
    notifyError(
      `${t(
        'businessManager.vmDetails.user.actions.assignMember.notification.error',
        {
          email: member?.email,
          error,
        },
      )}`,
    );
  };

  const handleAssignMember = async (selectedMember: IUser) => {
    setIsAssigningMember(true);

    try {
      await assignMemberToSubscription.mutateAsync({
        memberId: selectedMember?.id ?? '',
        subscriptionId: subscriptionId,
      });
      closeDrawer();
      onAssignMemberSuccess();
    } catch (e) {
      let message = computeErrorMessage(e);
      switch ((e as ApiError)?.code) {
        case 409:
          t(
            'businessManager.vmDetails.user.actions.assignMember.server.error',
            'User already assigned to another PC.',
          );
          break;
        case 412:
          message = t('businessManager.vmDetails.user.vmNotStopped');
          break;
      }

      onAssignMemberError(message);
    } finally {
      setIsAssigningMember(false);
    }
  };

  return (
    <>
      <Heading>
        <Typography variant={isSM ? 'body-lg-regular' : 'heading-h6'}>
          {t('businessManager.vmDetails.user.title', 'User')}
        </Typography>
      </Heading>
      {isAssigningOrRevokingDisabled && (
        <Alert type={NotificationState.WARNING}>
          {t(
            'businessManager.vmDetails.user.vmNotStopped',
            'Cannot assign or revoke new users until this PC is stopped.',
          )}
        </Alert>
      )}
      <Wrapper>
        {showAssigningInput ? (
          <MemberAssignInput
            subscriptionFamilyId={subscriptionFamilyId}
            isAssigningMember={isAssigningMember}
            handleAssignMember={handleAssignMember}
          />
        ) : (
          <MemberInformation member={member} />
        )}
        {member ? (
          <MemberRevokeModal
            closeDrawer={closeDrawer}
            member={member}
            isAssigningMember={isAssigningMember}
            setIsAssigningMember={setIsAssigningMember}
            subscriptionId={subscriptionId}
            isDisabled={isAssigningOrRevokingDisabled}
          />
        ) : (
          <MemberAssignButton
            isActive={showAssigningInput}
            isDisabled={isAssigningOrRevokingDisabled}
            onclick={() => setShowAssigningInput(!showAssigningInput)}
          />
        )}
      </Wrapper>
    </>
  );
};

export default MemberAssign;
