import styled from '@emotion/styled';
import { Popper } from '@mui/material';
import Autocomplete, { createFilterOptions } from '@mui/material/Autocomplete';
import { useTranslation } from 'next-i18next';
import { FormEvent, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button, Icon, theme, useThemeMediaQueries } from 'shared-components';
import { Icons, ProductFamilyId } from 'types';

import InputText from '@/components/ui/form/inputText';
import UserCard from '@/components/userCard/userCard';
import { useMembers } from '@/hooks/member/useMember';
import { IUser } from '@/types/adminManagers';
import { UserStatus } from '@/utils/constants';

const Container = styled.form`
  display: flex;
  width: 100%;
  flex-direction: row;
  align-items: stretch;
`;

const AutocompleteStyled = styled(Autocomplete)`
  flex: 1;
  fieldset.MuiOutlinedInput-notchedOutline {
    border-right-color: transparent;
    transition: none;
  }
  & .MuiInputBase-adornedStart {
    padding-left: 16px;
    padding-right: 16px;
    & > .MuiSvgIcon-root {
      margin-right: 16px;
    }
  }
  fieldset.MuiOutlinedInput-notchedOutline {
    border-radius: 8px 0 0 8px;
  }
  .MuiOutlinedInput-root:not(.Mui-disabled) .MuiOutlinedInput-notchedOutline {
    background: transparent;
  }
`;

const CustomPopper = styled(Popper)`
  background: ${theme.palette.white.main};
  border: 2px solid ${theme.palette.primary.main};
  &[data-popper-placement='bottom-start'] {
    border-top: 0;
    border-radius: 0 0 8px 8px;
    top: -11px !important;
    padding-top: 11px;
  }
  &[data-popper-placement='top-start'] {
    border-bottom: 0;
    border-radius: 8px 8px 0 0;
    bottom: -11px !important;
    padding-bottom: 11px;
  }
`;

const CustomPaper = styled.div`
  ul {
    padding: 0;
    .MuiAutocomplete-option {
      padding: 16px;
    }
    .MuiAutocomplete-option.Mui-focused {
      background: ${theme.palette.secondary.main10};
    }
  }
`;

const ButtonStyled = styled(Button)`
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
`;

interface IMemberAssignInputProps {
  isAssigningMember: boolean;
  handleAssignMember: (selectedMember: IUser) => void;
  subscriptionFamilyId: ProductFamilyId;
}

const MemberAssignInput = ({
  isAssigningMember,
  handleAssignMember,
  subscriptionFamilyId,
}: IMemberAssignInputProps) => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();
  const { control } = useForm();

  const [selectedMember, setSelectedMember] = useState<IUser>();
  const useMemberQuery = useMembers(subscriptionFamilyId);
  const [memberOptions, setMemberOptions] = useState<IUser[]>([]);

  const handleFormSubmit = (e: FormEvent) => {
    e.preventDefault();
  };

  useEffect(() => {
    if (
      useMemberQuery.isSuccess &&
      useMemberQuery.data &&
      Array.isArray(useMemberQuery.data)
    ) {
      setMemberOptions(
        useMemberQuery.data
          .filter(
            data =>
              data.status === UserStatus.ACTIVE &&
              !data.assigned_vm_product_families.includes(
                ProductFamilyId.CLOUDPC,
              ),
          )
          .map(
            ({ status, user, invite_email, invite_created_on }) =>
              ({
                status,
                id: user?.id || invite_email,
                email: user?.email || invite_email,
                firstName: user?.first_name || '',
                lastName: user?.last_name || '',
                createdAt: invite_created_on || user?.created_at,
              } as IUser),
          ),
      );
    }
  }, [useMemberQuery.data, useMemberQuery.isSuccess]);

  const filterOptions = createFilterOptions({
    stringify: option =>
      `${(option as IUser).firstName} ${(option as IUser).lastName} ${
        (option as IUser).email
      }`,
  });

  return (
    <Container onSubmit={handleFormSubmit}>
      <AutocompleteStyled
        openOnFocus
        id="member-assign"
        options={memberOptions}
        blurOnSelect
        disablePortal
        disableClearable
        loading={useMemberQuery.isLoading}
        filterOptions={filterOptions}
        popupIcon={false}
        onChange={(event, value) => {
          setSelectedMember(value as IUser);
        }}
        PopperComponent={props => (
          <CustomPopper
            {...props}
            placeholder={null}
            placement="bottom-start"
          />
        )}
        PaperComponent={CustomPaper}
        isOptionEqualToValue={(option, value) =>
          (option as IUser).id === (value as IUser).id
        }
        getOptionLabel={option => (option as IUser).email}
        renderOption={(props, option) => (
          <li {...props} key={(option as IUser).id} data-test-id={props.id}>
            <UserCard data={option as IUser} />
          </li>
        )}
        renderInput={params => (
          <InputText
            {...params}
            name="member-assign"
            control={control}
            placeholder={t(
              'businessManager.vmDetails.user.assignUserPlaceholder',
              'Search a user...',
            )}
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <Icon
                  name={Icons.MAGNIFIER}
                  color={theme.palette.black.main75}
                  width={24}
                  height={24}
                />
              ),
            }}
          />
        )}
      />
      <ButtonStyled
        data-test-id="memberAssignInputButton"
        size={isSM ? 'small' : 'medium'}
        disabled={!selectedMember}
        color="black"
        loading={isAssigningMember}
        onClick={() => handleAssignMember(selectedMember as IUser)}
      >
        {t('businessManager.vmDetails.user.assignUser', 'Assign')}
      </ButtonStyled>
    </Container>
  );
};

export default MemberAssignInput;
