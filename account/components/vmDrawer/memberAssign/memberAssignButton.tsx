import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Button, Icon } from 'shared-components';
import { Icons } from 'types';

interface IMemberActionProps {
  isActive: boolean;
  isDisabled: boolean;
  onclick: () => void;
}

const ButtonStyled = styled(Button)`
  flex-shrink: 0;
`;

const MemberAssignButton = ({
  isActive,
  isDisabled,
  onclick,
}: IMemberActionProps) => {
  const { t } = useTranslation();

  return (
    <>
      {!isActive ? (
        <ButtonStyled
          data-test-id="memberAssignButton"
          onClick={onclick}
          disabled={isDisabled}
          color="secondary"
          size="small"
          startIcon={<Icon name={Icons.USER_PLUS} width={16} height={16} />}
        >
          {t('businessManager.vmDetails.user.assignUser', 'Assign')}
        </ButtonStyled>
      ) : (
        <ButtonStyled
          onClick={onclick}
          color="error"
          size="small"
          startIcon={<Icon name={Icons.CROSS} width={8} height={8} />}
        >
          {t('global.cancel', 'Cancel', { ns: 'common' })}
        </ButtonStyled>
      )}
    </>
  );
};

export default MemberAssignButton;
