import { useTranslation } from 'next-i18next';
import { Typography } from 'shared-components';

import UserCard from '@/components/userCard/userCard';
import { IVmManager } from '@/types/adminManagers';

const MemberInformation = ({ member }: Pick<IVmManager, 'member'>) => {
  const { t } = useTranslation();

  return (
    <>
      {member ? (
        <UserCard
          data={{
            email: member.email,
            firstName: member.first_name || '',
            lastName: member.last_name || '',
            /* todo devFront: get role of current member associate to vm */
            role: '',
          }}
        />
      ) : (
        <Typography variant="body-xs">
          {t(
            'businessManager.vmDetails.user.noUser',
            'No user is assigned to this Shadow PC Pro.',
          )}
        </Typography>
      )}
    </>
  );
};

export default MemberInformation;
