import styled from '@emotion/styled';
import { useNotifications } from 'hooks';
import { Trans, useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  Button,
  Icon,
  Modal,
  ModalButton,
  ModalLink,
  Typography,
} from 'shared-components';
import { Icons, IMemberDetails } from 'types';
import { computeErrorMessage } from 'utils';

import { useRevokeMemberFromSubscription } from '@/hooks/subscription/useSubscription';
import { ApiError } from '@/types/api';

interface IMemberRevokeButtonProps {
  closeDrawer: () => void;
  isAssigningMember: boolean;
  member: IMemberDetails;
  setIsAssigningMember: (value: boolean) => void;
  subscriptionId: string;
  isDisabled?: boolean;
}

const ButtonStyled = styled(Button)`
  flex-shrink: 0;
`;

const MemberRevokeModal = ({
  closeDrawer,
  isAssigningMember,
  isDisabled,
  member,
  setIsAssigningMember,
  subscriptionId,
}: IMemberRevokeButtonProps) => {
  const { t } = useTranslation();
  const revokeMemberFromSubscription = useRevokeMemberFromSubscription();
  const { notifyError, notifySuccess } = useNotifications();

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleClick = () => {
    setIsModalOpen(!isModalOpen);
  };

  /** ACTION NOTIFICATIONS */
  const onRevokeMemberSuccess = () => {
    handleClick();
    notifySuccess(
      `${t(
        'businessManager.vmDetails.user.actions.revokeMember.notification.success',
        {
          email: member?.email,
        },
      )}`,
    );
  };

  const onRevokeMemberError = (error: string) => {
    handleClick();
    notifyError(
      `${t(
        'businessManager.vmDetails.user.actions.revokeMember.notification.error',
        {
          email: member?.email,
          error,
        },
      )}`,
    );
  };

  /** ACTION HANDLER */
  const handleRevokeMember = async () => {
    setIsAssigningMember(true);

    try {
      await revokeMemberFromSubscription.mutateAsync({
        memberId: member.id ?? '',
        subscriptionId,
      });
      closeDrawer();
      onRevokeMemberSuccess();
    } catch (e) {
      let message = computeErrorMessage(e);
      switch ((e as ApiError)?.code) {
        case 412:
          message = t('businessManager.vmDetails.user.vmNotStopped');
          break;
      }
      onRevokeMemberError(message);
    } finally {
      setIsAssigningMember(false);
    }
  };

  return (
    <>
      <ButtonStyled
        data-test-id="memberRevokeButton"
        color="secondary"
        size="small"
        onClick={handleClick}
        loading={isAssigningMember}
        startIcon={<Icon name={Icons.USER_MINUS} width={16} height={16} />}
        disabled={isDisabled}
      >
        {t(
          'businessManager.vmDetails.user.revokeMember.buttonLabel',
          'Revoke access',
        )}
      </ButtonStyled>
      <Modal
        open={isModalOpen}
        onClose={handleClick}
        aria-labelledby="Revoke-access-dialog-title"
        aria-describedby="Revoke-access-dialog-description"
        title={t(
          'businessManager.vmDetails.user.revokeMember.modal.title',
          'Revoke access',
        )}
      >
        <Typography align="center" variant="body-sm">
          <Trans i18nKey="businessManager.vmDetails.user.revokeMember.modal.content">
            The user will not have access to this Shadow PC anymore, but will
            still remains in your users list.
            <br />
            <br />
            The Shadow PC will not be deleted.
          </Trans>
        </Typography>
        <ModalButton
          data-test-id="memberRevokeModalButton"
          loading={isAssigningMember}
          onClick={handleRevokeMember}
          color={member.b2b ? 'black' : 'primary'}
        >
          {t('global.revoke', 'Revoke', { ns: 'common' })}
        </ModalButton>
        <ModalLink onClick={handleClick}>
          {t('global.cancel', 'Cancel', { ns: 'common' })}
        </ModalLink>
      </Modal>
    </>
  );
};

export default MemberRevokeModal;
