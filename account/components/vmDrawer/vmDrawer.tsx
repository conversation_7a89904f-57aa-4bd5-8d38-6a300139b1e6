import styled from '@emotion/styled';
import { LocalLoader, theme } from 'shared-components';
import { SubscriptionStatus } from 'types';

import VmHeading from './vmHeading/vmHeading';
import VmTagManager from './vmTagManager/vmTagManager';

import Drawer from '@/components/ui/drawer/drawer';
import MemberAssign from '@/components/vmDrawer/memberAssign/memberAssign';
import VmDetail from '@/components/vmDrawer/vmDetail/vmDetail';
import { useCatalog } from '@/hooks/useCatalog';
import { IVmManager } from '@/types/adminManagers';

interface IVmDrawerProps {
  handleClose: () => void;
  open: boolean;
  vmData?: IVmManager;
}

const Divider = styled.div`
  margin: 0 -40px 0px;
  border-top: 1px solid ${theme.palette.secondary.main25};

  ${theme.breakpoints.up('lg')} {
    margin: 0 -64px 0px;
  }
`;

const DrawerStyled = styled(Drawer)`
  padding: 40px;
`;

const VmDrawer = ({ handleClose, open, vmData }: IVmDrawerProps) => {
  const catalogQuery = useCatalog();
  const { data: catalog, isLoading } = catalogQuery;

  return (
    <DrawerStyled anchor="right" open={open} handleClose={handleClose}>
      {isLoading ? (
        <LocalLoader />
      ) : (
        vmData &&
        open &&
        catalog && (
          <>
            <VmHeading closeDrawer={handleClose} vm={vmData} />
            <Divider />
            <MemberAssign
              closeDrawer={handleClose}
              member={vmData.user}
              isVmRunning={vmData.isVmRunning}
              subscriptionId={vmData.id}
              subscriptionFamilyId={vmData.subscription.product_family_id}
              subscriptionStatus={
                vmData.subscription.status as SubscriptionStatus
              }
            />
            <Divider />
            <VmDetail catalog={catalog} closeDrawer={handleClose} vm={vmData} />
            <Divider />
            <VmTagManager vm={vmData} />
          </>
        )
      )}
    </DrawerStyled>
  );
};

export default VmDrawer;
