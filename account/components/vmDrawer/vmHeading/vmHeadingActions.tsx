import styled from '@emotion/styled';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import React, { useState } from 'react';
import { ClickTracker, Icon, theme } from 'shared-components';
import { Icons, SubscriptionStatus } from 'types';

import CancelVmButton from '@/components/subscription/actions/cancelVm/cancelVmButton';
import ChangeVmPlan from '@/components/subscription/actions/changeVmPlan/changeVmPlan';
import ReactivateVmButton from '@/components/subscription/actions/reactivateVm/reactivateVmButton';
import ButtonPopover from '@/components/ui/buttonPopover';
import Link from '@/components/ui/link';
import { VmStatusDisplay } from '@/components/vmStatusDisplay/vmStatusDisplay';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { IVmManager } from '@/types/adminManagers';
import { SHOP_URL } from '@/utils/constants';

const Actions = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  flex: 1;
`;

interface IHeadingActionsProps {
  vmId: string;
  subscription: IVmManager['subscription'];
  setIsResetModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsCancelModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsReactivateModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onVmName: () => void;
  onStopVm: () => void;
  isVmRunning: boolean;
  isVmInMaintenanceMode: boolean;
  isVmOnHold: boolean;
  subscriptionStatus: SubscriptionStatus;
  isStopVmEnabled: boolean;
  isStopVmDisabled: boolean;
  isClonable: boolean;
}

const VmHeadingActions = ({
  vmId,
  subscription,
  setIsResetModalOpen,
  setIsCancelModalOpen,
  setIsReactivateModalOpen,
  onVmName,
  onStopVm,
  isVmRunning,
  isVmInMaintenanceMode,
  isVmOnHold,
  isStopVmEnabled,
  subscriptionStatus,
  isStopVmDisabled,
  isClonable,
}: IHeadingActionsProps) => {
  const { t } = useTranslation();
  const router = useRouter();

  const [isVmActionsPopoverOpen, setIsVmActionsPopoverOpen] =
    useState<boolean>(false);

  const currentMemberQuery = useCurrentMember();
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  const { isCloudPcSubscriptionOnHold } = useIsSubscriptionOnHold();

  const handleConfigurationButtonClick = () => {
    if (isB2b) {
      window.location.href = `${SHOP_URL}?funnel=b2b_update_storage&subscription=${subscription.id}`;
    } else {
      window.location.href = `${SHOP_URL}?funnel=update_storage&subscription=${subscription?.id}`;
    }
  };
  const handleDuplicateVm = () => {
    if (isClonable) {
      setIsVmActionsPopoverOpen(false);
      router.push(`/redirect/shop/business/catalog/${subscription.id}`);
    }
  };
  const isSubscriptionActive =
    subscription?.status === SubscriptionStatus.ACTIVE;

  const displayChangePlan =
    isSubscriptionActive && !isCloudPcSubscriptionOnHold;

  const handleCloneText = () => {
    if (!isClonable) {
      return t(
        'businessManager.vmDetails.vm.CannotClone',
        'Cannot clone this VM',
      );
    }
    return t('businessManager.vmDetails.vm.clone', 'Clone this VM');
  };

  return (
    <Actions>
      <VmStatusDisplay
        subscriptionStatus={subscriptionStatus}
        isVmRunning={isVmRunning}
        isVmInMaintenanceMode={isVmInMaintenanceMode}
        isVmOnHold={isVmOnHold}
        isInTable={false}
      />
      <ButtonPopover
        dataTestId="businessManager-vmDetails-configuration-edit"
        icon={<Icon name={Icons.SETTINGS} width={16} height={16} />}
        name={`vmDetails-more ${vmId}`}
        isOpen={isVmActionsPopoverOpen}
        setIsOpen={setIsVmActionsPopoverOpen}
        hasBackground={true}
      >
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_modify_subscription',
              event_label: 'shadow_rename',
            },
          }}
        >
          <Link
            data-test-id="rename-vm"
            component="button"
            variant="label-sm-regular"
            color={theme.palette.black.main}
            onClick={() => {
              onVmName();
              setIsVmActionsPopoverOpen(false);
            }}
            startIcon={<Icon name={Icons.EDIT} width={16} height={16} />}
          >
            {t('businessManager.vmDetails.vm.renameVm', 'Rename this PC')}
          </Link>
        </ClickTracker>
        {isSubscriptionActive && (
          <ClickTracker
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'my_account_modify_subscription',
                event_label: 'update_storage',
              },
            }}
          >
            <Link
              data-test-id="update-vm-storage"
              component="button"
              variant="label-sm-regular"
              color={theme.palette.black.main}
              onClick={() => {
                handleConfigurationButtonClick();
                setIsVmActionsPopoverOpen(false);
              }}
              startIcon={<Icon name={Icons.SETTINGS} width={16} height={16} />}
            >
              {t('businessManager.vmDetails.vm.configuration', 'Configuration')}
            </Link>
          </ClickTracker>
        )}
        {displayChangePlan && <ChangeVmPlan subscription={subscription} />}
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_modify_subscription',
              event_label: 'clone_shadow',
            },
          }}
        >
          <Link
            data-test-id="clone-vm"
            component="button"
            variant="label-sm-regular"
            disabled={!isClonable}
            onClick={handleDuplicateVm}
            startIcon={<Icon name={Icons.COPY} width={16} height={16} />}
          >
            {handleCloneText()}
          </Link>
        </ClickTracker>
        {isStopVmEnabled && (
          <ClickTracker
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'my_account_modify_subscription',
                event_label: 'stop_shadow',
              },
            }}
          >
            <Link
              data-test-id="stop-vm"
              component="button"
              variant="label-sm-regular"
              color={theme.palette.black.main}
              disabled={isStopVmDisabled}
              onClick={() => {
                onStopVm();
                setIsVmActionsPopoverOpen(false);
              }}
              startIcon={<Icon name={Icons.STOP} width={16} height={16} />}
            >
              {t('businessManager.vmDetails.vm.stopVm', 'Stop this PC')}
            </Link>
          </ClickTracker>
        )}
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_modify_subscription',
              event_label: 'reset_my_shadow',
            },
          }}
        >
          <Link
            data-test-id="reset-vm"
            component="button"
            variant="label-sm-regular"
            color={theme.palette.black.main}
            onClick={() => {
              setIsVmActionsPopoverOpen(false);
              setIsResetModalOpen(true);
            }}
            startIcon={
              <Icon name={Icons.ROTATE_RIGHT} width={16} height={16} />
            }
          >
            {t('businessManager.vmDetails.vm.resetVm', 'Reset this PC')}
          </Link>
        </ClickTracker>
        {/* Cancel subscription button */}
        <CancelVmButton
          subscription={subscription}
          setIsCancelModalOpen={setIsCancelModalOpen}
          buttonVersion="manager"
          onClick={() => setIsVmActionsPopoverOpen(false)}
        />
        {subscription.status === SubscriptionStatus.NON_RENEWING && (
          <ReactivateVmButton
            setIsReactivateModalOpen={setIsReactivateModalOpen}
            onClick={() => setIsVmActionsPopoverOpen(false)}
          />
        )}
      </ButtonPopover>
    </Actions>
  );
};

export default VmHeadingActions;
