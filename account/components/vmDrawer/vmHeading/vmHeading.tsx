import styled from '@emotion/styled';
import { useClonableSubscription, useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { SubscriptionStatus } from 'types';
import { logError } from 'utils';

import CancelVmModalWithRetention from '@/components/subscription/actions/cancelVm/cancelVmModalWithRetention';
import ReactivateVmModal from '@/components/subscription/actions/reactivateVm/reactivateVmModal';
import ResetVmModal from '@/components/subscription/actions/resetVm/resetVmModal';
import VmNameModal from '@/components/subscription/actions/vmName/VmNameModal';
import VmHeadingActions from '@/components/vmDrawer/vmHeading/vmHeadingActions';
import VmHeadingInformation from '@/components/vmDrawer/vmHeading/vmHeadingInformation';
import { useVmName } from '@/hooks/subscription/useSubscription';
import { useVmStop } from '@/hooks/vm/useVdi';
import { IVmManager } from '@/types/adminManagers';

const VmHeadingWrapper = styled.div`
  display: grid;
  grid-template-columns: 15px minmax(20%, auto) auto;
  gap: 16px;
  margin-bottom: 40px;
`;

interface IVmHeadingProps {
  closeDrawer: () => void;
  vm: IVmManager;
}

const VmHeading = ({ closeDrawer, vm }: IVmHeadingProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();

  const vmName = useVmName();
  const vmStop = useVmStop();

  const [isNameModalOpen, setIsNameModalOpen] = useState(false);
  const [isResetModalOpen, setIsResetModalOpen] = useState(false);
  const [isStoppingVm, setIsStoppingVm] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isReactivateModalOpen, setIsReactivateModalOpen] = useState(false);

  const { data: clonableSubscriptionData } = useClonableSubscription();

  const isClonable =
    clonableSubscriptionData?.some(clone => clone.uid === vm?.id) || false;

  const onRenameVm = async (name: string) => {
    try {
      await vmName.mutateAsync({
        subscriptionId: vm.id,
        name,
      });

      onRenameVmSuccess();
      setIsNameModalOpen(false);
      closeDrawer();
    } catch (e) {
      onRenameVmError();
      logError('onRevmName', e);
    }
  };

  const onStopVm = async (id: string) => {
    setIsStoppingVm(true);

    try {
      await vmStop.mutateAsync(id);

      onStopVmSuccess();
    } catch (e) {
      onStopVmError();
      logError('onStopVm', e);
    } finally {
      setIsStoppingVm(false);
    }
  };

  const onRenameVmSuccess = () => {
    notifySuccess(
      t(
        'subscription.vmName.notification.success',
        "PC's name changed successfully",
      ),
    );
  };

  const onRenameVmError = () => {
    notifyError(
      t(
        'subscription.vmName.notification.error',
        "There was an error changing this PC's name. Please try again later.",
      ),
    );
  };

  const onStopVmSuccess = () => {
    notifySuccess(
      t(
        'subscription.stopVm.notification.success',
        'You have successfully triggered a stop of your PC. This process will take around a few minutes.',
      ),
    );
  };

  const onStopVmError = () => {
    notifyError(
      t(
        'subscription.stopVm.notification.error',
        'There was an error when trying to stop your PC, please try again later or contact support.',
      ),
    );
  };

  const onResetVmSuccess = () => {
    setIsResetModalOpen(false);
    notifySuccess(
      t(
        'subscription.resetVm.notification.success',
        'You have successfully triggered a reset of your PC. This process will take around 1 hour.',
      ),
    );
    closeDrawer();
  };
  const onResetVmError = () => {
    setIsResetModalOpen(false);
    notifyError(
      t(
        'subscription.resetVm.notification.error',
        'There was an error when trying to reset your PC, please try again later or contact support.',
      ),
    );
  };

  const onReactivateVmSuccess = () => {
    setIsReactivateModalOpen(false);
    notifySuccess(
      t(
        'subscription.reactivateVm.notification.success',
        'You have successfully reactivated your subscription.',
      ),
    );
  };
  const onReactivateVmError = () => {
    setIsReactivateModalOpen(false);
    notifyError(
      t(
        'subscription.reactivateVm.notification.error',
        'We could not reactivate your subscription, please try again later.',
      ),
    );
  };

  return (
    <>
      <VmHeadingWrapper>
        <VmHeadingInformation
          closeDrawer={closeDrawer}
          vmId={vm.id}
          vmName={vm.name}
          vmType={vm.type}
        />
        <VmHeadingActions
          vmId={vm.id}
          subscription={vm.subscription}
          setIsResetModalOpen={setIsResetModalOpen}
          setIsCancelModalOpen={setIsCancelModalOpen}
          setIsReactivateModalOpen={setIsReactivateModalOpen}
          onVmName={() => setIsNameModalOpen(true)}
          onStopVm={() => onStopVm(vm.id)}
          isVmRunning={vm.isVmRunning}
          isVmInMaintenanceMode={vm.isVmInMaintenanceMode}
          isVmOnHold={vm.isVmOnHold}
          subscriptionStatus={vm.subscription.status as SubscriptionStatus}
          isStopVmEnabled={vm.isVmRunning}
          isStopVmDisabled={isStoppingVm}
          isClonable={isClonable}
        />
      </VmHeadingWrapper>

      <VmNameModal
        isOpen={isNameModalOpen}
        handleClose={() => setIsNameModalOpen(false)}
        onRenameVm={onRenameVm}
        vmName={vm.name ?? ''}
      />

      <ResetVmModal
        isOpen={isResetModalOpen}
        handleClose={() => setIsResetModalOpen(false)}
        subscriptionId={vm.id}
        onResetVmSuccess={onResetVmSuccess}
        onResetVmError={onResetVmError}
      />

      <CancelVmModalWithRetention
        isCancelModalOpen={isCancelModalOpen}
        setIsCancelModalOpen={setIsCancelModalOpen}
        subscription={vm.subscription}
      />

      <ReactivateVmModal
        isOpen={isReactivateModalOpen}
        handleClose={() => setIsReactivateModalOpen(false)}
        subscriptionId={vm.subscription?.id ?? ''}
        onReactivateVmSuccess={onReactivateVmSuccess}
        onReactivateVmError={onReactivateVmError}
      />
    </>
  );
};

export default VmHeading;
