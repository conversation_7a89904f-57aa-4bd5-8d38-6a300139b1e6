import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Chip, Icon, theme, Typography } from 'shared-components';
import { Icons, ProductId } from 'types';

const ButtonStyled = styled.button`
  :hover {
    color: ${theme.palette.primary.main};
  }

  svg {
    display: block;
  }
`;

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 1;
`;

const WrapperId = styled.div`
  display: flex;
  align-items: center;
`;

const CopyWrapper = styled.button`
  position: relative;
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  cursor: pointer;
  gap: 8px;

  :hover {
    p {
      color: ${theme.palette.primary.main};
    }
    color: ${theme.palette.primary.main};
  }
`;

const CopyInfo = styled(Chip, {
  shouldForwardProp: prop => prop !== 'showCopyInfo',
})<{ showCopyInfo: boolean }>`
  position: absolute;
  right: -80px;
  opacity: ${({ showCopyInfo }) => (showCopyInfo ? `1` : '0')};
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
`;

const VmName = styled(Typography)`
  overflow-x: clip;
  text-overflow: ellipsis;
  white-space: wrap;
`;

interface IHeadingInformationProps {
  closeDrawer: () => void;
  vmId: string;
  vmType: ProductId;
  vmName: string | undefined;
}

const VmHeadingInformation = ({
  closeDrawer,
  vmId,
  vmType,
  vmName,
}: IHeadingInformationProps) => {
  const { t } = useTranslation();
  const [showCopyInfo, setShowCopyInfo] = useState<boolean>(false);

  const showInfo = () => {
    setShowCopyInfo(true);
    setTimeout(() => {
      setShowCopyInfo(false);
    }, 1500);
  };

  return (
    <>
      <ButtonStyled type="button" onClick={closeDrawer}>
        <Icon name={Icons.CROSS} width={12} height={12} />
      </ButtonStyled>
      <Wrapper>
        <VmName variant="heading-h6">
          {vmName !== null ? vmName : t(`subscription.details.name.${vmType}`)}
        </VmName>
        <WrapperId>
          <Typography variant="label-xs-light">
            {t('businessManager.vmDetails.vm.id', 'ID ')}
          </Typography>
          <CopyWrapper
            onClick={() => {
              navigator.clipboard.writeText(vmId);
              showInfo();
            }}
          >
            <Typography noWrap variant="label-xs-light">
              : {vmId}
            </Typography>
            <Icon name={Icons.COPY} width={12} height={12} />
            <CopyInfo
              showCopyInfo={showCopyInfo}
              variant="label-sm"
              backgroundColor={theme.palette.primary.main10}
              textColor={theme.palette.primary.main}
            >
              {t('businessManager.vmDetails.vm.copy', 'Copied')}
            </CopyInfo>
          </CopyWrapper>
        </WrapperId>
      </Wrapper>
    </>
  );
};

export default VmHeadingInformation;
