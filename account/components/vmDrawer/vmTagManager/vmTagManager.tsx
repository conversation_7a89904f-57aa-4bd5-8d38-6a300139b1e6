import styled from '@emotion/styled';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  LocalLoader,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { ITag } from 'types';
import { logError } from 'utils';

import AddVmTagsInput from '@/components/forms/tagsForm/addVmTagsInput';
import { useCreateTag, useCurrentMember } from '@/hooks/member/useMember';
import { useVmTags } from '@/hooks/subscription/useSubscription';
import { IVmManager } from '@/types/adminManagers';

const Heading = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40px 0;
`;

interface IVmTagManagerProps {
  vm: IVmManager;
}

const VmTagManager = ({ vm }: IVmTagManagerProps) => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();
  const { notifyError, notifySuccess } = useNotifications();
  const currentMemberQuery = useCurrentMember();

  const vmTags = useVmTags();
  const createTag = useCreateTag();

  const availableTags = currentMemberQuery.data?.available_tags as ITag[];
  const defaultAppliedTags = vm.subscription.tags; // this data is stored in a state in a parent component and does not refresh automatically.
  const [appliedTags, setAppliedTags] = useState(defaultAppliedTags);

  const onSuccess = () => {
    notifySuccess(
      t(
        'subscription.vmTags.notification.success',
        'You have successfully set tags your subscription.',
      ),
    );
  };

  const onError = () => {
    notifyError(
      t(
        'subscription.vmTags.notification.error',
        'There was an error when trying to set tags to your subscription, please try again later or contact support.',
      ),
    );
  };

  const saveAllTagsToVm = async (tags: ITag[], shouldRefetch: boolean) => {
    try {
      await vmTags.mutateAsync({
        subscriptionId: vm.id,
        tags,
      });

      if (shouldRefetch) {
        currentMemberQuery.refetch();
      }

      setAppliedTags(tags);
      onSuccess();
    } catch (e) {
      onError();
      logError('saveAllAppliedTagsToVm', e);
    }
  };

  const createNewTag = async (newTag: string) => {
    try {
      const updatedAvailableTags = await createTag.mutateAsync(newTag);
      if (updatedAvailableTags && updatedAvailableTags.length) {
        const newlyCreatedTag = updatedAvailableTags[0];
        return newlyCreatedTag;
      }
    } catch (e) {
      onError();
      logError('createNewTag', e);
    }
  };

  // Event triggered when the user selects an existing or new option.
  const onSaveConfirm = async (newTagNameValue: string) => {
    const isTagAlreadyAppliedToVm = appliedTags.some(
      tag => tag.name === newTagNameValue,
    );

    if (!newTagNameValue || isTagAlreadyAppliedToVm) {
      return;
    }

    const availableAndUnappliedTag = availableTags
      .filter(availableTag => !appliedTags.includes(availableTag))
      .find(tag => tag.name === newTagNameValue);
    const shouldCreateTag = !availableAndUnappliedTag;

    let newTagToSaveToVm: ITag | undefined;

    if (shouldCreateTag) {
      const newlyCreatedTag = await createNewTag(newTagNameValue);
      if (newlyCreatedTag) {
        newTagToSaveToVm = newlyCreatedTag;
      }
    } else {
      newTagToSaveToVm = availableAndUnappliedTag;
    }

    if (newTagToSaveToVm) {
      await saveAllTagsToVm([...appliedTags, newTagToSaveToVm], false);
    } else {
      onError();
      logError('onConfirm: newTagToSaveToVm is undefined');
    }
  };

  // Event triggered when the user delete a single tag
  const onDeleteSingleTag = async (tagToRemove: ITag) => {
    const newAppliedTags = appliedTags.filter(
      appliedTag => appliedTag !== tagToRemove,
    );

    saveAllTagsToVm(newAppliedTags, true);
  };

  // Event triggered when the user deletes all tags
  const onDeleteAllTags = async () => {
    saveAllTagsToVm([], true);
  };

  if (currentMemberQuery.isLoading) {
    return <LocalLoader />;
  }

  return (
    <>
      <Heading>
        <Typography variant={isSM ? 'body-lg-regular' : 'heading-h6'}>
          {t('businessManager.vmTagManager.title', 'Tags')}
        </Typography>
      </Heading>
      <AddVmTagsInput
        isSubmittingTags={vmTags.isLoading}
        availableTags={availableTags}
        appliedTags={appliedTags}
        onSaveConfirm={onSaveConfirm}
        onDeleteSingleTag={onDeleteSingleTag}
        onDeleteAllTags={onDeleteAllTags}
      />
    </>
  );
};

export default VmTagManager;
