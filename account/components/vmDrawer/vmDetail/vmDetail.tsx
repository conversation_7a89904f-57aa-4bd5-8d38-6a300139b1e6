import styled from '@emotion/styled';
import { useDatacenterList } from 'hooks';
import { useTranslation } from 'next-i18next';
import { theme } from 'shared-components';
import {
  AddonType,
  ICatalog,
  Icons,
  ProductType,
  SubscriptionStatus,
} from 'types';
import {
  formatDate,
  DATE_FORMAT_WITH_SLASH_BY_MARKET,
  INTERNAL_STORAGE_SIZE_GO,
  STORAGE_SLICE_SIZE_GO,
  getDatacenterName,
  getProductFromOfferId,
  FLAG_PATH,
} from 'utils';

import VmDetailCategory from '@/components/vmDrawer/vmDetail/vmDetailCategory';
import { useConfig } from '@/hooks/store/useConfig';
import { IVmManager } from '@/types/adminManagers';
import { getDatacenterByName } from '@/utils/datacenter';

const VmDetailCategoryList = styled.ul`
  display: grid;
  grid-template-columns: 1fr;
  flex: 1;
  gap: 40px;
  margin: 40px 0;

  ${theme.breakpoints.up('md')} {
    grid-template-columns: 1fr 1fr;
  }
`;

interface IVmDetailProps {
  catalog: ICatalog;
  closeDrawer: () => void;
  vm: IVmManager;
}

const VmDetail = ({ catalog, vm }: IVmDetailProps) => {
  const { t } = useTranslation();
  const { market } = useConfig();
  const datacenterListQuery = useDatacenterList();
  const vmDatacenter = getDatacenterByName(
    datacenterListQuery.data,
    vm.datacenter,
  );

  const sumStorageQuantity = vm.subscription.items
    .filter(
      subscriptionItem =>
        subscriptionItem.item_type === ProductType.ADDON &&
        getProductFromOfferId(catalog, subscriptionItem.id)?.meta_data?.type ===
          AddonType.STORAGE,
    )
    .reduce((acc, storageAddon) => {
      return acc + (storageAddon.quantity || 0);
    }, 0);

  const totalStorage =
    INTERNAL_STORAGE_SIZE_GO + sumStorageQuantity * STORAGE_SLICE_SIZE_GO;

  const getRenewalDateLabel = () => {
    if (vm.subscription.status === SubscriptionStatus.FUTURE) {
      return '';
    }

    if (vm.subscription.next_billing_at) {
      return formatDate(
        vm.subscription.next_billing_at * 1000,
        DATE_FORMAT_WITH_SLASH_BY_MARKET[market],
      );
    }

    return t('global.unknown', 'Unknown', { ns: 'common' });
  };

  return (
    <VmDetailCategoryList>
      <VmDetailCategory
        icon={Icons.CPU}
        title={t('businessManager.vmDetails.vm.offer', 'Offer')}
        value={t(`subscription.details.name.${vm.type}`)}
      />
      <VmDetailCategory
        icon={Icons.HARD_DRIVE}
        title={t(
          'businessManager.vmDetails.vm.internalStorage.label',
          'Internal Storage',
        )}
        value={t('businessManager.vmDetails.vm.internalStorage.value', {
          defaultValue: '{{totalStorageQty, storageUnit}}',
          totalStorageQty: totalStorage,
        })}
      />
      <VmDetailCategory
        icon={Icons.MAP_PIN}
        title={t('businessManager.vmDetails.vm.datacenter', 'Datacenter')}
        value={vmDatacenter ? getDatacenterName(vmDatacenter) : vm.datacenter}
        flag={
          vmDatacenter?.country
            ? `${FLAG_PATH}${vmDatacenter?.country.toLowerCase()}.png`
            : undefined
        }
      />
      <VmDetailCategory
        icon={Icons.CLOCK}
        title={t('businessManager.vmDetails.vm.creationDate', 'Creation Date')}
        value={formatDate(
          vm.createdAt * 1000,
          DATE_FORMAT_WITH_SLASH_BY_MARKET[market],
        )}
      />
      <VmDetailCategory
        icon={Icons.CALENDAR}
        title={t(
          'businessManager.vmDetails.vm.subscriptionRenewal',
          'Subscription renewal',
        )}
        value={getRenewalDateLabel()}
      />
    </VmDetailCategoryList>
  );
};

export default VmDetail;
