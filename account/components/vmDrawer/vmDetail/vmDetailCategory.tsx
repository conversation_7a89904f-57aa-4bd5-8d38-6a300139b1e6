import styled from '@emotion/styled';
import { Icon, theme, Typography } from 'shared-components';
import { Icons } from 'types';

interface IVmDetailCatProps {
  icon: Icons;
  title: string;
  value: string | number;
  flag?: string;
}

const Container = styled.li`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const Title = styled(Typography)`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const FlagWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
`;

const VmDetailCategory = ({ icon, title, value, flag }: IVmDetailCatProps) => {
  return (
    <Container>
      <Title variant="label-xs-light">
        <Icon
          name={icon}
          color={theme.palette.black.main}
          width={16}
          height={16}
        />
        {title}
      </Title>
      {flag ? (
        <FlagWrapper>
          <img src={flag} width={24} height={24} />
          <Typography variant="label-sm-regular">{value}</Typography>
        </FlagWrapper>
      ) : (
        <Typography variant="label-sm-regular">{value}</Typography>
      )}
    </Container>
  );
};

export default VmDetailCategory;
