import { usePaginatedSubscriptions } from 'hooks';
import { useTranslation } from 'next-i18next';
import { ClickTracker } from 'shared-components';
import {
  IAnyOffer,
  ICatalog,
  OfferPeriodUnit,
  ProductFamilyId,
  ProductId,
  SubscriptionStatus,
} from 'types';
import { findSubscription } from 'utils';

import SubscriptionAddon from '@/components/subscription/addon/subscriptionAddon';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { SHOP_URL } from '@/utils/constants';

interface IDriveAddonProps {
  catalog: ICatalog;
  driveOffer: IAnyOffer | undefined;
  subscriptionStatus: SubscriptionStatus;
  onGetDrive: () => void;
}

const DriveAddon = ({
  catalog,
  driveOffer,
  subscriptionStatus,
  onGetDrive,
}: IDriveAddonProps) => {
  const { t } = useTranslation();
  const subscriptionsQuery = usePaginatedSubscriptions(true);
  const { isDriveSubscriptionOnHold } = useIsSubscriptionOnHold();

  const currentSubscription = findSubscription(
    subscriptionsQuery.data?.items,
    ProductFamilyId.SHADOWDRIVE,
  );

  // TODO: to be reworked later so that we don't need ProductId.DRIVE_B2C_PREMIUM. I'm leaving as it is for now.
  const premiumDriveOffer = Object.values(catalog.offers.byId).find(offer => {
    return (
      offer.product_id === ProductId.DRIVE_B2C_PREMIUM &&
      offer.periodicity === driveOffer?.periodicity
    );
  });

  const shouldDisplayDriveUpgrade =
    !isDriveSubscriptionOnHold &&
    driveOffer?.product_id !== ProductId.DRIVE_B2C_PREMIUM &&
    subscriptionStatus === SubscriptionStatus.ACTIVE &&
    premiumDriveOffer;

  const changePlanUrl = `${SHOP_URL}?funnel=change_plan&familyId=${ProductFamilyId.SHADOWDRIVE}&subscription=${currentSubscription?.id}`;

  if (driveOffer?.product_id === ProductId.DRIVE_B2C_PREMIUM) {
    return null;
  }

  return (
    <>
      {!driveOffer && (
        <>
          <SubscriptionAddon
            id="drive-free"
            label={t(
              'subscription.plan.addon.drive.get.link',
              'Get your Shadow Drive',
            )}
            onClick={onGetDrive}
            amount={0}
            period={1}
            periodUnit={OfferPeriodUnit.MONTH}
          />
        </>
      )}

      {shouldDisplayDriveUpgrade && (
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'modify_my_drive_subscription',
            },
          }}
        >
          <SubscriptionAddon
            id="drive-premium-upgrade"
            label={t(
              'subscription.plan.addon.drive.upgrade.link',
              'Upgrade to Premium (2 TB)',
            )}
            href={changePlanUrl}
            showPrice={false}
          />
        </ClickTracker>
      )}
    </>
  );
};

export default DriveAddon;
