import styled from '@emotion/styled';
import { useGetPossibleSubscriptionUpdates, useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Button, ClickTracker, Icon, Link, theme } from 'shared-components';
import {
  IAnyOffer,
  IMemberDetails,
  ISubscription,
  Icons,
  ProductFamilyId,
  ProductType,
  SubscriptionStatus,
} from 'types';
import { logError, trackEvent } from 'utils';

import { SubscriptionContainer } from '../styles';

import CancelDriveModal from '@/components/subscription/actions/cancelDrive/cancelDriveModal';
import SubscriptionList from '@/components/subscription/subscriptionList';
import ButtonPopover from '@/components/ui/buttonPopover';
import SubscriptionDiscount from '@/components/ui/SubscriptionDiscount/SubscriptionDiscount';
import UserScheduledChange from '@/components/ui/userScheduledChange/userScheduledChange';
import {
  useIsSubscriptionOnHold,
  useReactivateSubscription,
} from '@/hooks/subscription/useSubscription';
import { useDate } from '@/hooks/useDate';
import { DRIVE_URL, SHOP_URL } from '@/utils/constants';

interface IDriveSubscriptionProps {
  subscription: ISubscription;
  driveOffer: IAnyOffer;
  userDetails: IMemberDetails | undefined;
}

const LinksContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  ${theme.breakpoints.up('md')} {
    align-items: flex-end;
  }
`;

const Wrapper = styled.div`
  display: flex;
  align-items: start;
  gap: 16px;
  margin-top: 16px;

  ${theme.breakpoints.up('md')} {
    justify-content: flex-end;
    gap: 24px;
    margin-top: 0px;
  }
`;

const SubscriptionRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;

  ${theme.breakpoints.up('md')} {
    justify-content: flex-end;
    flex-direction: row;
    gap: 24px;
    width: 100%;
  }
`;

const DriveSubscription = ({
  subscription,
  driveOffer,
  userDetails,
}: IDriveSubscriptionProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const { getSubscriptionStartDate } = useDate();
  const { isDriveSubscriptionOnHold } = useIsSubscriptionOnHold();
  const reactivateSubscription = useReactivateSubscription();
  const [isActionsPopoverOpen, setIsActionsPopoverOpen] =
    useState<boolean>(false);

  const [isCancelModalOpen, setIsCancelModalOpen] = useState<boolean>(false);
  const [isSubmittingReactivateDrive, setIsSubmittingReactivateDrive] =
    useState<boolean>(false);
  const status =
    (subscription?.status as SubscriptionStatus) || SubscriptionStatus.ACTIVE;
  const isSubscriptionActive = status === SubscriptionStatus.ACTIVE;
  const isSubscriptionNonRenewing = status === SubscriptionStatus.NON_RENEWING;

  const possibleSubscriptionUpdatesQuery = useGetPossibleSubscriptionUpdates(
    subscription?.id as string,
  );

  const possibleUpdateProducts = possibleSubscriptionUpdatesQuery.data?.filter(
    product =>
      product?.tiers_level !== null &&
      product.item_family_id === subscription.product_family_id &&
      product.type === ProductType.PLAN &&
      !product.offers.every(offer => offer.price === 0), // filters out drive free plan
  );

  const shouldDisplayDriveChangePlan =
    !isDriveSubscriptionOnHold &&
    subscription.status === SubscriptionStatus.ACTIVE &&
    !!possibleUpdateProducts &&
    !!possibleUpdateProducts.length;

  const changePlanUrl = `${SHOP_URL}?funnel=change_plan&familyId=${ProductFamilyId.SHADOWDRIVE}&subscription=${subscription?.id}`;

  const shouldDisableCancelButton = isDriveSubscriptionOnHold;

  const displayUserScheduledChange = subscription?.has_scheduled_changes;

  const handleReactivateDrive = async () => {
    setIsSubmittingReactivateDrive(true);

    try {
      if (!subscription) {
        throw new Error('No subscription provided!');
      }

      await reactivateSubscription.mutateAsync(subscription.id);

      trackEvent('click', {
        event_category: 'drive_reactivate',
        event_label: 'drive_reactivate_confirmed',
      });

      notifySuccess(
        t(
          'subscription.reactivateVm.notification.success',
          'You have successfully reactivated your subscription.',
        ),
      );
    } catch (e) {
      notifyError(
        t(
          'subscription.reactivateVm.notification.error',
          'We could not reactivate your subscription, please try again later.',
        ),
      );
      logError('handleReactivateDrive', e);
    }

    setIsSubmittingReactivateDrive(false);
  };

  return (
    <>
      <SubscriptionContainer>
        <SubscriptionRow>
          {/* Subscriptions list (name + date) */}
          <SubscriptionList
            status={status}
            details={[
              {
                label: t('subscription.plan.details', {
                  defaultValue: '{{planName}} ({{status}}{{startDate}})',
                  planName: t(
                    `subscription.details.name.${driveOffer.product_id}`,
                  ),
                  status: t(
                    `subscription.details.status.${subscription.status}`,
                  ),
                  startDate: getSubscriptionStartDate(
                    subscription as ISubscription,
                  ),
                }),

                id: driveOffer.product_id,
              },
            ]}
          />
          {/*
          Here you can have an action button showing an action bar below
          or a collection of components, usually call to action buttons
          Adding Both components at the same time is not recommended
        */}
          {/* Action button trigger */}
          {isSubscriptionActive && (
            <Wrapper>
              {(isSubscriptionActive || isSubscriptionNonRenewing) && (
                <ButtonPopover
                  dataTestId="subscription-drive-edit"
                  icon={<Icon name={Icons.EDIT} width={16} height={16} />}
                  name={`vmDetails-more`}
                  isOpen={isActionsPopoverOpen}
                  setIsOpen={setIsActionsPopoverOpen}
                  label={t('subscription.plan.drive.label', 'Edit')}
                  hasBackground={true}
                >
                  {shouldDisplayDriveChangePlan && (
                    <ClickTracker
                      eventPayload={{
                        action: 'click',
                        parameters: {
                          event_category:
                            'my_account_modify_drive_subscription',
                          event_label: 'change_offer',
                        },
                      }}
                    >
                      <Link
                        data-test-id="subscription-drive-change-plan"
                        key="drive-change-plan"
                        href={changePlanUrl}
                        variant="label-sm-regular"
                        startIcon={
                          <Icon name={Icons.EDIT} width={16} height={16} />
                        }
                      >
                        {t(
                          'subscription.plan.drive.action.changePlan',
                          'Change plan',
                        )}
                      </Link>
                    </ClickTracker>
                  )}
                  <ClickTracker
                    eventPayload={{
                      action: 'click',
                      parameters: {
                        event_category: 'my_account_modify_drive_subscription',
                        event_label: 'cancel_my_drive',
                      },
                    }}
                  >
                    <Link
                      data-test-id="subscription-drive-cancel-openModal"
                      disabled={shouldDisableCancelButton}
                      key="drive-cancel"
                      onClick={() => setIsCancelModalOpen(true)}
                      color="error"
                      variant="label-sm-regular"
                      startIcon={
                        <Icon name={Icons.CROSS} width={12} height={12} />
                      }
                    >
                      {t(
                        'subscription.plan.drive.action.cancel',
                        'Cancel my subscription',
                      )}
                    </Link>
                  </ClickTracker>
                </ButtonPopover>
              )}
            </Wrapper>
          )}

          {/* CTA components */}
          {subscription.status === SubscriptionStatus.NON_RENEWING && (
            <ClickTracker
              eventPayload={{
                action: 'click',
                parameters: {
                  event_category: 'my_account_modify_drive_subscription',
                  event_label: 'drive_reactivate',
                },
              }}
            >
              <Button
                data-test-id="subscription-drive-reactivate"
                key="drive-reactivate"
                loading={isSubmittingReactivateDrive}
                onClick={handleReactivateDrive}
                size="small"
              >
                {t('subscription.plan.drive.action.reactivate', 'Reactivate')}
              </Button>
            </ClickTracker>
          )}
          {subscription.status === SubscriptionStatus.CANCELLED && (
            <ClickTracker
              eventPayload={{
                action: 'click',
                parameters: {
                  event_category: 'my_account_modify_drive_subscription',
                  event_label: 'drive_resubscribe',
                },
              }}
            >
              <Button
                data-test-id="subscription-drive-reactivate"
                key="drive-reactivate"
                component="a"
                href={SHOP_URL}
                size="small"
              >
                {t('subscription.plan.drive.action.resubscribe', 'Resubscribe')}
              </Button>
            </ClickTracker>
          )}
        </SubscriptionRow>
      </SubscriptionContainer>

      {isSubscriptionActive && (
        <SubscriptionDiscount subscriptionId={subscription.id} />
      )}

      {(isSubscriptionActive || isSubscriptionNonRenewing) && (
        <LinksContainer>
          <ClickTracker
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'my_account_modify_drive_subscription',
                event_label: 'access_drive',
              },
            }}
          >
            <Button
              data-test-id="subscription-drive-app-link"
              component="a"
              size="small"
              href={DRIVE_URL}
              target="_blank"
              title={t(
                'subscription.plan.drive.application.link.title',
                'Go to Shadow Drive application',
              )}
            >
              {t(
                'subscription.plan.drive.application.link.label',
                'Access my Shadow Drive',
              )}
            </Button>
          </ClickTracker>
        </LinksContainer>
      )}

      {displayUserScheduledChange && (
        <UserScheduledChange
          subscription={subscription}
          onScheduledChangeCancelSuccess={() => {}}
        />
      )}

      {isCancelModalOpen && (
        <CancelDriveModal
          isOpen={isCancelModalOpen}
          handleClose={() => setIsCancelModalOpen(false)}
          subscription={subscription as ISubscription}
          email={userDetails?.email}
          onCancelDriveError={() => {
            setIsCancelModalOpen(false);
            notifyError(
              t(
                'subscription.cancelDrive.notification.error',
                'There was an error when trying to cancel your Drive subscription, please try again later or contact support.',
              ),
            );
          }}
        />
      )}
    </>
  );
};

export default DriveSubscription;
