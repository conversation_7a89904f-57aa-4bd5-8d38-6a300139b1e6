import { useTranslation } from 'next-i18next';
import { ProductFamilyId, ProductType, SubscriptionStatus } from 'types';
import { SVG_PATH } from 'utils';

import { DriveGroupInvite } from '../driveGroups/driveGroupInvite';

import DriveAddon from '@/components/subscription/drive/driveAddon';
import DriveSubscription from '@/components/subscription/drive/driveSubscription';
import Subscription, {
  ISubscriptionProps,
} from '@/components/subscription/subscription';
import { useCurrentMember } from '@/hooks/member/useMember';
import { SHOP_URL } from '@/utils/constants';

const DriveManager = () => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const userDetails = currentMemberQuery.data?.user;

  const renderContent: ISubscriptionProps['contentRenderer'] = (
    subscription,
    driveOffer,
  ) => {
    return driveOffer ? (
      <DriveSubscription
        driveOffer={driveOffer}
        subscription={subscription}
        userDetails={userDetails}
      />
    ) : null;
  };

  const renderAddon: ISubscriptionProps['addonRenderer'] = (
    subscription,
    driveOffer,
    catalog,
  ) => {
    return (
      <DriveAddon
        catalog={catalog}
        driveOffer={driveOffer}
        subscriptionStatus={subscription?.status as SubscriptionStatus}
        onGetDrive={() => {
          window.location.href = `${SHOP_URL}b2c?familyId=${ProductFamilyId.SHADOWDRIVE}`;
        }}
      />
    );
  };

  return (
    <>
      <DriveGroupInvite />
      <Subscription
        type={ProductType.PLAN}
        productFamilyId={ProductFamilyId.SHADOWDRIVE}
        logo={{
          src: `${SVG_PATH}logos/shadow-drive.svg`,
          alt: t('subscription.plan.drive.logoAlt', 'Shadow Drive'),
          id: 'shadow-drive',
          width: 286,
          height: 60,
        }}
        description={t(
          'subscription.plan.drive.description',
          'A simple cloud storage solution to back up your data and access it anywhere, fully secured via traffic encryption. The perfect companion to Shadow.',
        )}
        subscriptionTitle={t(
          'subscription.plan.drive.title',
          'My subscription',
        )}
        contentRenderer={renderContent}
        addonRenderer={renderAddon}
      />
    </>
  );
};

export default DriveManager;
