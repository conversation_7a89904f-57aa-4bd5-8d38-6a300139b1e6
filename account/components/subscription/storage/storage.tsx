import { useTranslation } from 'next-i18next';
import type { ReactNode } from 'react';
import { Button } from 'shared-components';
import {
  ISubscription,
  ISubscriptionItem,
  SubscriptionStatus,
  ICatalog,
  ProductAddonNecessity,
  AddonType,
  IAddon,
} from 'types';
import {
  getAddons,
  getProductFromOfferId,
  INTERNAL_STORAGE_SIZE_GO,
  STORAGE_SLICE_SIZE_GO,
} from 'utils';

import { SubscriptionContainer } from '../styles';

import SubscriptionList from '@/components/subscription/subscriptionList';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { SHOP_URL } from '@/utils/constants';

interface IStorageProps {
  subscription: ISubscription;
  catalog: ICatalog;
  cta?: ReactNode;
  displayCTAComponents?: boolean;
}

const getSubscriptionItemsCorrespondingToCatalogAddons = (
  catalog: ICatalog,
  catalogAddons: IAddon[],
  subscriptionItems: ISubscriptionItem[],
) => {
  const correspondingSubscriptionItems: ISubscriptionItem[] = [];

  subscriptionItems.forEach(subscriptionItem => {
    const subscriptionItemProduct = getProductFromOfferId(
      catalog,
      subscriptionItem.id,
    );
    const isSubscriptionItemIncludedInCatalogAddons = catalogAddons.some(
      catalogAddon => {
        return catalogAddon.id === subscriptionItemProduct?.id;
      },
    );

    if (isSubscriptionItemIncludedInCatalogAddons) {
      correspondingSubscriptionItems.push(subscriptionItem);
    }
  });

  return correspondingSubscriptionItems;
};

const Storage = ({
  catalog,
  subscription,
  displayCTAComponents = true,
}: IStorageProps) => {
  const { t } = useTranslation();
  const { isCloudPcSubscriptionOnHold } = useIsSubscriptionOnHold();
  const currentMemberQuery = useCurrentMember();
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  const status =
    (subscription?.status as SubscriptionStatus) || SubscriptionStatus.ACTIVE;

  const shouldDisabledUpdateButton =
    status !== SubscriptionStatus.ACTIVE || isCloudPcSubscriptionOnHold;

  const getStorageDetails = () => {
    const plan = getProductFromOfferId(catalog, subscription.plan_id);
    if (!plan) {
      return [];
    }

    const mandatoryStorageAddonsForPlan = getAddons(
      catalog,
      plan.id,
      (parameter, product) => {
        return (
          parameter.type === ProductAddonNecessity.MANDATORY &&
          product.meta_data?.type === AddonType.STORAGE
        );
      },
    );
    const optionalStorageAddonsForPlan = getAddons(
      catalog,
      plan.id,
      (parameter, product) => {
        return (
          parameter.type !== ProductAddonNecessity.MANDATORY &&
          product.meta_data?.type === AddonType.STORAGE
        );
      },
    );

    const includedStorageQuantity =
      getSubscriptionItemsCorrespondingToCatalogAddons(
        catalog,
        mandatoryStorageAddonsForPlan ?? [],
        subscription.items,
      ).reduce((quantity, subscriptionItem) => {
        return quantity + subscriptionItem.quantity;
      }, 0);

    const extraStorageQuantity =
      getSubscriptionItemsCorrespondingToCatalogAddons(
        catalog,
        optionalStorageAddonsForPlan ?? [],
        subscription.items,
      ).reduce((quantity, subscriptionItem) => {
        return quantity + subscriptionItem.quantity;
      }, 0);

    const details = [
      {
        label: t('subscription.plan.storage.included', {
          defaultValue: '{{storageQty, storageUnit}} included',
          storageQty:
            includedStorageQuantity * STORAGE_SLICE_SIZE_GO +
            INTERNAL_STORAGE_SIZE_GO,
        }),
        id: 'included-storage',
      },
    ];

    if (extraStorageQuantity > 0) {
      details.push({
        label: t('subscription.plan.storage.extra', {
          defaultValue: '{{extraStorageQty, storageUnit}} of extra storage',
          extraStorageQty: extraStorageQuantity * STORAGE_SLICE_SIZE_GO,
        }),
        id: 'extra-storage',
      });
    }

    return details;
  };

  const handleConfigurationButtonClick = () => {
    if (isB2b) {
      window.location.href = `${SHOP_URL}?funnel=b2b_update_storage&subscription=${subscription.id}`;
    } else {
      window.location.href = `${SHOP_URL}?funnel=update_storage&subscription=${subscription?.id}`;
    }
  };

  return (
    <>
      <SubscriptionContainer>
        {/* Subscriptions list (name + date) */}
        <SubscriptionList status={status} details={getStorageDetails()} />
        {/*
        Here you can have an action button showing an action bar below
        or a collection of components, usually call to action buttons
        Adding Both components at the same time is not recommended
      */}
        {/* CTA components */}
        {displayCTAComponents && (
          <Button
            data-test-id="manage-extra-storage-button"
            key="storage-add"
            size="small"
            disabled={shouldDisabledUpdateButton}
            onClick={handleConfigurationButtonClick}
          >
            {t('subscription.plan.storage.ctaLabel', 'Manage extra storage')}
          </Button>
        )}
      </SubscriptionContainer>
    </>
  );
};

export default Storage;
