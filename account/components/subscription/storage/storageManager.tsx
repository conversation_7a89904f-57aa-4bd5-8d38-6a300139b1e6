import { usePaginatedSubscriptions } from 'hooks';
import { useTranslation } from 'next-i18next';
import { IMemberDetails, ProductType, ProductFamilyId, UserRole } from 'types';
import { findSubscription } from 'utils';

import Storage from '@/components/subscription/storage/storage';
import Subscription, {
  ISubscriptionProps,
} from '@/components/subscription/subscription';
import { useCurrentMember } from '@/hooks/member/useMember';

const StorageManager = () => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const subscriptionsQuery = usePaginatedSubscriptions(true);
  const currentSubscription = findSubscription(
    subscriptionsQuery.data?.items,
    ProductFamilyId.CLOUDPC,
  );
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const displayCTAComponents =
    !isB2b ||
    [UserRole.ADMIN, UserRole.OWNER].includes(
      currentMemberQuery.data?.role as UserRole,
    );

  const renderContent: ISubscriptionProps['contentRenderer'] = (
    subscription,
    _storageOffer,
    catalog,
  ) => {
    return (
      <Storage
        subscription={subscription}
        displayCTAComponents={displayCTAComponents}
        catalog={catalog}
      />
    );
  };

  if (!currentSubscription) {
    return null;
  }

  return (
    <Subscription
      productFamilyId={ProductFamilyId.CLOUDPC}
      type={ProductType.ADDON}
      subscriptionTitle={t('subscription.plan.storage.title', 'Storage')}
      contentRenderer={renderContent}
    />
  );
};

export default StorageManager;
