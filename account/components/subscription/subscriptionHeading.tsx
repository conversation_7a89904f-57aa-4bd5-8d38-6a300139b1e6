import styled from '@emotion/styled';
import Image from 'next/image';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';

export interface ISubscriptionHeadingProps {
  logo: {
    src: string;
    alt: string;
    id: string;
    width: number;
    height: number;
  };
  illustration?: {
    src: string;
    alt: string;
    width: number;
    height: number;
  };
  description?: string;
}

const ImagesWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  gap: 40px;
  ${theme.breakpoints.only('xs')} {
    margin-top: 64px;
  }
`;

const Description = styled(Typography)`
  max-width: 632px;
  margin: 0 auto;
`;

const SubscriptionHeading = ({
  logo,
  illustration,
  description,
}: ISubscriptionHeadingProps) => {
  const { isMD } = useThemeMediaQueries();
  // logo is hidden for small screen when illustration is provided
  const isHiddenLogo = !isMD && illustration;

  return (
    <div>
      {/* Subscription type images */}
      <ImagesWrapper>
        {!isHiddenLogo && (
          <Image
            src={logo.src}
            alt={logo.alt}
            id={logo.id}
            width={logo.width}
            height={logo.height}
          />
        )}
        {illustration && (
          <Image
            src={illustration.src}
            alt={illustration.alt}
            width={illustration.width}
            height={illustration.height}
          />
        )}
      </ImagesWrapper>

      {/* Subscription type description */}
      {!!description && (
        <Description variant={isMD ? 'body-sm' : 'body-xs'} align="center">
          {description}
        </Description>
      )}
    </div>
  );
};

export default SubscriptionHeading;
