import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { UseMutationResult } from '@tanstack/react-query';
import { useAcceptDriveGroupInvite, useRejectDriveGroupInvite } from 'hooks';
import { useTranslation } from 'next-i18next';
import { FC, useState } from 'react';
import { Alert, Button, Icon } from 'shared-components';
import { theme, Typography } from 'shared-components';
import { Icons, NotificationState } from 'types';
import { APIError } from 'utils';

import Paper from '@/components/ui/paper';
import { useConfig } from '@/hooks/store/useConfig';

const Container = styled(Paper)`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 32px 16px;
  ${theme.breakpoints.up('md')} {
    padding: 32px 16px;
  }
`;

const Title = styled(Typography)`
  padding-bottom: 16px;
`;

const ButtonsContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const TextContainer = styled.div`
  text-align: center;
  padding-bottom: 12px;
  ${theme.breakpoints.up('md')} {
    padding-bottom: 20px;
  }
`;

export const DriveGroupInvite: FC = () => {
  const { t } = useTranslation();
  const { inviteToken, inviteDriveGroup, setInviteDriveGroup, setInviteToken } =
    useConfig();

  const [inviteStatus, setInviteStatus] = useState<
    'pending' | 'accepted' | 'rejected'
  >('pending');

  const [errorMessage, setErrorMessage] = useState<string>();
  const [isLoading, setIsLoading] = useState(false);

  const acceptDriveGroupInvite = useAcceptDriveGroupInvite();
  const rejectDriveGroupInvite = useRejectDriveGroupInvite();

  if ((!inviteDriveGroup || !inviteToken) && inviteStatus === 'pending') {
    return null;
  }

  const processInvite = (
    method: UseMutationResult<string | undefined, APIError, string, unknown>,
    status: 'accepted' | 'rejected',
  ) => {
    setIsLoading(true);
    setErrorMessage(undefined);

    // Why a timeout, you ask? Because after subscribing to shadow drive free/premium, which is required to join a drive group,
    // the backend takes a little time to register the subscription, which means the following throws an error if the user has fast fingers.
    // This action is not performed often, so a 2 seconds delay is not a big deal.
    setTimeout(() => {
      method
        .mutateAsync(inviteToken || '')
        .then(() => {
          setInviteStatus(status);
          setInviteToken('');
          setInviteDriveGroup(undefined);
        })
        .catch((error: APIError) => {
          // membership already exists, silent fail
          if (error.code === 409) {
            setInviteStatus(status);
            setInviteToken('');
            setInviteDriveGroup(undefined);
            return;
          }
          setErrorMessage(error.message);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }, 2000);
  };

  return (
    <>
      <Collapse in={!!errorMessage}>
        <Alert
          type={NotificationState.ERROR}
          title={t(
            'subscription.driveGroups.invite.error.title',
            'An error occurred',
          )}
        >
          {errorMessage}
        </Alert>
      </Collapse>

      <Collapse in={inviteStatus === 'accepted'}>
        <Alert
          type={NotificationState.SUCCESS}
          title={t(
            'subscription.driveGroups.invite.accepted.title',
            'You successfully joined the Family group',
          )}
        >
          {t(
            'subscription.driveGroups.invite.accepted.content',
            'You can now access your shared Family folder on Shadow Drive.',
          )}
        </Alert>
      </Collapse>

      <Collapse in={inviteStatus === 'pending'}>
        <Container>
          <TextContainer>
            <Title variant="heading-h6">
              {t(
                'subscription.driveGroups.invite.title',
                'Invitation Received',
              )}
            </Title>
            <Typography variant="body-md">
              {t(
                'subscription.driveGroups.invite.description',
                "You've been invited to a Shadow Drive Family group.",
              )}
            </Typography>
          </TextContainer>
          <ButtonsContainer>
            <Button
              data-test-id="drive-group-accept-invite-button"
              color="primary"
              size="small"
              startIcon={<Icon name={Icons.CHECK} width={10} height={10} />}
              onClick={() => processInvite(acceptDriveGroupInvite, 'accepted')}
              loading={isLoading}
              disabled={isLoading}
            >
              {t(
                'subscription.driveGroups.invite.acceptButton.label',
                'Accept invite',
              )}
            </Button>
            <Button
              data-test-id="drive-group-reject-invite-button"
              color="secondary"
              size="small"
              startIcon={<Icon name={Icons.CROSS} width={10} height={10} />}
              onClick={() => processInvite(rejectDriveGroupInvite, 'rejected')}
              loading={isLoading}
              disabled={isLoading}
            >
              {t(
                'subscription.driveGroups.invite.rejectButton.label',
                'Reject invite',
              )}
            </Button>
          </ButtonsContainer>
        </Container>
      </Collapse>
    </>
  );
};
