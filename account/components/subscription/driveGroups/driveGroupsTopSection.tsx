import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { FC } from 'react';
import { Button } from 'shared-components';
import { theme, Typography, Link } from 'shared-components';
import { ISubscription, ProductFamilyId } from 'types';

import { SHOP_URL } from '@/utils/constants';

const TopSectionContainer = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  ${theme.breakpoints.down('md')} {
    margin-top: 24px;
  }
`;

interface IDriveGroupsTopSectionProps {
  currentGroupsCount: number;
  maxGroupsCount: number;
  isFreeUser: boolean;
  openCreateGroupModal: () => void;
  isFetching: boolean;
  currentSubscription: ISubscription;
}

export const DriveGroupsTopSection: FC<IDriveGroupsTopSectionProps> = ({
  currentGroupsCount,
  maxGroupsCount,
  isFreeUser,
  openCreateGroupModal,
  isFetching,
  currentSubscription,
}) => {
  const { t } = useTranslation();

  const upgradeLink = `${SHOP_URL}?funnel=change_plan&familyId=${ProductFamilyId.SHADOWDRIVE}&subscription=${currentSubscription?.id}`;

  const renderUpgradeLink = () => {
    return (
      <>
        &nbsp;
        <Typography variant="body-sm" component="span">
          <Link
            data-test-id="drive-groups-upgrade-link"
            href={upgradeLink}
            variant="body-sm-regular-link"
          >
            {t(
              'subscription.driveGroups.upgradeLink',
              'Update to Premium for more',
            )}
          </Link>
        </Typography>
      </>
    );
  };

  const renderOnboardingText = () => {
    return (
      <Typography variant="body-sm">
        <Typography variant="body-sm" component="span">
          {isFreeUser
            ? t(
                'subscription.driveGroups.onboarding.maxGroupsCount.free',
                'As a free user, you can create only {{maxGroupsCount}} Family groups.',
                { maxGroupsCount },
              )
            : t(
                'subscription.driveGroups.onboarding.maxGroupsCount.premium',
                'As a premium user, you can create up to {{maxGroupsCount}} Family groups.',
                { maxGroupsCount },
              )}
        </Typography>
        {isFreeUser && renderUpgradeLink()}
      </Typography>
    );
  };

  const renderGroupsCountText = () => {
    return (
      <Typography variant="body-sm">
        <Typography variant="body-sm" component="span">
          {t(
            'subscription.driveGroups.currentGroupsCount',
            'You created {{currentGroupsCount}} out of {{maxGroupsCount}} groups.',
            { currentGroupsCount, maxGroupsCount },
          )}
        </Typography>
        {isFreeUser && renderUpgradeLink()}
      </Typography>
    );
  };

  return (
    <TopSectionContainer>
      {currentGroupsCount === 0 && renderOnboardingText()}
      {currentGroupsCount > 0 && renderGroupsCountText()}
      <Button
        data-test-id="create-drive-group-button"
        color="primary"
        size="small"
        disabled={currentGroupsCount === maxGroupsCount || isFetching}
        onClick={() => openCreateGroupModal()}
      >
        {t(
          'subscription.driveGroups.addGroupButton.label',
          'Add a family group',
        )}
      </Button>
    </TopSectionContainer>
  );
};
