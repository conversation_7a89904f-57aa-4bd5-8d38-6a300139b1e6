import { useTranslation } from 'next-i18next';
import { FC } from 'react';
import { OfferPeriodUnit, ProductFamilyId, ProductType } from 'types';
import { SVG_PATH } from 'utils';

import SubscriptionAddon from '../addon/subscriptionAddon';
import Subscription from '../subscription';

import { SHOP_URL } from '@/utils/constants';

export const NoDriveSubscription: FC = () => {
  const { t } = useTranslation();

  return (
    <Subscription
      type={ProductType.PLAN}
      productFamilyId={ProductFamilyId.SHADOWDRIVE}
      logo={{
        src: `${SVG_PATH}logos/shadow-drive.svg`,
        alt: t('subscription.plan.drive.logoAlt', 'Shadow Drive'),
        id: 'shadow-drive',
        width: 286,
        height: 60,
      }}
      description={t(
        'subscription.plan.drive.description',
        'A simple cloud storage solution to back up your data and access it anywhere, fully secured via traffic encryption. The perfect companion to Shadow.',
      )}
      subscriptionTitle={t('subscription.plan.drive.title', 'My subscription')}
      contentRenderer={() => null}
      addonRenderer={() => (
        <SubscriptionAddon
          id="drive-free"
          label={t(
            'subscription.plan.addon.drive.get.link',
            'Get your Shadow Drive',
          )}
          onClick={() => {
            window.location.href = `${SHOP_URL}b2c?familyId=${ProductFamilyId.SHADOWDRIVE}`;
          }}
          amount={0}
          period={1}
          periodUnit={OfferPeriodUnit.MONTH}
        />
      )}
    />
  );
};
