import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { FC } from 'react';
import { Button } from 'shared-components';
import { theme, Typography } from 'shared-components';

import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';

const Container = styled(Paper)`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 64px 32px;
  ${theme.breakpoints.up('md')} {
    padding: 64px 32px;
  }
`;

const TextContainer = styled.div`
  text-align: center;
  padding-bottom: 24px;
  ${theme.breakpoints.up('md')} {
    padding-bottom: 40px;
  }
`;

interface IDriveGroupsEmptyProps {
  openCreateGroupModal: () => void;
}

export const DriveGroupsEmpty: FC<IDriveGroupsEmptyProps> = ({
  openCreateGroupModal,
}) => {
  const { t } = useTranslation();

  return (
    <Container>
      <TextContainer>
        <PaperTitle>
          {t('subscription.driveGroups.onboarding.title', 'Family Groups')}
        </PaperTitle>
        <Typography variant="body-md">
          {t(
            'subscription.driveGroups.onboarding.description',
            'Family groups help sharing files between you and your loved ones thanks to a special group folder accessible directly from all of your Drive accounts.',
          )}
        </Typography>
      </TextContainer>
      <Button
        color="primary"
        onClick={() => openCreateGroupModal()}
        data-test-id="create-first-drive-group-button"
      >
        {t(
          'subscription.driveGroups.onboarding.firstGroupButton.label',
          'Add your first group',
        )}
      </Button>
    </Container>
  );
};
