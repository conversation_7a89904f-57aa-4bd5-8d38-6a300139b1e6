import { useGetDriveGroups, useModal, usePaginatedSubscriptions } from 'hooks';
import { FC } from 'react';
import { ProductFamilyId, ProductType } from 'types';
import {
  findSubscription,
  findSubscriptionItemByType,
  getProductFromOfferId,
} from 'utils';

import { DriveGroups } from './driveGroups';
import { DriveGroupsEmpty } from './driveGroupsEmpty';
import { DriveGroupsTopSection } from './driveGroupsTopSection';
import { NoDriveSubscription } from './noDriveSubscription';
import { CreateGroupModal } from '../actions/driveGroups/createGroupModal';

import ApiError from '@/components/ui/errors/apiError';
import PaperLoader from '@/components/ui/loader/paperLoader';
import { useCatalog } from '@/hooks/useCatalog';

export const DriveGroupsManager: FC = () => {
  const [isCreateGroupModalOpen, openCreateGroupModal, closeCreateGroupModal] =
    useModal();

  const subscriptionsQuery = usePaginatedSubscriptions(false);
  const currentSubscription = findSubscription(
    subscriptionsQuery.data?.items,
    ProductFamilyId.SHADOWDRIVE,
  );
  const catalogQuery = useCatalog(currentSubscription?.id);
  const currentSubscriptionItem = findSubscriptionItemByType(
    currentSubscription,
    ProductType.PLAN,
  );
  const currentSubscriptionProduct = getProductFromOfferId(
    catalogQuery.data,
    currentSubscriptionItem?.id,
  );

  const driveGroupsQuery = useGetDriveGroups();

  if (
    catalogQuery.isLoading ||
    subscriptionsQuery.isLoading ||
    driveGroupsQuery.isLoading
  ) {
    return <PaperLoader />;
  }

  if (
    subscriptionsQuery.isError ||
    catalogQuery.isError ||
    driveGroupsQuery.isError
  ) {
    return <ApiError />;
  }

  if (!currentSubscription || !currentSubscriptionProduct) {
    return <NoDriveSubscription />;
  }

  const groupsData = driveGroupsQuery.data.items;
  const currentGroupsCount = groupsData.length;
  const maxGroupsCount: number =
    currentSubscriptionProduct?.meta_data?.max_family_group;
  const isFreeUser = currentSubscription.items.every(item => item.price === 0);
  const defaultGroupStorage =
    currentSubscriptionProduct?.meta_data?.default_family_group_storage; // Value is in octets.
  const defaultGroupStorageInGb = defaultGroupStorage / (1024 * 1024 * 1024);
  const maxGroupMembers =
    currentSubscriptionProduct.meta_data?.max_family_group_member;

  return (
    <>
      <CreateGroupModal
        handleClose={closeCreateGroupModal}
        isOpen={isCreateGroupModalOpen}
        defaultGroupStorage={defaultGroupStorageInGb}
      />
      <DriveGroupsTopSection
        currentSubscription={currentSubscription}
        isFetching={driveGroupsQuery.isFetching}
        currentGroupsCount={currentGroupsCount}
        maxGroupsCount={maxGroupsCount}
        isFreeUser={isFreeUser}
        openCreateGroupModal={openCreateGroupModal}
      />
      {currentGroupsCount === 0 ? (
        <DriveGroupsEmpty openCreateGroupModal={openCreateGroupModal} />
      ) : (
        <DriveGroups
          groupsData={groupsData}
          maxGroupMembers={maxGroupMembers}
          currentSubscription={currentSubscription}
          defaultGroupStorage={defaultGroupStorage}
        />
      )}
    </>
  );
};
