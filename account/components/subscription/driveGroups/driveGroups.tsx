import styled from '@emotion/styled';
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { useGetDriveGroups, useModal } from 'hooks';
import { useTranslation } from 'next-i18next';
import { FC, useState } from 'react';
import {
  Chip,
  Icon,
  Typography,
  theme,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons, ISubscription } from 'types';

import { DriveGroupDrawer } from './driveGroupDrawer/driveGroupDrawer';
import { DriveStorageProgressBar } from './driveStorageProgressBar';
import { AddStorageModal } from '../actions/driveGroups/addStorageModal';
import { DeleteGroupModal } from '../actions/driveGroups/deleteGroupModal';
import { InviteMemberModal } from '../actions/driveGroups/inviteMemberModal';
import { ManageGroupsActions } from '../actions/driveGroups/manageGroupActions';

import DataGrid from '@/components/ui/dataGrid/dataGrid';
import { IDriveGroupsManager } from '@/types/adminManagers';
import { ManagerDataType } from '@/utils/constants';

const ChipsContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2px;
  max-width: 100%;
  flex-wrap: wrap;
  margin: 18px 0;
`;

const StyledChip = styled(Chip)`
  max-width: 100%;
`;

const NoUserAssignedText = styled(Typography)`
  margin-right: 8px;
`;

const IconButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.palette.primary.main};

  &:disabled {
    cursor: not-allowed;
    pointer-events: none;
    color: ${theme.palette.black.main25};
  }
`;

interface IDriveGroupsProps {
  groupsData: IDriveGroupsManager[];
  maxGroupMembers: number;
  currentSubscription: ISubscription;
  defaultGroupStorage: number;
}

export const DriveGroups: FC<IDriveGroupsProps> = ({
  groupsData,
  maxGroupMembers,
  currentSubscription,
  defaultGroupStorage,
}) => {
  const { t } = useTranslation();
  const { isFetching } = useGetDriveGroups();
  const { isMD } = useThemeMediaQueries();

  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);

  const [isGroupDrawerOpen, openGroupDrawer, closeGroupDrawer] = useModal();
  const [isInviteModalOpen, openInviteModal, closeInviteModal] = useModal();
  const [isDeleteGroupModalOpen, openDeleteGroupModal, closeDeleteGroupModal] =
    useModal();
  const [isAddStorageModalOpen, openAddStorageModal, closeAddStorageModal] =
    useModal();

  const handleActionClick = (id: string, action: () => void) => {
    setSelectedGroupId(id);
    action();
  };

  const selectedGroup = groupsData.find(group => group.id === selectedGroupId);

  const renderGroupName = (
    params: GridRenderCellParams<unknown, IDriveGroupsManager>,
  ) => (
    <Typography variant="body-xs" noWrap title={params.row.name}>
      {params.row.name}
    </Typography>
  );

  const renderGroupMembers = (
    params: GridRenderCellParams<unknown, IDriveGroupsManager>,
  ) => (
    <ChipsContainer>
      {params.row.members.length === 0 && (
        <NoUserAssignedText
          variant="body-xs"
          noWrap
          data-test-id="no-user-assigned"
        >
          {t(
            'subscription.driveGroups.manager.noUserAssigned',
            'No user is assigned',
          )}
        </NoUserAssignedText>
      )}

      {params.row.members.map(member => (
        <StyledChip
          size="xs"
          variant="label-xs-regular"
          key={member.email}
          shouldWrap={false}
          textColor={theme.palette.black.main75}
          backgroundColor={theme.palette.primary.main10}
          title={member.email}
        >
          {member.email}
        </StyledChip>
      ))}

      {params.row.members.length < maxGroupMembers ? (
        <Chip
          data-test-id={`invite-drive-member-button-${params.row.name
            .split(' ')
            .join('-')}`}
          size="xs"
          variant="label-xs-regular"
          textColor={theme.palette.white.main}
          backgroundColor={theme.palette.primary.main}
          leftAddon={<Icon name={Icons.PLUS} width={10} height={10} />}
          onClick={e => {
            e.stopPropagation();
            handleActionClick(params.row.id, openInviteModal);
          }}
        >
          {t('subscription.driveGroups.manager.inviteButton.label', 'Invite')}
        </Chip>
      ) : null}
    </ChipsContainer>
  );

  const renderStorage = (
    params: GridRenderCellParams<unknown, IDriveGroupsManager>,
  ) => (
    <DriveStorageProgressBar
      currentStorage={params.row.quota_used}
      maxStorage={params.row.quota_allowed}
      size="xs"
      addon={
        // If true, it means the group was already upgraded once and can't be upgraded anymore
        params.row.quota_allowed > defaultGroupStorage ? (
          <></>
        ) : (
          <IconButton
            data-test-id={`add-storage-button-${params.row.name
              .split(' ')
              .join('-')}`}
            type="button"
            onClick={e => {
              e.stopPropagation();
              handleActionClick(params.row.id, openAddStorageModal);
            }}
          >
            <Icon name={Icons.PLUS_CIRCLE} width={16} />
          </IconButton>
        )
      }
    />
  );

  const renderActions = (
    params: GridRenderCellParams<unknown, IDriveGroupsManager>,
  ) => (
    <ManageGroupsActions
      openEditGroupDrawer={() =>
        handleActionClick(params.row.id, openGroupDrawer)
      }
      openDeleteGroupModal={() =>
        handleActionClick(params.row.id, openDeleteGroupModal)
      }
    />
  );

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: t(
        'subscription.driveGroups.manager.groupName.heading',
        'Group name',
      ),
      ...(!isMD && { width: 150 }),
      ...(isMD && { flex: 1 }),
      headerAlign: 'left',
      align: 'left',
      renderCell: renderGroupName,
    },
    {
      field: 'members',
      headerName: t(
        'subscription.driveGroups.manager.groupMembers.heading',
        'Group members',
      ),
      flex: 1,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: renderGroupMembers,
    },
    {
      field: 'storage',
      headerName: t(
        'subscription.driveGroups.manager.storage.heading',
        'Storage',
      ),
      width: 150,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: renderStorage,
    },
    {
      field: 'actions',
      headerName: t(
        'subscription.driveGroups.manager.actions.heading',
        'Actions',
      ),
      width: 150,
      align: 'center',
      headerAlign: 'center',
      sortable: false,
      renderCell: renderActions,
    },
  ];

  return (
    <>
      <InviteMemberModal
        isOpen={isInviteModalOpen}
        handleClose={closeInviteModal}
        selectedGroup={selectedGroup}
        maxGroupMembers={maxGroupMembers}
      />
      <DeleteGroupModal
        key={selectedGroup?.id}
        isOpen={isDeleteGroupModalOpen}
        handleClose={closeDeleteGroupModal}
        selectedGroup={selectedGroup}
        openGroupDrawer={openGroupDrawer}
      />
      <AddStorageModal
        isOpen={isAddStorageModalOpen}
        handleClose={closeAddStorageModal}
        selectedGroup={selectedGroup}
        currentSubscription={currentSubscription}
      />
      <DriveGroupDrawer
        isOpen={isGroupDrawerOpen}
        handleClose={closeGroupDrawer}
        selectedGroup={selectedGroup}
        maxGroupMembers={maxGroupMembers}
      />
      <DataGrid
        columnVisibilityModel={{ members: isMD, storage: isMD }}
        columns={columns}
        rows={groupsData}
        type={ManagerDataType.DRIVE_GROUPS}
        rowCount={groupsData.length}
        loading={isFetching}
        disablePagination
        onRowClick={params => handleActionClick(params.row.id, openGroupDrawer)}
        disableSelectionOnClick={false}
      />
    </>
  );
};
