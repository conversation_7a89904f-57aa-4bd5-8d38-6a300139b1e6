import styled from '@emotion/styled';
import { IDriveGroup } from 'types';

import { DrawerGroupMembers } from './drawerGroupMembers';
import { DrawerHeading } from './drawerHeading';
import { EditGroupNameForm } from './editGroupNameForm';

import Drawer from '@/components/ui/drawer/drawer';

const DrawerStyled = styled(Drawer)`
  padding: 40px;
`;

interface IDriveGroupDrawerProps {
  handleClose: () => void;
  isOpen: boolean;
  selectedGroup: IDriveGroup | undefined;
  maxGroupMembers: number;
}

export const DriveGroupDrawer = ({
  handleClose,
  isOpen,
  selectedGroup,
  maxGroupMembers,
}: IDriveGroupDrawerProps) => {
  return (
    <DrawerStyled
      showCloseIcon
      anchor="right"
      open={isOpen}
      handleClose={handleClose}
    >
      <DrawerHeading selectedGroup={selectedGroup} />
      <EditGroupNameForm
        selectedGroup={selectedGroup}
        key={selectedGroup?.id}
      />
      <DrawerGroupMembers
        selectedGroup={selectedGroup}
        maxGroupMembers={maxGroupMembers}
      />
    </DrawerStyled>
  );
};
