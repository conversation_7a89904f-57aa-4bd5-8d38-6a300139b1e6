import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { FC } from 'react';
import { Typography } from 'shared-components';
import { IDriveGroup } from 'types';

import { DriveStorageProgressBar } from '../driveStorageProgressBar';

const Container = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 40px;
`;

const Heading = styled(Typography)`
  overflow-x: clip;
  text-overflow: ellipsis;
  white-space: wrap;
`;

interface IDrawerHeadingProps {
  selectedGroup: IDriveGroup | undefined;
}

export const DrawerHeading: FC<IDrawerHeadingProps> = ({ selectedGroup }) => {
  const { t } = useTranslation();

  return (
    <Container>
      <Heading variant="heading-h4">
        {t('subscription.driveGroups.drawer.title', 'Edit Family Group')}
      </Heading>
      {selectedGroup?.quota_used !== undefined &&
        selectedGroup?.quota_allowed !== undefined && (
          <DriveStorageProgressBar
            currentStorage={selectedGroup.quota_used}
            maxStorage={selectedGroup.quota_allowed}
            size="md"
          />
        )}
    </Container>
  );
};
