import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useGetDriveGroups } from 'hooks';
import { useTranslation } from 'next-i18next';
import { FC, SyntheticEvent, useState } from 'react';
import { Controller } from 'react-hook-form';
import {
  Alert,
  Button,
  Icon,
  Input,
  theme,
  Typography,
} from 'shared-components';
import { Icons, IDriveGroup, NotificationState } from 'types';

import { useUpdateDriveGroupForm } from '@/hooks/form/driveGroups/useUpdateDriveGroupForm';

const Container = styled.div`
  margin-bottom: 36px;
`;

const InputContainer = styled.div`
  margin: 24px 0;
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
`;

interface IEditGroupNameFormProps {
  selectedGroup: IDriveGroup | undefined;
}

export const EditGroupNameForm: FC<IEditGroupNameFormProps> = ({
  selectedGroup,
}) => {
  const { t } = useTranslation();

  const [errorMessage, setErrorMessage] = useState<string>();
  const defaultValues = { name: selectedGroup?.name || '' };
  const { isFetching: isFetchingDriveGroups } = useGetDriveGroups();

  const { onSubmit, control, isSubmitting } = useUpdateDriveGroupForm(
    selectedGroup?.id as string,
    defaultValues,
    () => {},
    (error: string) => setErrorMessage(error),
  );

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setErrorMessage(undefined);
    onSubmit();
  };

  return (
    <Container>
      <Typography variant="heading-h5">
        {t('subscription.driveGroups.editGroupName.title', 'Group name')}
      </Typography>

      <form onSubmit={handleSubmit}>
        <InputContainer>
          <Controller
            name="name"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="edit-drive-group-input"
                data-test-id="edit-drive-group-input"
                placeholder={t(
                  'subscription.driveGroups.form.editGroupName.placeholder',
                  'Group name',
                )}
                error={error?.message}
                isDisabled={field.disabled || isFetchingDriveGroups}
                leftAddon={
                  <Icon
                    name={Icons.LABEL_OUTLINED}
                    color={theme.palette.black.main75}
                    width={24}
                    height={24}
                  />
                }
              />
            )}
          />
        </InputContainer>

        <Collapse in={!!errorMessage}>
          <Alert
            data-test-id="edit-drive-group-error-alert"
            type={NotificationState.ERROR}
            title={t(
              'subscription.driveGroups.form.editGroup.alert',
              'An error occurred while editing your group',
            )}
          >
            {errorMessage}
          </Alert>
        </Collapse>

        <ButtonWrapper>
          <Button
            data-test-id="edit-drive-group-submit-button"
            loading={isSubmitting}
            disabled={isFetchingDriveGroups || isSubmitting}
            size="medium"
            type="submit"
            color="primary"
          >
            {t(
              'subscription.driveGroups.form.editGroupName.confirmButton.label',
              'Save',
            )}
          </Button>
        </ButtonWrapper>
      </form>
    </Container>
  );
};
