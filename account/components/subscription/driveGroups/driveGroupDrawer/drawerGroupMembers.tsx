import styled from '@emotion/styled';
import { useGetDriveGroups, useModal } from 'hooks';
import { useTranslation } from 'next-i18next';
import { FC, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import { Button, Chip, Icon, theme, Typography } from 'shared-components';
import { Icons, IDriveGroup, IDriveGroupMember } from 'types';

import { InviteMemberForm } from './inviteMemberForm';
import { DeleteMemberModal } from '../../actions/driveGroups/deleteMemberModal';

const Container = styled.div`
  margin-top: 60px;
`;

const MemberLine = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: space-between;
`;

const MembersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
`;

const MemberDetails = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: center;
`;

const IconButton = styled(Button)`
  align-items: center;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  padding: 8px;
`;

interface IDrawerGroupMembers {
  selectedGroup: IDriveGroup | undefined;
  maxGroupMembers: number;
}

export const DrawerGroupMembers: FC<IDrawerGroupMembers> = ({
  selectedGroup,
  maxGroupMembers,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [selectedMember, setSelectedMember] = useState<IDriveGroupMember>();
  const [isDeleteModalOpen, openDeleteModal, closeDeleteModal] = useModal();
  const { isFetching: isFetchingDriveGroups } = useGetDriveGroups();

  const statusLabel = (member: IDriveGroupMember) =>
    t(`subscription.driveGroups.manageGroupMembers.status.${member.status}`) ===
    `subscription.driveGroups.manageGroupMembers.status.${member.status}`
      ? member.status
      : t(
          `subscription.driveGroups.manageGroupMembers.status.${member.status}`,
        );

  return (
    <>
      <DeleteMemberModal
        handleClose={() => {
          closeDeleteModal();
          setSelectedMember(undefined);
        }}
        isOpen={isDeleteModalOpen}
        selectedMember={selectedMember}
        selectedGroup={selectedGroup}
      />
      <Container>
        <Typography variant="heading-h5">
          {t(
            'subscription.driveGroups.manageGroupMembers.title',
            'Group members',
          )}
        </Typography>

        <MembersContainer>
          {selectedGroup?.members.map(member => (
            <MemberLine key={member.email}>
              <MemberDetails>
                <Typography variant="body-md-regular">
                  {member.email}
                </Typography>

                <Chip
                  variant="body-sm-regular"
                  size="small"
                  color={theme.palette.primary.main}
                  backgroundColor={theme.palette.primary.main10}
                >
                  {statusLabel(member)}
                </Chip>
              </MemberDetails>

              <IconButton
                data-test-id={`delete-drive-member-button-${member.email}`}
                color="errorSecondary"
                title={t(
                  'subscription.driveGroups.manageGroupMembers.deleteMember',
                  'Delete member',
                )}
                onClick={() => {
                  setSelectedMember(member);
                  openDeleteModal();
                }}
                disabled={
                  member.email === user?.profile.email || isFetchingDriveGroups
                }
                startIcon={<Icon name={Icons.TRASH} width={20} />}
              />
            </MemberLine>
          ))}

          <InviteMemberForm
            driveGroupId={selectedGroup?.id || ''}
            key={selectedGroup?.id}
            hasReachedMaxMembersCount={
              (selectedGroup?.members?.length || 0) >= maxGroupMembers
            }
          />
        </MembersContainer>
      </Container>
    </>
  );
};
