import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useGetDriveGroups } from 'hooks';
import { useTranslation } from 'next-i18next';
import { FC, SyntheticEvent, useState } from 'react';
import { Controller } from 'react-hook-form';
import { Alert, Button, Icon, Input, theme } from 'shared-components';
import { Icons, NotificationState } from 'types';

import { useDriveGroupInviteForm } from '@/hooks/form/driveGroups/useDriveGroupInviteForm';

const Container = styled.div`
  margin-bottom: 36px;
`;

const InputContainer = styled.div`
  margin-bottom: 24px;
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
`;

interface IInviteMemberFormProps {
  buttonDisplay?: 'regular' | 'fullWidth';
  driveGroupId: string;
  hasReachedMaxMembersCount: boolean;
}

export const InviteMemberForm: FC<IInviteMemberFormProps> = ({
  buttonDisplay,
  driveGroupId,
  hasReachedMaxMembersCount,
}) => {
  const { t } = useTranslation();
  const { isFetching: isFetchingDriveGroups } = useGetDriveGroups();

  const [isNotificationActive, setIsNotificationActive] = useState(false);
  const [notification, setNotification] = useState({
    type: NotificationState.SUCCESS,
    title: '',
    description: '',
  });

  const showNotification = (notificationProps: {
    type: NotificationState;
    title: string;
    description: string;
  }) => {
    setNotification(notificationProps);
    setIsNotificationActive(true);
  };

  const onInviteSuccess = () => {
    showNotification({
      type: NotificationState.SUCCESS,
      title: t(
        'subscription.driveGroups.form.inviteMember.success.title',
        'Invitation sent',
      ),
      description: t(
        'subscription.driveGroups.form.inviteMember.success.message',
        '{{ email }} will receive an email shortly inviting him to join your Family group.',
        { email: getValues('invite_email') },
      ),
    });
    reset();
  };

  const onInviteError = (errorMessage: string) => {
    showNotification({
      type: NotificationState.ERROR,
      title: t(
        'subscription.driveGroups.form.inviteMember.error.title',
        'An error has occured while sending your invitation',
      ),
      description: errorMessage,
    });
  };

  const { onSubmit, control, isSubmitting, getValues, reset } =
    useDriveGroupInviteForm(driveGroupId, onInviteSuccess, onInviteError, {
      invite_email: '',
    });

  const handleInviteFormSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setIsNotificationActive(false);
    onSubmit();
  };

  return (
    <Container>
      <Collapse in={isNotificationActive}>
        <Alert type={notification.type} title={notification.title}>
          {notification.description}
        </Alert>
      </Collapse>
      <Collapse in={hasReachedMaxMembersCount}>
        <Alert
          type={NotificationState.INFO}
          title={t(
            'subscription.driveGroups.form.inviteMember.maxMembersCountReached.title',
            'Maximum members count reached',
          )}
        >
          {t(
            'subscription.driveGroups.form.inviteMember.maxMembersCountReached.message',
            'You have reached the maximum number of members for this group.',
          )}
        </Alert>
      </Collapse>
      <form onSubmit={handleInviteFormSubmit}>
        <InputContainer>
          <Controller
            name="invite_email"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="invite-drive-member-input"
                data-test-id="invite-drive-member-input"
                placeholder={t(
                  'subscription.driveGroups.form.inviteMemberInput.placeholder',
                  'Invite member by email',
                )}
                error={error?.message}
                isDisabled={
                  field.disabled ||
                  hasReachedMaxMembersCount ||
                  isFetchingDriveGroups
                }
                leftAddon={
                  <Icon
                    name={Icons.EMAIL}
                    color={theme.palette.black.main75}
                    width={24}
                    height={24}
                  />
                }
              />
            )}
          />
        </InputContainer>
        <ButtonWrapper>
          <Button
            data-test-id="invite-drive-group-member-submit-button"
            size="medium"
            fullWidth={buttonDisplay === 'fullWidth'}
            disabled={
              isSubmitting || hasReachedMaxMembersCount || isFetchingDriveGroups
            }
            loading={isSubmitting}
            type="submit"
          >
            {t(
              'subscription.driveGroups.form.inviteMember.confirmButton.label',
              'Send',
            )}
          </Button>
        </ButtonWrapper>
      </form>
    </Container>
  );
};
