import styled from '@emotion/styled';
import React, { FC } from 'react';
import { theme, Typography } from 'shared-components';
import { formatDriveStorageValue } from 'utils';

import { useConfig } from '@/hooks/store/useConfig';

const Container = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
`;

const Wrapper = styled('div', {
  shouldForwardProp: prop => !['hasAddon'].includes(prop),
})<{ hasAddon: boolean }>`
  align-items: center;
  display: flex;
  justify-content: ${({ hasAddon }) => (hasAddon ? 'space-between' : 'center')};
  width: 100%;
`;

const OuterBar = styled('div', {
  shouldForwardProp: prop => !['size'].includes(prop),
})<{ size: IDriveStorageProgressBarProps['size'] }>`
  background-color: ${theme.palette.primary.main10};
  border-radius: 5px;
  border: 1px solid ${theme.palette.secondary.main25};
  height: 10px;
  overflow: hidden;
  width: ${({ size }) => (size === 'xs' ? '100px' : '140px')};
`;

const InnerBar = styled('div', {
  shouldForwardProp: prop => !['progress'].includes(prop),
})<{ progress: number }>`
  background-color: ${theme.palette.primary.main};
  height: 100%;
  transition: width 0.1s ease-in-out;
  ${({ progress }) => `width: ${progress}%;`}
`;

interface IDriveStorageProgressBarProps {
  currentStorage: number;
  maxStorage: number;
  size: 'xs' | 'md';
  addon?: React.ReactNode;
}

export const DriveStorageProgressBar: FC<IDriveStorageProgressBarProps> = ({
  currentStorage,
  maxStorage,
  size = 'md',
  addon,
}) => {
  const { locale } = useConfig();

  const label = formatDriveStorageValue(currentStorage, maxStorage, locale);
  return (
    <Container>
      <Wrapper hasAddon={!!addon}>
        <Typography variant={`body-${size}`}>{label}</Typography>
        {addon}
      </Wrapper>
      <OuterBar size={size}>
        <InnerBar progress={(currentStorage / maxStorage) * 100}></InnerBar>
      </OuterBar>
    </Container>
  );
};
