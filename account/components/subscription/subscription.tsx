import styled from '@emotion/styled';
import { usePaginatedSubscriptions } from 'hooks';
import { ReactNode } from 'react';
import { theme } from 'shared-components';
import {
  IAnyOffer,
  ICatalog,
  IMemberDetails,
  ISubscription,
  ProductFamilyId,
  ProductType,
  UserRole,
} from 'types';
import { findSubscription, findSubscriptionItemByType } from 'utils';

import SubscriptionHeading, {
  ISubscriptionHeadingProps,
} from '@/components/subscription/subscriptionHeading';
import SubscriptionPrice from '@/components/subscription/subscriptionPrice';
import SubscriptionTitle from '@/components/subscription/subscriptionTitle';
import ApiError from '@/components/ui/errors/apiError';
import PaperLoader from '@/components/ui/loader/paperLoader';
import Paper from '@/components/ui/paper';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useCatalog } from '@/hooks/useCatalog';

const Container = styled(Paper)`
  padding: 0;
  ${theme.breakpoints.up('md')} {
    padding: 0;
  }
`;

const TopWrapper = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
`;

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 64px;
  padding: 24px;
  ${theme.breakpoints.up('md')} {
    padding: 40px;
    gap: 40px;
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  ${theme.breakpoints.up('md')} {
    gap: 24px;
  }
`;

export interface ISubscriptionProps extends Partial<ISubscriptionHeadingProps> {
  contentRenderer: (
    subscription: ISubscription,
    itemOffer: IAnyOffer | undefined,
    catalog: ICatalog,
  ) => ReactNode;
  subscriptionTitle: string;
  addonRenderer?: (
    subscription: ISubscription | undefined,
    itemOffer: IAnyOffer | undefined,
    catalog: ICatalog,
  ) => ReactNode;
  type: ProductType;
  isB2b?: boolean;
  productFamilyId: ProductFamilyId;
  userDetails?: IMemberDetails;
}

const Subscription = ({
  productFamilyId,
  logo,
  illustration,
  description,
  subscriptionTitle,
  contentRenderer,
  addonRenderer,
  type,
  userDetails,
}: ISubscriptionProps) => {
  const currentMemberQuery = useCurrentMember();
  const hasAdminRights = [UserRole.ADMIN, UserRole.OWNER].includes(
    currentMemberQuery.data?.role as UserRole,
  );
  const isB2b = currentMemberQuery.data?.user?.b2b;
  const subscriptionsQuery = usePaginatedSubscriptions(
    isB2b ? !hasAdminRights : true,
  );
  const currentSubscription = findSubscription(
    subscriptionsQuery.data?.items,
    productFamilyId,
  );
  const catalogQuery = useCatalog(currentSubscription?.id);

  if (subscriptionsQuery.isError || catalogQuery.isError) {
    return <ApiError />;
  }

  if (catalogQuery.isLoading || subscriptionsQuery.isLoading) {
    return <PaperLoader />;
  }

  const currentSubscriptionItem = findSubscriptionItemByType(
    currentSubscription,
    type,
  );

  const hasSubscriptionAndAssignedVm = subscriptionsQuery.data.items.find(
    sub =>
      sub.user_id === userDetails?.id &&
      sub.product_family_id === productFamilyId,
  );

  const amount = currentSubscriptionItem?.price;
  const itemOffer =
    catalogQuery.data.offers.byId[currentSubscriptionItem?.id ?? ''];

  const isVmsSubscription =
    (hasAdminRights || hasSubscriptionAndAssignedVm) &&
    productFamilyId === ProductFamilyId.CLOUDPC &&
    currentSubscription;

  const isDriveSubscription =
    currentSubscription && productFamilyId === ProductFamilyId.SHADOWDRIVE;

  return (
    <Container>
      <Wrapper>
        {logo && (
          <SubscriptionHeading
            logo={logo}
            illustration={illustration}
            description={description}
          />
        )}
        {(isVmsSubscription || isDriveSubscription) && (
          <Content>
            <TopWrapper>
              <SubscriptionTitle name={subscriptionTitle} />
              {!!amount && !!itemOffer && (
                <SubscriptionPrice
                  isAddon={type === ProductType.ADDON}
                  amount={amount}
                  period={itemOffer.period}
                  periodUnit={itemOffer.period_unit}
                />
              )}
            </TopWrapper>
            {contentRenderer(currentSubscription, itemOffer, catalogQuery.data)}
          </Content>
        )}
      </Wrapper>
      {addonRenderer?.(currentSubscription, itemOffer, catalogQuery.data)}
    </Container>
  );
};

export default Subscription;
