import { useTranslation } from 'next-i18next';
import { IMemberDetails, ProductFamilyId, ProductType, UserRole } from 'types';
import { IMAGE_PATH, SVG_PATH } from 'utils';

import VmManagerAddons from './vmManagerAddons';

import Subscription, {
  ISubscriptionProps,
} from '@/components/subscription/subscription';
import VmSubscription from '@/components/subscription/vm/vmSubscription';
import { useCurrentMember } from '@/hooks/member/useMember';

interface IVmManagerProps {
  onVmPeriodicityChangeSuccess?: (value: any) => void;
  onScheduledChangeCancelSuccess?: () => void;
}

const VmManager = ({
  onVmPeriodicityChangeSuccess,
  onScheduledChangeCancelSuccess,
}: IVmManagerProps) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();

  const userDetails = currentMemberQuery.data?.user;
  const isB2b = userDetails?.b2b as IMemberDetails['b2b'];
  const hasAdminRights = [UserRole.ADMIN, UserRole.OWNER].includes(
    currentMemberQuery.data?.role as UserRole,
  );
  const hideActionButtons = isB2b || !hasAdminRights;

  const renderContent: ISubscriptionProps['contentRenderer'] = (
    subscription,
    planOffer,
  ) => {
    return planOffer ? (
      <VmSubscription
        subscription={subscription}
        planOffer={planOffer}
        hideActionButtons={hideActionButtons}
        onVmPeriodicityChangeSuccess={onVmPeriodicityChangeSuccess}
        onScheduledChangeCancelSuccess={onScheduledChangeCancelSuccess}
      />
    ) : (
      <></>
    );
  };

  const renderAddons: ISubscriptionProps['addonRenderer'] = (
    _subscription,
    planOffer,
    catalog,
  ) => <VmManagerAddons planOffer={planOffer} catalog={catalog} />;

  return (
    <Subscription
      productFamilyId={ProductFamilyId.CLOUDPC}
      type={ProductType.PLAN}
      data-test-id="subscription-vm"
      logo={
        !isB2b
          ? {
              src: `${SVG_PATH}logos/shadow-dark.svg`,
              alt: t('subscription.plan.vm.logoAlt', 'Shadow'),
              id: 'logo-shadow-dark',
              width: 143,
              height: 32,
            }
          : {
              src: `${SVG_PATH}logos/shadow-business.svg`,
              alt: t('businessManager.subscription.plan.vm.logoAlt', 'Shadow'),
              id: 'logo-shadow-business',
              width: 160,
              height: 56,
            }
      }
      illustration={{
        src: `${IMAGE_PATH}shadow-vm.png`,
        alt: t('subscription.plan.vm.illustrationAlt', 'Shadow PC'),
        width: 272,
        height: 120,
      }}
      description={t(
        'subscription.plan.vm.description',
        'All you need from a PC, in just a click: Anything you can do on a PC; Works on all your devices; Secured storage for all your files; Ready-to-use',
      )}
      isB2b={isB2b}
      userDetails={userDetails}
      subscriptionTitle={
        hasAdminRights
          ? t('subscription.plan.vm.title', 'My subscription')
          : t('subscription.plan.vm.titleMember', 'My assigned PC')
      }
      contentRenderer={renderContent}
      addonRenderer={renderAddons}
    />
  );
};

export default VmManager;
