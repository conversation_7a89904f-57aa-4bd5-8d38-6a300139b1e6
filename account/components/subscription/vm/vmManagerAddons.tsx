import { usePaginatedSubscriptions } from 'hooks';
import { useGetPossibleSubscriptionUpdates } from 'hooks';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { FC } from 'react';
import { ClickTracker } from 'shared-components';
import {
  ProductId,
  IMemberDetails,
  ProductFamilyId,
  IAnyOffer,
  ICatalog,
  UserRole,
  ProductType,
} from 'types';
import { canUserUpdateToOffer, findSubscription } from 'utils';

import SubscriptionAddon from '@/components/subscription/addon/subscriptionAddon';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { SHOP_URL, ROUTES_PATH } from '@/utils/constants';

interface IVmManagerAddonsProps {
  planOffer: IAnyOffer | undefined;
  catalog: ICatalog;
}

const VmManagerAddons: FC<IVmManagerAddonsProps> = ({ planOffer, catalog }) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const router = useRouter();
  const userDetails = currentMemberQuery.data?.user;
  const isBillingAccount = userDetails?.isbillingaccount;
  const isB2b = userDetails?.b2b as IMemberDetails['b2b'];
  const isMember = currentMemberQuery.data?.role === UserRole.MEMBER;

  const subscriptionsQuery = usePaginatedSubscriptions(true);
  const { isCloudPcSubscriptionOnHold } = useIsSubscriptionOnHold();

  const currentSubscription = findSubscription(
    subscriptionsQuery.data?.items,
    ProductFamilyId.CLOUDPC,
  );

  const displayNoSubscription =
    !currentSubscription && !isB2b && isBillingAccount;

  const possibleSubscriptionUpdatesQuery = useGetPossibleSubscriptionUpdates(
    currentSubscription?.id as string,
  );

  // TODO: Remove all use of ProductId in favor of meta_data of plans.
  const powerShadowPcOffer = Object.values(catalog.offers.byId).find(offer => {
    return (
      offer.product_id === ProductId.CLOUDPC_B2C_POWER_C1 &&
      offer.periodicity === planOffer?.periodicity &&
      offer?.stock?.in_stock
    );
  });
  const powerPlusShadowPcOffer = Object.values(catalog.offers.byId).find(
    offer => {
      return (
        offer.product_id === ProductId.CLOUDPC_B2C_POWER2023 &&
        offer.periodicity === planOffer?.periodicity &&
        offer?.stock?.in_stock
      );
    },
  );

  const canUpgradeToPower =
    !!possibleSubscriptionUpdatesQuery.data &&
    canUserUpdateToOffer(
      possibleSubscriptionUpdatesQuery.data,
      ProductId.CLOUDPC_B2C_POWER_C1,
    );

  const canUpgradeToPowerPlus =
    !!possibleSubscriptionUpdatesQuery.data &&
    canUserUpdateToOffer(
      possibleSubscriptionUpdatesQuery.data,
      ProductId.CLOUDPC_B2C_POWER2023,
    );

  // We want to display Power Upgrade label only if Power+ is not available
  const canDisplayPowerUpgrade =
    planOffer &&
    powerShadowPcOffer &&
    !powerPlusShadowPcOffer &&
    canUpgradeToPower;

  const canDisplayPowerPlusUpgrade =
    planOffer && !!powerPlusShadowPcOffer && canUpgradeToPowerPlus;

  const upgradePowerOffer = powerPlusShadowPcOffer ?? powerShadowPcOffer;

  const shouldDisplayUpgradeToPower =
    !isB2b &&
    !isCloudPcSubscriptionOnHold &&
    upgradePowerOffer &&
    (canDisplayPowerUpgrade || canDisplayPowerPlusUpgrade);

  const hasSubscriptionAndAssignedVm = subscriptionsQuery?.data?.items.find(
    sub =>
      sub.user_id === userDetails?.id &&
      sub.product_family_id === ProductFamilyId.CLOUDPC,
  );

  const planItem = hasSubscriptionAndAssignedVm?.items.find(
    item => item.item_type === ProductType.PLAN,
  );

  const changePlanUrl = `${SHOP_URL}?funnel=change_plan&familyId=${ProductFamilyId.CLOUDPC}&subscription=${currentSubscription?.id}`;
  const getNewSubscriptionUrl = `${SHOP_URL}b2c?familyId=${ProductFamilyId.CLOUDPC}`;

  const handleRedirect = () => {
    router.push(ROUTES_PATH.VM_MANAGER);
  };

  return (
    <>
      {shouldDisplayUpgradeToPower && hasSubscriptionAndAssignedVm && (
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'modify_my_subscription',
            },
          }}
        >
          <SubscriptionAddon
            id="powerUpgrade"
            label={t(
              'subscription.plan.vm.powerPlus.get.link',
              'Need more power? Modify your subscription.',
            )}
            href={changePlanUrl}
            amount={upgradePowerOffer.price * 100}
            hasStartingPrice
            period={upgradePowerOffer.period}
            periodUnit={upgradePowerOffer.period_unit}
          />
        </ClickTracker>
      )}
      {displayNoSubscription && (
        <SubscriptionAddon
          id="cloudpc"
          label={t(
            'subscription.plan.vm.shadow.get.link',
            'Get your Shadow PC',
          )}
          href={getNewSubscriptionUrl}
          showPrice={false}
          hasSecondaryBackground
          description={t(
            'subscription.plan.vm.shadow.get.description',
            'Click here to access our shop and buy your new Shadow PC',
          )}
        />
      )}
      {!hasSubscriptionAndAssignedVm && isMember && (
        <SubscriptionAddon
          id="shadowPc"
          label={t(
            'subscription.plan.vm.shadow.ask',
            'Ask your admin for a PC.',
          )}
          showPrice={false}
          icon={false}
        />
      )}
      {hasSubscriptionAndAssignedVm && isMember && (
        <SubscriptionAddon
          id="shadowPc"
          label={t(`subscription.details.name.${planItem?.name}`)}
          showPrice={false}
          icon={false}
        />
      )}
      {currentSubscription && !hasSubscriptionAndAssignedVm && (
        <SubscriptionAddon
          id="shadowPc"
          showPrice={false}
          label={t(
            'subscription.plan.vm.assign.title',
            'No Shadow PC assigned',
          )}
          onClick={handleRedirect}
          description={t(
            'subscription.plan.vm.assign.description',
            'A Shadow PC has not been assigned. Please assign a PC.',
          )}
        />
      )}
    </>
  );
};

export default VmManagerAddons;
