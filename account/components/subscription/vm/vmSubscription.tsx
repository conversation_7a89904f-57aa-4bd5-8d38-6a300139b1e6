import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import React, { ReactElement, useState } from 'react';
import { Button, ClickTracker, Icon, theme } from 'shared-components';
import { ISubscription, SubscriptionStatus, Icons, IAnyOffer } from 'types';

import CancelVmButton from '../actions/cancelVm/cancelVmButton';
import CancelVmModalWithRetention from '../actions/cancelVm/cancelVmModalWithRetention';
import ChangeVmPeriodicity from '../actions/changeVmPeriodicity/changeVmPeriodicity';
import ChangeVmPlan from '../actions/changeVmPlan/changeVmPlan';
import { SubscriptionContainer } from '../styles';

import ReactivateVm from '@/components/subscription/actions/reactivateVm/reactivateVm';
import RedeemAccessCode from '@/components/subscription/actions/redeemAccessCode/redeemAccessCode';
import ResetVm from '@/components/subscription/actions/resetVm/resetVm';
import SubscriptionList from '@/components/subscription/subscriptionList';
import ButtonPopover from '@/components/ui/buttonPopover';
import SubscriptionDiscount from '@/components/ui/SubscriptionDiscount/SubscriptionDiscount';
import UserScheduledChange from '@/components/ui/userScheduledChange/userScheduledChange';
import {
  useCanUpdateBillingPeriodicity,
  useIsSubscriptionOnHold,
} from '@/hooks/subscription/useSubscription';
import { useDate } from '@/hooks/useDate';
import { SHOP_URL } from '@/utils/constants';

interface IVmSubscriptionProps {
  subscription: ISubscription;
  planOffer: IAnyOffer;
  hideActionButtons?: boolean;
  onVmPeriodicityChangeSuccess?: (element: ReactElement) => void;
  onScheduledChangeCancelSuccess?: () => void;
}

const ActionButtons = styled.div`
  display: flex;
  align-items: start;
  gap: 16px;
  margin-top: 16px;

  ${theme.breakpoints.up('md')} {
    justify-content: inherit;
    gap: 24px;
    margin-top: 0px;
  }
`;

const SubscriptionRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;

  ${theme.breakpoints.up('md')} {
    justify-content: flex-end;
    flex-direction: row;
    gap: 24px;
    width: 100%;
  }
`;

const VmSubscription = ({
  subscription,
  planOffer,
  hideActionButtons = false,
  onVmPeriodicityChangeSuccess,
  onScheduledChangeCancelSuccess,
}: IVmSubscriptionProps) => {
  const { t } = useTranslation();
  const { getSubscriptionStartDate } = useDate();

  const { isCloudPcSubscriptionOnHold } = useIsSubscriptionOnHold();
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  const [isActionsPopoverOpen, setIsActionsPopoverOpen] =
    useState<boolean>(false);
  const status = subscription.status as SubscriptionStatus;
  const isSubscriptionActive = status === SubscriptionStatus.ACTIVE;
  const isSubscriptionNonRenewing = status === SubscriptionStatus.NON_RENEWING;

  const getSubscriptionDetails = () => {
    const details = [
      {
        label: t('subscription.details.label', {
          defaultValue: '{{planName}} ({{status}} {{startDate}})',
          planName: t(`subscription.details.name.${planOffer.product_id}`),
          status: t(`subscription.details.status.${subscription.status}`),
          startDate: getSubscriptionStartDate(subscription),
        }),
        id: planOffer.product_id,
      },
    ];

    return details;
  };

  const displayRedeemAccessCode =
    isSubscriptionActive && !isCloudPcSubscriptionOnHold;
  const displayChangePlan =
    isSubscriptionActive && !isCloudPcSubscriptionOnHold;
  const displayChangeVmPeriodicity =
    useCanUpdateBillingPeriodicity(subscription) &&
    (isSubscriptionActive || isSubscriptionNonRenewing) &&
    !isCloudPcSubscriptionOnHold;
  const displayUserScheduledChange = subscription?.has_scheduled_changes;

  return (
    <>
      <SubscriptionContainer>
        <CancelVmModalWithRetention
          subscription={subscription}
          isCancelModalOpen={isCancelModalOpen}
          setIsCancelModalOpen={setIsCancelModalOpen}
        />
        <SubscriptionRow>
          {/* Subscriptions list (name + date) */}
          <SubscriptionList
            status={status}
            details={getSubscriptionDetails()}
          />
          {!hideActionButtons && (
            <ActionButtons>
              {/* Reactivate VM button */}
              {subscription.status === SubscriptionStatus.NON_RENEWING && (
                <ReactivateVm subscription={subscription} />
              )}
              {/* Resubscribe subscription button */}
              {subscription.status === SubscriptionStatus.CANCELLED && (
                <ClickTracker
                  eventPayload={{
                    action: 'click',
                    parameters: {
                      event_category: 'my_account_modify_subscription',
                      event_label: 'shadow_resubscribe',
                    },
                  }}
                >
                  <Button
                    data-test-id="vm-resubscribe"
                    key="vm-resubscribe"
                    href={`${SHOP_URL}billing-details`}
                    target="_blank"
                    size="small"
                  >
                    {t(
                      'subscription.plan.vm.action.resubscribe',
                      'Resubscribe',
                    )}
                  </Button>
                </ClickTracker>
              )}
              {/* Popover button with multiple actions  */}
              {(isSubscriptionActive || isSubscriptionNonRenewing) && (
                <ButtonPopover
                  dataTestId="subscription-vm-edit"
                  icon={<Icon name={Icons.EDIT} width={16} height={16} />}
                  name={`vmDetails-more`}
                  isOpen={isActionsPopoverOpen}
                  setIsOpen={setIsActionsPopoverOpen}
                  label={t('subscription.plan.vm.label', 'Edit')}
                  hasBackground={true}
                >
                  {/* Change periodicity button that opens modal */}
                  {displayChangeVmPeriodicity && (
                    <ChangeVmPeriodicity
                      setIsActionsPopoverOpen={setIsActionsPopoverOpen}
                      subscription={subscription}
                      onVmPeriodicityChangeSuccess={
                        onVmPeriodicityChangeSuccess
                      }
                    />
                  )}
                  {/* Change VM plan button */}
                  {displayChangePlan && (
                    <ChangeVmPlan subscription={subscription} />
                  )}
                  {/* Redeem access code button */}
                  {displayRedeemAccessCode && (
                    <RedeemAccessCode
                      subscription={subscription}
                      setIsActionsPopoverOpen={setIsActionsPopoverOpen}
                    />
                  )}
                  {/* Reset vm button */}
                  {(isSubscriptionActive || isSubscriptionNonRenewing) && (
                    <ResetVm
                      subscription={subscription}
                      setIsActionsPopoverOpen={setIsActionsPopoverOpen}
                    />
                  )}
                  {/* Cancel subscription button */}
                  <CancelVmButton
                    subscription={subscription}
                    setIsCancelModalOpen={setIsCancelModalOpen}
                    buttonVersion="subscription"
                    onClick={() => setIsActionsPopoverOpen(false)}
                  />
                </ButtonPopover>
              )}
            </ActionButtons>
          )}
        </SubscriptionRow>
      </SubscriptionContainer>
      {displayUserScheduledChange && (
        <UserScheduledChange
          subscription={subscription}
          onScheduledChangeCancelSuccess={onScheduledChangeCancelSuccess}
        />
      )}
      {isSubscriptionActive && (
        <SubscriptionDiscount subscriptionId={subscription.id} />
      )}
    </>
  );
};

export default VmSubscription;
