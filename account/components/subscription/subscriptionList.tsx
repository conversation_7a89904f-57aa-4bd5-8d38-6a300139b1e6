import styled from '@emotion/styled';
import {
  Icon,
  theme,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons, SubscriptionStatus } from 'types';

import { STATUS_COLORS } from '@/utils/constants';

interface SubscriptionListDetail {
  id: string;
  label: string;
}
interface ISubscriptionListProps {
  details: SubscriptionListDetail[];
  status: SubscriptionStatus;
}

const Container = styled.ul`
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  padding: 0;
  column-gap: 24px;
  row-gap: 8px;
  ${theme.breakpoints.down('md')} {
    justify-content: space-between;
  }
`;

const ListItem = styled.li`
  display: inline-flex;
  align-items: center;
`;

const StatusIcon = styled(Icon)<{ status: SubscriptionStatus }>`
  margin-right: 8px;
  ${({ status }) =>
    status && `color: ${STATUS_COLORS[status || SubscriptionStatus.ACTIVE]};`}
`;

const SubscriptionList = ({ details, status }: ISubscriptionListProps) => {
  const { isMD } = useThemeMediaQueries();

  return (
    <Container data-test-id="subscription-list">
      {details.map(detail => (
        <ListItem
          data-test-id={`subscription-list-item-${detail.id}`}
          key={detail.id}
        >
          <StatusIcon
            name={Icons.CIRCLE}
            width={8}
            color={theme.palette.primary.main}
            status={status}
          />
          <Typography
            data-test-id={`subscription-list-item-${detail.id}-label`}
            variant={isMD ? 'body-sm' : 'body-xs'}
          >
            {detail.label}
          </Typography>
        </ListItem>
      ))}
    </Container>
  );
};

export default SubscriptionList;
