import styled from '@emotion/styled';
import type { ReactNode } from 'react';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';

interface ISubscriptionTitleProps {
  name: string;
  actions?: ReactNode[] | null;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  gap: 16px;
  ${theme.breakpoints.up('md')} {
    gap: 24px;
  }
`;

const SubscriptionTitle = ({ name }: ISubscriptionTitleProps) => {
  const { isMD } = useThemeMediaQueries();

  return (
    <Container>
      {/* Subscription type name */}
      <Typography color="primary" variant={isMD ? 'heading-h5' : 'heading-h6'}>
        {name}
      </Typography>
    </Container>
  );
};

export default SubscriptionTitle;
