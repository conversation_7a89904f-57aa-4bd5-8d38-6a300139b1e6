import styled from '@emotion/styled';
import { ReactNode } from 'react';
import { Icon, Typography, useThemeMediaQueries } from 'shared-components';
import { Icons } from 'types';

interface ICancelInfosProps {
  title: string;
  items: Array<{
    label: string;
    description: ReactNode;
  }>;
}

const Container = styled.div`
  display: block;
  &:not(:last-child) {
    margin-bottom: 40px;
  }
`;

const List = styled.ul`
  display: block;
  padding-top: 16px;
`;

const ListItem = styled.li`
  margin-bottom: 16px;
`;

const TitleWrapper = styled.div`
  display: flex;
  margin-bottom: 4px;
  align-items: center;
`;

const Title = styled(Typography)`
  flex: 1;
`;

const CircleIcon = styled(Icon)`
  margin-right: 8px;
`;

const CancelInfo = ({ title, items }: ICancelInfosProps) => {
  const { isLG } = useThemeMediaQueries();

  return (
    <Container>
      <Typography
        variant={isLG ? 'body-lg-regular' : 'body-md-regular'}
        color="primary"
      >
        {title}
      </Typography>
      <List>
        {items.map(({ label, description }, index) => (
          <ListItem key={index}>
            <TitleWrapper>
              <CircleIcon name={Icons.CIRCLE} width={4} />
              <Title variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}>
                {label}
              </Title>
            </TitleWrapper>
            <Typography variant={isLG ? 'body-sm' : 'body-xs'}>
              {description}
            </Typography>
          </ListItem>
        ))}
      </List>
    </Container>
  );
};

export default CancelInfo;
