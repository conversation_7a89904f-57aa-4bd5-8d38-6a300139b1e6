import { useTranslation } from 'next-i18next';
import { useRef, useState } from 'react';
import { Modal, ModalButton, ModalLink } from 'shared-components';
import { CouponType } from 'types';

import CouponForm from '@/components/forms/couponForm/couponForm';

interface IActivateDriveProps {
  isOpen: boolean;
  handleClose: () => void;
  onActivateDriveSuccess?: () => void;
  onActivateDriveError?: () => void;
  isPremium?: boolean;
}

const ActivateDrive = ({
  isOpen,
  handleClose,
  onActivateDriveSuccess,
  onActivateDriveError,
  isPremium,
}: IActivateDriveProps) => {
  const { t } = useTranslation();
  const submitCouponRef = useRef<HTMLButtonElement | null>(null);

  const [isSubmittingActivateFreeDrive, setIsSubmittingActivateFreeDrive] =
    useState<boolean>(false);

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isSubmittingActivateFreeDrive) {
      return;
    }

    handleClose();
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingActivateFreeDrive}
      aria-labelledby="activate-free-drive-title"
      aria-describedby="activate-free-drive-description"
      title={
        isPremium
          ? t(
              'subscription.activatePremiumDrive.title',
              'Get my Premium Shadow Drive',
            )
          : t(
              'subscription.activateFreeDrive.title',
              'Activate my free Shadow Drive',
            )
      }
      subtitle={
        isPremium
          ? t(
              'subscription.activatePremiumDrive.description',
              'By activating your Premium Shadow Drive, you agree to our ToS.',
            )
          : t(
              'subscription.activateFreeDrive.description',
              'By activating your free Shadow Drive, you agree to our ToS.',
            )
      }
    >
      <CouponForm
        onSuccess={onActivateDriveSuccess}
        onError={onActivateDriveError}
        onSubmitting={isSubmitting =>
          setIsSubmittingActivateFreeDrive(isSubmitting)
        }
        hideSubmitButton
        submitRef={submitCouponRef}
        couponType={
          isPremium ? CouponType.DRIVE_PREMIUM : CouponType.DRIVE_FREE
        }
      />
      <ModalButton
        loading={isSubmittingActivateFreeDrive}
        onClick={() => submitCouponRef?.current?.click()}
      >
        {isPremium
          ? t(
              'subscription.activatePremiumDrive.submitLabel',
              'Get my Premium Shadow Drive',
            )
          : t(
              'subscription.activateFreeDrive.submitLabel',
              'Activate my free Shadow Drive',
            )}
      </ModalButton>
      <ModalLink onClick={handleClose}>
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};

export default ActivateDrive;
