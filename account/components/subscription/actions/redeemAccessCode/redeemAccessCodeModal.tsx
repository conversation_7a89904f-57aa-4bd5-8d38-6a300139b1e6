import { useTranslation } from 'next-i18next';
import { useRef, useState } from 'react';
import { Modal, ModalButton, ModalLink } from 'shared-components';
import { CouponType } from 'types';

import CouponForm from '@/components/forms/couponForm/couponForm';

interface IRedeemAccessCodeModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onRedeemAccessCodeSuccess?: () => void;
  onRedeemAccessCodeError?: () => void;
  subscriptionId?: string;
}

const RedeemAccessCodeModal = ({
  isOpen,
  handleClose,
  onRedeemAccessCodeSuccess,
  onRedeemAccessCodeError,
  subscriptionId,
}: IRedeemAccessCodeModalProps) => {
  const { t } = useTranslation();
  const submitCouponRef = useRef<HTMLButtonElement | null>(null);

  const [isSubmittingRedeemAccessCode, setIsSubmittingRedeemAccessCode] =
    useState<boolean>(false);

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isSubmittingRedeemAccessCode) {
      return;
    }
    handleClose();
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingRedeemAccessCode}
      aria-labelledby="redeem-access-code-title"
      aria-describedby="redeem-access-code-description"
      title={t(
        'subscription.redeemAccessCode.modal.title',
        'Upgrade to an other Shadow PC',
      )}
      subtitle={t(
        'subscription.redeemAccessCode.modal.description',
        'By activating your new plan, you agree to our ToS.',
      )}
    >
      <CouponForm
        onSuccess={onRedeemAccessCodeSuccess}
        onError={onRedeemAccessCodeError}
        onSubmitting={isSubmitting =>
          setIsSubmittingRedeemAccessCode(isSubmitting)
        }
        hideSubmitButton
        submitRef={submitCouponRef}
        couponType={CouponType.POWER}
        subscriptionId={subscriptionId}
      />
      <ModalButton
        loading={isSubmittingRedeemAccessCode}
        fullWidth
        onClick={() => submitCouponRef?.current?.click()}
        size="medium"
      >
        {t('subscription.redeemAccessCode.modal.submitLabel', 'Upgrade')}
      </ModalButton>
      <ModalLink onClick={handleClose}>
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};

export default RedeemAccessCodeModal;
