import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import React, { useState } from 'react';
import { ClickTracker, Icon } from 'shared-components';
import { Icons, ISubscription } from 'types';

import RedeemAccessCodeModal from './redeemAccessCodeModal';

import Link from '@/components/ui/link';

interface IRedeemAccessCodeProps {
  setIsActionsPopoverOpen: (value: boolean) => void;
  subscription: ISubscription;
}
const RedeemAccessCode = ({
  setIsActionsPopoverOpen,
  subscription,
}: IRedeemAccessCodeProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const [isRedeemAccessCodeModalOpen, setIsRedeemAccessCodeModalOpen] =
    useState<boolean>(false);
  const onRedeemAccessCodeSuccess = () => {
    setIsRedeemAccessCodeModalOpen(false);
    notifySuccess(
      t(
        'subscription.redeemAccessCode.notification.success',
        'You have successfully activated your private offer!',
      ),
    );
  };
  const onRedeemAccessCodeError = () => {
    setIsRedeemAccessCodeModalOpen(false);
    notifyError(
      t(
        'subscription.redeemAccessCode.notification.error',
        'We could not activate your private offer, please try again later.',
      ),
    );
  };

  const onHandleClose = () => {
    setIsActionsPopoverOpen(false);
    setIsRedeemAccessCodeModalOpen(false);
  };

  return (
    <>
      <ClickTracker
        eventPayload={{
          action: 'click',
          parameters: {
            event_category: 'my_account_modify_subscription',
            event_label: 'enter_code',
          },
        }}
      >
        <Link
          data-test-id="redeem-access-code-button"
          key="redeem-access-code"
          onClick={() => setIsRedeemAccessCodeModalOpen(true)}
          variant="label-sm-regular"
          startIcon={<Icon name={Icons.PLUS_CIRCLE} width={16} height={16} />}
        >
          {t('subscription.plan.vm.action.redeemAccessCode', 'Redeem code')}
        </Link>
      </ClickTracker>
      <RedeemAccessCodeModal
        subscriptionId={subscription?.id ?? ''}
        isOpen={isRedeemAccessCodeModalOpen}
        handleClose={onHandleClose}
        onRedeemAccessCodeSuccess={onRedeemAccessCodeSuccess}
        onRedeemAccessCodeError={onRedeemAccessCodeError}
      />
    </>
  );
};

export default RedeemAccessCode;
