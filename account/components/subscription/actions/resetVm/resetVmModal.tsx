import { useTranslation } from 'next-i18next';
import { useRef, useState } from 'react';
import { Alert, Modal, ModalButton, ModalLink } from 'shared-components';
import { IMemberDetails, NotificationState, ResetVmSteps } from 'types';
import { trackEvent } from 'utils';

import ResetVmForm from '@/components/forms/resetVmForm/resetVmForm';
import WordConfirmationForm from '@/components/forms/wordConfirmationForm/wordConfirmationForm';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import { FORM_VALIDATION_WORD_COMPARE_RESET_VM } from '@/utils/constants';

interface IResetVmModalProps {
  isOpen: boolean;
  subscriptionId: string;
  handleClose: () => void;
  onResetVmSuccess: () => void;
  onResetVmError: () => void;
}

const RESET_VM_STEP: Record<ResetVmSteps, string> = {
  password: 'password-confirmation',
  configuration: 'vm-configuration',
};

const ResetVmModal = ({
  isOpen,
  subscriptionId,
  handleClose,
  onResetVmSuccess,
  onResetVmError,
}: IResetVmModalProps) => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const { t } = useTranslation();
  const { language } = useConfig();
  const submitPasswordRef = useRef<HTMLButtonElement | null>(null);
  const submitResetVmRef = useRef<HTMLButtonElement | null>(null);

  const initialStep = RESET_VM_STEP.password;
  const [step, setStep] = useState<string>(initialStep);
  const [isSubmittingPassword, setIsSubmittingPassword] =
    useState<boolean>(false);
  const [isSubmittingResetVm, setIsSubmittingResetVm] =
    useState<boolean>(false);

  const closeAndResetInitialStep = () => {
    handleClose();
    // Timeout that prevents a flicker by letting the fade out animation end before reseting state.
    setTimeout(() => {
      setStep(initialStep);
    }, 200);
  };

  const handleDialogClose = (reason: string) => {
    if (
      reason === 'backdropClick' &&
      (isSubmittingPassword || isSubmittingResetVm)
    ) {
      return;
    }

    closeAndResetInitialStep();
  };

  const renderErrorAlert = () => (
    <Alert
      type={NotificationState.ERROR}
      title={t('subscription.resetVm.alert.title', 'Warning')}
    >
      {t(
        'subscription.resetVm.alert.description',
        'You will lose all your data (except D: Disk) in the process.',
      )}
    </Alert>
  );

  const computeSubtitle = () => {
    switch (step) {
      case RESET_VM_STEP.password:
        return t(
          'subscription.resetVm.subtitle',
          'Resetting your Shadow will bring a new clean install of your Windows, and reset all your settings.',
        );
      case RESET_VM_STEP.configuration:
        return t(
          'subscription.resetVm.selectConfiguration',
          'Please select your new configuration',
        );
    }
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingPassword || isSubmittingResetVm}
      aria-labelledby="reset-vm-title"
      aria-describedby="reset-vm-description"
      title={t('subscription.resetVm.title', 'Reset my Shadow')}
      subtitle={computeSubtitle()}
    >
      {step === RESET_VM_STEP.password && (
        <>
          {renderErrorAlert()}
          <WordConfirmationForm
            onSubmitting={isSubmitting => setIsSubmittingPassword(isSubmitting)}
            onSuccess={() => setStep(RESET_VM_STEP.configuration)}
            hideSubmitButton
            submitRef={submitPasswordRef}
            wordToCompare={FORM_VALIDATION_WORD_COMPARE_RESET_VM[language]}
          />
        </>
      )}
      {step === RESET_VM_STEP.configuration && (
        <>
          {renderErrorAlert()}
          <ResetVmForm
            onSubmitting={isSubmitting => setIsSubmittingResetVm(isSubmitting)}
            subscriptionId={subscriptionId}
            onSuccess={() => {
              onResetVmSuccess();
              trackEvent('click', {
                event_category: 'reset_my_shadow',
                event_label: 'reset_confirmed',
              });
            }}
            onError={onResetVmError}
            hideSubmitButton
            submitRef={submitResetVmRef}
            isB2b={isB2b}
          />
        </>
      )}
      {step === RESET_VM_STEP.password && (
        <ModalButton
          data-test-id="reset-vm-modal-continue-button"
          loading={isSubmittingPassword}
          onClick={() => submitPasswordRef?.current?.click()}
          color={isB2b ? 'black' : 'primary'}
        >
          {t('global.continue', 'Continue', { ns: 'common' })}
        </ModalButton>
      )}
      {step === RESET_VM_STEP.configuration && (
        <ModalButton
          color="error"
          loading={isSubmittingResetVm}
          onClick={() => submitResetVmRef?.current?.click()}
        >
          {t('subscription.resetVm.resetLabel', 'Reset my Shadow PC')}
        </ModalButton>
      )}
      <ModalLink onClick={closeAndResetInitialStep}>
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};

export default ResetVmModal;
