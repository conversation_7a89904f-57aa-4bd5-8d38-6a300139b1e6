import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import React, { useState } from 'react';
import { ClickTracker, Icon } from 'shared-components';
import { Icons, ISubscription } from 'types';

import ResetVmModal from './resetVmModal';

import Link from '@/components/ui/link';

interface IResetVmProps {
  setIsActionsPopoverOpen: (value: boolean) => void;
  subscription: ISubscription;
}
const ResetVm = ({ setIsActionsPopoverOpen, subscription }: IResetVmProps) => {
  const { t } = useTranslation();
  const [isResetModalOpen, setIsResetModalOpen] = useState<boolean>(false);
  const { notifyError, notifySuccess } = useNotifications();

  const onResetVmSuccess = () => {
    setIsResetModalOpen(false);
    notifySuccess(
      t(
        'subscription.resetVm.notification.success',
        'You have successfully triggered a reset of your PC. This process will take around 1 hour.',
      ),
    );
  };
  const onResetVmError = () => {
    setIsResetModalOpen(false);
    notifyError(
      t(
        'subscription.resetVm.notification.error',
        'There was an error when trying to reset your PC, please try again later or contact support.',
      ),
    );
  };

  const onHandleClose = () => {
    setIsActionsPopoverOpen(false);
    setIsResetModalOpen(false);
  };

  return (
    <>
      <ClickTracker
        eventPayload={{
          action: 'click',
          parameters: {
            event_category: 'my_account_modify_subscription',
            event_label: 'reset_my_shadow',
          },
        }}
      >
        <Link
          data-test-id="reset-vm-button"
          key="vm-reset"
          color="error"
          onClick={() => setIsResetModalOpen(true)}
          variant="label-sm-regular"
          startIcon={<Icon name={Icons.ROTATE_RIGHT} width={16} height={16} />}
        >
          {t('subscription.plan.vm.action.reset', 'Reset my Shadow PC')}
        </Link>
      </ClickTracker>
      <ResetVmModal
        isOpen={isResetModalOpen}
        handleClose={onHandleClose}
        subscriptionId={subscription?.id ?? ''}
        onResetVmSuccess={onResetVmSuccess}
        onResetVmError={onResetVmError}
      />
    </>
  );
};

export default ResetVm;
