import { useTranslation } from 'next-i18next';
import React from 'react';
import { ClickTracker, Icon } from 'shared-components';
import { ISubscription, Icons, ProductFamilyId } from 'types';

import Link from '@/components/ui/link';
import { SHOP_URL } from '@/utils/constants';

interface ChangeVmPlanProps {
  subscription: ISubscription;
}

const ChangeVmPlan = ({ subscription }: ChangeVmPlanProps) => {
  const { t } = useTranslation();

  return (
    <ClickTracker
      eventPayload={{
        action: 'click',
        parameters: {
          event_category: 'my_account_modify_subscription',
          event_label: 'change_offer',
        },
      }}
    >
      <Link
        data-test-id="change-vm-plan-button"
        key="change-vm-plan"
        component="button"
        variant="label-sm-regular"
        startIcon={<Icon name={Icons.EDIT} width={16} height={16} />}
        onClick={() => {
          window.location.href = `${SHOP_URL}?funnel=change_plan&familyId=${ProductFamilyId.CLOUDPC}&subscription=${subscription?.id}`;
        }}
      >
        {t('subscription.plan.vm.action.changePlan', 'Change plan')}
      </Link>
    </ClickTracker>
  );
};

export default ChangeVmPlan;
