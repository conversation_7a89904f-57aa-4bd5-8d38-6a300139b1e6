import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Modal, ModalLink } from 'shared-components';
import { IMemberDetails } from 'types';

import VmNameForm from '@/components/forms/vmNameForm/vmNameForm';
import { useCurrentMember } from '@/hooks/member/useMember';
import { IVmNameForm } from '@/types/api';

interface IVmNameModalProps {
  vmName: string;
  isOpen: boolean;
  handleClose: () => void;
  onRenameVm: (name: string) => void;
}

const VmNameModal = ({
  vmName = '',
  isOpen,
  handleClose,
  onRenameVm,
}: IVmNameModalProps) => {
  const { t } = useTranslation();
  const [isSubmittingName, setIsSubmittingName] = useState<boolean>(false);

  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isSubmittingName) {
      return;
    }

    handleClose();
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingName}
      aria-labelledby="rename-vm-title"
      aria-describedby="rename-vm-description"
      title={t('subscription.vmName.title', 'Rename this PC')}
    >
      <VmNameForm
        onSubmitting={isSubmitting => setIsSubmittingName(isSubmitting)}
        onSuccess={(formValues: IVmNameForm) => onRenameVm(formValues.name)}
        isB2b={isB2b}
        vmName={vmName}
      />
      <ModalLink onClick={handleClose}>
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};

export default VmNameModal;
