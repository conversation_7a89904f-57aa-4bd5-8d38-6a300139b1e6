import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import React from 'react';
import {
  Icon,
  ModalLink,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons } from 'types';

const List = styled.ul`
  display: block;
  padding-top: 16px;
`;

const ListItem = styled.li`
  margin-bottom: 16px;
`;

const TextWrapper = styled.div`
  display: flex;
  margin-bottom: 4px;
  align-items: center;
`;

const Text = styled(Typography)`
  flex: 1;
`;

const CircleIcon = styled(Icon)`
  margin-right: 8px;
`;

interface ICancelVmModalDiscountAcceptedProps {
  handleDialogClose: () => void;
  discountAmount: string;
}

const CancelVmModalDiscountAccepted = ({
  handleDialogClose,
  discountAmount,
}: ICancelVmModalDiscountAcceptedProps) => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();

  return (
    <>
      <List>
        <ListItem>
          <TextWrapper>
            <CircleIcon name={Icons.CIRCLE} width={4} />
            <Text variant={isLG ? 'body-sm' : 'body-xs'}>
              {t(
                'subscription.cancelVm.discountAccepted.confirmationText',
                'You can now continue to play, create, work, or do anything you want on your Shadow PC',
              )}
            </Text>
          </TextWrapper>
        </ListItem>
        <ListItem>
          <TextWrapper>
            <CircleIcon name={Icons.CIRCLE} width={4} />
            <Text variant={isLG ? 'body-sm' : 'body-xs'}>
              <Trans
                i18nKey="subscription.cancelVm.discountAccepted.confirmationText2"
                defaults={
                  'Good news! We will deduct <bold>{{ amount }}</bold> from your next bill.'
                }
                values={{ amount: discountAmount }}
                components={{ bold: <strong /> }}
              />
            </Text>
          </TextWrapper>
        </ListItem>
      </List>
      <ModalLink
        data-test-id="cancel-vm-modal-ok-button"
        onClick={handleDialogClose}
      >
        {t('global.ok', 'OK', { ns: 'common' })}
      </ModalLink>
    </>
  );
};

export default CancelVmModalDiscountAccepted;
