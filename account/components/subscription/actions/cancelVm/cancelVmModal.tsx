import { Trans, useTranslation } from 'next-i18next';
import { useEffect, useRef, useState } from 'react';
import { Modal } from 'shared-components';
import {
  Currency,
  ISubscriptionRetention,
  ProductType,
  type CancelVmSteps,
  type ISubscription,
} from 'types';
import {
  DATE_FORMAT_BY_MARKET,
  findSubscriptionItemByType,
  logError,
} from 'utils';

import CancelVmModalConfirmation from './cancelVmModalConfirmation';
import CancelVmModalDiscount from './cancelVmModalDiscount';
import CancelVmModalDiscountAccepted from './cancelVmModalDiscountAccepted';
import CancelVmModalInformation from './cancelVmModalInformation';
import CancelVmModalSurvey from './cancelVmModalSurvey';

import {
  useApplySubscriptionRetention,
  useCancelSubscription,
} from '@/hooks/subscription/useSubscription';
import { useDate } from '@/hooks/useDate';
import { usePrice } from '@/hooks/usePrice';
import type { CancellationReasons } from '@/types/subscriptions';

interface ICancelVmModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onCancelVmSuccess?: () => void;
  onCancelVmError: () => void;
  onAcceptDiscountSuccess?: () => void;
  onAcceptDiscountError?: () => void;
  subscription: ISubscription;
  email: string | undefined;
  isB2b?: boolean;
  firstStep?: string;
  coupon?: ISubscriptionRetention['coupon'];
}

export const CANCEL_VM_STEP: Record<CancelVmSteps, string> = {
  discount: 'cancel-vm-discount',
  discountAccepted: 'cancel-vm-discount-accepted',
  survey: 'cancel-vm-survey',
  information: 'cancel-vm-information',
  confirmation: 'cancel-vm-confirmation',
};

const CancelVmModal = ({
  isOpen,
  handleClose,
  onCancelVmSuccess,
  onCancelVmError,
  onAcceptDiscountSuccess,
  onAcceptDiscountError,
  subscription,
  email,
  isB2b,
  firstStep = CANCEL_VM_STEP.survey,
  coupon,
}: ICancelVmModalProps) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();

  const { getSubscriptionNextBillingDate } = useDate();
  const cancelSubscription = useCancelSubscription();
  const applySubscriptionRetention = useApplySubscriptionRetention();

  const submitReasonRef = useRef<HTMLButtonElement | null>(null);
  const [currentStep, setCurrentStep] = useState(firstStep);
  const [isCancellingVm, setIsCancellingVm] = useState<boolean>(false);
  const [isAcceptingDiscount, setIsAcceptingDiscount] =
    useState<boolean>(false);
  const isAnyRequestLoading = isAcceptingDiscount || isCancellingVm;
  const [lastBillingDate, setLastBillingDate] = useState<string | null>(null);
  const [cancellationReasons, setCancellationReasons] =
    useState<CancellationReasons>({
      reasons: [],
      comment: '',
    });

  // We need this useEffect because when the subscription is
  // cancelled from the modal, the lastBillingDate is null.
  useEffect(() => {
    const subscriptionNextBillingDate = getSubscriptionNextBillingDate(
      subscription,
      DATE_FORMAT_BY_MARKET,
    );
    if (subscriptionNextBillingDate) {
      setLastBillingDate(subscriptionNextBillingDate);
    }
  }, [getSubscriptionNextBillingDate, subscription]);

  const currentSubscriptionItem = findSubscriptionItemByType(
    subscription,
    ProductType.PLAN,
  );

  if (!currentSubscriptionItem) {
    logError('currentSubscriptionItem is undefined');
  }

  const currentPrice = currentSubscriptionItem?.price
    ? formatPrice(
        currentSubscriptionItem?.price,
        subscription?.currency_code as Currency,
      )
    : '';
  const discountedPrice = currentSubscriptionItem?.price
    ? formatPrice(
        currentSubscriptionItem?.price - (coupon?.discount_amount || 0),
        subscription?.currency_code as Currency,
      )
    : '';
  const discountAmount = coupon?.discount_amount
    ? formatPrice(
        coupon?.discount_amount,
        subscription?.currency_code as Currency,
      )
    : '';

  const handleDialogClose = (reason?: string) => {
    if (reason && reason === 'backdropClick' && isAnyRequestLoading) {
      return;
    }

    handleClose();
    setCurrentStep(firstStep);
  };

  const handleCancelVM = async () => {
    setIsCancellingVm(true);

    try {
      await cancelSubscription.mutateAsync({
        cancellationReasons,
        subscriptionId: subscription.id,
      });
      setCurrentStep(CANCEL_VM_STEP.confirmation);
      onCancelVmSuccess?.();
    } catch (e) {
      setCurrentStep(CANCEL_VM_STEP.survey);
      onCancelVmError();
      logError('handleCancelVM', e);
    } finally {
      setIsCancellingVm(false);
    }
  };

  const handleAcceptDiscount = async () => {
    setIsAcceptingDiscount(true);
    try {
      await applySubscriptionRetention.mutateAsync(subscription.id);
      setCurrentStep(CANCEL_VM_STEP.discountAccepted);
      onAcceptDiscountSuccess?.();
    } catch (e) {
      onAcceptDiscountError?.();
      logError('handleAccept', e);
    } finally {
      setIsAcceptingDiscount(false);
    }

    setCurrentStep(CANCEL_VM_STEP.discountAccepted);
  };

  const handleDeclineDiscount = () => {
    setCurrentStep(CANCEL_VM_STEP.survey);
  };

  const computeTitle = () => {
    switch (currentStep) {
      case CANCEL_VM_STEP.discount:
        return t(
          'subscription.cancelVm.discount.title',
          'We are sad to let you go!',
        );
      case CANCEL_VM_STEP.discountAccepted:
        return t(
          'subscription.cancelVm.discountAccepted.title',
          'Delighted to keep you with us!',
        );
      case CANCEL_VM_STEP.survey:
        return t(
          'subscription.cancelVm.survey.title',
          "We're always improving our service and your feedback matters",
        );
      case CANCEL_VM_STEP.information:
        return (
          <Trans
            i18nKey="subscription.cancelVm.information.subtitle"
            defaults="If you cancel your subscription, your access to Shadow will end on <bold>{{ lastBillingDate }}</bold>"
            values={{ lastBillingDate }}
            components={{ bold: <strong /> }}
          />
        );
      case CANCEL_VM_STEP.confirmation:
        return t(
          'subscription.cancelVm.confirmation.title',
          'Your subscription has been canceled',
        );
    }
  };

  const computeSubtitle = () => {
    switch (currentStep) {
      case CANCEL_VM_STEP.survey:
        return t(
          'subscription.cancelVm.survey.subtitle',
          'Please select your main reason for canceling. It will only take a minute.',
        );
      case CANCEL_VM_STEP.information:
        return (
          <Trans
            i18nKey="subscription.cancelDrive.information.subtitle"
            defaults="If you cancel your subscription, your access to Shadow Drive will end on <bold>{{ lastBillingDate }}</bold>"
            values={{ lastBillingDate }}
            components={{ bold: <strong /> }}
          />
        );
      case CANCEL_VM_STEP.confirmation:
        return t(
          'subscription.cancelVm.confirmation.subtitle',
          'We are sad to see you leave.',
        );
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case CANCEL_VM_STEP.discount:
        return (
          <CancelVmModalDiscount
            discountedPrice={discountedPrice}
            regularPrice={currentPrice}
            onAccept={handleAcceptDiscount}
            onDecline={handleDeclineDiscount}
            isAccepting={isAnyRequestLoading}
          />
        );
      case CANCEL_VM_STEP.discountAccepted:
        return (
          <CancelVmModalDiscountAccepted
            handleDialogClose={handleDialogClose}
            discountAmount={discountAmount}
          />
        );
      case CANCEL_VM_STEP.survey:
        return (
          <CancelVmModalSurvey
            setCurrentStep={setCurrentStep}
            setCancellationReasons={setCancellationReasons}
            isB2b={isB2b}
            submitRef={submitReasonRef}
          />
        );
      case CANCEL_VM_STEP.confirmation:
        return (
          <CancelVmModalConfirmation
            email={email}
            handleDialogClose={handleDialogClose}
            isB2b={isB2b}
            lastBillingDate={lastBillingDate}
          />
        );
      case CANCEL_VM_STEP.information:
        return (
          <CancelVmModalInformation
            handleCancelVM={handleCancelVM}
            handleDialogClose={handleDialogClose}
            isCancellingVm={isCancellingVm}
            isB2b={isB2b}
            lastBillingDate={lastBillingDate}
          />
        );
    }
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isAnyRequestLoading}
      aria-labelledby="cancel-vm-title"
      aria-describedby="cancel-vm-description"
      title={computeTitle()}
      subtitle={computeSubtitle()}
      size={currentStep === CANCEL_VM_STEP.discount ? 'lg' : 'md'}
    >
      {renderCurrentStep()}
    </Modal>
  );
};

export default CancelVmModal;
