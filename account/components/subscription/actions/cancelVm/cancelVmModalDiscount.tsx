import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import React from 'react';
import {
  theme,
  Button,
  Typography,
  Alert,
  useThemeMediaQueries,
} from 'shared-components';
import { NotificationState } from 'types';

const StyledAlert = styled(Alert)`
  padding: 40px 24px;
  border: 1px solid ${theme.palette.primary.main}40;
`;

const ButtonsGroup = styled('div', {
  shouldForwardProp: prop => prop !== 'isMD',
})<{ isMD?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: ${props => (props.isMD ? 'row' : 'column')};
  gap: ${props => (props.isMD ? '60px' : '30px')};
  width: 100%;
  margin-top: 40px;
`;

const AcceptButton = styled(Button)`
  background-color: ${theme.palette.white.main};
`;

const DeclineButton = styled(Button)`
  background-color: ${theme.palette.primary.main}40;
`;

interface ICancelVmModalDiscountProps {
  discountedPrice: string;
  regularPrice: string;
  onAccept: () => void;
  onDecline: () => void;
  isAccepting: boolean;
}

const CancelVmModalDiscount = ({
  discountedPrice,
  regularPrice,
  onAccept,
  onDecline,
  isAccepting,
}: ICancelVmModalDiscountProps) => {
  const { t } = useTranslation();
  const { isMD } = useThemeMediaQueries();

  return (
    <StyledAlert type={NotificationState.INFO} hideIcon withoutMargin>
      <Typography
        variant={isMD ? 'body-md-regular' : 'body-sm-regular'}
        align="center"
      >
        <Trans
          i18nKey="subscription.cancelVm.discount.description"
          defaults="That’s why we want to offer you your Shadow PC for <bold>only {{ discountedPrice }}</bold> next month instead of {{ regularPrice }}."
          values={{ discountedPrice, regularPrice }}
          components={{ bold: <strong /> }}
        />
      </Typography>
      <ButtonsGroup isMD={isMD}>
        <AcceptButton
          data-test-id="cancel-vm-modal-accept-discount-button"
          size="medium"
          fullWidth
          variant={isMD ? 'label-md-regular' : 'label-sm-regular'}
          color="secondary"
          loading={isAccepting}
          onClick={onAccept}
        >
          {t('subscription.cancelVm.discount.acceptDiscountButton.label', {
            defaultValue:
              'Yes, I want to keep Shadow PC for another month at {{ discountedPrice }}',
            discountedPrice: discountedPrice,
          })}
        </AcceptButton>
        <DeclineButton
          data-test-id="cancel-vm-modal-decline-discount-button"
          size="medium"
          fullWidth
          variant={isMD ? 'label-md-regular' : 'label-sm-regular'}
          color="secondary"
          disabled={isAccepting}
          onClick={onDecline}
        >
          {t(
            'subscription.cancelVm.discount.declineDiscountButton.label',
            'No, I definitely want to cancel',
          )}
        </DeclineButton>
      </ButtonsGroup>
    </StyledAlert>
  );
};

export default CancelVmModalDiscount;
