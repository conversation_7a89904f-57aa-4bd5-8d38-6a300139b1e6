import { useFsFlag } from '@flagship.io/react-sdk';
import { useQueryClient } from '@tanstack/react-query';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import React from 'react';
import { IMemberDetails, ISubscription } from 'types';
import { getPlanOfferFromSubscription, dataLayerEvent } from 'utils';

import CancelVmModal from './cancelVmModal';

import { CANCEL_VM_STEP } from '@/components/subscription/actions/cancelVm/cancelVmModal';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useGetSubscriptionRetention } from '@/hooks/subscription/useSubscription';
import { useCatalog } from '@/hooks/useCatalog';

interface ICancelVmProps {
  subscription: ISubscription;
  isCancelModalOpen: boolean;
  setIsCancelModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const CancelVmModalWithRetention = ({
  subscription,
  isCancelModalOpen,
  setIsCancelModalOpen,
}: ICancelVmProps) => {
  const { t } = useTranslation();
  const { notifyError } = useNotifications();
  const queryClient = useQueryClient();
  const currentMemberQuery = useCurrentMember();
  const user = currentMemberQuery.data?.user as IMemberDetails;
  const catalogQuery = useCatalog(subscription.id);
  const productName = catalogQuery?.data
    ? getPlanOfferFromSubscription(catalogQuery?.data, subscription)?.name
    : undefined;
  const isAntiChurnDiscountEnabled = useFsFlag(
    'feature_antichurndiscount_enabled',
    false,
  ).getValue();

  const {
    data: subscriptionRetentionData,
    isLoading: isSubscriptionRetentionLoading,
  } = useGetSubscriptionRetention(subscription.id);

  const isEligibleForDiscountAndIsEnabled =
    isAntiChurnDiscountEnabled && subscriptionRetentionData?.eligibility;

  const commonChurningProcessEventPayload = {
    uri: window.location.pathname,
    pageName: 'CS - Shadow PC',
    subscriptionId: subscription.id,
    productName: productName,
    productId: subscription.plan_id,
    userId: user.id,
    antiChurnEligible: subscriptionRetentionData?.eligibility,
  };

  const onCancelVmSuccess = () => {
    dataLayerEvent('churning_process', {
      ...commonChurningProcessEventPayload,
      antiChurnRevokeChurn: false,
    });
  };

  const onCancelVmError = () => {
    setIsCancelModalOpen(false);
    notifyError(
      t(
        'subscription.cancelVm.notification.error',
        'There was an error when trying to cancel your subscription, please try again later or contact support.',
      ),
    );
  };

  const onAcceptDiscountSuccess = () => {
    dataLayerEvent('churning_process', {
      ...commonChurningProcessEventPayload,
      antiChurnEligible: true,
      antiChurnRevokeChurn: true,
    });
  };

  const onAcceptDiscountError = () => {
    setIsCancelModalOpen(false);
    notifyError(
      t(
        'subscription.cancelVm.discount.notification.error',
        'There was an error, please try again later or contact support.',
      ),
    );
  };

  const handleClose = () => {
    setIsCancelModalOpen(false);
    queryClient.invalidateQueries({
      queryKey: ['subscriptionRetention'],
      refetchType: 'none',
    });
  };

  return (
    <>
      {!isSubscriptionRetentionLoading ? (
        <CancelVmModal
          firstStep={
            isEligibleForDiscountAndIsEnabled
              ? CANCEL_VM_STEP.discount
              : CANCEL_VM_STEP.survey
          }
          isOpen={isCancelModalOpen}
          handleClose={handleClose}
          subscription={subscription}
          email={currentMemberQuery.data?.user?.email}
          onCancelVmError={onCancelVmError}
          onCancelVmSuccess={onCancelVmSuccess}
          onAcceptDiscountSuccess={onAcceptDiscountSuccess}
          onAcceptDiscountError={onAcceptDiscountError}
          coupon={subscriptionRetentionData?.coupon}
        />
      ) : null}
    </>
  );
};

export default CancelVmModalWithRetention;
