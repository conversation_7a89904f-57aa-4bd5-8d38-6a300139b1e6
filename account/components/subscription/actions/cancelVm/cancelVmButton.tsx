import { useTranslation } from 'next-i18next';
import React from 'react';
import { ClickTracker, Icon } from 'shared-components';
import {
  Icons,
  IMemberDetails,
  ISubscription,
  SubscriptionStatus,
} from 'types';
import { getPlanOfferFromSubscription, dataLayerEvent } from 'utils';

import Link from '@/components/ui/link';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useGetSubscriptionRetention } from '@/hooks/subscription/useSubscription';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { useCatalog } from '@/hooks/useCatalog';

interface ICancelVmProps {
  subscription: ISubscription;
  setIsCancelModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  buttonVersion: 'manager' | 'subscription';
  onClick?: () => void;
}

const CancelVmButton = ({
  subscription,
  setIsCancelModalOpen,
  buttonVersion,
  onClick,
}: ICancelVmProps) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const user = currentMemberQuery.data?.user as IMemberDetails;
  const catalogQuery = useCatalog(subscription.id);
  const productName = catalogQuery?.data
    ? getPlanOfferFromSubscription(catalogQuery?.data, subscription)?.name
    : undefined;

  const {
    data: subscriptionRetentionData,
    isLoading: isSubscriptionRetentionLoading,
  } = useGetSubscriptionRetention(subscription.id);

  const isSubscriptionActive =
    subscription.status === SubscriptionStatus.ACTIVE;

  const commonChurningProcessEventPayload = {
    uri: window.location.pathname,
    pageName: 'CS - Shadow PC',
    subscriptionId: subscription.id,
    productName: productName,
    productId: subscription.plan_id,
    userId: user.id,
    antiChurnEligible: subscriptionRetentionData?.eligibility,
  };
  const { isCloudPcSubscriptionOnHold } = useIsSubscriptionOnHold();

  const shouldDisableCancelButton = isCloudPcSubscriptionOnHold;

  const handleClick = () => {
    if (!isSubscriptionRetentionLoading) {
      onClick?.();
      setIsCancelModalOpen(true);

      dataLayerEvent('starting_churning_process', {
        ...commonChurningProcessEventPayload,
        antiChurnRevokeChurn: undefined,
      });
    }
  };

  const renderButtonIcon = () => {
    if (buttonVersion === 'manager') {
      return <Icon name={Icons.TRASH} width={16} height={16} />;
    }
    return <Icon name={Icons.CROSS} width={12} height={12} />;
  };

  const renderButtonLabel = () => {
    if (buttonVersion === 'manager') {
      return t('businessManager.vmDetails.vm.deleteVm', 'Delete this PC');
    }
    return t('subscription.plan.vm.action.cancel', 'Cancel my subscription');
  };

  return (
    <>
      {isSubscriptionActive && (
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_modify_subscription',
              event_label: 'cancel_my_shadow',
            },
          }}
        >
          <Link
            data-test-id="cancel-vm-button"
            key="vm-cancel"
            color="error"
            disabled={shouldDisableCancelButton}
            onClick={handleClick}
            component="button"
            variant="label-sm-regular"
            startIcon={renderButtonIcon()}
          >
            {renderButtonLabel()}
          </Link>
        </ClickTracker>
      )}
    </>
  );
};

export default CancelVmButton;
