import { Trans, useTranslation } from 'next-i18next';
import React from 'react';
import { ModalButton, ModalLink } from 'shared-components';

import CancelInfo from '@/components/subscription/cancelInfo';

interface ICancelVmModalInformationProps {
  handleCancelVM: () => void;
  handleDialogClose: () => void;
  isCancellingVm: boolean;
  isB2b?: boolean;
  lastBillingDate: string | null;
}

const CancelVmModalInformation = ({
  handleCancelVM,
  handleDialogClose,
  isCancellingVm,
  isB2b,
  lastBillingDate,
}: ICancelVmModalInformationProps) => {
  const { t } = useTranslation();

  return (
    <>
      <CancelInfo
        title={t(
          'subscription.cancelVm.information.whenSubscriptionEnds.title',
          'When your subscription ends',
        )}
        items={[
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionEnds.shadowAccess',
              "You'll lose access to your Shadow",
            ),
            description: t(
              'subscription.cancelVm.information.whenSubscriptionEnds.dataAccess',
              "You'll no longer be able to access games, data, or software on your high-performance PC.",
            ),
          },
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionEnds.reSubscribe.title',
              'Restart your subscription anytime!',
            ),
            description: (
              <Trans
                i18nKey="subscription.cancelVm.information.whenSubscriptionEnds.description"
                defaults="If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!"
                values={{ lastBillingDate }}
                components={{ bold: <strong /> }}
              />
            ),
          },
        ]}
      />
      <CancelInfo
        title={t(
          'subscription.cancelVm.information.whenSubscriptionActive.title',
          'While your subscription is still active',
        )}
        items={[
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionActive.enjoy',
              'Enjoy the most out of your games',
            ),
            description: t(
              'subscription.cancelVm.information.whenSubscriptionActive.access',
              'You can still access your Shadow until the end of your subscription. You still have time to make it to the top of the leaderboard.',
            ),
          },
          {
            label: t(
              'subscription.cancelVm.information.whenSubscriptionActive.backupTip1',
              "Don't forget to back up your files",
            ),
            description: t(
              'subscription.cancelVm.information.whenSubscriptionActive.backupTip2',
              'Make sure to transfer important files from Shadow to your local PC. Your data will be deleted after your subscription ends.',
            ),
          },
        ]}
      />
      <ModalButton
        data-test-id="cancel-vm-modal-confirm-button"
        color={isB2b ? 'black' : 'primary'}
        loading={isCancellingVm}
        onClick={handleCancelVM}
      >
        {t('global.confirm', 'Confirm', { ns: 'common' })}
      </ModalButton>
      {!isCancellingVm && (
        <ModalLink
          data-test-id="cancel-vm-modal-abort-button"
          onClick={handleDialogClose}
        >
          {t('global.cancel', 'Cancel', { ns: 'common' })}
        </ModalLink>
      )}
    </>
  );
};

export default CancelVmModalInformation;
