import { useTranslation } from 'next-i18next';
import React, { RefObject, useState } from 'react';
import { ModalButton, ModalLink } from 'shared-components';

import CancelVmFormB2B from '@/components/business/cancelVmFormB2B/cancelVmFormB2B';
import CancelVmForm from '@/components/forms/cancelVmForm/cancelVmForm';
import { CANCEL_VM_STEP } from '@/components/subscription/actions/cancelVm/cancelVmModal';

interface ICancelVmSurveyProps {
  setCurrentStep: (CANCEL_VM_STEP: string) => void;
  setCancellationReasons: (payload: { reasons: any; comment: string }) => void;
  submitRef: RefObject<HTMLButtonElement> | null;
  isB2b?: boolean;
}
const CancelVmModalSurvey = ({
  setCurrentStep,
  setCancellationReasons,
  isB2b,
  submitRef,
}: ICancelVmSurveyProps) => {
  const { t } = useTranslation();
  const [hasSelectedReason, setHasSelectedReason] = useState<boolean>(false);

  const onCancelVmFormSuccess = (data: any) => {
    const { comment, ...reasons } = data;
    setCancellationReasons({
      reasons: Object.keys(reasons),
      comment,
    });
    setCurrentStep(CANCEL_VM_STEP.information);
  };
  const onCancelVmFormSelection = ({ reasons, comment }: any) => {
    const hasReason = reasons.some((reason: string) => reason) || comment;

    setHasSelectedReason(hasReason);
  };

  return (
    <>
      {isB2b ? (
        <CancelVmFormB2B
          onSelection={onCancelVmFormSelection}
          onSuccess={onCancelVmFormSuccess}
          hideSubmitButton
          submitRef={submitRef}
        />
      ) : (
        <CancelVmForm
          onSelection={onCancelVmFormSelection}
          onSuccess={onCancelVmFormSuccess}
          hideSubmitButton
          submitRef={submitRef}
        />
      )}
      <ModalButton
        data-test-id="cancel-vm-modal-next-button"
        color={isB2b ? 'black' : 'primary'}
        disabled={!hasSelectedReason}
        onClick={() => submitRef?.current?.click()}
      >
        {t('global.next', 'Next', { ns: 'common' })}
      </ModalButton>
      <ModalLink
        data-test-id="cancel-vm-modal-skipReason-button"
        onClick={() => setCurrentStep(CANCEL_VM_STEP.information)}
      >
        {t('global.skipReason', "I don't want to answer", {
          ns: 'common',
        })}
      </ModalLink>
    </>
  );
};

export default CancelVmModalSurvey;
