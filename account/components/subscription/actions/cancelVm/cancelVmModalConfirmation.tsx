import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import React from 'react';
import {
  ModalButton,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';

const ListItem = styled.li`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  ::before {
    content: '•';
    font-size: 18px;
  }
`;

interface ICancelVmModalConfirmationProps {
  email: string | undefined;
  handleDialogClose: () => void;
  isB2b?: boolean;
  lastBillingDate: string | null;
}

const CancelVmModalConfirmation = ({
  email,
  handleDialogClose,
  isB2b,
  lastBillingDate,
}: ICancelVmModalConfirmationProps) => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();

  return (
    <>
      <ul>
        <ListItem>
          <Typography variant={isLG ? 'body-sm' : 'body-xs'}>
            <Trans
              i18nKey="subscription.cancelVm.confirmation.subscription.item-1"
              defaults="You will still have access to your Shadow until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>"
              values={{ lastBillingDate }}
              components={{ bold: <strong /> }}
            />
          </Typography>
        </ListItem>
        <ListItem>
          <Typography variant={isLG ? 'body-sm' : 'body-xs'}>
            <Trans
              i18nKey="subscription.cancelVm.confirmation.subscription.item-2"
              defaults="We will send you a confirmation email to <bold>{{ email }}</bold>"
              values={{ email }}
              components={{ bold: <strong /> }}
            />
          </Typography>
        </ListItem>
        <ListItem>
          <Typography variant={isLG ? 'body-sm' : 'body-xs'}>
            {t(
              'subscription.cancelVm.confirmation.subscription.item-3',
              "You won't be charged anymore.",
            )}
          </Typography>
        </ListItem>
        <ListItem>
          <Typography variant={isLG ? 'body-sm' : 'body-xs'}>
            <Trans
              i18nKey="subscription.cancelVm.confirmation.subscription.changedYourMind"
              defaults="We are always improving Shadow so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime."
              components={{ bold: <strong /> }}
            />
          </Typography>
        </ListItem>
      </ul>
      <ModalButton
        data-test-id="cancel-vm-modal-ok-button"
        color={isB2b ? 'black' : 'primary'}
        onClick={handleDialogClose}
      >
        {t('global.ok', 'OK', { ns: 'common' })}
      </ModalButton>
    </>
  );
};

export default CancelVmModalConfirmation;
