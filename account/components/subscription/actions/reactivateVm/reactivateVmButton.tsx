import { useTranslation } from 'next-i18next';
import React, { FC } from 'react';
import { ClickTracker } from 'shared-components';

import Link from '@/components/ui/link';

interface IReactivateVmButtonProps {
  setIsReactivateModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onClick: () => void;
}

const ReactivateVm: FC<IReactivateVmButtonProps> = ({
  setIsReactivateModalOpen,
  onClick,
}) => {
  const { t } = useTranslation();

  const handleClick = () => {
    setIsReactivateModalOpen(true);
    onClick?.();
  };

  return (
    <ClickTracker
      eventPayload={{
        action: 'click',
        parameters: {
          event_category: 'my_account_modify_subscription',
          event_label: 'shadow_reactivate',
        },
      }}
    >
      <Link
        data-test-id="subscription-vm-reactivate"
        key="vm-reactivate"
        onClick={handleClick}
        component="button"
        variant="label-sm-regular"
        color="primary"
      >
        {t('subscription.plan.vm.action.reactivate', 'Reactivate')}
      </Link>
    </ClickTracker>
  );
};

export default ReactivateVm;
