import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import React, { useState } from 'react';
import { Button, ClickTracker } from 'shared-components';
import { ISubscription } from 'types';

import ReactivateVmModal from './reactivateVmModal';

interface IReactivateVmProps {
  subscription: ISubscription;
}

const ReactivateVm = ({ subscription }: IReactivateVmProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();

  const [isReactivateModalOpen, setIsReactivateModalOpen] =
    useState<boolean>(false);
  const onReactivateVmSuccess = () => {
    setIsReactivateModalOpen(false);
    notifySuccess(
      t(
        'subscription.reactivateVm.notification.success',
        'You have successfully reactivated your subscription.',
      ),
    );
  };
  const onReactivateVmError = () => {
    setIsReactivateModalOpen(false);
    notifyError(
      t(
        'subscription.reactivateVm.notification.error',
        'We could not reactivate your subscription, please try again later.',
      ),
    );
  };

  return (
    <>
      <ClickTracker
        eventPayload={{
          action: 'click',
          parameters: {
            event_category: 'my_account_modify_subscription',
            event_label: 'shadow_reactivate',
          },
        }}
      >
        <Button
          data-test-id="subscription-vm-reactivate"
          key="vm-reactivate"
          onClick={() => {
            setIsReactivateModalOpen(true);
          }}
          size="small"
        >
          {t('subscription.plan.vm.action.reactivate', 'Reactivate')}
        </Button>
      </ClickTracker>
      <ReactivateVmModal
        isOpen={isReactivateModalOpen}
        handleClose={() => setIsReactivateModalOpen(false)}
        subscriptionId={subscription?.id ?? ''}
        onReactivateVmSuccess={onReactivateVmSuccess}
        onReactivateVmError={onReactivateVmError}
      />
    </>
  );
};

export default ReactivateVm;
