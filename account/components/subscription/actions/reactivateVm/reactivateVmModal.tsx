import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  Modal,
  ModalButton,
  ModalLink,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { IMemberDetails } from 'types';
import { logError, trackEvent } from 'utils';

import { useCurrentMember } from '@/hooks/member/useMember';
import { useReactivateSubscription } from '@/hooks/subscription/useSubscription';

interface IReactivateVmModalProps {
  isOpen: boolean;
  subscriptionId: string;
  handleClose: () => void;
  onReactivateVmSuccess: () => void;
  onReactivateVmError: () => void;
}

const ReactivateVmModal = ({
  isOpen,
  subscriptionId,
  handleClose,
  onReactivateVmSuccess,
  onReactivateVmError,
}: IReactivateVmModalProps) => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();
  const reactivateSubscription = useReactivateSubscription();

  const [isSubmittingReactivateVm, setIsSubmittingReactivateVm] =
    useState<boolean>(false);

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isSubmittingReactivateVm) {
      return;
    }

    handleClose();
  };

  const handleReactivateVm = async () => {
    setIsSubmittingReactivateVm(true);

    try {
      await reactivateSubscription.mutateAsync(subscriptionId);

      trackEvent('click', {
        event_category: 'shadow_reactivate',
        event_label: 'reactivate_confirmed',
      });

      onReactivateVmSuccess();
    } catch (e) {
      onReactivateVmError();
      logError('handleReactivateVm', e);
    }

    setIsSubmittingReactivateVm(false);
  };

  return (
    <Modal
      data-test-id="reactivate-vm-modal"
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingReactivateVm}
      aria-labelledby="reactivate-vm-title"
      aria-describedby="reactivate-vm-description"
      title={t('subscription.reactivateVm.title', 'Reactivate my Shadow')}
    >
      <Typography
        data-test-id="reactivate-vm-modal-description"
        variant={isLG ? 'body-sm' : 'body-xs'}
        align="center"
      >
        {t(
          'subscription.reactivateVm.description',
          'Your subscription is currently cancelled but can be reactivated right now.',
        )}
      </Typography>
      <ModalButton
        data-test-id="reactivate-vm-modal-confirm-button"
        loading={isSubmittingReactivateVm}
        onClick={handleReactivateVm}
        color={isB2b ? 'black' : 'primary'}
      >
        {t('subscription.reactivateVm.resetLabel', 'Reactivate my Shadow PC')}
      </ModalButton>

      <ModalLink
        data-test-id="reactivate-vm-modal-cancel-button"
        onClick={handleClose}
      >
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};

export default ReactivateVmModal;
