import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Alert, Button, Modal, Typography } from 'shared-components';
import {
  IAnyOffer,
  IDriveGroup,
  ISubscription,
  NotificationState,
} from 'types';
import {
  getAddons,
  getOfferFromId,
  getOffer,
  formatOctetStorageValue,
} from 'utils';

import { useConfig } from '@/hooks/store/useConfig';
import { useCatalog } from '@/hooks/useCatalog';
import { usePrice } from '@/hooks/usePrice';
import { SHOP_URL } from '@/utils/constants';

const Container = styled.div`
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  padding-top: 24px;
`;

interface IAddStorageModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onSuccess?: () => void;
  onError?: () => void;
  selectedGroup: IDriveGroup | undefined;
  currentSubscription: ISubscription;
}

export const AddStorageModal = ({
  isOpen,
  handleClose,
  selectedGroup,
  currentSubscription,
}: IAddStorageModalProps) => {
  const { t } = useTranslation();
  const { locale } = useConfig();
  const { formatPrice } = usePrice();
  const catalogQuery = useCatalog(currentSubscription?.id);

  const currentSubscribedOffer = getOfferFromId(
    catalogQuery.data,
    currentSubscription.plan_id,
  );

  const addons =
    getAddons(catalogQuery.data, currentSubscribedOffer?.product_id || '') ??
    [];
  const addonOffers = addons
    .map(addon =>
      getOffer(
        catalogQuery.data,
        addon.id,
        offer =>
          offer.periodicity ===
          (currentSubscribedOffer?.periodicity ?? '1-month'),
      ),
    )
    .filter(offer => offer !== undefined) as IAnyOffer[];

  const renderOffersOrAlert = () => {
    if (addonOffers.length === 0) {
      return (
        <Alert
          type={NotificationState.INFO}
          title={t(
            'subscription.driveGroups.addStorage.noUpgrades.title',
            'No upgrades available',
          )}
        >
          {t(
            'subscription.driveGroups.addStorage.noUpgrades',
            'There are no upgrades available for your current subscription.',
          )}
        </Alert>
      );
    }

    return addonOffers.map(addonOffer => {
      const storageSizeInOctet = addons.find(addon =>
        addon.offers.includes(addonOffer.id),
      )?.meta_data?.quota_per_unit;

      const funnelUrl = `${SHOP_URL}?funnel=update_drive_group&offer=${addonOffer.id}&drive_group=${selectedGroup?.id}&subscription=${currentSubscription?.id}`;

      return (
        <Button key={addonOffer.id} href={funnelUrl}>
          {t(
            'subscription.driveGroups.addStorage.offer.label',
            '{{storageSize}} for {{price}}/{{periodUnit}}',
            {
              storageSize: formatOctetStorageValue(storageSizeInOctet, locale),
              price: formatPrice(addonOffer.price * 100),
              periodUnit: addonOffer.period_unit,
            },
          )}
        </Button>
      );
    });
  };

  return (
    <Modal
      scroll="body"
      showCloseButton
      open={isOpen}
      onClose={handleClose}
      title={t(
        'subscription.driveGroups.addStorage.title',
        'Add storage to your Family group',
      )}
    >
      <Typography variant="body-md">
        {t(
          'subscription.driveGroups.addStorage.description',
          'Choose how you want to add space to your Family group:',
        )}
      </Typography>

      <Container>{renderOffersOrAlert()}</Container>
    </Modal>
  );
};
