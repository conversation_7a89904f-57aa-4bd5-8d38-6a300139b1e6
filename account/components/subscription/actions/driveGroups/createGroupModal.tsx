import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useTranslation } from 'next-i18next';
import { SyntheticEvent, useState } from 'react';
import { Controller } from 'react-hook-form';
import {
  Alert,
  Icon,
  Input,
  Modal,
  ModalButton,
  theme,
  Typography,
} from 'shared-components';
import { Icons, NotificationState } from 'types';

import { useCreateDriveGroupForm } from '@/hooks/form/driveGroups/useCreateDriveGroupForm';

const InputContainer = styled.div`
  margin-bottom: 36px;
`;

interface ICreateGroupModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onSuccess?: () => void;
  onError?: () => void;
  defaultGroupStorage: number;
}

export const CreateGroupModal = ({
  isOpen,
  handleClose,
  defaultGroupStorage,
}: ICreateGroupModalProps) => {
  const { t } = useTranslation();
  const [errorMessage, setErrorMessage] = useState<string>();

  const defaultValues = { name: '' };

  const { onSubmit, control, isSubmitting, reset } = useCreateDriveGroupForm(
    defaultValues,
    () => handleDialogClose(''),
    (error: string) => setErrorMessage(error),
  );

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setErrorMessage(undefined);
    onSubmit();
  };

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isSubmitting) {
      return;
    }
    handleClose();
    setErrorMessage(undefined);
    reset();
  };

  return (
    <Modal
      scroll="body"
      showCloseButton
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmitting}
      title={t(
        'subscription.driveGroups.createGroup.title',
        'Create a Family group',
      )}
      subtitle={t(
        'subscription.driveGroups.createGroup.subtitle',
        'Family groups help you share your storage quota with people around you who will be able to see a special volume directly in their Drive account.',
      )}
    >
      <Collapse in={!!errorMessage}>
        <Alert
          data-test-id="create-drive-group-modal-error-alert"
          type={NotificationState.ERROR}
          title={t(
            'subscription.driveGroups.form.createGroup.alert',
            'An error occurred while creating your group',
          )}
        >
          {errorMessage}
        </Alert>
      </Collapse>

      <form onSubmit={handleSubmit}>
        <InputContainer>
          <Controller
            name="name"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="create-drive-group-input"
                data-test-id="create-drive-group-input"
                label={t(
                  'subscription.driveGroups.form.createGroupName.label',
                  'Group name',
                )}
                placeholder={t(
                  'subscription.driveGroups.form.createGroupName.placeholder',
                  'Group name',
                )}
                error={error?.message}
                leftAddon={
                  <Icon
                    name={Icons.LABEL_OUTLINED}
                    color={theme.palette.black.main75}
                    width={24}
                    height={24}
                  />
                }
              />
            )}
          />
        </InputContainer>

        <Typography variant="body-md">
          {t(
            'subscription.driveGroups.createGroup.description',
            'This will create a {{defaultGroupStorage, storageUnit}} shared folder accessible to all future group members.',
            { defaultGroupStorage },
          )}
        </Typography>

        <ModalButton
          data-test-id="create-drive-group-modal-submit-button"
          loading={isSubmitting}
          type="submit"
          color="primary"
        >
          {t(
            'subscription.driveGroups.createGroup.submitButton.label',
            'Create',
          )}
        </ModalButton>
      </form>
    </Modal>
  );
};
