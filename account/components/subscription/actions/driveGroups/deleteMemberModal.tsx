import styled from '@emotion/styled';
import { useIsMutating } from '@tanstack/react-query';
import { useDeleteDriveGroupMember } from 'hooks';
import { Trans, useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  Alert,
  Modal,
  ModalButton,
  ModalLink,
  Typography,
} from 'shared-components';
import { IDriveGroup, IDriveGroupMember, NotificationState } from 'types';
import { APIError, log } from 'utils';

const StyledAlert = styled(Alert)`
  margin-top: 24px;
`;

interface IDeleteMemberModalProps {
  isOpen: boolean;
  selectedMember: IDriveGroupMember | undefined;
  selectedGroup: IDriveGroup | undefined;
  handleClose: () => void;
  onSuccess?: () => void;
  onError?: () => void;
}

export const DeleteMemberModal = ({
  isOpen,
  handleClose,
  selectedMember,
  selectedGroup,
}: IDeleteMemberModalProps) => {
  const { t } = useTranslation();
  const [errorMessage, setErrorMessage] = useState<string>();

  const deleteDriveGroupMember = useDeleteDriveGroupMember(
    selectedGroup?.id || '',
  );
  const isMutating = !!useIsMutating(['deleteDriveGroupMember']);

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isMutating) {
      return;
    }
    handleClose();
  };

  const onSubmit = async () => {
    if (!selectedMember || !selectedGroup?.id) {
      return;
    }

    try {
      await deleteDriveGroupMember.mutateAsync(
        selectedMember?.membership_id.toString(),
        {
          onSuccess: () => {
            handleClose();
          },
          onError: (error: APIError) => {
            throw error.message;
          },
        },
      );
    } catch (error) {
      log('Error on api request:', 'error', error);
      setErrorMessage(error as string);
    }
  };

  return (
    <Modal
      scroll="body"
      showCloseButton
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isMutating}
      title={t(
        'subscription.driveGroups.deleteMember.title',
        'Delete a member',
      )}
    >
      <Typography variant="body-md">
        <Trans
          i18nKey="subscription.driveGroups.deleteMember.description"
          defaults="Are you sure you want to remove <bold>{{memberName}}</bold> from the group <bold>{{groupName}}</bold>? This will remove their access to files in this group."
          values={{
            memberName: selectedMember?.email,
            groupName: selectedGroup?.name,
          }}
          components={{ bold: <strong /> }}
        />
      </Typography>

      {errorMessage && (
        <StyledAlert
          data-test-id="delete-drive-group-member-modal-error-alert"
          type={NotificationState.ERROR}
          title={t(
            'subscription.driveGroups.deleteMember.alert',
            'An error occurred while deleting this user',
          )}
        >
          {errorMessage}
        </StyledAlert>
      )}

      <ModalButton
        data-test-id="delete-drive-member-modal-submit-button"
        loading={isMutating}
        onClick={onSubmit}
        color="error"
      >
        {t('global.delete', 'Delete', { ns: 'common' })}
      </ModalButton>

      <ModalLink
        data-test-id="delete-drive-member-modal-cancel-button"
        onClick={handleClose}
        color="black"
      >
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};
