import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Icon, Link, theme } from 'shared-components';
import { Icons } from 'types';

import ButtonPopover from '@/components/ui/buttonPopover';

export interface IManageGroupsActionsProps {
  openEditGroupDrawer: () => void;
  openDeleteGroupModal: () => void;
}

export const ManageGroupsActions = ({
  openEditGroupDrawer,
  openDeleteGroupModal,
}: IManageGroupsActionsProps) => {
  const { t } = useTranslation();

  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  return (
    <>
      <ButtonPopover
        dataTestId="drive-group-actions"
        name="drive-group-actions"
        icon={<Icon name={Icons.MENU_DOTS_VERTICAL} width={16} height={16} />}
        isOpen={isPopoverOpen}
        setIsOpen={setIsPopoverOpen}
      >
        <Link
          data-test-id="drive-group-actions-edit-button"
          component="button"
          variant="label-sm-regular"
          color={theme.palette.black.main}
          onClick={() => {
            openEditGroupDrawer();
            setIsPopoverOpen(false);
          }}
          startIcon={<Icon name={Icons.EDIT} width={16} height={16} />}
        >
          {t('subscription.driveGroups.manager.editGroupButton.label', 'Edit')}
        </Link>
        <Link
          data-test-id="drive-group-actions-delete-button"
          component="button"
          variant="label-sm-regular"
          color={theme.palette.error.main}
          onClick={() => {
            openDeleteGroupModal();
            setIsPopoverOpen(false);
          }}
          startIcon={<Icon name={Icons.TRASH} width={16} height={16} />}
        >
          {t(
            'subscription.driveGroups.manager.deleteGroupButton.label',
            'Delete',
          )}
        </Link>
      </ButtonPopover>
    </>
  );
};
