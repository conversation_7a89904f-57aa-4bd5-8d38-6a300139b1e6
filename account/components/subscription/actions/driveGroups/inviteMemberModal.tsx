import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Modal } from 'shared-components';
import { IDriveGroup } from 'types';

import { InviteMemberForm } from '../../driveGroups/driveGroupDrawer/inviteMemberForm';

interface IInviteMemberModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onSuccess?: () => void;
  onError?: () => void;
  selectedGroup: IDriveGroup | undefined;
  maxGroupMembers: number;
}

export const InviteMemberModal = ({
  isOpen,
  handleClose,
  selectedGroup,
  maxGroupMembers,
}: IInviteMemberModalProps) => {
  const { t } = useTranslation();

  const [isSubmitting, _setIsSubmitting] = useState(false);

  const handleDialogClose = () => {
    if (isSubmitting) {
      return;
    }
    handleClose();
  };

  return (
    <Modal
      scroll="body"
      showCloseButton
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmitting}
      title={t(
        'subscription.driveGroups.inviteMember.title',
        'Invite a member',
      )}
      subtitle={t(
        'subscription.driveGroups.inviteMember.subtitle',
        'Family groups help you share your storage quota with people around you who will be able to see a special volume directly in their Drive account.',
      )}
    >
      <InviteMemberForm
        buttonDisplay="fullWidth"
        driveGroupId={selectedGroup?.id || ''}
        key={selectedGroup?.id}
        hasReachedMaxMembersCount={
          (selectedGroup?.members?.length || 0) >= maxGroupMembers
        }
      />
    </Modal>
  );
};
