import styled from '@emotion/styled';
import { useIsMutating } from '@tanstack/react-query';
import { useDeleteDriveGroup } from 'hooks';
import { Trans, useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  Alert,
  Icon,
  Input,
  Modal,
  ModalButton,
  ModalLink,
  theme,
  Typography,
} from 'shared-components';
import { Icons, IDriveGroup, NotificationState } from 'types';
import { APIError, formatOctetStorageValue, log } from 'utils';

import { useConfig } from '@/hooks/store/useConfig';

const StyledAlert = styled(Alert)`
  margin-top: 24px;
`;

const WordConfirmationText = styled(Typography)`
  margin: 24px 0;
`;

interface IDeleteGroupModalProps {
  isOpen: boolean;
  selectedGroup: IDriveGroup | undefined;
  openGroupDrawer: () => void;
  handleClose: () => void;
  onSuccess?: () => void;
  onError?: () => void;
}

export const DeleteGroupModal = ({
  isOpen,
  handleClose,
  selectedGroup,
  openGroupDrawer,
}: IDeleteGroupModalProps) => {
  const { t } = useTranslation();
  const { locale } = useConfig();
  const [errorMessage, setErrorMessage] = useState<string>();
  const [confirmationInputValue, setConfirmationInputValue] =
    useState<string>('');

  const deleteDriveGroup = useDeleteDriveGroup();
  const isMutating = !!useIsMutating(['deleteDriveGroup']);
  const isSubmitDisabled =
    confirmationInputValue !== selectedGroup?.name ||
    selectedGroup.members.length > 0;

  const handleDialogClose = () => {
    if (isMutating) {
      return;
    }
    handleClose();
  };

  const onSubmit = async () => {
    if (!selectedGroup?.id) {
      return;
    }

    try {
      await deleteDriveGroup.mutateAsync(selectedGroup.id, {
        onSuccess: () => {
          handleClose();
        },
        onError: (error: APIError) => {
          throw error.message;
        },
      });
    } catch (error) {
      log('Error on api request:', 'error', error);
      setErrorMessage(error as string);
    }
  };

  const renderGroupNotEmptyAlert = () => (
    <>
      <Alert
        type={NotificationState.INFO}
        title={t(
          'subscription.driveGroups.deleteGroup.notEmpty.title',
          'Your group still has members.',
        )}
      >
        {t(
          'subscription.driveGroups.deleteGroup.notEmpty.description',
          'In order to delete this group, you must first remove all members and invites.',
        )}
      </Alert>
      <ModalButton
        data-test-id="delete-drive-group-modal-manage-members-button"
        onClick={() => {
          openGroupDrawer();
          handleClose();
        }}
        color="primary"
      >
        {t(
          'subscription.driveGroups.deleteGroup.notEmpty.manageMembersButton',
          'Manage members',
        )}
      </ModalButton>
    </>
  );

  const renderModalContent = () => (
    <>
      <Alert type={NotificationState.WARNING}>
        {t(
          'subscription.driveGroups.deleteGroup.warning',
          'Note that all files in the group shared folder will be deleted!',
        )}
      </Alert>

      <Typography variant="body-md">
        <Trans
          i18nKey="subscription.driveGroups.deleteGroup.description"
          defaults="<bold>You have currently {{storageSize}} shared across your group members.</bold> Please back up your files manually before you delete the group."
          values={{
            storageSize: formatOctetStorageValue(
              selectedGroup?.quota_used,
              locale,
            ),
          }}
          components={{ bold: <strong /> }}
        />
      </Typography>

      <WordConfirmationText variant="body-md">
        <Trans
          i18nKey="subscription.driveGroups.deleteGroup.wordConfirmation.description"
          defaults="To validate this group deletion, please write the name of the group (<bold>{{groupName}}</bold>) in the field below."
          values={{
            groupName: selectedGroup?.name,
          }}
          components={{ bold: <strong /> }}
        />
      </WordConfirmationText>

      <Input
        value={confirmationInputValue}
        onChange={e => setConfirmationInputValue(e.target.value)}
        data-test-id="delete-drive-group-name-input"
        id="delete-drive-group-name-input"
        placeholder={t(
          'subscription.driveGroups.form.deleteGroupName.placeholder',
          'Enter group name to validate',
        )}
        leftAddon={
          <Icon
            name={Icons.LABEL_OUTLINED}
            color={theme.palette.black.main75}
            width={24}
            height={24}
          />
        }
      />

      {errorMessage && (
        <StyledAlert
          data-test-id="delete-drive-group-modal-error-alert"
          type={NotificationState.ERROR}
          title={t(
            'subscription.driveGroups.deleteGroup.alert',
            'An error occurred while deleting your group',
          )}
        >
          {errorMessage}
        </StyledAlert>
      )}

      <ModalButton
        data-test-id="delete-drive-group-modal-submit-button"
        loading={isMutating}
        onClick={onSubmit}
        disabled={isSubmitDisabled}
        color="error"
      >
        {t('global.delete', 'Delete', { ns: 'common' })}
      </ModalButton>

      <ModalLink
        data-test-id="delete-drive-group-modal-cancel-button"
        onClick={handleClose}
        color="black"
      >
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </>
  );

  return (
    <Modal
      scroll="body"
      showCloseButton
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isMutating}
      title={t(
        'subscription.driveGroups.deleteGroup.title',
        'Delete a Family group',
      )}
    >
      {selectedGroup?.members.length !== 0
        ? renderGroupNotEmptyAlert()
        : renderModalContent()}
    </Modal>
  );
};
