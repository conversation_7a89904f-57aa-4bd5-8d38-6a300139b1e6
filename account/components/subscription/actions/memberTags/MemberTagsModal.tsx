import { useTranslation } from 'next-i18next';
import { Modal, ModalLink } from 'shared-components';
import { IMemberDetails, ITag } from 'types';

import TagsForm from '@/components/forms/tagsForm/tagsForm';
import { useCurrentMember } from '@/hooks/member/useMember';

interface IMemberTagsModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onUpdateMemberTags: (tags: ITag[]) => void;
  userTags: ITag[];
  isSubmittingTags: boolean;
}

const MemberTagsModal = ({
  isOpen,
  handleClose,
  onUpdateMemberTags,
  userTags,
  isSubmittingTags,
}: IMemberTagsModalProps) => {
  const { t } = useTranslation();

  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isSubmittingTags) {
      return;
    }

    handleClose();
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingTags}
      aria-labelledby="tags-member-title"
      aria-describedby="tags-member-description"
      title={t('subscription.memberTags.title', "User's tags")}
    >
      <TagsForm
        onUpdateTags={onUpdateMemberTags}
        isB2b={isB2b}
        defaultTags={userTags}
        isSubmittingTags={isSubmittingTags}
      />
      <ModalLink onClick={handleClose}>
        {t('global.cancel', 'Cancel', { ns: 'common' })}
      </ModalLink>
    </Modal>
  );
};

export default MemberTagsModal;
