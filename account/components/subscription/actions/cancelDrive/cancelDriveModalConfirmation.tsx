import { List, ListItem, ListItemText } from '@mui/material';
import { Trans, useTranslation } from 'next-i18next';

interface ICancelDriveModalConfirmationProps {
  lastBillingDate: string | null;
  email: string | undefined;
}

const CancelDriveModalConfirmation = ({
  lastBillingDate,
  email,
}: ICancelDriveModalConfirmationProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <List>
        <ListItem>
          <ListItemText>
            <Trans
              i18nKey="subscription.cancelDrive.confirmation.subscription.item-1"
              defaults="You will still have access to your Shadow Drive until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>"
              values={{ lastBillingDate }}
              components={{ bold: <strong /> }}
            />
          </ListItemText>
        </ListItem>
        <ListItem>
          <ListItemText>
            <Trans
              i18nKey="subscription.cancelDrive.confirmation.subscription.item-2"
              defaults="We will send you a confirmation email to <bold>{{ email }}</bold> shortly."
              values={{ email }}
              components={{ bold: <strong /> }}
            />
          </ListItemText>
        </ListItem>
        <ListItem>
          <ListItemText>
            {t(
              'subscription.cancelDrive.confirmation.subscription.item-3',
              "You won't be charged anymore.",
            )}
          </ListItemText>
        </ListItem>
        <ListItem>
          <ListItemText>
            <Trans
              i18nKey="subscription.cancelDrive.confirmation.subscription.changedYourMind"
              defaults="We are always improving Shadow Drive so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime."
              components={{ bold: <strong /> }}
            />
          </ListItemText>
        </ListItem>
      </List>
    </div>
  );
};

export default CancelDriveModalConfirmation;
