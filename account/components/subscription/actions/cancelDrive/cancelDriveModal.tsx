import { Trans, useTranslation } from 'next-i18next';
import { useEffect, useRef, useState } from 'react';
import { Modal, ModalButton, ModalLink } from 'shared-components';
import type { CancelDriveSteps, IMemberDetails, ISubscription } from 'types';
import { DATE_FORMAT_BY_MARKET, logError, trackEvent } from 'utils';

import CancelDriveModalConfirmation from './cancelDriveModalConfirmation';
import CancelDriveModalInformation from './cancelDriveModalInformation';

import CancelDriveForm from '@/components/forms/cancelDriveForm/cancelDriveForm';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useCancelSubscription } from '@/hooks/subscription/useSubscription';
import { useDate } from '@/hooks/useDate';

interface ICancelDriveProps {
  isOpen: boolean;
  handleClose: () => void;
  onCancelDriveError: () => void;
  subscription: ISubscription;
  email: string | undefined;
}

const CANCEL_DRIVE_STEP: Record<CancelDriveSteps, string> = {
  survey: 'cancel-survey',
  information: 'cancel-information',
  confirmation: 'cancel-confirmation',
};

const CancelDriveModal = ({
  isOpen,
  handleClose,
  onCancelDriveError,
  subscription,
  email,
}: ICancelDriveProps) => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const { t } = useTranslation();
  const { getSubscriptionNextBillingDate } = useDate();
  const cancelSubscription = useCancelSubscription();

  const submitReasonRef = useRef<HTMLButtonElement | null>(null);
  const [step, setStep] = useState<string>(CANCEL_DRIVE_STEP.survey);
  const [isCancellingDrive, setIsCancellingDrive] = useState<boolean>(false);
  const [lastBillingDate, setLastBillingDate] = useState<string | null>(null);
  const [cancellationReason, setCancellationReason] = useState<string>('');

  // We need this useEffect because when the subscription is
  // cancelled from the modal, the lastBillingDate is null.
  useEffect(() => {
    const subscriptionNextBillingDate = getSubscriptionNextBillingDate(
      subscription,
      DATE_FORMAT_BY_MARKET,
    );
    if (subscriptionNextBillingDate) {
      setLastBillingDate(subscriptionNextBillingDate);
    }
  }, [getSubscriptionNextBillingDate, subscription]);

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && isCancellingDrive) {
      return;
    }

    handleClose();
  };

  const handleCancelDrive = async () => {
    setIsCancellingDrive(true);

    try {
      await cancelSubscription.mutateAsync({
        cancellationReasons: { comment: cancellationReason },
        subscriptionId: subscription.id,
      });

      trackEvent('click', {
        event_category: 'cancel_my_drive',
        event_label: 'churn_complete',
      });

      setStep(CANCEL_DRIVE_STEP.confirmation);
    } catch (e) {
      setStep(CANCEL_DRIVE_STEP.survey);
      onCancelDriveError();
      logError('handleCancelDrive', e);
    } finally {
      setCancellationReason('');
      setIsCancellingDrive(false);
    }
  };

  const getTitle = () => {
    switch (step) {
      case CANCEL_DRIVE_STEP.survey:
        return t(
          'subscription.cancelDrive.survey.title',
          "We're always improving our service and your feedback matters",
        );
      case CANCEL_DRIVE_STEP.information:
        return t(
          'subscription.cancelDrive.information.title',
          "Here's some information before you cancel",
        );
      case CANCEL_DRIVE_STEP.confirmation:
        return t(
          'subscription.cancelDrive.confirmation.title',
          'Your subscription has been canceled',
        );
    }
  };

  const getSubtitle = () => {
    switch (step) {
      case CANCEL_DRIVE_STEP.information:
        return (
          <Trans
            i18nKey="subscription.cancelDrive.information.subtitle"
            defaults="If you cancel your subscription, your access to Shadow Drive will end on <bold>{{ lastBillingDate }}</bold>"
            values={{ lastBillingDate }}
            components={{ bold: <strong /> }}
          />
        );
      case CANCEL_DRIVE_STEP.confirmation:
        return t(
          'subscription.cancelDrive.confirmation.subtitle',
          'We are sad to see you leave.',
        );
    }
  };

  const renderContent = () => {
    switch (step) {
      case CANCEL_DRIVE_STEP.survey:
        return (
          <>
            <CancelDriveForm
              onSuccess={({ reason }) => {
                setCancellationReason(reason);
                setStep(CANCEL_DRIVE_STEP.information);
              }}
              hideSubmitButton
              submitRef={submitReasonRef}
            />
          </>
        );
      case CANCEL_DRIVE_STEP.information:
        return (
          <CancelDriveModalInformation lastBillingDate={lastBillingDate} />
        );
      case CANCEL_DRIVE_STEP.confirmation:
        return (
          <CancelDriveModalConfirmation
            lastBillingDate={lastBillingDate}
            email={email}
          />
        );
    }
  };

  const renderActionButtons = () => {
    switch (step) {
      case CANCEL_DRIVE_STEP.survey:
        return (
          <>
            <ModalButton
              data-test-id="cancel-drive-modal-next-button"
              onClick={() => submitReasonRef?.current?.click()}
              color={isB2b ? 'black' : 'primary'}
            >
              {t('global.next', 'Next', { ns: 'common' })}
            </ModalButton>
          </>
        );
      case CANCEL_DRIVE_STEP.information:
        return (
          <>
            <ModalButton
              data-test-id="cancel-drive-modal-confirm-button"
              loading={isCancellingDrive}
              onClick={handleCancelDrive}
              color={isB2b ? 'black' : 'primary'}
            >
              {t('global.confirm', 'Confirm', { ns: 'common' })}
            </ModalButton>
            {!isCancellingDrive && (
              <ModalLink
                data-test-id="cancel-drive-modal-cancel-button"
                onClick={handleClose}
              >
                {t('global.cancel', 'Cancel', { ns: 'common' })}
              </ModalLink>
            )}
          </>
        );
      case CANCEL_DRIVE_STEP.confirmation:
        return (
          <ModalButton
            data-test-id="cancel-drive-modal-ok-button"
            onClick={handleClose}
            color={isB2b ? 'black' : 'primary'}
          >
            {t('global.ok', 'OK', { ns: 'common' })}
          </ModalButton>
        );
    }
  };

  return (
    <Modal
      data-test-id="cancel-drive-modal"
      scroll="body"
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isCancellingDrive}
      aria-labelledby="cancel-drive-title"
      aria-describedby="cancel-drive-description"
      title={getTitle()}
      subtitle={getSubtitle()}
    >
      {renderContent()}
      {renderActionButtons()}
    </Modal>
  );
};

export default CancelDriveModal;
