import { Trans, useTranslation } from 'next-i18next';

import CancelInfo from '@/components/subscription/cancelInfo';

interface ICancelDriveModalInformationProps {
  lastBillingDate: string | null;
}

const CancelDriveModalInformation = ({
  lastBillingDate,
}: ICancelDriveModalInformationProps) => {
  const { t } = useTranslation();

  return (
    <>
      <CancelInfo
        title={t(
          'subscription.cancelDrive.information.whenSubscriptionEnds.title',
          'When your subscription ends',
        )}
        items={[
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionEnds.shadowAccess',
              "You'll lose access to your Shadow Drive",
            ),
            description: t(
              'subscription.cancelDrive.information.whenSubscriptionEnds.dataAccess',
              "You'll no longer be able to access your files and they will be permanently deleted.",
            ),
          },
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionEnds.reSubscribe.title',
              'Restart your subscription anytime!',
            ),
            description: (
              <Trans
                i18nKey="subscription.cancelDrive.information.whenSubscriptionEnds.description"
                defaults="If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!"
                values={{ lastBillingDate }}
                components={{ bold: <strong /> }}
              />
            ),
          },
        ]}
      />
      <CancelInfo
        title={t(
          'subscription.cancelDrive.information.whenSubscriptionActive.title',
          'While your subscription is still active',
        )}
        items={[
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.enjoy',
              'Enjoy the most out of Shadow Drive',
            ),
            description: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.access',
              'You can still access your Shadow Drive until the end of your subscription.',
            ),
          },
          {
            label: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.backupTip1',
              "Don't forget to back up your files",
            ),
            description: t(
              'subscription.cancelDrive.information.whenSubscriptionActive.backupTip2',
              'Make sure to transfer important files from Shadow Drive to your local PC. Your data will be deleted after your subscription ends.',
            ),
          },
        ]}
      />
    </>
  );
};

export default CancelDriveModalInformation;
