import { useTranslation } from 'next-i18next';
import React from 'react';
import { ClickTracker, Icon } from 'shared-components';
import { Icons } from 'types';

import Link from '@/components/ui/link';

interface IChangeVmPeriodicityButtonProps {
  onClick: () => void;
}

const ChangeVmPeriodicityButton = ({
  onClick,
}: IChangeVmPeriodicityButtonProps) => {
  const { t } = useTranslation();

  return (
    <ClickTracker
      eventPayload={{
        action: 'click',
        parameters: {
          event_category: 'my_account_modify_subscription',
          event_label: 'change_offer_periodicity',
        },
      }}
    >
      <Link
        data-test-id="change-periodicity-button"
        key="change-periodicity"
        variant="label-sm-regular"
        startIcon={<Icon name={Icons.PERIODICITY} width={16} height={16} />}
        onClick={onClick}
      >
        {t(
          'subscription.plan.vm.action.changeVmPeriodicity',
          'Change periodicity',
        )}
      </Link>
    </ClickTracker>
  );
};

export default ChangeVmPeriodicityButton;
