import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import {
  LocalLoader,
  Typography,
  theme,
  useThemeMediaQueries,
} from 'shared-components';

interface IChangeVmPeriodicityModalBodyProps {
  isLoading: boolean;
  nextBillingDate: string;
  newPrice?: string;
  relativeDuration: string;
}

const ListItem = styled.li`
  color: ${theme.palette.primary.main};
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;

  ::before {
    content: '•';
    font-size: 18px;
  }
`;

const ChangeVmPeriodicityModalBody = ({
  isLoading,
  nextBillingDate,
  newPrice,
  relativeDuration,
}: IChangeVmPeriodicityModalBodyProps) => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();

  if (isLoading || !newPrice) {
    return <LocalLoader />;
  }

  return (
    <ul>
      <ListItem>
        <Typography
          variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}
          color="primary"
        >
          {t(
            'subscription.changeVmPeriodicity.modal.bullet-1.title',
            'Your change will be effective at your next billing date',
          )}
        </Typography>
      </ListItem>
      <Typography variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}>
        <Trans
          i18nKey="subscription.changeVmPeriodicity.modal.bullet-1.description"
          defaults="Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment."
          values={{
            nextBillingDate,
            newPrice,
            relativeDuration,
          }}
          components={{ bold: <strong /> }}
        />
      </Typography>

      <ListItem>
        <Typography
          variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}
          color="primary"
        >
          {t(
            'subscription.changeVmPeriodicity.modal.bullet-2.title',
            'Cancel your change anytime',
          )}
        </Typography>
      </ListItem>
      <Typography variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}>
        <Trans
          i18nKey="subscription.changeVmPeriodicity.modal.bullet-2.description"
          defaults="If you cancel your change before <bold>{{ nextBillingDate }}</bold>, you'll be able to keep your current subscription!"
          values={{
            nextBillingDate,
          }}
          components={{ bold: <strong /> }}
        />
      </Typography>
    </ul>
  );
};

export default ChangeVmPeriodicityModalBody;
