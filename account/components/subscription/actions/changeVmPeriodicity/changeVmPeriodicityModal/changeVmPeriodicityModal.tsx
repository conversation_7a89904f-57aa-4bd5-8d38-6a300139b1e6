import styled from '@emotion/styled';
import { useNotifications, useScheduledChangesSubscription } from 'hooks';
import { compact } from 'lodash';
import { Trans, useTranslation } from 'next-i18next';
import { ReactElement, type SyntheticEvent } from 'react';
import { Al<PERSON>, Modal, ModalButton, ModalLink } from 'shared-components';
import {
  ISubscription,
  NotificationState,
  OfferPeriodicity,
  ProductType,
  SelectOption,
} from 'types';
import {
  computePercentGain,
  formatRelativePeriodicity,
  getMinimalOffer,
  getMaximalOffer,
  getOfferFromId,
  getOffers,
  findSubscriptionItemByType,
  trackEvent,
} from 'utils';

import ChangeVmPeriodicityModalBody from './changeVmPeriodicityModalBody';
import { useChangeVmPeriodicityModal } from './useChangeVmPeriodicityModal';

import InputSelect from '@/components/ui/form/inputSelect';
import LabelSelect from '@/components/ui/form/labelSelect';
import useChangeVmPeriodicityForm from '@/hooks/form/useChangeVmPeriodicityForm';
import { useCatalog } from '@/hooks/useCatalog';

interface IChangeVmPeriodicityModalProps {
  isOpen: boolean;
  handleClose: () => void;
  subscription: ISubscription | undefined;
  onVmPeriodicityChangeSuccess?: (element: ReactElement) => void;
}

const SelectContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
`;

const ChangeVmPeriodicityModal = ({
  isOpen,
  handleClose,
  subscription,
  onVmPeriodicityChangeSuccess,
}: IChangeVmPeriodicityModalProps) => {
  const { t } = useTranslation();
  const { notifyError } = useNotifications();

  const catalogQuery = useCatalog(subscription?.id);
  const scheduledChangesSubscriptionQuery = useScheduledChangesSubscription(
    subscription?.id as string,
    subscription?.has_scheduled_changes,
  );
  const scheduledChangesItems = subscription?.has_scheduled_changes
    ? scheduledChangesSubscriptionQuery?.data?.items
    : [];

  const getPeriodicitySelectOptions = () => {
    const currentPlan = findSubscriptionItemByType(
      subscription,
      ProductType.PLAN,
    );

    const currentOffer = getOfferFromId(
      catalogQuery.data,
      currentPlan?.id ?? '',
    );

    if (!currentOffer) {
      return [];
    }

    const allOffersForProduct =
      getOffers(catalogQuery.data, currentOffer?.product_id ?? '') ?? [];

    const periodicitySelectOptions: SelectOption[] = compact(
      allOffersForProduct.map(offerForProduct => {
        if (
          offerForProduct.periodicity !== currentOffer?.periodicity &&
          !scheduledChangesItems?.find(item => item.id === offerForProduct.id)
        ) {
          const minimalOffer = getMinimalOffer([currentOffer, offerForProduct]);
          const maximalOffer = getMaximalOffer([currentOffer, offerForProduct]);
          const percentGain = computePercentGain(minimalOffer, maximalOffer);

          const label = `${t(
            `subscription.periodicity.relative.${offerForProduct.period_unit}`,
            {
              count: offerForProduct.period,
            },
          )} ${
            percentGain > 0
              ? t('subscription.periodicity.save', {
                  defaultValue: '(save {{percent}}%)',
                  percent: percentGain || 0,
                })
              : ''
          }`;

          return {
            label,
            value: offerForProduct.periodicity,
          };
        }
      }),
    );

    return periodicitySelectOptions;
  };

  const periodicitySelectOptions = getPeriodicitySelectOptions();

  const defaultFormValues = {
    periodicity: periodicitySelectOptions[0]?.value as OfferPeriodicity,
  };

  const onFormSuccess = () => {
    onVmPeriodicityChangeSuccess?.(
      <Trans
        i18nKey="subscription.changeVmPeriodicity.success.content"
        defaults="Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment."
        values={{
          nextBillingDate: formattedSubscriptionNextBillingDate,
          newPrice: formattedNewPrice,
          relativeDuration,
        }}
        components={{ bold: <strong /> }}
      />,
    );

    trackEvent('click', {
      event_category: 'change_offer_periodicity',
      event_label: 'change_offer_periodicity_confirmed',
    });

    handleCloseModal();
  };

  const onFormError = () => {
    notifyError(
      t(
        'errors.api.changeVmPeriodicity',
        'An error occurred while updating your subscription, please try again later.',
      ),
    );
  };

  const {
    onSubmit,
    control,
    isSubmitting,
    selectedPeriod,
    selectedPeriodUnit,
    selectedPeriodicity,
    reset,
  } = useChangeVmPeriodicityForm(
    onFormSuccess,
    onFormError,
    subscription as ISubscription,
    defaultFormValues,
  );

  const {
    isEstimationLoading,
    formattedNewPrice,
    formattedSubscriptionNextBillingDate,
  } = useChangeVmPeriodicityModal({
    subscription,
    selectedPeriodicity,
    isModalOpen: isOpen,
  });

  const relativeDuration = formatRelativePeriodicity(
    selectedPeriod,
    selectedPeriodUnit,
  );

  const handleCloseModal = () => {
    if (isSubmitting) {
      return;
    }
    handleClose();
    reset(defaultFormValues);
  };

  const handleFormSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    onSubmit();
  };

  const renderScheduledChangeAlert = () => {
    return (
      <Alert
        type={NotificationState.WARNING}
        title={t(
          'subscription.changeVmPeriodicity.modal.cancelCurrentChangeAlert.title',
          'Attention',
        )}
      >
        {t(
          'subscription.changeVmPeriodicity.modal.cancelCurrentChangeAlert.description',
          'Any new changes to your Shadow PC subscription will cancel the current change.',
        )}
      </Alert>
    );
  };

  return (
    <Modal
      scroll="body"
      open={isOpen}
      onClose={handleCloseModal}
      disableEscapeKeyDown={isSubmitting}
      aria-labelledby="change-periodicity-title"
      aria-describedby="change-periodicity-description"
      title={t(
        'subscription.changeVmPeriodicity.modal.title',
        'Change your periodicity',
      )}
      subtitle={t(
        'subscription.changeVmPeriodicity.modal.subtitle',
        'Customize billing cycles for better control over your finances.',
      )}
    >
      <form onSubmit={handleFormSubmit}>
        {subscription?.has_scheduled_changes && renderScheduledChangeAlert()}

        <SelectContainer>
          <LabelSelect
            id="periodicity"
            text={t(
              'subscription.changeVmPeriodicity.modal.selectPeriodicity.label',
              'Periodicity',
            )}
          />
          <InputSelect
            disabled={isEstimationLoading}
            required
            control={control}
            name="periodicity"
            label="periodicity"
            items={periodicitySelectOptions}
          />
        </SelectContainer>

        <ChangeVmPeriodicityModalBody
          isLoading={isEstimationLoading}
          newPrice={formattedNewPrice}
          nextBillingDate={formattedSubscriptionNextBillingDate}
          relativeDuration={formatRelativePeriodicity(
            selectedPeriod,
            selectedPeriodUnit,
          )}
        />

        <ModalButton
          loading={isSubmitting}
          disabled={isEstimationLoading}
          type="submit"
          color="primary"
        >
          {t('global.confirm', 'Confirm', { ns: 'common' })}
        </ModalButton>

        <ModalLink
          onClick={handleCloseModal}
          disabled={isSubmitting}
          type="button"
        >
          {t('global.cancel', 'Cancel', { ns: 'common' })}
        </ModalLink>
      </form>
    </Modal>
  );
};

export default ChangeVmPeriodicityModal;
