import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { Currency, IEstimation, ISubscription, OfferPeriodicity } from 'types';
import {
  DATE_FORMAT_BY_LANGUAGE,
  computeSubscriptionItemPricesWithTargetPeriodicity,
  formatDate,
  logError,
} from 'utils';

import { useConfig } from '@/hooks/store/useConfig';
import { useSubscriptionModificationEstimation } from '@/hooks/subscription/useSubscription';
import { useCatalog } from '@/hooks/useCatalog';
import { usePrice } from '@/hooks/usePrice';

interface IChangeVmPeriodicityModalProps {
  subscription: ISubscription | undefined;
  selectedPeriodicity: OfferPeriodicity;
  isModalOpen: boolean;
}

export const useChangeVmPeriodicityModal = ({
  subscription,
  selectedPeriodicity,
  isModalOpen,
}: IChangeVmPeriodicityModalProps) => {
  const { t } = useTranslation();
  const { notifyError } = useNotifications();
  const { formatPrice } = usePrice();
  const { language, currency } = useConfig();

  const catalogQuery = useCatalog(subscription?.id);
  const catalog = catalogQuery.data;

  const [estimation, setEstimation] = useState<IEstimation>();

  const getSubscriptionModificationEstimation =
    useSubscriptionModificationEstimation(subscription?.id ?? '');

  // Fetches the estimate when the modal is open & selectedPeriodicity changes in the select form
  useEffect(() => {
    if (!isModalOpen) {
      return;
    }

    const getEstimationData = async () => {
      try {
        const itemPricesPayload =
          computeSubscriptionItemPricesWithTargetPeriodicity(
            catalog,
            subscription,
            selectedPeriodicity,
          );

        await getSubscriptionModificationEstimation.mutateAsync(
          {
            item_prices: itemPricesPayload,
          },
          {
            onSuccess: (estimationData: IEstimation | undefined) => {
              setEstimation(estimationData);
            },
          },
        );
      } catch (error) {
        setEstimation(undefined);

        logError(
          'Error during estimate API call for periodicity change : ',
          error,
        );

        notifyError(
          t(
            'errors.api.changeVmPeriodicityEstimate',
            'An error occurred while getting an estimation for updating your subscription, please try again later.',
          ),
        );
      }
    };

    getEstimationData();

    // Do not add missing dependencie here, this causes infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen, selectedPeriodicity]);

  const isEstimationLoading =
    getSubscriptionModificationEstimation.isLoading || !estimation;

  const newPrice =
    estimation?.next_invoice_estimate?.amount_due ??
    estimation?.future_estimate?.custom_next_invoice_estimate;
  const currencyCode =
    estimation?.next_invoice_estimate?.currency_code ??
    estimation?.future_estimate?.next_invoice_estimate?.currency_code ??
    currency;

  const formattedNewPrice = newPrice
    ? formatPrice(newPrice, currencyCode as Currency)
    : '';

  if (!isEstimationLoading && !newPrice) {
    logError(
      `No new price found in estimation for subscription ${subscription?.id} with target periodicity ${selectedPeriodicity}`,
    );
  }

  const formattedSubscriptionNextBillingDate = subscription?.next_billing_at
    ? formatDate(
        subscription.next_billing_at * 1000,
        DATE_FORMAT_BY_LANGUAGE[language],
      )
    : '';

  return {
    isEstimationLoading,
    formattedNewPrice,
    formattedSubscriptionNextBillingDate,
  };
};
