import React, { ReactElement, useState } from 'react';
import { ISubscription } from 'types';

import ChangeVmPeriodicityButton from './changeVmPeriodicityButton';
import ChangeVmPeriodicityModal from './changeVmPeriodicityModal/changeVmPeriodicityModal';

interface IChangeVmPeriodicityProps {
  subscription: ISubscription;
  onVmPeriodicityChangeSuccess?: (element: ReactElement) => void;
  setIsActionsPopoverOpen: (value: boolean) => void;
}

const ChangeVmPeriodicity = ({
  subscription,
  onVmPeriodicityChangeSuccess,
  setIsActionsPopoverOpen,
}: IChangeVmPeriodicityProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };
  const handleCloseModal = () => {
    setIsActionsPopoverOpen(false);
    setIsModalOpen(false);
  };

  return (
    <>
      <ChangeVmPeriodicityButton onClick={handleOpenModal} />
      <ChangeVmPeriodicityModal
        isOpen={isModalOpen}
        handleClose={handleCloseModal}
        subscription={subscription}
        onVmPeriodicityChangeSuccess={onVmPeriodicityChangeSuccess}
      />
    </>
  );
};

export default ChangeVmPeriodicity;
