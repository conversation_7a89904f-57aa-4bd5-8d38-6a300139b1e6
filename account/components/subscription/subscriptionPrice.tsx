import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Typography, useThemeMediaQueries } from 'shared-components';

import { usePrice } from '@/hooks/usePrice';
import type { IPrice } from '@/types/subscriptions';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  text-align: right;
`;

const Period = styled(Typography)`
  margin-top: 4px;
`;

const SubscriptionPrice = ({
  id,
  amount,
  period,
  periodUnit,
  isAddon = false,
  hasStartingPrice = false,
}: IPrice) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();
  const { isLG } = useThemeMediaQueries();

  return (
    <Container data-test-id={`subscription-${id}-addon-price-container`}>
      <Typography
        data-test-id={`subscription-${id}-addon-price-amount`}
        color="primary"
        variant={isLG ? 'heading-h6' : 'label-lg-regular'}
        component="p"
      >
        {hasStartingPrice && (
          <>{t('subscription.details.hasStartingPrice', 'Starting')} </>
        )}
        {isAddon && '+'}
        {formatPrice(amount)}
      </Typography>
      <Period
        data-test-id={`subscription-${id}-addon-price-period`}
        color="primary"
        variant={isLG ? 'label-sm-light' : 'label-xs-light'}
      >
        {t(`subscription.periodicity.adjective.${periodUnit}`, {
          count: period,
        })}
      </Period>
    </Container>
  );
};

export default SubscriptionPrice;
