import styled from '@emotion/styled';
import {
  Icon,
  theme,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons, OfferPeriodUnit } from 'types';

import { ISubscriptionAddonProps } from './subscriptionAddon';

import SubscriptionPrice from '@/components/subscription/subscriptionPrice';
import Link from '@/components/ui/link';

const Container = styled.div`
  display: flex;
  gap: 24px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const Title = styled(Link)<{
  component: string;
  href?: string;
}>`
  display: flex;
  align-items: center;
  gap: 8px;
  ${theme.breakpoints.up('md')} {
    gap: 16px;
  }
`;

const SubscriptionAddonHeading = ({
  label,
  href,
  id,
  subtitle,
  amount = 0,
  isAddon = false,
  hasStartingPrice = false,
  period = 1,
  periodUnit = OfferPeriodUnit.MONTH,
  showPrice = true,
  icon = true,
  onClick,
}: ISubscriptionAddonProps) => {
  const { isLG } = useThemeMediaQueries();

  return (
    <Container data-test-id={`subscription-${id}-addon-heading-container`}>
      {/* Addon link */}
      <Wrapper>
        <Title
          data-test-id={`subscription-${id}-addon-heading-link`}
          color={theme.palette.primary.main}
          variant={isLG ? 'body-lg-regular' : 'body-md-regular'}
          component={href ? 'a' : 'button'}
          href={href}
          target={href ? '_blank' : '_self'}
          onClick={onClick}
        >
          {label}
          {icon && <Icon name={Icons.ARROW_RIGHT} width={16} height={12} />}
        </Title>
        {subtitle && (
          <Typography
            data-test-id={`subscription-${id}-addon-heading-subtitle`}
            color="primary"
            variant="label-xs-light"
          >
            {subtitle}
          </Typography>
        )}
      </Wrapper>
      {/* Addon price */}
      {showPrice && (
        <SubscriptionPrice
          id={id}
          isAddon={isAddon}
          hasStartingPrice={hasStartingPrice}
          amount={amount}
          period={period}
          periodUnit={periodUnit}
        />
      )}
    </Container>
  );
};

export default SubscriptionAddonHeading;
