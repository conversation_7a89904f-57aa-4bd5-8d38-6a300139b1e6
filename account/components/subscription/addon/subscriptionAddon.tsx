import styled from '@emotion/styled';
import type { ReactNode } from 'react';
import { theme } from 'shared-components';
import { OfferPeriodUnit } from 'types';

import SubscriptionAddonContent from '@/components/subscription/addon/subscriptionAddonContent';
import SubscriptionAddonHeading from '@/components/subscription/addon/subscriptionAddonHeading';

export interface ISubscriptionAddonProps {
  id: string;
  amount?: number;
  description?: ReactNode;
  href?: string;
  isAddon?: boolean;
  label: string;
  onClick?: () => void;
  period?: number;
  periodUnit?: OfferPeriodUnit;
  showPrice?: boolean;
  specs?: ReactNode;
  hasStartingPrice?: boolean;
  subtitle?: string;
  hasSecondaryBackground?: boolean;
  icon?: boolean;
}

const Container = styled.div<{
  hasSecondaryBackground: boolean;
}>`
  ${({ hasSecondaryBackground }) =>
    `background-color: ${
      hasSecondaryBackground
        ? theme.palette.secondary.main10
        : theme.palette.primary.main10
    };`}
  padding: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  ${theme.breakpoints.up('md')} {
    padding: 40px;
  }
`;

const SubscriptionAddon = ({
  description,
  specs,
  label,
  href,
  id,
  subtitle,
  amount,
  isAddon,
  hasStartingPrice,
  period,
  periodUnit,
  showPrice = true,
  icon = true,
  onClick,
  hasSecondaryBackground = false,
}: ISubscriptionAddonProps) => (
  <Container hasSecondaryBackground={hasSecondaryBackground}>
    {/* Addon Heading */}
    <SubscriptionAddonHeading
      id={id}
      label={label}
      href={href}
      subtitle={subtitle}
      amount={amount}
      isAddon={isAddon}
      hasStartingPrice={hasStartingPrice}
      period={period}
      periodUnit={periodUnit}
      showPrice={showPrice}
      onClick={onClick}
      icon={icon}
    />
    {/* Addon Content */}
    {description && (
      <SubscriptionAddonContent
        id={id}
        description={description}
        specs={specs}
      />
    )}
  </Container>
);

export default SubscriptionAddon;
