import { useTranslation } from 'next-i18next';
import type { ReactNode } from 'react';
import {
  TechnicalSpecs,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';

interface ISubscriptionAddonContentProps {
  id: string;
  description: ReactNode;
  specs?: ReactNode;
}

const SubscriptionAddonContent = ({
  id,
  description,
  specs,
}: ISubscriptionAddonContentProps) => {
  const { isMD } = useThemeMediaQueries();
  const { t } = useTranslation();

  return (
    <>
      {/* Addon description */}
      <Typography
        data-test-id="subscription-addon-description"
        variant={isMD ? 'body-sm' : 'body-xs'}
      >
        {description}
      </Typography>
      {/* Addon tech specs */}
      {specs && (
        <TechnicalSpecs
          id={`subscription-${id}-addon-technicalSpecs`}
          title={t(
            'subscription.plan.addon.technicalSpecs',
            'Technical Specifications',
          )}
          titleVariant="label-xs-link"
          textVariant="body-xs"
          textColor="black"
        >
          {specs}
        </TechnicalSpecs>
      )}
    </>
  );
};

export default SubscriptionAddonContent;
