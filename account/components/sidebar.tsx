import styled from '@emotion/styled';
import { theme, useThemeMediaQueries } from 'shared-components';

import Navigation from '@/components/navigation/navigation';
import ReferralModule from '@/components/referral/referralModule';

const Container = styled.div`
  ${theme.breakpoints.down('lg')} {
    order: 2;
  }

  ${theme.breakpoints.up('lg')} {
    position: sticky;
    top: 64px;
    flex: 0 0 320px;
  }
`;

interface ISidebarProps {
  isB2b: boolean;
}

const Sidebar = ({ isB2b = false }: ISidebarProps) => {
  const { isLG } = useThemeMediaQueries();

  return (
    <Container>
      {isLG && <Navigation />}
      {!isB2b && <ReferralModule />}
    </Container>
  );
};

export default Sidebar;
