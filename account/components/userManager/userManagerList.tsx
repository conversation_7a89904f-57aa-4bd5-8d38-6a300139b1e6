import styled from '@emotion/styled';
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { useTranslation } from 'next-i18next';
import { useState, useEffect } from 'react';
import {
  Chip,
  LocalLoader,
  theme,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { ITag, UserRole } from 'types';
import { formatDate, DATE_FORMAT_WITH_SLASH_BY_MARKET } from 'utils';

import DataGrid from '@/components/ui/dataGrid/dataGrid';
import UserCard, { IUserCardData } from '@/components/userCard/userCard';
import UserManagerActions from '@/components/userManager/userManagerActions/userManagerActions';
import { useCurrentMember, useMembers } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import { IUserManager } from '@/types/adminManagers';
import { ManagerDataType, UserStatus } from '@/utils/constants';

const Container = styled.div`
  ${theme.breakpoints.down('md')} {
    .MuiDataGrid-root .MuiDataGrid-row,
    .MuiDataGrid-root .MuiDataGrid-columnHeaders {
      padding: 0;
    }
  }
`;

const UserCardStyled = styled(UserCard)`
  position: absolute;
  z-index: 2;
`;

export interface IUserManagerList {
  selectedTags: string[];
}

const UserManagerList = ({ selectedTags }: IUserManagerList) => {
  const { t } = useTranslation();
  const useMembersQuery = useMembers(undefined, selectedTags);
  const useCurrentMemberQuery = useCurrentMember();
  const { market } = useConfig();

  const [userRows, setUserRows] = useState<IUserManager[]>([]);

  const { isMD } = useThemeMediaQueries();

  useEffect(() => {
    if (
      useMembersQuery.isSuccess &&
      useMembersQuery.data &&
      Array.isArray(useMembersQuery.data)
    ) {
      setUserRows(
        useMembersQuery.data.map(
          ({ status, role, user, invite_email, invite_created_on }) =>
            ({
              status,
              role,
              id: user?.id || invite_email,
              email: user?.email || invite_email,
              firstName: user?.first_name || '',
              lastName: user?.last_name || '',
              createdAt: invite_created_on || user?.created_at,
              hasUser: !!user,
              tags: user?.tags || [],
            } as IUserManager),
        ),
      );
    }
  }, [useMembersQuery.data, useMembersQuery.isSuccess]);

  const renderUser = ({
    row: { email, firstName, lastName },
  }: GridRenderCellParams) => {
    const data: IUserCardData = {
      email: email,
      firstName: firstName,
      lastName: lastName,
    };

    return <UserCardStyled onlyThumbnailDisplay data={data} />;
  };

  const renderUserStatusOrRole = ({
    row: { status, role },
  }: GridRenderCellParams) => {
    let color = theme.palette.black.main75,
      backgroundColor,
      isStatusInactive = false;

    switch (status) {
      case UserStatus.PENDING:
        backgroundColor = theme.palette.info.light;
        isStatusInactive = true;
        break;
      case UserStatus.EXPIRED:
        backgroundColor = theme.palette.warning.light;
        isStatusInactive = true;
        break;
      case UserStatus.DISABLED:
        color = theme.palette.error.main;
        backgroundColor = theme.palette.error.light;
        isStatusInactive = true;
        break;
    }

    if (isStatusInactive) {
      return (
        <Chip variant="body-sm" color={color} backgroundColor={backgroundColor}>
          {t(`businessManager.user.status.${status}`)}
        </Chip>
      );
    }

    return (
      <Typography color={color} variant="body-xs">
        {t(`businessManager.user.role.${role}`)}
      </Typography>
    );
  };

  const renderMemberTags = (params: GridRenderCellParams) => (
    <Typography variant="body-xs" noWrap>
      {params.row.tags.map(
        (tag: ITag, key: number) =>
          `${tag.name}${key < params.row.tags.length - 1 ? ', ' : ''}`,
      )}
    </Typography>
  );

  const usersColumns: GridColDef[] = [
    {
      field: 'createdAt',
      type: 'date',
      headerName: t(
        'businessManager.managerList.user.heading.created',
        'Created',
      ),
      flex: isMD ? 0.2 : 0.15,
      headerAlign: 'center',
      align: 'center',
      renderCell: (cellValues: GridRenderCellParams) =>
        formatDate(
          cellValues.row.createdAt * 1000,
          DATE_FORMAT_WITH_SLASH_BY_MARKET[market],
        ),
    },
    {
      field: 'user',
      headerName: t('businessManager.managerList.user.heading.user', 'User'),
      width: 96,
      headerAlign: 'left',
      align: 'left',
      valueGetter: params => params.row.email,
      renderCell: renderUser,
    },
    {
      field: 'role',
      headerName: t('businessManager.managerList.user.heading.role', 'Role'),
      /* todo integration: change size for V2 and new column */
      flex: isMD ? 0.2 : 0.15,
      renderCell: renderUserStatusOrRole,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
    },
    {
      field: 'tags',
      headerName: t('businessManager.managerList.vm.heading.tags', 'Tags'),
      flex: 0.15,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: renderMemberTags,
    },
    {
      field: 'manage',
      headerName: '',
      flex: 0.15,
      align: 'center',
      sortable: false,
      renderCell: ({ row: user }: { row: IUserManager }) => {
        if (
          ![UserRole.ADMIN, UserRole.OWNER].includes(
            useCurrentMemberQuery.data?.role as UserRole,
          ) ||
          useCurrentMemberQuery?.data?.user?.id === user.id
        ) {
          return <></>;
        }

        return (
          <UserManagerActions
            userTags={user.tags}
            memberId={user.id}
            status={user.status}
            inviteEmail={user.email}
          />
        );
      },
    },
  ];

  if (useCurrentMemberQuery.isLoading) {
    return <LocalLoader />;
  }

  return (
    <Container>
      <DataGrid
        disableSelectionOnClick={true}
        columns={usersColumns}
        rows={userRows}
        rowCount={userRows.length}
        type={ManagerDataType.USERS}
        paginationMode="client"
        loading={useMembersQuery.isLoading}
      />
    </Container>
  );
};

export default UserManagerList;
