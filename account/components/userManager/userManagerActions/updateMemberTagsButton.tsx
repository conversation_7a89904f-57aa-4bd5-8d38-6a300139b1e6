import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Icon, theme } from 'shared-components';
import { Icons, ITag } from 'types';
import { computeErrorMessage, logError } from 'utils';

import MemberTagsModal from '@/components/subscription/actions/memberTags/MemberTagsModal';
import Link from '@/components/ui/link';
import { useUpdateMemberTags } from '@/hooks/member/useMember';

interface IDeleteMemberProps {
  setIsActionsPopoverOpen: (value: boolean) => void;
  userTags: ITag[];
  memberId: string;
}

const UpdateMemberTagsButton = ({
  userTags,
  setIsActionsPopoverOpen,
  memberId,
}: IDeleteMemberProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const updateMemberTags = useUpdateMemberTags();

  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);

  /** ACTION NOTIFICATIONS */
  const onUpdateMemberTagsSuccess = () => {
    notifySuccess(
      `${t(
        'businessManager.managerList.user.actions.updateTags.notification.success',
        "Successfully updating user's tags",
      )}`,
    );
  };

  const onUpdateMemberTagsError = (error: string) => {
    notifyError(
      `${t(
        'businessManager.managerList.user.actions.updateTags.notification.error',
        "Error updating user's tags : {{error}}",
        {
          error,
        },
      )}`,
    );
  };

  /** ACTION HANDLER */
  const onUpdateMemberTags = async (tags: ITag[]) => {
    setIsActionsPopoverOpen(false);

    try {
      await updateMemberTags.mutateAsync({
        memberId: memberId,
        tags,
      });

      onUpdateMemberTagsSuccess();
      setIsOpenModal(false);
    } catch (e) {
      onUpdateMemberTagsError(computeErrorMessage(e));
      logError('handleUpdateMemberTags', e);
    }
  };

  return (
    <>
      <Link
        component="button"
        variant="label-sm-regular"
        color={theme.palette.black.main}
        onClick={() => {
          setIsOpenModal(true);
        }}
        startIcon={<Icon name={Icons.LABEL_OUTLINED} width={16} height={16} />}
      >
        {t('businessManager.vmDetails.vm.vmTags', 'Manage tags')}
      </Link>

      <MemberTagsModal
        isOpen={isOpenModal}
        handleClose={() => setIsOpenModal(false)}
        onUpdateMemberTags={onUpdateMemberTags}
        userTags={userTags}
        isSubmittingTags={updateMemberTags.isLoading}
      />
    </>
  );
};

export default UpdateMemberTagsButton;
