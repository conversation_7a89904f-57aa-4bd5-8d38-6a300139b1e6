import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import {
  Icon,
  Modal,
  ModalButton,
  ModalLink,
  theme,
  Typography,
} from 'shared-components';
import { Icons } from 'types';
import { computeErrorMessage, logError } from 'utils';

import Link from '@/components/ui/link';
import { IUserManagerActionsProps } from '@/components/userManager/userManagerActions/userManagerActions';
import { useDeleteMember } from '@/hooks/member/useMember';
import { ApiError } from '@/types/api';
import { UserStatus } from '@/utils/constants';

interface IDeleteMemberProps extends IUserManagerActionsProps {
  setIsActionsPopoverOpen: (value: boolean) => void;
}

const DeleteMemberModal = ({
  inviteEmail,
  memberId,
  setIsActionsPopoverOpen,
  status,
}: IDeleteMemberProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuc<PERSON>, notifyWarning } = useNotifications();
  const deleteMember = useDeleteMember();
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);

  const handleClick = () => {
    setIsOpenModal(!isOpenModal);
  };

  /** ACTION NOTIFICATIONS */
  const onDeleteMemberSuccess = () => {
    notifySuccess(
      `${t(
        'businessManager.managerList.user.actions.deleteMember.notification.success',
        {
          email: inviteEmail,
        },
      )}`,
    );
  };

  const onDeleteMemberError = (error: string) => {
    notifyError(
      `${t(
        'businessManager.managerList.user.actions.deleteMember.notification.error',
        {
          email: inviteEmail,
          error,
        },
      )}`,
    );
  };

  const onDeleteMemberWarning = (label: string) => {
    notifyWarning(label);
  };

  /** ACTION HANDLER */
  const handleDeleteMember = async () => {
    setIsActionsPopoverOpen(false);

    try {
      await deleteMember.mutateAsync({
        member_id: status === UserStatus.PENDING ? null : memberId,
        invite_email: inviteEmail,
      });

      onDeleteMemberSuccess();
    } catch (e) {
      // @todo wait for backend to return an error code string
      if ((e as ApiError)?.code === 400) {
        onDeleteMemberWarning(
          t(
            'businessManager.managerList.user.actions.deleteMember.notification.alreadyAssigned',
            'Cannot delete, member is currently assigned to a PC.',
          ),
        );
      } else {
        onDeleteMemberError(computeErrorMessage(e));
        logError('handleDeleteMember', e);
      }
    }
  };

  return (
    <>
      <Link
        component="button"
        variant="label-sm-regular"
        color={theme.palette.error.main}
        onClick={handleClick}
      >
        <Icon name={Icons.USER_MINUS} width={16} height={16} />
        {t(
          'businessManager.managerList.user.actions.deleteMember.buttonLabel',
          'Delete user',
        )}
      </Link>
      <Modal
        open={isOpenModal}
        onClose={handleClick}
        aria-labelledby="Delete-member-dialog-title"
        aria-describedby="Delete-member-dialog-description"
        title={t(
          'businessManager.managerList.user.actions.deleteMember.modal.title',
          'Delete user',
        )}
      >
        <Typography align="center" variant="body-sm">
          {t(
            'businessManager.managerList.user.actions.deleteMember.modal.content',
            'This operation cannot be undone.',
          )}
        </Typography>
        <ModalButton onClick={handleDeleteMember} color="error">
          {t(
            'businessManager.managerList.user.actions.deleteMember.buttonLabel',
            'Delete user',
          )}
        </ModalButton>
        <ModalLink onClick={handleClick}>
          {t('global.cancel', 'Cancel', { ns: 'common' })}
        </ModalLink>
      </Modal>
    </>
  );
};

export default DeleteMemberModal;
