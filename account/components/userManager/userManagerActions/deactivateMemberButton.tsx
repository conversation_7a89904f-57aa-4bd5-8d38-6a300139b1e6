import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { Icon, theme } from 'shared-components';
import { ErrorTypes, Icons } from 'types';
import { computeErrorMessage, logError } from 'utils';

import Link from '@/components/ui/link';
import { IUserManagerActionsProps } from '@/components/userManager/userManagerActions/userManagerActions';
import { useDeactivateMember } from '@/hooks/member/useMember';

interface IDeactivateMemberProps
  extends Omit<IUserManagerActionsProps, 'status'> {
  setIsActionsPopoverOpen: (value: boolean) => void;
}

const DeactivateMemberButton = ({
  inviteEmail,
  memberId,
  setIsActionsPopoverOpen,
}: IDeactivateMemberProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const deactivateMember = useDeactivateMember();

  /** ACTION NOTIFICATIONS */
  const onDeactivateMemberSuccess = () => {
    notifySuccess(
      `${t(
        'businessManager.managerList.user.actions.deactivateMember.notification.success',
        {
          email: inviteEmail,
        },
      )}`,
    );
  };

  const onDeactivateMemberError = (error: ErrorTypes) => {
    const errorMessage = computeErrorMessage(error);
    notifyError(
      `${t(
        'businessManager.managerList.user.actions.deactivateMember.notification.error',
        {
          email: inviteEmail,
          errorMessage,
        },
      )}`,
    );
    logError('handleDeactivateMember', error);
  };

  /** ACTION HANDLER */
  const handleDeactivateMember = async () => {
    setIsActionsPopoverOpen(false);

    try {
      await deactivateMember.mutateAsync(memberId);

      onDeactivateMemberSuccess();
    } catch (e) {
      onDeactivateMemberError(e);
    }
  };

  return (
    <Link
      component="button"
      variant="label-sm-regular"
      color={theme.palette.error.main}
      onClick={handleDeactivateMember}
    >
      <Icon name={Icons.BLOCK} width={16} height={16} />
      {t(
        'businessManager.managerList.user.actions.deactivateMember.buttonLabel',
        'Disable user',
      )}
    </Link>
  );
};

export default DeactivateMemberButton;
