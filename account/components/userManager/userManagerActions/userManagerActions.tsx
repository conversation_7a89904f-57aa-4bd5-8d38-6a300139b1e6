import { useState } from 'react';
import { Icon } from 'shared-components';
import { Icons, ITag } from 'types';

import UpdateMemberTagsButton from './updateMemberTagsButton';

import ButtonPopover from '@/components/ui/buttonPopover';
import ActivateMemberButton from '@/components/userManager/userManagerActions/activateMemberButton';
import DeactivateMemberButton from '@/components/userManager/userManagerActions/deactivateMemberButton';
import DeleteMemberModal from '@/components/userManager/userManagerActions/deleteMemberModal';
import ResentInvitationButton from '@/components/userManager/userManagerActions/resentInvitationButton';
import { UserStatus } from '@/utils/constants';

export interface IUserManagerActionsProps {
  userTags?: ITag[];
  memberId: string;
  status: string;
  inviteEmail: string;
}

const UserManagerActions = ({
  userTags = [],
  memberId,
  status,
  inviteEmail,
}: IUserManagerActionsProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const displayUpdateMemberTagsButton = status === UserStatus.ACTIVE;
  const displayResentInvitationButton = status === UserStatus.PENDING;
  const displayActivateMemberButton = status === UserStatus.DISABLED;
  const displayDeactivateMemberButton = status === UserStatus.ACTIVE;

  return (
    <ButtonPopover
      dataTestId="user-manager-edit"
      icon={<Icon name={Icons.MENU_DOTS_VERTICAL} width={16} height={16} />}
      name={`user-manager-action`}
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      {displayUpdateMemberTagsButton && (
        <UpdateMemberTagsButton
          setIsActionsPopoverOpen={setIsOpen}
          memberId={memberId}
          userTags={userTags}
        />
      )}
      {displayResentInvitationButton && (
        <>
          <ResentInvitationButton
            inviteEmail={inviteEmail}
            setIsActionsPopoverOpen={setIsOpen}
          />
          <DeleteMemberModal
            inviteEmail={inviteEmail}
            memberId={memberId}
            setIsActionsPopoverOpen={setIsOpen}
            status={status}
          />
        </>
      )}
      {displayActivateMemberButton && (
        <ActivateMemberButton
          inviteEmail={inviteEmail}
          memberId={memberId}
          setIsActionsPopoverOpen={setIsOpen}
        />
      )}
      {displayDeactivateMemberButton && (
        <>
          <DeactivateMemberButton
            inviteEmail={inviteEmail}
            memberId={memberId}
            setIsActionsPopoverOpen={setIsOpen}
          />
          <DeleteMemberModal
            inviteEmail={inviteEmail}
            memberId={memberId}
            setIsActionsPopoverOpen={setIsOpen}
            status={status}
          />
        </>
      )}
    </ButtonPopover>
  );
};

export default UserManagerActions;
