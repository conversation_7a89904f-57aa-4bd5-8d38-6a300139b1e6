import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { Icon, theme } from 'shared-components';
import { ErrorTypes, Icons } from 'types';
import { computeErrorMessage, logError } from 'utils';

import Link from '@/components/ui/link';
import { IUserManagerActionsProps } from '@/components/userManager/userManagerActions/userManagerActions';
import { useActivateMember } from '@/hooks/member/useMember';

interface IActivateMemberProps
  extends Omit<IUserManagerActionsProps, 'status'> {
  setIsActionsPopoverOpen: (value: boolean) => void;
}

const ActivateMemberButton = ({
  inviteEmail,
  memberId,
  setIsActionsPopoverOpen,
}: IActivateMemberProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const activateMember = useActivateMember();

  /** ACTION NOTIFICATIONS */
  const onActivateMemberSuccess = () => {
    notifySuccess(
      `${t(
        'businessManager.managerList.user.actions.activateMember.notification.success',
        {
          email: inviteEmail,
        },
      )}`,
    );
  };

  const onActivateMemberError = (error: ErrorTypes) => {
    const errorMessage = computeErrorMessage(error);
    notifyError(
      `${t(
        'businessManager.managerList.user.actions.activateMember.notification.error',
        {
          email: inviteEmail,
          errorMessage,
        },
      )}`,
    );
  };

  /** ACTION HANDLER */
  const handleActivateMember = async () => {
    setIsActionsPopoverOpen(false);

    try {
      await activateMember.mutateAsync(memberId);

      onActivateMemberSuccess();
    } catch (e) {
      onActivateMemberError(e);
      logError('handleActivateMember', e);
    }
  };

  return (
    <Link
      component="button"
      variant="label-sm-regular"
      color={theme.palette.primary.main}
      onClick={handleActivateMember}
    >
      <Icon name={Icons.USER_CHECK} width={16} height={16} />
      {t(
        'businessManager.managerList.user.actions.activateMember.buttonLabel',
        'Activate this account',
      )}
    </Link>
  );
};

export default ActivateMemberButton;
