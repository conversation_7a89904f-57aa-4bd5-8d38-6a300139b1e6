import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { Icon, theme } from 'shared-components';
import { Icons } from 'types';
import { computeErrorMessage, logError } from 'utils';

import Link from '@/components/ui/link';
import { useResendInvitation } from '@/hooks/member/useMember';

interface IResentInvitationProps {
  inviteEmail: string;
  setIsActionsPopoverOpen: (value: boolean) => void;
}

const ResentInvitationButton = ({
  inviteEmail,
  setIsActionsPopoverOpen,
}: IResentInvitationProps) => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const resendInvitation = useResendInvitation();

  /** ACTION NOTIFICATIONS */
  const onResendInvitationSuccess = () => {
    notifySuccess(
      `${t(
        'businessManager.managerList.user.actions.resendInvitation.notification.success',
        { email: inviteEmail },
      )}`,
    );
  };

  const onResendInvitationError = (error: string) => {
    notifyError(
      `${t(
        'businessManager.managerList.user.actions.resendInvitation.notification.error',
        {
          email: inviteEmail,
          error,
        },
      )}`,
    );
  };

  /** ACTION HANDLER */
  const handleResentInvitation = async () => {
    setIsActionsPopoverOpen(false);

    try {
      const encodedInviteEmail = encodeURIComponent(inviteEmail);

      await resendInvitation.mutateAsync(encodedInviteEmail);

      onResendInvitationSuccess();
    } catch (e) {
      onResendInvitationError(computeErrorMessage(e));
      logError('handleResentInvitation', e);
    }
  };

  return (
    <Link
      component="button"
      variant="label-sm-regular"
      color={theme.palette.black.main}
      onClick={handleResentInvitation}
    >
      <Icon name={Icons.EMAIL} width={16} height={16} />
      {t(
        'businessManager.managerList.user.actions.resendInvitation.buttonLabel',
        'Resend email confirmation',
      )}
    </Link>
  );
};

export default ResentInvitationButton;
