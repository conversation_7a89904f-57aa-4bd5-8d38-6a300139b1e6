import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useTranslation } from 'next-i18next';
import { useState, SyntheticEvent, Dispatch } from 'react';
import { Controller } from 'react-hook-form';
import { Alert, Button, Icon, Input, theme } from 'shared-components';
import { Icons, ITag, NotificationState } from 'types';

import TagFilterSelect from '@/components/tagFilter/tagFilterSelect';
import useInviteForm from '@/hooks/form/useInviteForm';

const Container = styled.div`
  margin: 24px 0;
`;

const Row = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const Action = styled.div`
  display: flex;
  justify-content: end;
  flex: 1;
`;

const InviteButton = styled(Button)`
  padding-inline: 24px;
`;

const Filters = styled.div`
  display: flex;
  width: 100%;
  flex: 1;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;

  ${theme.breakpoints.up('md')} {
    width: auto;
    justify-content: start;
  }
`;

/*
const Selects = styled.div`
  width: 408px;
`;
*/

const inviteFormData = {
  invite_email: '',
};

export interface IUserManagerOptions {
  isB2b: boolean;
  availableTagsList: ITag[];
  selectedTagsOptions: string[];
  setSelectedTagsOptions: Dispatch<string[]>;
}

const UserManagerOptions = ({
  isB2b,
  availableTagsList,
  selectedTagsOptions,
  setSelectedTagsOptions,
}: IUserManagerOptions) => {
  const { t } = useTranslation();

  const [isInviteUserVisible, setIsInviteUserVisible] = useState(false);
  const [isNotificationActive, setIsNotificationActive] = useState(false);
  const [notification, setNotification] = useState({
    type: NotificationState.SUCCESS,
    title: '',
    description: '',
  });

  const showNotification = (notificationProps: {
    type: NotificationState;
    title: string;
    description: string;
  }) => {
    setNotification(notificationProps);
    setIsNotificationActive(true);
  };

  const onInviteSuccess = () => {
    showNotification({
      type: NotificationState.SUCCESS,
      title: t(
        'businessManager.managerList.user.invite.success.title',
        'Invitation sent',
      ),
      description: t(
        'businessManager.managerList.user.invite.success.message',
        '{{ email }} will receive an email shortly inviting him to create an account in your team and get access to his Shadow PC Pro.',
        { email: getInviteValues('invite_email') },
      ),
    });
    inviteReset();
  };

  const onInviteError = () => {
    showNotification({
      type: NotificationState.ERROR,
      title: t(
        'businessManager.managerList.user.invite.error.title',
        'An error has occured when sending an invitation',
      ),
      description: t(
        'businessManager.managerList.user.invite.error.message',
        'You have already sent an invitation to {{ email }}',
        { email: getInviteValues('invite_email') },
      ),
    });
  };

  const {
    onInviteSubmit,
    inviteControl,
    inviteIsDirty,
    inviteIsSubmitting,
    getInviteValues,
    inviteReset,
  } = useInviteForm(onInviteSuccess, onInviteError, inviteFormData);

  const handleInviteFormSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setIsNotificationActive(false);
    onInviteSubmit();
  };

  return (
    <Container>
      <Collapse in={isNotificationActive}>
        <Alert type={notification.type} title={notification.title}>
          {notification.description}
        </Alert>
      </Collapse>
      <Row>
        <Filters>
          <TagFilterSelect
            filterName="tags"
            options={availableTagsList}
            selectedOptions={selectedTagsOptions}
            setSelectedOptions={setSelectedTagsOptions}
          />
        </Filters>
        <Action>
          {isInviteUserVisible ? (
            <form onSubmit={handleInviteFormSubmit}>
              <Controller
                name="invite_email"
                control={inviteControl}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    id="invite-email-input"
                    data-test-id="invite-email-input"
                    placeholder={t('global.email', 'Email', { ns: 'common' })}
                    error={error?.message}
                    leftAddon={
                      <Icon
                        name={Icons.EMAIL}
                        color={theme.palette.black.main75}
                        width={24}
                        height={24}
                      />
                    }
                    rightAddon={
                      <InviteButton
                        data-test-id="invite-email-form-button"
                        disabled={!inviteIsDirty || inviteIsSubmitting}
                        loading={inviteIsSubmitting}
                      >
                        {t('global.invite', 'Invite', { ns: 'common' })}
                      </InviteButton>
                    }
                  />
                )}
              />
            </form>
          ) : (
            <Button
              data-test-id="createUserButton"
              onClick={() => setIsInviteUserVisible(true)}
              color={isB2b ? 'black' : 'primary'}
              size="medium"
              startIcon={<Icon name={Icons.PLUS} width={16} height={16} />}
            >
              {t(
                'businessManager.managerList.user.options.createUser',
                'Add new user',
              )}
            </Button>
          )}
        </Action>
      </Row>
      {/*
      <InputText
        control={control}
        name="search"
        placeholder={t('global.search', 'Search', { ns: 'common' })}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Icon
                name={Icons.MAGNIFIER}
                color={theme.palette.primary.main}
              />
            </InputAdornment>
          ),
        }}
      />
      */}
    </Container>
  );
};

export default UserManagerOptions;
