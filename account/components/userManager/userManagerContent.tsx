import { useState } from 'react';
import { ITag } from 'types';

import UserManagerList from '@/components/userManager/userManagerList';
import UserManagerOptions from '@/components/userManager/userManagerOptions';
import { useCurrentMember } from '@/hooks/member/useMember';

const UserManagerContent = () => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = !!currentMemberQuery.data?.user?.b2b;
  const availableTagsList = currentMemberQuery.data?.available_tags as ITag[];

  const [selectedTagsOptions, setSelectedTagsOptions] = useState<string[]>([]);

  return (
    <>
      <UserManagerOptions
        isB2b={isB2b}
        availableTagsList={availableTagsList}
        selectedTagsOptions={selectedTagsOptions}
        setSelectedTagsOptions={setSelectedTagsOptions}
      />
      <UserManagerList selectedTags={selectedTagsOptions} />
    </>
  );
};

export default UserManagerContent;
