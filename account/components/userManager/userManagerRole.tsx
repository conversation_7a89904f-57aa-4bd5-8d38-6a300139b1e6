import styled from '@emotion/styled';
import { Select, MenuItem } from '@mui/material';
import { useTranslation } from 'next-i18next';
import { Typography } from 'shared-components';

import { IUserManager } from '@/types/adminManagers';
import { USER_ROLES } from '@/utils/constants';

const Container = styled(Select)`
  fieldset {
    display: none;
  }
  .MuiSelect-select {
    padding-left: 0;
  }
`;

/* @todo create or use existing Select component */
const UserManagerRole = ({ status }: Pick<IUserManager, 'status'>) => {
  const { t } = useTranslation();

  return (
    <Container value={status}>
      {USER_ROLES.map((item, index) => (
        <MenuItem value={item.value} key={index}>
          <Typography variant="body-sm">
            {t(`businessManager.user.role.${item.label}`)}
          </Typography>
        </MenuItem>
      ))}
    </Container>
  );
};

export default UserManagerRole;
