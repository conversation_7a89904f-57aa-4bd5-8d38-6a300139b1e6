import styled from '@emotion/styled';
import { theme } from 'shared-components';

import UserIdentity from '@/components/userCard/userIdentity';
import UserThumbnail from '@/components/userCard/userThumbnail';

const Container = styled.div<
  Pick<IUserCardProps, 'onlyThumbnailDisplay'> & { hasName?: boolean }
>`
  position: relative;
  display: grid;
  grid-template-areas: ${({ hasName }) =>
    hasName ? "'thumbnail name'  'thumbnail email'" : "'thumbnail email'"};
  column-gap: 16px;
  align-items: center;

  ${({ onlyThumbnailDisplay }) =>
    onlyThumbnailDisplay &&
    `
      max-width: 250px;
      
      &:not(:hover) > p {
        opacity: 0;
        display: none;
      }
      &:hover p {
      opacity: 1;
      display: -webkit-box;

      ::after {
        content: '';
        top: -8px;
        left: -16px;
        position: absolute;
        border: 1px solid ${theme.palette.secondary.main25};
        border-radius: 100px;
        background: ${theme.palette.primary.main10};
        width: 100%;
        height: 100%;
        z-index: -1;
        padding: 8px 16px;
        box-sizing: content-box;
      }
    }`}
`;

export interface IUserCardData {
  lastName: string;
  firstName: string;
  email: string;
  role?: string;
}

interface IUserCardProps {
  data: IUserCardData;
  isRoleDisplay?: boolean;
  onlyThumbnailDisplay?: boolean;
}

const UserCard = ({
  data,
  isRoleDisplay = false,
  onlyThumbnailDisplay = false,
  ...props
}: IUserCardProps) => {
  return (
    <Container
      onlyThumbnailDisplay={onlyThumbnailDisplay}
      hasName={!!data.firstName || !!data.lastName}
      {...props}
    >
      <UserThumbnail firstName={data.firstName} lastName={data.lastName} />
      <UserIdentity data={data} isRoleDisplay={isRoleDisplay} />
    </Container>
  );
};

export default UserCard;
