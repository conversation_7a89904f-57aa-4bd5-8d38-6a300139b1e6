import styled from '@emotion/styled';
import { Typography } from 'shared-components';

import { IUserCardData } from './userCard';
const Name = styled(Typography)`
  grid-area: name;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  overflow-wrap: anywhere;
  white-space: break-spaces;
`;

const EmailText = styled(Typography)`
  grid-area: email;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  overflow-wrap: anywhere;
  white-space: break-spaces;
`;

interface IUserIdentityProps {
  data: IUserCardData;
  isRoleDisplay?: boolean;
}

const UserIdentity = ({
  data,
  isRoleDisplay = false,
  ...props
}: IUserIdentityProps) => {
  return (
    <>
      {(data.firstName || data.lastName) && (
        <Name variant="body-sm" {...props}>
          {data.firstName} {data.lastName} {isRoleDisplay && `(${data.role})`}
        </Name>
      )}
      <EmailText variant="label-xs-link" color="primary" {...props}>
        {data.email}
      </EmailText>
    </>
  );
};

export default UserIdentity;
