import styled from '@emotion/styled';
import { theme, Typography } from 'shared-components';

const Container = styled.div`
  display: flex;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  background: ${theme.palette.primary.main10};
  overflow: hidden;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  grid-area: thumbnail;

  img {
    width: 100%;
    height: auto;
  }
`;

const TypographyStyled = styled(Typography)`
  text-transform: uppercase;
`;

type UserThumbnailProps = {
  firstName: string;
  lastName: string;
};

const renderThumbnail = ({ firstName, lastName }: UserThumbnailProps) => {
  if (firstName || lastName) {
    return firstName.charAt(0) + lastName.charAt(0);
  }
  return '@';
};

const UserThumbnail = ({ firstName, lastName }: UserThumbnailProps) => {
  return (
    <Container>
      <TypographyStyled variant="body-lg-regular" color="primary">
        {renderThumbnail({ firstName, lastName })}
      </TypographyStyled>
    </Container>
  );
};

export default UserThumbnail;
