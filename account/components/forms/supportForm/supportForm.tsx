import styled from '@emotion/styled';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { SyntheticEvent, useCallback, useState } from 'react';
import { Controller } from 'react-hook-form';
import {
  Button,
  Textarea,
  theme,
  useThemeMediaQueries,
} from 'shared-components';
import { IMemberDetails } from 'types';

import DropzoneArea from './dropzoneArea/dropzoneArea';
import SelectTopic from './selectTopic';

import { useSupportForm } from '@/hooks/form/useSupportForm';
import { useCurrentMember } from '@/hooks/member/useMember';
import { ISupportSelectDisplay, ISupportSelectValues } from '@/types/support';

const ButtonWrapper = styled.div`
  display: flex;
  margin-top: 24px;
  flex: 1;

  ${theme.breakpoints.up('md')} {
    margin-top: 40px;
    justify-content: flex-end;
  }
`;

const SupportForm = () => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();

  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const { notifyError, notifySuccess } = useNotifications();

  const initialDisplayInputForm: ISupportSelectDisplay = {
    subject: true,
    account: false,
    device: false,
    driveDevice: false,
    messageInput: false,
  };

  const initialFormValues: ISupportSelectValues = {
    subject: '',
    account: '',
    device: '',
    driveDevice: '',
    messageInput: '',
  };

  const [displayInputForm, setDisplayInputForm] = useState(
    initialDisplayInputForm,
  );
  const [selectedValues, setSelectedValues] = useState(initialFormValues);
  const [files, setFiles] = useState([]);
  // We hide the dropzone for now
  // TODO : restore dropzone state
  const showDropzone = false;
  // const [showDropzone, setShowDropzone] = useState(true);

  const formData = { message: '' };

  const gatherInformationForm = () => ({
    ...selectedValues,
    files,
  });

  const onSuccess = () => {
    setDisplayInputForm(initialDisplayInputForm);
    setSelectedValues(initialFormValues);
    reset({ message: '' });

    // We hide the dropzone for now
    // TODO : restore dropzone state
    // // Unmount/mount component to clear files (lib has no method for that for now)
    // setShowDropzone(false);

    // // React 18 batch these two setstate updates, so we have to wait for another cycle before changing state, hence the setTimeout
    // setTimeout(() => {
    //   setShowDropzone(true);
    // }, 0);

    notifySuccess(
      t(
        'support.contactUs.update.success',
        'Your message has been send to our support team!',
      ),
    );
    setFiles([]);
  };

  const onError = () => {
    notifyError(
      t(
        'support.contactUs.update.error',
        'An error occurred when sending your message to our support team. Please try again later.',
      ),
    );
  };

  const { onSubmit, reset, control, isDirty, isSubmitting } = useSupportForm(
    gatherInformationForm,
    onSuccess,
    onError,
    formData,
  );

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      onSubmit();
    },
    [onSubmit],
  );

  const onFileAdded = (filesAdded: any) => {
    setFiles(filesAdded);
  };

  return (
    <form onSubmit={handleSubmit}>
      <SelectTopic
        setDisplayInputForm={setDisplayInputForm}
        setSelectedValues={setSelectedValues}
        selectedValues={selectedValues}
        displayInputForm={displayInputForm}
      />
      {displayInputForm.messageInput && (
        <Controller
          name="message"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Textarea
              {...field}
              id="support_message-input"
              data-test-id="last_name-input"
              rows={7}
              placeholder={t(
                'support.contactUs.textarea.label',
                'Please provide additional details to help us efficiently solve your request.',
              )}
              label={t(
                'support.contactUs.textarea.label',
                'Please provide additional details to help us efficiently solve your request.',
              )}
              error={error?.message}
            />
          )}
        />
      )}
      {showDropzone && <DropzoneArea onFileAdded={onFileAdded} files={files} />}
      <ButtonWrapper>
        <Button
          color={isB2b ? 'black' : 'primary'}
          fullWidth={isSM}
          type="submit"
          disabled={!isDirty}
          loading={isSubmitting}
          size="medium"
        >
          {t('global.send', 'Send', { ns: 'common' })}
        </Button>
      </ButtonWrapper>
    </form>
  );
};

export default SupportForm;
