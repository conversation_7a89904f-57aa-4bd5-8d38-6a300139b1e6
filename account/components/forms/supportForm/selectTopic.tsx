import styled from '@emotion/styled';
import { FormControl, MenuItem, SelectChangeEvent } from '@mui/material';
import { isEmpty } from 'lodash';
import { SetStateAction, Dispatch } from 'react';
import { theme } from 'shared-components';

import Select from '@/components/ui/select';
import { useSupportFormChoicesList } from '@/hooks/support/useSupportFormChoicesList';
import {
  ISelectOption,
  SupportCase,
  ISupportSelectDisplay,
  ISupportSelectValues,
  ISupportFormChoice,
} from '@/types/support';

const SelectMenuItem = styled(MenuItem)`
  &:hover {
    background: ${theme.palette.secondary.main10};
  }
`;

const SelectStyled = styled(Select)`
  margin-bottom: 16px;

  ${theme.breakpoints.up('lg')} {
    margin-bottom: 24px;
  }
`;

interface ISelectTopicProps {
  setDisplayInputForm: Dispatch<SetStateAction<ISupportSelectDisplay>>;
  setSelectedValues: Dispatch<SetStateAction<ISupportSelectValues>>;
  selectedValues: ISupportSelectValues;
  displayInputForm: ISupportSelectDisplay;
}

const SelectTopic = ({
  setSelectedValues,
  selectedValues,
  displayInputForm,
  setDisplayInputForm,
}: ISelectTopicProps) => {
  const { supportChoicesList } = useSupportFormChoicesList();

  const getSupportChoiceOptions = (supportChoice: any): ISelectOption[] => {
    return supportChoice.options;
  };

  const handleFormChange = (
    e: SelectChangeEvent,
    supportQuestion: ISupportFormChoice,
  ) => {
    const selectedValue = e.target.value;

    const resetValues = selectedValues;
    const resetDisplayInputForm = displayInputForm;

    // Reset some values for each input change (see json)
    if (supportQuestion?.reset?.length > -1) {
      supportQuestion?.reset?.forEach((resetElement: string) => {
        resetValues[resetElement as keyof typeof selectedValues] = '';
        resetDisplayInputForm[resetElement as keyof typeof displayInputForm] =
          false;
      });
    }

    // eslint-disable-next-line no-prototype-builtins
    if (supportQuestion.nextToDisplay.hasOwnProperty(SupportCase.AllCase)) {
      setSelectedValues({
        ...resetValues,
        [supportQuestion.id as keyof typeof selectedValues]: selectedValue,
      });
      setDisplayInputForm({
        ...resetDisplayInputForm,
        [supportQuestion.nextToDisplay[
          SupportCase.AllCase
        ] as keyof typeof displayInputForm]: true,
      });
    }

    // eslint-disable-next-line no-prototype-builtins
    if (supportQuestion.nextToDisplay.hasOwnProperty(selectedValue)) {
      setSelectedValues({
        ...resetValues,
        [supportQuestion.id as keyof typeof selectedValues]: selectedValue,
      });
      setDisplayInputForm({
        ...resetDisplayInputForm,
        [supportQuestion.nextToDisplay[
          selectedValue
        ] as keyof typeof displayInputForm]: true,
      });
      return;
    } else if (
      // eslint-disable-next-line no-prototype-builtins
      supportQuestion.nextToDisplay.hasOwnProperty(SupportCase.OthersCase)
    ) {
      setSelectedValues({
        ...resetValues,
        [supportQuestion.id as keyof typeof selectedValues]: selectedValue,
      });
      setDisplayInputForm({
        ...resetDisplayInputForm,
        [supportQuestion.nextToDisplay[
          SupportCase.OthersCase
        ] as keyof typeof displayInputForm]: true,
      });
      return;
    }
  };

  return (
    <>
      {supportChoicesList.map(
        (supportChoice, index) =>
          (displayInputForm[
            supportChoice?.id as keyof typeof displayInputForm
          ] as boolean) && (
            <FormControl fullWidth key={index}>
              <SelectStyled
                labelId={`${supportChoice.id}-select-label`}
                id={`${supportChoice.id}-select`}
                onChange={e => handleFormChange(e as any, supportChoice)}
                value={
                  selectedValues[
                    supportChoice?.id as keyof typeof selectedValues
                  ]
                }
                displayEmpty
                isValue={
                  !isEmpty(
                    selectedValues[
                      supportChoice?.id as keyof typeof selectedValues
                    ],
                  )
                }
                placeholder={supportChoice.label}
              >
                {getSupportChoiceOptions(supportChoice)?.map(
                  (option, indexOption) => (
                    <SelectMenuItem
                      value={option.value}
                      key={`${supportChoice.id}_${index}_${indexOption}`}
                    >
                      {option.displayedValue}
                    </SelectMenuItem>
                  ),
                )}
              </SelectStyled>
            </FormControl>
          ),
      )}
    </>
  );
};

export default SelectTopic;
