import styled from '@emotion/styled';
import { Icon, theme } from 'shared-components';
import { Icons } from 'types';

import { fileType } from '@/components/forms/supportForm/dropzoneArea/dropzoneArea';

const Container = styled.div`
  position: relative;
  border-radius: 4px;
  border: 1px solid ${theme.palette.secondary.main25};
  overflow: hidden;
  max-width: 33.3%;
  height: 100px;

  :hover button {
    display: flex;
  }
`;

const Img = styled.img`
  height: 100%;
`;

const RemoveButton = styled.button`
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
  transition: background 0.4s;

  :hover {
    background: ${theme.palette.black.main60};
  }
`;

const IconWrapper = styled.div`
  display: flex;
  width: 50px;
  height: 50px;
  align-items: center;
  background: ${theme.palette.white.main};
  border-radius: 50%;
  justify-content: center;
`;

interface ThumbPreview {
  file: fileType;
  removeFile: (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    file: fileType,
  ) => void;
}

const ThumbPreview = ({ file, removeFile }: ThumbPreview) => (
  <Container key={file.name} onClick={e => e.stopPropagation()}>
    <Img src={file.preview} />
    <RemoveButton onClick={e => removeFile(e, file)} type="button">
      <IconWrapper>
        <Icon name={Icons.TRASH} width={24} />
      </IconWrapper>
    </RemoveButton>
  </Container>
);

export default ThumbPreview;
