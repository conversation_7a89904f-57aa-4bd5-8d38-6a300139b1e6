import styled from '@emotion/styled';
import { isEmpty } from 'lodash';
import { Trans } from 'next-i18next';
import { useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { theme, Typography } from 'shared-components';

import ThumbPreview from '@/components/forms/supportForm/dropzoneArea/thumbPreview';

const Container = styled.div`
  margin-top: 16px;

  ${theme.breakpoints.up('lg')} {
    margin-top: 24px;
  }
`;

const Wrapper = styled.div`
  padding: 24px;
  background: ${theme.palette.secondary.main10};
  border: 2px solid ${theme.palette.secondary.main10};
  border-radius: ${theme.shape.borderRadius}px;
  cursor: pointer;
  text-align: center;
  transition: background 0.4s, border 0.4s;

  :hover {
    border: 2px solid ${theme.palette.secondary.main25};
    background: ${theme.palette.secondary.main25};
  }
`;

const PreviewContainer = styled.div`
  display: flex;
  margin-top: 24px;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
`;

export type fileType = {
  path: string;
  preview: string;
  name: string;
};

interface IDropzoneAreaProps {
  onFileAdded: (filesAdded: any) => void;
  files: fileType[] | [];
}

const DropzoneArea = ({ files, onFileAdded }: IDropzoneAreaProps) => {
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/*': [],
    },
    onDrop: acceptedFiles => {
      const newFiles = acceptedFiles.map(file =>
        Object.assign(file, {
          preview: URL.createObjectURL(file),
        }),
      );

      onFileAdded([...files, ...newFiles]);
    },
  });

  const removeFile = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    file: fileType,
  ) => {
    e.stopPropagation();
    const newFiles = [...files];

    newFiles.splice((files as fileType[]).indexOf(file), 1);
    onFileAdded(newFiles);
  };

  useEffect(() => {
    // Revoke the data uris to avoid memory leaks
    return () => files.forEach(file => URL.revokeObjectURL(file.preview));
  }, []);

  return (
    <Container>
      <Wrapper {...getRootProps()}>
        <input {...getInputProps()} />
        <Typography variant="label-sm-regular">
          <Trans i18nKey="support.contactUs.dropzone.label">
            <Typography variant="label-sm-regular-underline" component="span">
              Import a file
            </Typography>{' '}
            or drag it here (max. 3 Mo)
          </Trans>
        </Typography>
        {!isEmpty(files) && (
          <PreviewContainer>
            {files.map((file, index) => (
              <ThumbPreview
                key={`thumbPreview-${index}`}
                file={file}
                removeFile={removeFile}
              />
            ))}
          </PreviewContainer>
        )}
      </Wrapper>
    </Container>
  );
};

export default DropzoneArea;
