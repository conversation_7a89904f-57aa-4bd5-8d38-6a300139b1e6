import styled from '@emotion/styled';
import {
  Accordion as Mu<PERSON><PERSON><PERSON>rdion,
  AccordionDetails as MuiAccordionDetails,
  AccordionSummary as MuiAccordionSummary,
  FormGroup,
} from '@mui/material';
import throttle from 'lodash/throttle';
import { useTranslation } from 'next-i18next';
import {
  Fragment,
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { Controller } from 'react-hook-form';
import {
  Alert,
  Icon,
  Textarea,
  theme,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons, NotificationState } from 'types';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import InputCheckbox from '@/components/ui/form/inputCheckbox';
import useCancelVmForm from '@/hooks/form/useCancelVmForm';
import useCancellationReasons from '@/hooks/useCancellationReasons';
import type { IFormProps } from '@/types/forms';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const Accordion = styled(MuiAccordion)`
  box-shadow: inherit;
  &::before {
    background: none;
  }
`;

const AccordionDetails = styled(MuiAccordionDetails)`
  padding: 16px;
`;

const AccordionSummary = styled(MuiAccordionSummary, {
  shouldForwardProp: prop => prop !== 'isActive',
})<{ isActive?: boolean }>`
  padding: 0 23px 0 16px;
  border-radius: 8px;
  background: ${theme.palette.secondary.main25};

  &.Mui-expanded {
    p {
      color: ${theme.palette.white.main};
    }

    background-color: ${theme.palette.primary.main};
    svg path {
      stroke: white;
    }
  }
`;

const CancelVmForm = ({
  onSelection = () => null,
  onSuccess = () => null,
  onError = () => null,
  hideSubmitButton = false,
  submitRef,
}: IFormProps) => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();
  const reasons = useCancellationReasons();
  const { onSubmit, watch, control, isSubmitting } = useCancelVmForm(
    onSuccess,
    onError,
  );
  const reasonsKeys = useMemo(
    () =>
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      reasons.map(([_, values]) => values.reasons.map(([key]) => key)).flat(),
    [reasons],
  );
  const [expanded, setExpanded] = useState<string | false>(false);

  const watchReasons = watch(reasonsKeys as any); // this also force rerender when a checkbox has been clicked
  const watchComment = watch('comment');

  const splitWatchReasons = useMemo(() => {
    const arr: undefined[][] | boolean[][] = [];
    const clonedWatchReasons = watchReasons.slice();

    for (let index = 0; index < reasons.length; index++) {
      arr.push(
        clonedWatchReasons.splice(0, reasons[index]?.[1].reasons.length),
      );
    }

    return arr;
  }, [reasons, watchReasons]);

  const handlePanelChange =
    (panel: string) => (event: SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(
    throttle(
      () => onSelection({ reasons: watchReasons, comment: watchComment }),
      200,
    ),
    [watchReasons, watchComment],
  );

  return (
    <Form onSubmit={handleSubmit}>
      {reasons.map(([reasonKey, reasonData], index) => {
        const isActive = splitWatchReasons[index]?.some(
          (reason: undefined | boolean) => reason,
        );

        return (
          <Accordion
            disableGutters
            key={reasonKey}
            expanded={expanded === `panel-${reasonKey}`}
            onChange={handlePanelChange(`panel-${reasonKey}`)}
          >
            <AccordionSummary
              aria-controls={`panel-${reasonKey}-content`}
              id={`panel-${reasonKey}-header`}
              isActive={isActive}
              expandIcon={<Icon name={Icons.CHEVRON_DOWN} width={12} />}
            >
              <Typography
                variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}
              >
                {reasonData.title}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormGroup>
                {reasonData.reasons.map(
                  ([reasonId, reasonLabel, reasonAlertContent], index2) => {
                    const showAlert =
                      splitWatchReasons[index]?.[index2] && reasonAlertContent;

                    return (
                      <Fragment key={reasonId}>
                        <InputCheckbox
                          id={reasonId}
                          dataTestId={`b2c-cancelSubscription-reason-${reasonKey}`}
                          checkboxSize={isLG ? 'large' : 'medium'}
                          control={control}
                          label={reasonLabel}
                          name={reasonId}
                        />
                        {showAlert && (
                          <Alert type={NotificationState.INFO}>
                            {reasonAlertContent}
                          </Alert>
                        )}
                      </Fragment>
                    );
                  },
                )}
              </FormGroup>
            </AccordionDetails>
          </Accordion>
        );
      })}
      <Accordion
        disableGutters
        expanded={expanded === 'panel-comment'}
        onChange={handlePanelChange('panel-comment')}
      >
        <AccordionSummary
          aria-controls={`panel-comment-content`}
          id={`panel-comment-header`}
          isActive={!!watchComment}
          expandIcon={<Icon name={Icons.CHEVRON_DOWN} width={12} />}
        >
          <Typography variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}>
            {t('subscription.cancelVm.otherReason.title', 'Other')}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Controller
            name="comment"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Textarea
                {...field}
                aria-label="comment"
                id="coupon-input"
                data-test-id="coupon-input"
                rows={7}
                placeholder={t(
                  'subscription.cancelVm.otherReason.placeholder',
                  'Please, describe the issue you had',
                )}
                error={error?.message}
                hasResize={false}
              />
            )}
          />
        </AccordionDetails>
      </Accordion>
      <SubmitButton
        ref={submitRef}
        loading={isSubmitting}
        hideSubmitButton={hideSubmitButton}
      >
        {t('global.continue', 'Continue', { ns: 'common' })}
      </SubmitButton>
    </Form>
  );
};

export default CancelVmForm;
