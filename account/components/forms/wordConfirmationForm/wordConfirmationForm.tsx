import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import { useCallback, useEffect, SyntheticEvent } from 'react';
import { Controller } from 'react-hook-form';
import { Input, Typography, useThemeMediaQueries } from 'shared-components';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import useWordConfirmationForm from '@/hooks/form/useWordConfirmationForm';
import { IFormProps } from '@/types/forms';

const Title = styled(Typography)`
  padding: 0 0 16px;
`;

const formData = {
  word: '',
};

interface IWordConfirmationFormProps extends IFormProps {
  wordToCompare: string | undefined;
}

const WordConfirmationForm = ({
  onSubmitting = () => null,
  onSuccess = () => null,
  hideSubmitButton = false,
  submitRef,
  wordToCompare = '',
}: IWordConfirmationFormProps) => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();
  const { onSubmit, control, isSubmitting } = useWordConfirmationForm(
    onSuccess,
    wordToCompare,
    formData,
  );

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  useEffect(() => {
    onSubmitting(isSubmitting);
  }, [isSubmitting, onSubmitting]);

  return (
    <>
      <Title variant={isLG ? 'body-sm' : 'body-xs'}>
        <Trans
          i18nKey="form.word.info"
          ns="common"
          defaults="Please write <bold>{{ wordToCompare }}</bold> below in order to continue:"
          values={{ wordToCompare }}
          components={{ bold: <strong /> }}
        />
      </Title>
      <form onSubmit={handleSubmit}>
        <Controller
          name="word"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="wordConfirmation-form-input"
              data-test-id="wordConfirmation-form-input"
              label={t('form.word.label', 'Word to type', { ns: 'common' })}
              error={error?.message}
            />
          )}
        />
        <SubmitButton
          ref={submitRef}
          loading={isSubmitting}
          hideSubmitButton={hideSubmitButton}
        >
          {t('global.continue', 'Continue', { ns: 'common' })}
        </SubmitButton>
      </form>
    </>
  );
};

export default WordConfirmationForm;
