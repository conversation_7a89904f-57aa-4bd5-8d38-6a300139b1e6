import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useEffect, useCallback, SyntheticEvent } from 'react';
import { KeyboardISO } from 'types';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import InputSelect from '@/components/ui/form/inputSelect';
import LabelSelect from '@/components/ui/form/labelSelect';
import useResetVmForm from '@/hooks/form/useResetVmForm';
import { useConfig } from '@/hooks/store/useConfig';
import type { IFormProps } from '@/types/forms';
import { VmConfiguration } from '@/types/vm';
import {
  CONFIGURATION_OPTIONS,
  KEYBOARD_OPTIONS,
  KEYBOARD_PER_LANGUAGE,
  VM_LANGUAGES_WITH_FLAG_OPTIONS,
} from '@/utils/constants';

interface IResetVmFormProps extends IFormProps {
  subscriptionId: string;
  isB2b: boolean;
}

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ResetVmForm = ({
  onSubmitting = () => null,
  onSuccess = () => null,
  onError = () => null,
  hideSubmitButton = false,
  submitRef,
  subscriptionId,
  isB2b,
}: IResetVmFormProps) => {
  const { language } = useConfig();
  const { t } = useTranslation();

  const shouldDisplayVmConfigurationOptions = !isB2b;
  const defaultVmConfiguration = isB2b
    ? VmConfiguration.MANUAL
    : VmConfiguration.AUTOMATIC;

  const formData = {
    configuration: defaultVmConfiguration,
    language,
    keyboard: KEYBOARD_PER_LANGUAGE[language] as KeyboardISO,
  };

  const { onSubmit, control, watch, isSubmitting } = useResetVmForm(
    onSuccess,
    onError,
    subscriptionId,
    formData,
  );

  const watchConfiguration = watch('configuration');

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  useEffect(() => {
    onSubmitting(isSubmitting);
  }, [isSubmitting, onSubmitting]);

  return (
    <Form onSubmit={handleSubmit}>
      {shouldDisplayVmConfigurationOptions && (
        <div>
          <LabelSelect
            id="configuration"
            text={t('form.configuration.label', 'Configuration', {
              ns: 'common',
            })}
            tooltipTitle={t(
              `form.configuration.label.information`,
              'In manual configuration, you will need to set up the language, keyboard, and your Windows PC yourself.',
            )}
          />
          <InputSelect
            label="configuration"
            required
            control={control}
            name="configuration"
            items={CONFIGURATION_OPTIONS}
          />
        </div>
      )}
      {watchConfiguration === VmConfiguration.AUTOMATIC && (
        <>
          <div>
            <LabelSelect
              id="language"
              text={t('form.language.label', 'Language', {
                ns: 'common',
              })}
            />
            <InputSelect
              required
              control={control}
              name="language"
              label="language"
              items={VM_LANGUAGES_WITH_FLAG_OPTIONS}
            />
          </div>
          <div>
            <LabelSelect
              id="keyboard"
              text={t('form.keyboard.label', 'Keyboard', {
                ns: 'common',
              })}
            />
            <InputSelect
              required
              control={control}
              name="keyboard"
              label="keyboard"
              items={KEYBOARD_OPTIONS}
            />
          </div>
        </>
      )}
      <SubmitButton
        ref={submitRef}
        loading={isSubmitting}
        hideSubmitButton={hideSubmitButton}
      >
        {t('global.continue', 'Continue', { ns: 'common' })}
      </SubmitButton>
    </Form>
  );
};

export default ResetVmForm;
