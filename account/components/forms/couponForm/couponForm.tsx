import { useTranslation } from 'next-i18next';
import { useCallback, useEffect, SyntheticEvent } from 'react';
import { Controller } from 'react-hook-form';
import { Input } from 'shared-components';
import { CouponType } from 'types';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import useCouponForm from '@/hooks/form/useCouponForm';
import { IFormProps } from '@/types/forms';
import { COUPON_INPUT_PLACEHOLDER } from '@/utils/constants';

const formData = {
  coupon: '',
};

interface ICouponFormProps extends IFormProps {
  couponType: CouponType;
  subscriptionId?: string;
}

const CouponForm = ({
  onSuccess = () => null,
  onError = () => null,
  onSubmitting = () => null,
  hideSubmitButton = false,
  submitRef,
  couponType,
  subscriptionId,
}: ICouponFormProps) => {
  const { t } = useTranslation();
  const { onSubmit, control, isSubmitting } = useCouponForm(
    onSuccess,
    onError,
    formData,
    subscriptionId,
  );

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  useEffect(() => {
    onSubmitting(isSubmitting);
  }, [isSubmitting, onSubmitting]);

  return (
    <form onSubmit={handleSubmit}>
      <Controller
        name="coupon"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            id="coupon-input"
            data-test-id="coupon-input"
            label={t('form.coupon.info', 'Please enter a valid coupon.', {
              ns: 'common',
            })}
            placeholder={COUPON_INPUT_PLACEHOLDER[couponType]}
            error={error?.message}
          />
        )}
      />
      <SubmitButton
        ref={submitRef}
        loading={isSubmitting}
        hideSubmitButton={hideSubmitButton}
      >
        {couponType === CouponType.DRIVE_PREMIUM
          ? t('global.continue', 'Continue', { ns: 'common' })
          : t('global.confirm', 'Confirm', { ns: 'common' })}
      </SubmitButton>
    </form>
  );
};

export default CouponForm;
