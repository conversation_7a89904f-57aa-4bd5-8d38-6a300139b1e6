import styled from '@emotion/styled';
import { forwardRef, ReactNode } from 'react';
import { theme, Button } from 'shared-components';
import { IButton } from 'types';

import { IFormProps } from '@/types/forms';

interface ISubmitButtonProps
  extends Pick<IFormProps, 'hideSubmitButton'>,
    IButton {
  children: string | ReactNode;
  loading?: boolean;
  disabled?: boolean;
}

const ButtonStyled = styled(Button, {
  shouldForwardProp: prop => prop !== 'hideSubmitButton',
})<ISubmitButtonProps & IButton>`
  ${({ hideSubmitButton }) => hideSubmitButton && `display: none;`}
  margin-top: 24px;

  ${theme.breakpoints.up('md')} {
    margin-top: 40px;
  }
`;

const SubmitButton = forwardRef(
  (
    {
      hideSubmitButton,
      children,
      color = 'primary',
      fullWidth = false,
      loading,
      disabled,
    }: ISubmitButtonProps,
    ref,
  ) => (
    <ButtonStyled
      color={color}
      fullWidth={fullWidth}
      hideSubmitButton={hideSubmitButton}
      disabled={disabled}
      loading={loading}
      ref={ref}
      type="submit"
      size="medium"
      variant="label-md-caps"
    >
      {children}
    </ButtonStyled>
  ),
);

SubmitButton.displayName = 'SubmitButton';

export default SubmitButton;
