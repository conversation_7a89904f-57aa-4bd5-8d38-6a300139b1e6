import { useTranslation } from 'next-i18next';
import { useCallback, useEffect, SyntheticEvent } from 'react';
import { Controller } from 'react-hook-form';
import { Input } from 'shared-components';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import useVmNameForm from '@/hooks/form/useVmNameForm';
import { IFormProps } from '@/types/forms';

interface IVmNameProps extends IFormProps {
  isB2b: boolean;
  vmName: string;
}

const VmNameForm = ({
  onSubmitting = () => null,
  onSuccess = () => null,
  isB2b = false,
  vmName = '',
}: IVmNameProps) => {
  const { t } = useTranslation('common');
  const { onSubmit, control, isSubmitting } = useVmNameForm(onSuccess, {
    name: vmName,
  });

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  useEffect(() => {
    onSubmitting(isSubmitting);
  }, [isSubmitting, onSubmitting]);

  return (
    <form onSubmit={handleSubmit}>
      <Controller
        name="name"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            id="renameVm-input"
            data-test-id="renameVm-input"
            label={t('form.vmName.label', 'Name')}
            error={error?.message}
          />
        )}
      />
      <SubmitButton
        data-test-id="renameVm-button"
        color={isB2b ? 'black' : 'primary'}
        loading={isSubmitting}
        fullWidth
      >
        {t('global.confirm')}
      </SubmitButton>
    </form>
  );
};

export default VmNameForm;
