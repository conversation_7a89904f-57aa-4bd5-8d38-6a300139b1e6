import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useCallback, SyntheticEvent } from 'react';
import { Controller } from 'react-hook-form';
import { Textarea } from 'shared-components';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import useCancelDriveForm from '@/hooks/form/useCancelDriveForm';
import type { IFormProps } from '@/types/forms';

const Form = styled.form`
  margin-top: 24px;
`;

const CancelDriveForm = ({
  onSuccess = () => null,
  onError = () => null,
  hideSubmitButton = false,
  submitRef,
}: IFormProps) => {
  const { t } = useTranslation();
  const { onSubmit, control, isSubmitting } = useCancelDriveForm(
    onSuccess,
    onError,
  );

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  return (
    <Form id="cancel_drive_form" onSubmit={handleSubmit}>
      <Controller
        name="reason"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <Textarea
            {...field}
            id="coupon-input"
            data-test-id="coupon-input"
            rows={7}
            label={t(
              'subscription.cancelDrive.reason.placeholder',
              'Please, describe why you want to cancel your Drive subscription',
            )}
            error={error?.message}
            hasResize={false}
          />
        )}
      />
      <SubmitButton
        ref={submitRef}
        loading={isSubmitting}
        hideSubmitButton={hideSubmitButton}
      >
        {t('global.continue', 'Continue', { ns: 'common' })}
      </SubmitButton>
    </Form>
  );
};

export default CancelDriveForm;
