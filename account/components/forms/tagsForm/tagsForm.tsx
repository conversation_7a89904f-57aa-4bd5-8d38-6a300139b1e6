import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { theme, Button, LocalLoader } from 'shared-components';
import { IButton, ITag } from 'types';

import AddTagsInput from '@/components/forms/tagsForm/addTagsInput';
import { useCurrentMember } from '@/hooks/member/useMember';

const ButtonStyled = styled(Button)<IButton>`
  margin-top: 24px;

  ${theme.breakpoints.up('md')} {
    margin-top: 40px;
  }
`;
interface ITagsProps {
  isB2b: boolean;
  defaultTags: ITag[];
  onUpdateTags: (tags: ITag[]) => void;
  isSubmittingTags: boolean;
}

const TagsForm = ({
  isB2b = false,
  defaultTags,
  onUpdateTags,
  isSubmittingTags,
}: ITagsProps) => {
  const { t } = useTranslation('common');
  const currentMemberQuery = useCurrentMember();

  const [usedTags, setUsedTags] = useState(defaultTags);

  if (currentMemberQuery.isLoading) {
    return <LocalLoader />;
  }

  const availableTagsList = currentMemberQuery.data?.available_tags as ITag[];

  return (
    <>
      <AddTagsInput
        availableTagsList={availableTagsList}
        usedTags={usedTags}
        setUsedTags={setUsedTags}
      />
      <ButtonStyled
        color={isB2b ? 'black' : 'primary'}
        onClick={() => onUpdateTags(usedTags)}
        loading={isSubmittingTags}
        fullWidth
      >
        {t('global.confirm')}
      </ButtonStyled>
    </>
  );
};

export default TagsForm;
