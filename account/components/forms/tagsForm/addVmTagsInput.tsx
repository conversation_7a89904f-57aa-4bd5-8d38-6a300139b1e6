import styled from '@emotion/styled';
import { Popper, PopperProps } from '@mui/material';
import Autocomplete, {
  AutocompleteRenderGetTagProps,
  AutocompleteRenderInputParams,
} from '@mui/material/Autocomplete';
import { useTranslation } from 'next-i18next';
import { useCallback, SyntheticEvent, HTMLAttributes } from 'react';
import { Button, Chip, Icon, theme, Typography } from 'shared-components';
import { Icons, ITag } from 'types';

import InputText from '@/components/ui/form/inputText';
import useVmTagsForm from '@/hooks/form/useVmTagsForm';
import { IVmNameForm } from '@/types/api';

const CustomPopper = styled(Popper)`
  display: none;
  &:has(ul) {
    display: block;
  }
  background: ${theme.palette.white.main};
  border: 2px solid ${theme.palette.primary.main};
  overflow: hidden;
  &[data-popper-placement='bottom-start'] {
    border-top: 0;
    border-bottom: 2px solid ${theme.palette.primary.main};
    border-radius: 0 0 8px 8px;
    top: -8px !important;
  }

  &[data-popper-placement='top-start'] {
    border-bottom: 0;
    border-radius: 8px 8px 0 0;
    bottom: -8px !important;
  }
`;

const CustomPaper = styled.div`
  ul {
    padding: 0;
    .MuiAutocomplete-option {
      padding: 12px 16px;
    }
    .Mui-focused {
      background: ${theme.palette.primary.main10};
    }
  }
`;

const InputTextStyled = styled(InputText)`
  .MuiOutlinedInput-root {
    padding: 16px;
    column-gap: 12px;
    row-gap: 8px;
    .MuiOutlinedInput-input {
      padding: 0;
    }
  }

  // background color if some tags are selected
  :has(.MuiInputBase-adornedStart) fieldset {
    background: ${theme.palette.secondary.main10};
  }
`;

const IconButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ButtonStyled = styled(Button)`
  margin-top: 8px;
`;

interface IAddVmTagsInputProps {
  availableTags: ITag[];
  appliedTags: ITag[];
  onSaveConfirm: (newTagNameValue: string) => Promise<void>;
  onDeleteSingleTag: (tagToRemove: ITag) => Promise<void>;
  onDeleteAllTags: () => Promise<void>;
  isSubmittingTags: boolean;
}
const AddVmTagsInput = ({
  availableTags,
  appliedTags,
  onSaveConfirm,
  onDeleteSingleTag,
  onDeleteAllTags,
  isSubmittingTags,
}: IAddVmTagsInputProps) => {
  const { t } = useTranslation('common');

  const availableAndUnappliedTags = availableTags.filter(
    availableTag =>
      !appliedTags.some(appliedTag => appliedTag.id === availableTag.id),
  );

  const onAddNewTagSuccess = (formValues: IVmNameForm) =>
    onSaveConfirm(formValues.tag as string);

  const {
    onSubmit,
    control,
    reset: resetForm,
    isSubmitting,
    watch,
    setValue,
  } = useVmTagsForm(onAddNewTagSuccess);

  const isAnySubmitting = isSubmitting || isSubmittingTags;
  const inputValue = watch('tag', '');
  const isInputValueAlreadyAvailable =
    inputValue?.length === 0 ||
    !!availableTags.find(tag => tag.name === inputValue);

  const removeUsedTagName = (tagNameToRemove: string) => {
    const tagToRemove = appliedTags.find(
      appliedTag => appliedTag.name === tagNameToRemove,
    );

    if (tagToRemove) {
      onDeleteSingleTag(tagToRemove);
    }
  };

  const options = availableAndUnappliedTags
    .sort((a, b) => -b.name.localeCompare(a.name))
    .map(key => key.name);

  // Event triggered when the user selects an option, hits ENTER, or clicks on CLEARALL button.
  const onSelectOrEnter = (event: SyntheticEvent, value: string[]) => {
    // Because we set value prop of Autocomplete as string[], the last element of the array is the newValue input by the user.
    const newValue = value[value.length - 1];

    const isAlreadyCreatedTag = !!availableTags.find(
      tag => tag.name === newValue,
    );

    if (isAlreadyCreatedTag && newValue) {
      // tag was already checked and created, we can just save it to VM.
      onSaveConfirm(newValue);
    } else if (value.length === 0) {
      // the user clicked the clear all button, which means that value is now an empty array.
      onDeleteAllTags();
      setValue('tag', '');
    } else {
      // this is a new entry and must be checked before saved to backend.
      handleSubmit(event as SyntheticEvent);
    }
    resetForm();
  };

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  const handleCreateButtonClick = async () => {
    await onSubmit();
    setValue('tag', '');
  };

  const renderInput = (params: AutocompleteRenderInputParams) => (
    <InputTextStyled
      {...params}
      name="tag"
      control={control}
      placeholder={t('form.tags.label', 'Add a tag')}
      InputProps={{
        ...params.InputProps,
      }}
    />
  );

  const renderOption = (
    props: HTMLAttributes<HTMLLIElement>,
    option: string,
  ) => (
    <li {...props}>
      <Typography variant="body-md">{option}</Typography>
    </li>
  );

  const listboxProps = { style: { maxHeight: 150 } };

  const popperComponent = (props: PopperProps) => (
    <CustomPopper {...props} placeholder={null} placement="bottom-start" />
  );

  const renderTags = (
    value: string[],
    getTagProps: AutocompleteRenderGetTagProps,
  ) => (
    <>
      {value.map((tagName, index) => (
        <Chip
          variant="body-sm-regular"
          size="small"
          color={theme.palette.primary.main}
          backgroundColor={theme.palette.primary.main10}
          rightAddon={
            <IconButton
              type="button"
              onClick={() => removeUsedTagName(tagName)}
            >
              <Icon
                name={Icons.CROSS_CIRCLE_FILLED}
                width={20}
                color={theme.palette.primary.main}
              />
            </IconButton>
          }
          {...getTagProps({ index })}
          className=""
        >
          {tagName}
        </Chip>
      ))}
    </>
  );

  return (
    <form onSubmit={handleSubmit}>
      <Autocomplete
        id="tag"
        inputValue={watch('tag', '')}
        options={options}
        value={appliedTags.map(tag => tag.name)}
        onChange={onSelectOrEnter}
        disableClearable={!appliedTags.length}
        fullWidth
        freeSolo
        multiple
        filterSelectedOptions
        popupIcon={false}
        renderInput={renderInput}
        renderOption={renderOption}
        renderTags={renderTags}
        PopperComponent={popperComponent}
        PaperComponent={CustomPaper}
        ListboxProps={listboxProps}
      />
      <ButtonStyled
        size="medium"
        disabled={isInputValueAlreadyAvailable}
        color="black"
        type="button"
        onClick={handleCreateButtonClick}
        loading={isAnySubmitting}
      >
        {t('form.tags.addButtonLabel', 'Create')}
      </ButtonStyled>
    </form>
  );
};

export default AddVmTagsInput;
