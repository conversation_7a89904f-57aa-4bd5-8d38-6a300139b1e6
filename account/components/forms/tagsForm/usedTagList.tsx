import styled from '@emotion/styled';
import { Chip, Icon, theme } from 'shared-components';
import { Icons, ITag } from 'types';

const Container = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
`;

interface IUsedTagListProps {
  availableTagsList: ITag[];
  unusedAvailableTags: ITag[];
  setUnusedAvailableTags: (value: ITag[]) => void;
  setUsedTags: (value: ITag[]) => void;
  usedTags: ITag[];
}

const UsedTagList = ({
  availableTagsList,
  unusedAvailableTags,
  setUnusedAvailableTags,
  setUsedTags,
  usedTags,
}: IUsedTagListProps) => {
  // Remove tag from used tags list, and add it to the available tags if it exist in availableTagsList array
  const removeUsedTag = (tagToRemove: ITag) => {
    setUsedTags(usedTags.filter(usedTag => usedTag.id !== tagToRemove.id));

    const removedTag = availableTagsList.find(
      availableTag => availableTag.id === tagToRemove.id,
    );
    if (removedTag) {
      setUnusedAvailableTags(unusedAvailableTags.concat(removedTag));
    }
  };

  const chipRightAddon = (
    <Icon
      name={Icons.CROSS_CIRCLE_FILLED}
      width={20}
      color={theme.palette.primary.main}
    />
  );

  return (
    <Container>
      {usedTags.map((usedTag, index) => (
        <Chip
          onClick={() => removeUsedTag(usedTag)}
          key={index}
          variant="body-sm-regular"
          color={theme.palette.black.main}
          backgroundColor={theme.palette.primary.main10}
          rightAddon={chipRightAddon}
        >
          {usedTag.name}
        </Chip>
      ))}
    </Container>
  );
};

export default UsedTagList;
