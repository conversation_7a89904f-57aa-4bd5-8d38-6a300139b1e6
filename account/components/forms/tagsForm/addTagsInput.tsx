import styled from '@emotion/styled';
import { <PERSON><PERSON>, PopperProps } from '@mui/material';
import Autocomplete, {
  AutocompleteRenderInputParams,
} from '@mui/material/Autocomplete';
import { useTranslation } from 'next-i18next';
import {
  useCallback,
  useEffect,
  SyntheticEvent,
  useState,
  HTMLAttributes,
} from 'react';
import {
  Button,
  Icon,
  theme,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons, ITag } from 'types';
import { logError } from 'utils';

import UsedTagList from '@/components/forms/tagsForm/usedTagList';
import InputText from '@/components/ui/form/inputText';
import useVmTagsForm from '@/hooks/form/useVmTagsForm';
import { useCreateTag } from '@/hooks/member/useMember';
import { IVmNameForm } from '@/types/api';

const Container = styled.div`
  display: flex;
  flex-direction: row;
  align-items: stretch;
`;

const AutocompleteStyled = styled(Autocomplete)`
  flex: 1;
  align-items: center;
  legend {
    display: none;
  }
  fieldset.MuiOutlinedInput-root:not(
      .Mui-disabled
    ).MuiOutlinedInput-notchedOutline {
    background: white;
    border-right-color: transparent;
    transition: none;
  }
  & .MuiInputBase-adornedStart {
    padding-left: 16px;
    padding-right: 16px;
    & > .MuiSvgIcon-root {
      margin-right: 16px;
    }
  }
  fieldset.MuiOutlinedInput-notchedOutline {
    background: white;
    border-radius: 8px 0 0 8px;
    top: 0;
  }
  .MuiOutlinedInput-root:not(.Mui-disabled) .MuiOutlinedInput-notchedOutline {
    background: transparent;
  }
`;

const CustomPopper = styled(Popper)`
  display: none;
  &:has(ul) {
    display: block;
  }
  background: ${theme.palette.white.main};
  border: 2px solid ${theme.palette.primary.main};
  &[data-popper-placement='bottom-start'] {
    border-top: 0;
    border-radius: 0 0 8px 8px;
    top: -11px !important;
    padding-top: 11px;
  }
`;

const CustomPaper = styled.div`
  ul {
    padding: 0;
    .MuiAutocomplete-option {
      padding: 12px 16px;
    }
    .Mui-focused {
      background: ${theme.palette.primary.main10};
    }
  }
`;

const ButtonStyled = styled(Button)`
  height: 56px;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
`;

interface IAddTagsInputProps {
  availableTagsList: ITag[];
  usedTags: ITag[];
  setUsedTags: (value: ITag[]) => void;
}
const AddTagsInput = ({
  availableTagsList,
  usedTags,
  setUsedTags,
}: IAddTagsInputProps) => {
  const { t } = useTranslation('common');
  const { isSM } = useThemeMediaQueries();
  const createTag = useCreateTag();

  const [unusedAvailableTags, setUnusedAvailableTags] = useState<ITag[]>(
    availableTagsList.filter(
      availableTag => !usedTags.find(usedTag => usedTag.id === availableTag.id),
    ),
  );

  const [canAddTag, setCanAddTag] = useState(false);
  const [isSubmittingNewTag, setSubmittingNewTag] = useState(false);

  const [tagValue, setTagValue] = useState('');
  const [inputValue, setInputValue] = useState('');

  const onAddNewTagSuccess = (formValues: IVmNameForm) =>
    addNewTag(formValues.tag);

  const { onSubmit, control, isSubmitting } = useVmTagsForm(onAddNewTagSuccess);

  useEffect(() => {
    setSubmittingNewTag(isSubmitting);
  }, [isSubmitting]);

  const addTag = (newTag: ITag) => {
    setUsedTags([...usedTags, newTag]);
    setUnusedAvailableTags(
      unusedAvailableTags.filter(
        unusedAvailableTag => unusedAvailableTag.id !== newTag.id,
      ),
    );
    setTagValue('');
    setInputValue('');
    setCanAddTag(false);
  };

  const addExistingTag = (newTag: string) => {
    const tagToAdd = availableTagsList.find(
      availableTag => availableTag.name === newTag,
    ) as ITag;

    addTag(tagToAdd);
  };

  const addNewTag = async (newTag: string) => {
    try {
      const newTagsList = await createTag.mutateAsync(newTag);

      if (newTagsList && newTagsList.length) {
        addTag(newTagsList[0] as ITag);
      }
    } catch (e) {
      logError('addNewTag', e);
    }
  };

  const options = unusedAvailableTags
    .sort((a, b) => -b.name.localeCompare(a.name))
    .map(key => key.name);

  // Event triggered when the user selected an option
  // TODO : find why we need to set newValue as any (TS error with onChange prop)
  const onSelect = (_event: any, newValue: any) => {
    setTagValue(newValue);
    if (!!newValue && !usedTags.find(usedTag => usedTag.name === newValue)) {
      if (
        unusedAvailableTags.find(
          unusedAvailableTag => unusedAvailableTag.name === newValue,
        )
      ) {
        addExistingTag(newValue);
      } else {
        void addNewTag(newValue);
      }
    }
  };

  // Event triggered as the user types
  const onInputChange = (event: any, newInputValue: string) => {
    setInputValue(newInputValue);
    // if value not in used tags && available tags, can add it
    if (
      !usedTags.find(usedTag => usedTag.name === newInputValue) &&
      !unusedAvailableTags.find(
        availableTag => availableTag.name === newInputValue,
      )
    ) {
      setCanAddTag(true);
    } else {
      setCanAddTag(false);
    }
  };

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  const renderInput = (params: AutocompleteRenderInputParams) => (
    <InputText
      {...params}
      name="tag"
      control={control}
      placeholder={t('form.tags.label', 'Add a tag')}
      InputProps={{
        ...params.InputProps,
        startAdornment: (
          <Icon
            name={Icons.MAGNIFIER}
            color={theme.palette.black.main75}
            width={24}
            height={24}
          />
        ),
      }}
    />
  );

  const renderOption = (
    props: HTMLAttributes<HTMLLIElement>,
    option: unknown,
  ) => (
    <li {...props}>
      <Typography variant="body-md">{option as string}</Typography>
    </li>
  );

  const listboxProps = { style: { maxHeight: 150 } };

  const popperComponent = (props: PopperProps) => (
    <CustomPopper {...props} placeholder={null} placement="bottom-start" />
  );

  return (
    <>
      <form onSubmit={handleSubmit}>
        <Container>
          <AutocompleteStyled
            id="tag"
            autoHighlight
            freeSolo
            value={tagValue}
            inputValue={inputValue}
            options={options}
            ListboxProps={listboxProps}
            disableClearable
            popupIcon={false}
            onChange={onSelect}
            onInputChange={onInputChange}
            PopperComponent={popperComponent}
            PaperComponent={CustomPaper}
            renderInput={renderInput}
            renderOption={renderOption}
          />
          <ButtonStyled
            size={isSM ? 'small' : 'medium'}
            disabled={!canAddTag}
            color="black"
            type="submit"
            loading={isSubmittingNewTag}
          >
            {t('form.tags.addButtonLabel', 'Create')}
          </ButtonStyled>
        </Container>
      </form>
      <UsedTagList
        availableTagsList={availableTagsList}
        unusedAvailableTags={unusedAvailableTags}
        setUnusedAvailableTags={setUnusedAvailableTags}
        setUsedTags={setUsedTags}
        usedTags={usedTags}
      />
    </>
  );
};

export default AddTagsInput;
