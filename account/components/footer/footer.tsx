import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { theme } from 'shared-components';

import Link from '@/components/ui/link';
import { useUrl } from '@/hooks/useUrl';
import {
  COOKIES_URL,
  LANDING_URL,
  LEGAL_URL,
  PRIVACY_POLICY_URL,
} from '@/utils/constants';
const Container = styled.footer`
  width: 100%;
  padding: 24px;
  background: ${theme.palette.primary.main10};
  ${theme.breakpoints.up('lg')} {
    height: ${theme.footer.height.lg};
    padding: 24px 40px;
  }
`;

const List = styled.ul`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  column-gap: 8px;
  row-gap: 16px;
  ${theme.breakpoints.up('md')} {
    column-gap: 80px;
  }
`;

const ListItem = styled.li`
  display: flex;
  align-items: center;
`;

const FooterLink = styled(Link)`
  &:hover {
    color: ${theme.palette.primary.main};
  }
`;

const Footer = ({ isB2b }: { isB2b: boolean }) => {
  const { t } = useTranslation();
  const { getCguUrl } = useUrl();

  const footerListItems = useMemo(() => {
    return [
      {
        href: LANDING_URL,
        label: t('footer.companyName', '© Shadow'),
      },
      {
        href: getCguUrl(isB2b),
        label: t('footer.cgu', 'Terms of Use'),
      },
      {
        href: LEGAL_URL,
        label: t('footer.legal', 'Legal'),
      },
      {
        href: PRIVACY_POLICY_URL,
        label: t('footer.privacy', 'Privacy'),
      },
      {
        href: COOKIES_URL,
        label: t('footer.cookies', 'Cookies'),
      },
    ];
  }, [t]);

  return (
    <Container>
      <nav>
        <List>
          {footerListItems.map((item, key) => (
            <ListItem key={key}>
              <FooterLink
                component="a"
                href={item.href}
                target="_blank"
                variant="body-xs"
              >
                {item.label}
              </FooterLink>
            </ListItem>
          ))}
        </List>
      </nav>
    </Container>
  );
};

export default Footer;
