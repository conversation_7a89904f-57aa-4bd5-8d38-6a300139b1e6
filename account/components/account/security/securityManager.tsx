import { useTranslation } from 'next-i18next';

import UserSecurityHub from '../user/userSecurityHub/userSecurityHub';

import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import { isKratosFederationProvider } from '@/utils/persistedUser';

const SecurityManager = () => {
  const isKratosFedered = isKratosFederationProvider();
  const { t } = useTranslation();

  return (
    <>
      {isKratosFedered && (
        <Paper>
          <PaperTitle>
            {t('account.user.security.title', 'Security')}
          </PaperTitle>
          <UserSecurityHub />
        </Paper>
      )}
    </>
  );
};

export default SecurityManager;
