import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { SyntheticEvent, useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { Alert, InputPhone, Input, theme } from 'shared-components';
import { NotificationState } from 'types';

import DownloadDataModal from '@/components/account/user/downloadDataModal/downloadDataModal';
import UserActions from '@/components/account/user/userForm/userActions';
import FormRow from '@/components/ui/form/formRow';
import InputSelect from '@/components/ui/form/inputSelect';
import LabelSelect from '@/components/ui/form/labelSelect';
import useUserForm from '@/hooks/form/useUserForm';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import {
  COUNTRY_CODE,
  LANGUAGES_PER_MARKET,
  LANGUAGES_WITH_FLAG_OPTIONS,
} from '@/utils/constants';

const formData = {
  first_name: '',
  last_name: '',
  birthdate: '',
  phone: '',
  language: 'en',
};

const SelectContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
`;

const Select = styled(InputSelect)`
  margin-bottom: 16px;

  ${theme.breakpoints.up('md')} {
    width: calc(50% - 12px); // width 50% - gap / 2
  }
`;

const UserForm = () => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();

  const { market } = useConfig();
  const { notifyError } = useNotifications();

  const [showSuccessNotification, setShowSuccessNotification] =
    useState<boolean>(false);
  const [isDownloadUserDataModalOpen, setIsDownloadUserDataModalOpen] =
    useState<boolean>(false);

  // We need to get the correct languages list for the user country
  const languageSelectInputItems = LANGUAGES_WITH_FLAG_OPTIONS.filter(
    languageWithFlagOption =>
      !!LANGUAGES_PER_MARKET[market].find(
        languagePerMarket => languagePerMarket === languageWithFlagOption.value,
      ),
  );

  const onSuccess = () => {
    setShowSuccessNotification(true);
  };

  const onError = (error: string) => {
    notifyError(
      `${t(
        'account.user.update.error.message',
        `An error occurred when updating your personal information:`,
      )} ${error}`,
    );
  };

  const { onSubmit, control, setValue, reset, isDirty, isSubmitting } =
    useUserForm(onSuccess, onError, formData);

  const handlePhoneOnChange = (value: string) => {
    setValue('phone', value);
  };

  const handleFormSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setShowSuccessNotification(false);
    onSubmit();
  };

  useEffect(() => {
    if (currentMemberQuery.isSuccess) {
      reset(currentMemberQuery.data?.user);
    }
  }, [reset, currentMemberQuery.isSuccess, currentMemberQuery.data?.user]);

  return (
    <>
      <form onSubmit={handleFormSubmit}>
        <FormRow>
          <Controller
            name="first_name"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="first_name-input"
                data-test-id="first_name-input"
                label={t('form.firstName.label', 'First name', {
                  ns: 'common',
                })}
                error={error?.message}
              />
            )}
          />
          <Controller
            name="last_name"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="last_name-input"
                data-test-id="last_name-input"
                label={t('form.lastName.label', 'Last name', { ns: 'common' })}
                error={error?.message}
              />
            )}
          />
        </FormRow>
        <FormRow>
          <InputPhone
            control={control}
            data-test-id="phone-input"
            name="phone"
            defaultCountry={COUNTRY_CODE[market].toLowerCase()}
            label={t('form.phone.label', 'Phone number', {
              ns: 'common',
            })}
            onChange={handlePhoneOnChange}
          />

          <Controller
            name="birthdate"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="birthdate-input"
                data-test-id="birthdate-input"
                type="date"
                label={t('form.birthdate.label', 'Birthdate', {
                  ns: 'common',
                })}
                error={error?.message}
              />
            )}
          />
        </FormRow>
        <SelectContainer>
          <LabelSelect
            id="language"
            text={t('form.language.label', 'Language', {
              ns: 'common',
            })}
          />
          <Select
            required
            control={control}
            name="language"
            label="language"
            items={languageSelectInputItems}
          />
        </SelectContainer>
        <Collapse in={showSuccessNotification}>
          <Alert
            type={NotificationState.SUCCESS}
            title={t('account.user.update.success.title', 'User infos updated')}
          >
            {t(
              'account.user.update.success.message',
              'Your personal information has been updated.',
            )}
          </Alert>
        </Collapse>
        <UserActions
          openDownloadDataModal={() => setIsDownloadUserDataModalOpen(true)}
          formState={{
            isDirty,
            isSubmitting,
          }}
        />
      </form>
      <DownloadDataModal
        isOpen={isDownloadUserDataModalOpen}
        onClose={() => setIsDownloadUserDataModalOpen(false)}
      />
    </>
  );
};

export default UserForm;
