import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import {
  Button,
  ClickTracker,
  Icon,
  theme,
  useThemeMediaQueries,
} from 'shared-components';
import { IMemberDetails, Icons } from 'types';

import { useCurrentMember } from '@/hooks/member/useMember';

const WrapperLinks = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 24px;
  text-align: center;
  gap: 24px;
  ${theme.breakpoints.up('lg')} {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    column-gap: 24px;
  }
`;

interface IUserActionsProps {
  openDownloadDataModal: () => void;
  formState: {
    isDirty: boolean;
    isSubmitting: boolean;
  };
}

const UserActions = ({
  openDownloadDataModal,
  formState,
}: IUserActionsProps) => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();

  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  return (
    <>
      <WrapperLinks>
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_download_data',
              event_label: 'download_my_data',
            },
          }}
        >
          <Button
            data-test-id="download-data-button"
            id="download-data-button"
            children={t('account.user.downloadData.label', 'Download my data')}
            startIcon={
              <Icon name={Icons.CLOUD_DOWNLOAD} width={24} height={24} />
            }
            color="secondary"
            size="medium"
            onClick={openDownloadDataModal}
            type="button"
          />
        </ClickTracker>

        <Button
          color={isB2b ? 'black' : 'primary'}
          disabled={!formState.isDirty || formState.isSubmitting}
          loading={formState.isSubmitting}
          type="submit"
          fullWidth={isSM}
          data-test-id="submit-user_form-button"
        >
          {t('account.user.form.submit.label', 'Save account information')}
        </Button>
      </WrapperLinks>
    </>
  );
};

export default UserActions;
