import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import { Button, ClickTracker, Icon, Typography } from 'shared-components';
import { Icons } from 'types';

import { AUTH_SOCIALS_LOGIN } from '@/utils/constants';

const PaperContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

const SocialButton = styled(Button)`
  align-self: flex-end;
`;

const UserSocialLogin = () => {
  const { t } = useTranslation();
  return (
    <PaperContainer>
      <Typography
        children={
          <Trans i18nKey={'account.user.socials.text'}>
            Lorem ipsum dolor sit amet consectetur. Et non libero porta
            suspendisse. Risus vitae adipiscing vitae non feugiat vel sem
            convallis.
          </Trans>
        }
        variant="body-md"
      />
      <ClickTracker
        eventPayload={{
          action: 'click',
          parameters: {
            event_category: 'my_account_account',
            event_label: 'manage_my_login_settings',
          },
        }}
      >
        <SocialButton
          data-test-id="manage-social-login-button"
          id="manage-social-login-button"
          size="medium"
          children={t(
            'account.user.socials.buttons.text',
            'Manage my social logins',
          )}
          color="secondary"
          startIcon={<Icon name={Icons.LOCK} width={24} height={24} />}
          href={AUTH_SOCIALS_LOGIN}
        />
      </ClickTracker>
    </PaperContainer>
  );
};

export default UserSocialLogin;
