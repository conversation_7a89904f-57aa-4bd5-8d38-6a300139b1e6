import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import {
  Button,
  ClickTracker,
  ClickTrackerLink,
  Icon,
  Typography,
} from 'shared-components';
import { Icons } from 'types';

import { AUTH_SECURITY_HUB, AUTH_SETTINGS } from '@/utils/constants';

const PaperContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 24px;
`;

const UserSecurityHub = () => {
  const { t } = useTranslation();
  return (
    <PaperContainer>
      <Typography variant="body-md">
        <Trans i18nKey={'account.user.security.text'}>
          In your Security Hub, you’ll able to secure your account effectively
          by accessing your sessions history or strengthen your Shadow by adding
          two factor authentication and generating backup codes.{' '}
          <ClickTrackerLink
            eventPayload={{
              action: 'click',
              parameters: {
                event_category: 'my_account_account',
                event_label: 'more_info_2FA',
              },
            }}
            data-test-id="security-learn-more-link"
            id="security-learn-more-link"
            href="https://support.shadow.tech/en_US/articles/authentication/how-does-twofactor-authentication-2fa-work-on-shadow/64e3101304a01d0067a5ef1f"
            target="_blank"
            variant="body-md-regular-link"
          >
            Learn more about security at Shadow.
          </ClickTrackerLink>
        </Trans>
      </Typography>
      <ButtonContainer>
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_account',
              event_label: 'change_password',
            },
          }}
        >
          <Button
            data-test-id="edit-password-button"
            id="edit-password-button"
            children={t(
              'account.user.security.buttons.password',
              'Edit my password',
            )}
            color="secondary"
            startIcon={<Icon name={Icons.EDIT} width={24} height={24} />}
            href={AUTH_SETTINGS}
            size="medium"
          />
        </ClickTracker>
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_account',
              event_label: 'manage_my_security_settings',
            },
          }}
        >
          <Button
            data-test-id="manage-security-settings-button"
            id="manage-security-settings-button"
            children={t(
              'account.user.security.buttons.hub',
              'Access my Security Hub',
            )}
            color="secondary"
            startIcon={<Icon name={Icons.LOCK} width={24} height={24} />}
            href={AUTH_SECURITY_HUB}
            size="medium"
          />
        </ClickTracker>
      </ButtonContainer>
    </PaperContainer>
  );
};

export default UserSecurityHub;
