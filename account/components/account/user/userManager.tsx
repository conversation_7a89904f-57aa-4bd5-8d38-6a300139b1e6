import { useTranslation } from 'next-i18next';

import UserEmail from './userEmail/userEmail';
import UserSocialLogin from './userSocialLogin/userSocialLogin';

import UserForm from '@/components/account/user/userForm/userForm';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import { isKratosFederationProvider } from '@/utils/persistedUser';

const UserManager = () => {
  const isKratosFedered = isKratosFederationProvider();
  const { t } = useTranslation();

  return (
    <>
      <Paper>
        <PaperTitle>
          {t('account.user.title', 'Account information')}
        </PaperTitle>
        <UserEmail />
        <UserForm />
      </Paper>
      {isKratosFedered && (
        <Paper>
          <PaperTitle>
            {t('account.user.socials.title', 'Socials Login')}
          </PaperTitle>
          <UserSocialLogin />
        </Paper>
      )}
    </>
  );
};

export default UserManager;
