import { useTranslation } from 'next-i18next';
import { useRef, useState } from 'react';
import { Modal, ModalButton, ModalLink } from 'shared-components';
import type { IMemberDetails, IModalProps } from 'types';
import { trackEvent, logError } from 'utils';

import WordConfirmationForm from '@/components/forms/wordConfirmationForm/wordConfirmationForm';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import { usePersonalData } from '@/hooks/user/useUser';
import { FORM_VALIDATION_WORD_COMPARE_DOWNLOAD_DATA } from '@/utils/constants';
import { downloadObjectAsJson } from '@/utils/download';

const DownloadDataModal = ({ isOpen, onClose }: IModalProps) => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const { t } = useTranslation();
  const { refetch: refetchPersonalData } = usePersonalData();
  const { language } = useConfig();
  const [isSubmittingWord, setIsSubmittingWord] = useState<boolean>(false);
  const [isDownloadingData, setIsDownloadingData] = useState<boolean>(false);
  const submitDownloadDataRef = useRef<HTMLButtonElement | null>(null);

  const downloadPersonalData = async () => {
    setIsDownloadingData(true);

    try {
      const { data } = await refetchPersonalData();

      downloadObjectAsJson(data ?? {}, 'shadow_customer_data');
    } catch (e) {
      logError('downloadPersonalData', e);
    } finally {
      setIsDownloadingData(false);
      trackEvent('click', {
        event_category: 'my_account_download_data',
        event_label: 'download_my_data_confirmed',
      });
      onClose();
    }
  };

  const handleDialogClose = (reason: string) => {
    if (reason === 'backdropClick' && (isSubmittingWord || isDownloadingData)) {
      return;
    }

    onClose();
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleDialogClose}
      disableEscapeKeyDown={isSubmittingWord || isDownloadingData}
      title={t('downloadData.modal.title', 'Download personal information')}
      // subtitle={t(
      //   'downloadData.modal.body',
      //   'Confirm your password to start downloading',
      // )}
    >
      <WordConfirmationForm
        onSubmitting={isSubmitting => setIsSubmittingWord(isSubmitting)}
        onSuccess={downloadPersonalData}
        hideSubmitButton
        submitRef={submitDownloadDataRef}
        wordToCompare={FORM_VALIDATION_WORD_COMPARE_DOWNLOAD_DATA[language]}
      />
      <ModalButton
        loading={isSubmittingWord || isDownloadingData}
        onClick={() => submitDownloadDataRef?.current?.click()}
        color={isB2b ? 'black' : 'primary'}
      >
        {t('global.confirm', 'Confirm', { ns: 'common' })}
      </ModalButton>
      {(!isSubmittingWord || !isDownloadingData) && (
        <ModalLink onClick={onClose}>
          {t('global.cancel', 'Cancel', { ns: 'common' })}
        </ModalLink>
      )}
    </Modal>
  );
};

export default DownloadDataModal;
