import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import { ClickTracker, Icon, Input, theme } from 'shared-components';
import { Icons, UserRole } from 'types';

import EditLink from '@/components/ui/editLink';
import Link from '@/components/ui/link';
import { useCurrentMember } from '@/hooks/member/useMember';
import {
  AUTH_RESEND_VERIFICATION_EMAIL,
  AUTH_SETTINGS,
} from '@/utils/constants';
import { isKratosFederationProvider } from '@/utils/persistedUser';

const Container = styled.div`
  margin-bottom: 16px;
  ${theme.breakpoints.up('lg')} {
    margin-bottom: 24px;
  }
`;

const EditLinkStyled = styled(EditLink)`
  margin-right: 16px;
`;

const UserEmail = () => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const { user } = useAuth();

  const [email, setEmail] = useState<string>('');
  const [isMember, setIsMember] = useState(false);

  const displayResendVerificationEmailLink =
    !user?.profile.email_verified && isKratosFederationProvider();

  const isNotMemberAndIsKratosFedered =
    !isMember && isKratosFederationProvider();

  useEffect(() => {
    if (currentMemberQuery.isSuccess) {
      setEmail(currentMemberQuery.data?.user?.email ?? '');
      setIsMember(currentMemberQuery.data?.role === UserRole.MEMBER);
    }
  }, [
    currentMemberQuery.data?.role,
    currentMemberQuery.data?.user?.email,
    currentMemberQuery.isSuccess,
  ]);

  return (
    <Container>
      <Input
        id="email-input"
        data-test-id="email-input"
        value={email}
        isDisabled
        leftAddon={
          <Icon
            name={Icons.EMAIL}
            color={theme.palette.black.main75}
            width={24}
            height={24}
          />
        }
        rightAddon={
          isNotMemberAndIsKratosFedered && (
            <EditLinkStyled
              href={AUTH_SETTINGS}
              target="_blank"
              label={t('account.user.editEmail.label', 'Edit my email')}
            />
          )
        }
      />

      {displayResendVerificationEmailLink && (
        <ClickTracker
          eventPayload={{
            action: 'click',
            parameters: {
              event_category: 'my_account_account',
              event_label: 'send_email_confirmation',
            },
          }}
        >
          <Link
            data-test-id="resend-verification-email"
            id="resend-verification-email"
            href={AUTH_RESEND_VERIFICATION_EMAIL}
            target="_blank"
            variant="label-sm-regular-underline"
            component="a"
          >
            {t(
              'account.user.resendVerificationEmail.label',
              'Resend the verification email',
            )}
          </Link>
        </ClickTracker>
      )}
    </Container>
  );
};

export default UserEmail;
