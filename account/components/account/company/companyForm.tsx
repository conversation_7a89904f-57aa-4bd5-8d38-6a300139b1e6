import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { SyntheticEvent, useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { Alert, Button, Input, useThemeMediaQueries } from 'shared-components';
import { NotificationState } from 'types';
import { HIDDEN_VAT_NUMBER_MARKETS } from 'utils';

import FormRow from '@/components/ui/form/formRow';
import useCompanyForm from '@/hooks/form/useCompanyForm';
import { useConfig } from '@/hooks/store/useConfig';
import { useBillingDetails } from '@/hooks/user/useUser';

const ButtonWrapper = styled.div`
  display: flex;
  width: 100%;
  justify-content: end;
`;

const formData = {
  company: '',
  vat_number: '',
};

const CompanyForm = () => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();

  const { notifyError } = useNotifications();
  const { market } = useConfig();
  const [showSuccessNotification, setShowSuccessNotification] =
    useState<boolean>(false);

  const billingDetailsQuery = useBillingDetails();

  const onSuccess = () => {
    setShowSuccessNotification(true);
  };

  const onError = (error: string) => {
    notifyError(
      `${t(
        'account.company.update.error.message',
        `An error occurred when updating your company information:`,
      )} ${error}`,
    );
  };

  const { onSubmit, control, reset, isDirty, isSubmitting } = useCompanyForm(
    onSuccess,
    onError,
    formData,
  );

  const shouldDisplayVatField = !HIDDEN_VAT_NUMBER_MARKETS.includes(market);

  const handleFormSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setShowSuccessNotification(false);
    onSubmit();
  };

  useEffect(() => {
    if (billingDetailsQuery.isSuccess) {
      reset(billingDetailsQuery.data);
    }
  }, [reset, billingDetailsQuery.isSuccess, billingDetailsQuery.data]);

  return (
    <form onSubmit={handleFormSubmit}>
      <FormRow>
        <Controller
          name="company"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="company-input"
              data-test-id="company-input"
              label={t('form.companyName.label', 'Company name', {
                ns: 'common',
              })}
              error={error?.message}
            />
          )}
        />
        {shouldDisplayVatField && (
          <Controller
            name="vat_number"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                id="vat_number-input"
                data-test-id="company-input"
                label={t('form.vatNumber.label', 'VAT number', {
                  ns: 'common',
                })}
                error={error?.message}
              />
            )}
          />
        )}
      </FormRow>
      <Collapse in={showSuccessNotification}>
        <Alert
          type={NotificationState.SUCCESS}
          title={t(
            'account.company.update.success.title',
            'Company infos updated',
          )}
        >
          {t(
            'account.company.update.success.message',
            'Your company information has been updated.',
          )}
        </Alert>
      </Collapse>
      <ButtonWrapper>
        <Button
          color="black"
          type="submit"
          fullWidth={isSM}
          disabled={!isDirty || isSubmitting}
          loading={isSubmitting}
        >
          {t('company.button', 'Save company information')}
        </Button>
      </ButtonWrapper>
    </form>
  );
};

export default CompanyForm;
