import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { Icon, Link } from 'shared-components';
import { Icons } from 'types';
import { logError } from 'utils';

import ButtonPopover from '@/components/ui/buttonPopover';
import {
  useDownloadCsvInvoice,
  useInvoiceDownloadLink,
} from '@/hooks/user/useUser';

interface IInvoicesDownloadProps {
  invoiceId: string;
}

const InvoicesDownload = ({ invoiceId }: IInvoicesDownloadProps) => {
  const { t } = useTranslation();
  const downloadCsvInvoice = useDownloadCsvInvoice();
  const fetchInvoiceDownloadLink = useInvoiceDownloadLink();
  const { notifyError } = useNotifications();

  const [isInvoicesDownloadPopoverOpen, setIsInvoicesDownloadPopoverOpen] =
    useState(false);

  const downloadCSVInvoiceOnClick = async () => {
    try {
      await downloadCsvInvoice.mutateAsync(invoiceId, {
        onSuccess: csvInvoiceBlob => {
          if (!csvInvoiceBlob) {
            throw 'Empty blob received';
            return;
          }
          // Create blob link to download
          const url = window.URL.createObjectURL(new Blob([csvInvoiceBlob]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `Shadow_invoice_${invoiceId}.csv`);
          // Append to html page
          document.body.appendChild(link);
          // Force download
          link.click();
          // Clean up and remove the link
          link.parentNode?.removeChild(link);

          setIsInvoicesDownloadPopoverOpen(false);
        },
        onError: error => {
          setIsInvoicesDownloadPopoverOpen(false);
          throw error.message;
        },
      });
    } catch (error) {
      logError('Error on csv download request:', error);
      notifyError(
        t(
          'invoices.list.download.error',
          `An error occurred while downloading your invoice, please try again later`,
        ),
      );
    }
  };

  const downloadPDFInvoiceOnClick = async () => {
    try {
      await fetchInvoiceDownloadLink.mutateAsync(invoiceId, {
        onSuccess: invoiceDownloadResponse => {
          if (!invoiceDownloadResponse?.link) {
            throw `No download link for invoice ${invoiceId}`;
          }
          window.location.href = invoiceDownloadResponse?.link;
          setIsInvoicesDownloadPopoverOpen(false);
        },
        onError: error => {
          setIsInvoicesDownloadPopoverOpen(false);
          throw error.message;
        },
      });
    } catch (error) {
      logError('Error pdf link download request:', error);
      notifyError(
        t(
          'invoices.list.download.error',
          `An error occurred while downloading your invoice, please try again later`,
        ),
      );
    }
  };

  return (
    <ButtonPopover
      dataTestId="invoices-download"
      icon={<Icon name={Icons.DOWNLOAD} width={20} height={20} />}
      name={`invoiceDownload-more`}
      isOpen={isInvoicesDownloadPopoverOpen}
      setIsOpen={setIsInvoicesDownloadPopoverOpen}
    >
      <Link
        onClick={downloadPDFInvoiceOnClick}
        startIcon={<Icon name={Icons.FILE_TEXT} width={16} height={16} />}
        title={t('invoices.list.body.link.title', 'Download your invoice')}
        variant="label-sm-regular"
      >
        {t('invoices.list.body.link.pdf', 'Download PDF')}
      </Link>
      <Link
        onClick={downloadCSVInvoiceOnClick}
        startIcon={<Icon name={Icons.CSV_LOGO} width={16} height={16} />}
        title={t('invoices.list.body.link.title', 'Download your invoice')}
        variant="label-sm-regular"
      >
        {t('invoices.list.body.link.csv', 'Download CSV')}
      </Link>
    </ButtonPopover>
  );
};

export default InvoicesDownload;
