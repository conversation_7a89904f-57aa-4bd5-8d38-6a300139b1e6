import styled from '@emotion/styled';
import {
  Table,
  TableHead as MuiTableHead,
  TableBody,
  TableRow,
  TableCell as MuiTable<PERSON>ell,
  TablePagination as MuiTablePagination,
} from '@mui/material/';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import { theme, Typography } from 'shared-components';
import type { Currency } from 'types';

import InvoicesDate from '@/components/account/invoices/invoicesDate';
import InvoicesDownload from '@/components/account/invoices/invoicesDownload';
import InvoicesStatusCell from '@/components/account/invoices/invoicesStatus';
import { usePrice } from '@/hooks/usePrice';
import { IInvoice } from '@/types/api';
import { InvoicesStatus } from '@/types/invoices';
import { INVOICES_PER_PAGE } from '@/utils/constants';

interface IInvoicesListProps {
  invoices: IInvoice[];
}

const TableHead = styled(MuiTableHead)`
  .MuiTableCell-root {
    background-color: transparent;
    border: 0;
    color: ${theme.palette.primary.main};
    font-size: ${theme.typography['body-md-regular'].fontSize};

    ${theme.breakpoints.up('md')} {
      font-size: ${theme.typography['body-lg-regular'].fontSize};
    }
  }
`;

const TableCell = styled(MuiTableCell, {
  shouldForwardProp: prop => prop !== 'hideMobile',
})<{ hideMobile?: boolean }>`
  padding: 16px 24px 16px 0;
  :last-child {
    padding-right: 0;
  }
  border-color: ${theme.palette.primary.main10};
  font-size: ${theme.typography['body-xs'].fontSize};
  line-height: ${theme.typography['body-xs'].lineHeight};

  ${theme.breakpoints.up('md')} {
    font-size: ${theme.typography['body-sm'].fontSize};
  }
  ${({ hideMobile }) =>
    hideMobile &&
    `
      ${theme.breakpoints.down('md')} {
        display: none;
      }
    `}
`;

const TablePagination = styled(MuiTablePagination)<{ component: string }>`
  .MuiTablePagination-displayedRows {
    font-size: ${theme.typography['body-xs'].fontSize};
    ${theme.breakpoints.up('md')} {
      font-size: ${theme.typography['body-sm'].fontSize};
    }
  }
`;

const InvoicesList = ({ invoices }: IInvoicesListProps) => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(INVOICES_PER_PAGE);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  if (invoices.length === 0) {
    return (
      <Typography variant="label-md-regular">
        {t('invoices.noInvoice', 'No invoices')}
      </Typography>
    );
  }

  return (
    <>
      <Table stickyHeader aria-label="sticky table">
        <TableHead>
          <TableRow>
            <TableCell width="35%">
              {t('invoices.list.heading.date', 'Date')}{' '}
            </TableCell>
            <TableCell hideMobile width="28%">
              {t('invoices.list.heading.status', 'Status')}
            </TableCell>
            <TableCell width="28%">
              {t('invoices.list.heading.total', 'Amount')}
            </TableCell>
            <TableCell align="right" width="9%">
              {t('invoices.list.heading.link', ' ')}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices
            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
            .map(row => {
              return (
                <TableRow key={row.id}>
                  <TableCell>
                    <InvoicesDate
                      date={row.date}
                      status={row.status as InvoicesStatus}
                    />
                  </TableCell>
                  <TableCell hideMobile>
                    <InvoicesStatusCell status={row.status as InvoicesStatus} />
                  </TableCell>
                  <TableCell>
                    {formatPrice(
                      row.total as number,
                      row.currency_code as Currency,
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <InvoicesDownload invoiceId={row.id} />
                  </TableCell>
                </TableRow>
              );
            })}
        </TableBody>
      </Table>
      <TablePagination
        rowsPerPageOptions={[]}
        component="div"
        count={invoices.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </>
  );
};

export default InvoicesList;
