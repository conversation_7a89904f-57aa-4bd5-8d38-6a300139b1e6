import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { theme } from 'shared-components';

import Tooltip from '@/components/ui/tooltip';
import { InvoicesStatus } from '@/types/invoices';

interface IStatusProps {
  status: InvoicesStatus;
  isOnDateCell?: boolean;
}

export const INVOICE_STATUS_COLOR = {
  [InvoicesStatus.PAID]: {
    color: theme.palette.success.main,
  },
  [InvoicesStatus.NOT_PAID]: {
    color: theme.palette.error.main,
  },
  [InvoicesStatus.PENDING]: {
    color: theme.palette.warning.main,
  },
  [InvoicesStatus.PAYMENT_DUE]: {
    color: theme.palette.warning.main,
  },
  [InvoicesStatus.POSTED]: {
    color: theme.palette.warning.main,
  },
  [InvoicesStatus.VOIDED]: {
    color: theme.palette.warning.main,
  },
};

const Container = styled.div<IStatusProps>`
  display: flex;
  align-items: center;

  ::before {
    content: '•';
    margin-right: 8px;
    ${({ isOnDateCell }) => isOnDateCell && 'margin-left: 8px;'}
    color: ${({ status }) => INVOICE_STATUS_COLOR[status].color};
    font-size: 16px;
    ${({ isOnDateCell, status }) =>
      isOnDateCell && status === InvoicesStatus.NOT_PAID && 'display:none;'}
  }
`;

const Label = styled.span`
  ${theme.breakpoints.down('md')} {
    display: none;
  }
`;

const InvoicesStatusCell = ({ status, isOnDateCell }: IStatusProps) => {
  const { t } = useTranslation();

  return (
    <Container status={status} isOnDateCell={isOnDateCell}>
      <Label>{t(`invoices.list.body.status.${status}`)}</Label>
      {status !== InvoicesStatus.PAID && (
        <Tooltip
          title={t(`invoices.list.body.statusTooltip.${status}`)}
          placement="bottom-start"
          color="error"
          iconWidth={14}
        />
      )}
    </Container>
  );
};

export default InvoicesStatusCell;
