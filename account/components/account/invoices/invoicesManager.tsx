import { isEmpty } from 'lodash';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';

import InvoicesList from '@/components/account/invoices/invoicesList';
import ApiError from '@/components/ui/errors/apiError';
import PaperLoader from '@/components/ui/loader/paperLoader';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import { useInvoices } from '@/hooks/user/useUser';
import { useBillingDetails } from '@/hooks/user/useUser';
import { IInvoice } from '@/types/api';

const InvoicesManager = () => {
  const { t } = useTranslation();
  const invoicesQuery = useInvoices();
  const [invoices, setInvoices] = useState<[] | IInvoice[]>([]);
  const billingDetailsQuery = useBillingDetails();

  useEffect(() => {
    if (invoicesQuery.isSuccess) {
      setInvoices(invoicesQuery.data as IInvoice[]);
    }
  }, [invoicesQuery.data, invoicesQuery.isSuccess]);

  if (isEmpty(billingDetailsQuery.data)) {
    return <></>;
  }

  if (invoicesQuery.isLoading) {
    return <PaperLoader />;
  }

  if (invoicesQuery.isError) {
    return <ApiError />;
  }

  return (
    <Paper>
      <PaperTitle>{t('invoices.title', 'Invoices')}</PaperTitle>

      <InvoicesList invoices={invoices} />
    </Paper>
  );
};

export default InvoicesManager;
