import styled from '@emotion/styled';
import { useMemo } from 'react';
import { useThemeMediaQueries } from 'shared-components';
import { Language } from 'types';
import { formatDate, DATE_FORMAT_BY_LANGUAGE } from 'utils';

import InvoicesStatusCell from '@/components/account/invoices/invoicesStatus';
import { useCurrentMember } from '@/hooks/member/useMember';
import { InvoicesStatus } from '@/types/invoices';
import { DEFAULT_LANGUAGE } from '@/utils/constants';

interface IInvoicesDateProps {
  date: number;
  status: InvoicesStatus;
}

const Container = styled.div`
  display: flex;
  align-items: center;
`;

const InvoicesDate = ({ date, status }: IInvoicesDateProps) => {
  const { isSM } = useThemeMediaQueries();
  const currentMemberQuery = useCurrentMember();

  const formattedDate = useMemo(() => {
    const dateInMilliseconds =
      date * 1000 > new Date().getTime() ? date : date * 1000;

    const userLanguage = currentMemberQuery?.data?.user?.language
      ? (currentMemberQuery?.data?.user?.language as Language)
      : DEFAULT_LANGUAGE;

    return formatDate(
      dateInMilliseconds,
      DATE_FORMAT_BY_LANGUAGE[userLanguage],
    );
  }, [date, currentMemberQuery.data]);

  return (
    <Container>
      {formattedDate}
      {isSM && <InvoicesStatusCell status={status} isOnDateCell />}
    </Container>
  );
};

export default InvoicesDate;
