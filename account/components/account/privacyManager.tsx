import { useTranslation } from 'next-i18next';
import { Button, ClickTracker } from 'shared-components';

import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';

const PrivacyManager = () => {
  const { t } = useTranslation();

  const openPrivacyPreferences = () => {
    window.Didomi?.preferences?.show();
  };

  return (
    <Paper>
      <PaperTitle>
        {t('account.privacy.title', 'Manage your privacy preferences')}
      </PaperTitle>

      <ClickTracker
        eventPayload={{
          action: 'click',
          parameters: {
            event_category: 'my_account_account',
            event_label: 'manage_my_privacy_settings',
          },
        }}
      >
        <Button
          data-test-id="manage-privacy-settings"
          id="manage-privacy-settings"
          onClick={openPrivacyPreferences}
          color="secondary"
          size="medium"
        >
          {t('account.privacy.cta.label', 'Change my privacy preferences')}
        </Button>
      </ClickTracker>
    </Paper>
  );
};

export default PrivacyManager;
