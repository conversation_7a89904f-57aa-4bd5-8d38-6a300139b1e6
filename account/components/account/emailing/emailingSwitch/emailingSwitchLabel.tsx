import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';

import { emailTypes } from '@/types/email';

interface IEmailingSwitchLabelProps {
  emailType: emailTypes;
}

const SubTitle = styled(Typography)`
  margin-left: 16px;

  ${theme.breakpoints.down('md')} {
    display: none;
  }
`;

const EmailingSwitchLabel = ({ emailType }: IEmailingSwitchLabelProps) => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();

  return (
    <>
      <Typography variant={isSM ? 'body-sm-regular' : 'body-md-regular'}>
        {t(`emailing.${emailType}.title`)}
      </Typography>
      <SubTitle variant="body-sm">
        {t(`emailing.${emailType}.description`)}
      </SubTitle>
    </>
  );
};

export default EmailingSwitchLabel;
