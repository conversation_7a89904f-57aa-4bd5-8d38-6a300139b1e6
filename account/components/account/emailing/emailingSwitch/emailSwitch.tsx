import styled from '@emotion/styled';
import {
  FormControlLabel as MuiForm<PERSON>ontrolLabel,
  Switch,
} from '@mui/material/';
import { useEffect, useState, ChangeEvent } from 'react';
import { theme } from 'shared-components';

import EmailingSwitchLabel from '@/components/account/emailing/emailingSwitch/emailingSwitchLabel';
import type { emailTypes } from '@/types/email';

interface IEmailSwitch {
  emailType: emailTypes;
  isChecked: boolean | undefined;
  onChecked: (value: boolean) => void;
}

const FormControlLabel = styled(MuiFormControlLabel)`
  display: flex;
  margin: 0;
  padding: 16px;
  background: ${theme.palette.secondary.main10};
  border: 2px solid ${theme.palette.secondary.main10};
  border-radius: ${theme.shape.borderRadius}px;
  flex: 1;
  justify-content: space-between;

  ${theme.breakpoints.up('md')} {
    padding: 24px;
  }
`;

const EmailSwitch = ({ emailType, isChecked, onChecked }: IEmailSwitch) => {
  const [checked, setChecked] = useState(isChecked);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    setChecked(!checked);
    onChecked(event.target.checked);
  };

  useEffect(() => setChecked(isChecked), [isChecked]);

  return (
    <FormControlLabel
      id={emailType}
      control={<Switch color="primary" />}
      label={<EmailingSwitchLabel emailType={emailType} />}
      labelPlacement="start"
      checked={checked}
      onChange={e => handleChange(e as ChangeEvent<HTMLInputElement>)}
    />
  );
};

export default EmailSwitch;
