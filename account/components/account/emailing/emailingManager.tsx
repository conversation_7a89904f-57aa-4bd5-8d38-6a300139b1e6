import { useTranslation } from 'next-i18next';

import EmailingForm from '@/components/account/emailing/emailingForm';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';

const EmailingManager = () => {
  const { t } = useTranslation();

  return (
    <Paper>
      <PaperTitle>{t('emailing.title', 'Newsletter')}</PaperTitle>
      <EmailingForm />
    </Paper>
  );
};

export default EmailingManager;
