import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { logError } from 'utils';

import EmailSwitch from '@/components/account/emailing/emailingSwitch/emailSwitch';
import {
  useBillingDetails,
  useUpdateBillingDetails,
} from '@/hooks/user/useUser';
import type { emailTypes } from '@/types/email';

type IEmailPreferences = Record<string, boolean>;

const emailPreferences: emailTypes[] = ['shadow'];

const getDefaultEmailPreferences = (emailPrefs: emailTypes[]) => {
  const defaultPreferences: IEmailPreferences = {};

  emailPrefs.forEach(type => (defaultPreferences[type] = false));

  return defaultPreferences;
};

const EmailingForm = () => {
  const { t } = useTranslation();
  const { notifyError, notifySuccess } = useNotifications();
  const billingDetailsQuery = useBillingDetails();
  const updateBillingDetails = useUpdateBillingDetails();
  const [emailPreferenceChecked, setEmailPreferenceChecked] =
    useState<IEmailPreferences>(getDefaultEmailPreferences(emailPreferences));

  // @todo update payload when we'll have multiple newsletters type using emailType param
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const updateEmailPreference = (value: boolean, emailType: emailTypes) => {
    updateBillingDetails.mutate(
      {
        consent_newsletter: value,
      },
      {
        onSuccess: () => {
          notifySuccess(
            t('emailing.update.success', 'Your preferences have been saved!'),
          );
        },
        onError: error => {
          logError('Error updating email preferences', error);
          notifyError(
            t(
              'emailing.update.error',
              `An error occurred while updating your preferences: ${error}`,
            ),
          );
        },
      },
    );
  };

  useEffect(() => {
    if (billingDetailsQuery.isSuccess) {
      setEmailPreferenceChecked({
        ...emailPreferenceChecked,
        shadow: billingDetailsQuery?.data?.consent_newsletter as boolean,
      });
    }
  }, [
    billingDetailsQuery?.data?.consent_newsletter,
    billingDetailsQuery.isSuccess,
    emailPreferenceChecked,
  ]);

  return (
    <form>
      {emailPreferences.map((emailType, index) => (
        <EmailSwitch
          key={index}
          emailType={emailType}
          isChecked={emailPreferenceChecked[emailType]}
          onChecked={(value: boolean) =>
            updateEmailPreference(value, emailType)
          }
        />
      ))}
    </form>
  );
};

export default EmailingForm;
