import styled from '@emotion/styled';
import { isEmpty } from 'lodash';
import { Trans, useTranslation } from 'next-i18next';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';

import BillingForm from '@/components/account/billing/billingForm/billingForm';
import PaymentMethod from '@/components/account/billing/paymentMethod/paymentMethod';
import NextLink from '@/components/ui/nextLink';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import { useBillingDetails } from '@/hooks/user/useUser';
import { ROUTES_PATH } from '@/utils/constants';

const ContactText = styled(Typography)`
  margin-top: 8px;

  ${theme.breakpoints.up('md')} {
    margin-top: 16px;
  }
`;

const BillingManager = () => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();
  const billingDetailsQuery = useBillingDetails();

  if (isEmpty(billingDetailsQuery.data)) {
    return null;
  }

  return (
    <Paper id="billing">
      <PaperTitle>
        {t('account.billing.title', 'Billing information')}
      </PaperTitle>
      <PaymentMethod />
      <BillingForm />
      <ContactText variant={isLG ? 'body-md' : 'body-sm'}>
        <Trans
          i18nKey="account.billing.contactSupport"
          components={[
            <NextLink
              href={ROUTES_PATH.SUPPORT}
              variant={isLG ? 'label-md-regular-link' : 'body-sm-link'}
            />,
          ]}
        />
      </ContactText>
    </Paper>
  );
};

export default BillingManager;
