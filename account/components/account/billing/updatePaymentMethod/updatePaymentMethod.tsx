import styled from '@emotion/styled';
import { Dialog, DialogContent } from '@mui/material';
import { Icon, theme } from 'shared-components';
import { Icons, IModalProps } from 'types';

import { SHOP_URL } from '@/utils/constants';

const Modal = styled(Dialog)`
  .MuiPaper-root {
    margin-top: 5vh;
    margin-bottom: 5vh;
    height: 90vh;
    background: ${theme.palette.background.default};
    border-radius: ${theme.shape.borderRadius}px;
    height: 100%;
  }
`;

const Iframe = styled.iframe`
  display: flex; // to prevent useless scrollbar
  border: 0;
  overflow-y: auto;

  ${theme.breakpoints.down('md')} {
    padding-top: 32px;
  }
`;

const CrossButton = styled.button`
  position: absolute;
  right: 8px;
  top: 8px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;

  ${theme.breakpoints.up('md')} {
    display: none;
  }
`;

const CrossIcon = styled(Icon)`
  path {
    stroke-width: 1;
  }
`;

interface IUpdatePaymentMethodProps extends IModalProps {
  isB2B: boolean;
}

const UpdatePaymentMethod = ({
  isOpen,
  onClose,
  isB2B,
}: IUpdatePaymentMethodProps) => (
  <Modal open={isOpen} onClose={onClose} fullWidth>
    <DialogContent id="update-payment-method">
      <CrossButton onClick={onClose}>
        <CrossIcon name={Icons.CROSS} width={20} />
      </CrossButton>
      <Iframe
        src={`${SHOP_URL}/account-content?page_type=update_payment_method&is_b2b=${
          isB2B ? '1' : '0'
        }`}
        width="100%"
        height="100%"
      />
    </DialogContent>
  </Modal>
);

export default UpdatePaymentMethod;
