import styled from '@emotion/styled';
import { Collapse } from '@mui/material';
import { useNotifications } from 'hooks';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { useEffect, useState, SyntheticEvent } from 'react';
import { Controller } from 'react-hook-form';
import {
  Alert,
  Button,
  Input,
  theme,
  useThemeMediaQueries,
} from 'shared-components';
import { NotificationState } from 'types';
import { logError, FLAG_PATH } from 'utils';

import FormRow from '@/components/ui/form/formRow';
import useBillingForm from '@/hooks/form/useBillingForm';
import { useConfig } from '@/hooks/store/useConfig';
import { useBillingDetails } from '@/hooks/user/useUser';

const SuccessAlert = styled(Alert)`
  margin: 16px 0 8px;

  ${theme.breakpoints.up('md')} {
    margin: 24px 0 8px;
  }
`;

const formData = {
  address1: '',
  zipcode: '',
  city: '',
  country: '',
};

const BillingForm = () => {
  const { t } = useTranslation();
  const { isSM } = useThemeMediaQueries();

  const { market } = useConfig();
  const billingDetailsQuery = useBillingDetails();
  const { notifyError } = useNotifications();

  const [showSuccessNotification, setShowSuccessNotification] =
    useState<boolean>(false);

  const onSuccess = () => {
    setShowSuccessNotification(true);
  };

  const onError = (error: string) => {
    logError('Error updating billing information', error);

    notifyError(
      `${t(
        'account.billing.update.error.message',
        `An error occurred when updating your billing information:`,
      )} ${error}`,
    );
  };

  const { control, reset, onSubmit, isDirty, isSubmitting } = useBillingForm(
    onSuccess,
    onError,
    formData,
  );

  const handleBillingFormSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    setShowSuccessNotification(false);
    onSubmit();
  };

  useEffect(() => {
    if (billingDetailsQuery.isSuccess) {
      reset(billingDetailsQuery.data);
    }
  }, [billingDetailsQuery.data, billingDetailsQuery.isSuccess, reset]);

  return (
    <form onSubmit={handleBillingFormSubmit}>
      <FormRow>
        <Controller
          name="first_name"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="billing-firstname-input"
              data-test-id="billing-firstname-input"
              label={t('form.firstname.label', 'Firstname', { ns: 'common' })}
              error={error?.message}
            />
          )}
        />
        <Controller
          name="last_name"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="billing-lastname-input"
              data-test-id="billing-lastname-input"
              label={t('form.lastname.label', 'Lastname', { ns: 'common' })}
              error={error?.message}
            />
          )}
        />
      </FormRow>
      <FormRow>
        <Controller
          name="address1"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="billing-address1-input"
              data-test-id="billing-address1-input"
              label={t('form.address1.label', 'Address', { ns: 'common' })}
              error={error?.message}
            />
          )}
        />
        <Controller
          name="zipcode"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="billing-zipcode-input"
              data-test-id="billing-zipcode-input"
              label={t('form.zipcode.label', 'Zipcode', { ns: 'common' })}
              error={error?.message}
            />
          )}
        />
      </FormRow>
      <FormRow>
        <Controller
          name="city"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="billing-city-input"
              data-test-id="billing-city-input"
              label={t('form.city.label', 'City', { ns: 'common' })}
              error={error?.message}
            />
          )}
        />
        <Controller
          name="country"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              id="billing-country-input"
              data-test-id="billing-country-input"
              isDisabled
              label={t('form.country.label', 'Country', { ns: 'common' })}
              error={error?.message}
              leftAddon={
                <Image
                  src={`${FLAG_PATH}${market}.png`}
                  width="40px"
                  height="40px"
                />
              }
            />
          )}
        />
      </FormRow>
      <Button
        data-test-id="submit-billing-form-button"
        color="primary"
        disabled={!isDirty || isSubmitting}
        loading={isSubmitting}
        type="submit"
        fullWidth={isSM}
      >
        {t('account.billing.update.submit.label', 'Save billing information')}
      </Button>
      <Collapse in={showSuccessNotification}>
        <SuccessAlert
          type={NotificationState.SUCCESS}
          title={t(
            'account.billing.update.success.title',
            'Billing information updated',
          )}
        >
          {t(
            'account.billing.update.success.message',
            'Your billing information has been updated.',
          )}
        </SuccessAlert>
      </Collapse>
    </form>
  );
};

export default BillingForm;
