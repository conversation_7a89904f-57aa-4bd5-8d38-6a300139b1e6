import styled from '@emotion/styled';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { Icon, theme, Typography } from 'shared-components';
import {
  IMemberDetails,
  IPaymentMethod,
  Icons,
  PaymentMethodStatus,
  PaymentMethodType,
} from 'types';
import { CARD_PATH, SVG_PATH } from 'utils';

import UpdatePaymentMethod from '@/components/account/billing/updatePaymentMethod/updatePaymentMethod';
import EditLink from '@/components/ui/editLink';
import ApiError from '@/components/ui/errors/apiError';
import PaperLoader from '@/components/ui/loader/paperLoader';
import { useCurrentMember } from '@/hooks/member/useMember';
import { usePaymentDetails } from '@/hooks/user/useUser';

const Container = styled.div`
  display: flex;
  margin-bottom: 24px;
  padding: 16px;
  align-items: center;
  background: ${theme.palette.secondary.main10};
  border: 2px solid ${theme.palette.secondary.main10};
  border-radius: ${theme.shape.borderRadius}px;

  ${theme.breakpoints.up('md')} {
    padding: 24px;
  }
`;

const Title = styled(Typography, {
  shouldForwardProp: prop => prop !== 'hasPaymentData',
})<{ hasPaymentData: boolean }>`
  ${theme.breakpoints.up('md')} {
    padding-right: 40px;
  }

  ${({ hasPaymentData }) =>
    hasPaymentData &&
    `
    ${theme.breakpoints.down('md')} {
      display: none;
    }
  `}
`;

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
`;

const CardNumber = styled(Typography)`
  margin-left: 12px;
`;

const PaymentMethod = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const paymentDetailsQuery = usePaymentDetails();
  const currentMemberQuery = useCurrentMember();
  const isB2B = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  const [paymentData, setPaymentData] = useState<IPaymentMethod | undefined>();

  const [openPaymentModal, setOpenPaymentModal] = useState<boolean>(false);

  const getPaymentValue = (paymentMethod: IPaymentMethod) => {
    switch (paymentMethod?.type) {
      case PaymentMethodType.CARD:
        return `XXXX XXXX XXXX ${paymentMethod.card?.masked_number}`;
      case PaymentMethodType.BANK_ACCOUNT:
        return `XXXXXXXXXXXXXXXXXXXXXXX${paymentMethod.bank_account?.last4}`;
      case PaymentMethodType.PAYPAL:
        return paymentMethod.paypal?.email;
      default:
        return '';
    }
  };

  useEffect(() => {
    if (paymentDetailsQuery.isSuccess) {
      const primaryPaymentMethod = paymentDetailsQuery?.data?.find(
        paymentDetail =>
          [PaymentMethodStatus.VALID, PaymentMethodStatus.EXPIRING].includes(
            paymentDetail.status,
          ) && paymentDetail.is_primary,
      );

      setPaymentData(primaryPaymentMethod as IPaymentMethod);
    }
  }, [paymentDetailsQuery?.data, paymentDetailsQuery.isSuccess]);

  const renderPaymentMethodIcon = (paymentMethod: IPaymentMethod) => {
    switch (paymentMethod?.type) {
      case PaymentMethodType.CARD:
        return (
          <Image
            src={`${CARD_PATH}${paymentMethod?.card?.brand}.svg`}
            width="30px"
            height="24px"
          />
        );
      case PaymentMethodType.BANK_ACCOUNT:
        return <Icon name={Icons.ACCOUNT_BALANCE} width={24} />;
      case PaymentMethodType.PAYPAL:
        return (
          <Image
            src={`${SVG_PATH}banks/paypal.svg`}
            width="90px"
            height="24px"
          />
        );
      case PaymentMethodType.GOOGLE_PAY:
        return (
          <Image
            src={`${SVG_PATH}banks/google-pay.png`}
            width="55px"
            height="26px"
          />
        );
      case PaymentMethodType.APPLE_PAY:
        return (
          <Image
            src={`${SVG_PATH}banks/apple-pay.png`}
            width="63px"
            height="26px"
          />
        );
      default:
        return '';
    }
  };

  const onUpdatePaymentMethodModalClose = () => {
    queryClient.invalidateQueries(['paymentDetails']);
    setOpenPaymentModal(false);
  };

  if (paymentDetailsQuery.isLoading) {
    return <PaperLoader />;
  }

  if (paymentDetailsQuery.isError) {
    return <ApiError />;
  }

  return (
    <>
      <Container>
        <Wrapper>
          <Title variant="body-md-regular" hasPaymentData={!!paymentData}>
            {paymentData
              ? t(`account.payment.type.${paymentData?.type}`)
              : t('account.payment.type.none', 'No payment method')}
          </Title>
          {renderPaymentMethodIcon(paymentData as IPaymentMethod)}
          {paymentData && (
            <CardNumber variant="label-sm">
              {getPaymentValue(paymentData as IPaymentMethod)}
            </CardNumber>
          )}
        </Wrapper>
        <EditLink
          onClick={() => setOpenPaymentModal(true)}
          label={
            paymentData
              ? t('account.payment.edit.link', 'Edit')
              : t('account.payment.add.link', 'Add a new payment method')
          }
        />
      </Container>
      <UpdatePaymentMethod
        isOpen={openPaymentModal}
        onClose={onUpdatePaymentMethodModalClose}
        isB2B={isB2B}
      />
    </>
  );
};

export default PaymentMethod;
