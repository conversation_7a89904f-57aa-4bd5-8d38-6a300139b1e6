import { useEffect, useState } from 'react';

import ReferralCopyCode from '@/components/referral/referralCopyCode';
import ReferralSocialSharing from '@/components/referral/referralSocialSharing';
import { useReferral } from '@/hooks/subscription/useSubscription';

const ReferralSharing = () => {
  const [referralCode, setReferralCode] = useState<string>('');
  const referralQuery = useReferral();

  useEffect(() => {
    if (referralQuery.isSuccess) {
      setReferralCode(referralQuery.data?.public_coupon as string);
    }
  }, [referralQuery.data?.public_coupon, referralQuery.isSuccess]);

  return (
    <div>
      <ReferralCopyCode code={referralCode} />
      <ReferralSocialSharing code={referralCode} />
    </div>
  );
};

export default ReferralSharing;
