import styled from '@emotion/styled';
import { useNotifications } from 'hooks';
import { useTranslation } from 'next-i18next';
import { theme, Typography } from 'shared-components';

import Link from '@/components/ui/link';
import useClipboard from '@/hooks/useClipboard';

interface IReferralCopyCode {
  code: string;
}

const Container = styled.div`
  display: flex;
  margin: 0 auto;
  padding: 0 16px;
  height: 46px;
  align-items: center;
  background: ${theme.palette.secondary.main10};
  border: 2px solid ${theme.palette.secondary.main10};
  border-radius: 4px;
  justify-content: space-between;

  ${theme.breakpoints.only('md')} {
    width: 272px;
  }
`;

const ReferralCopyCode = ({ code }: IReferralCopyCode) => {
  const { t } = useTranslation();
  const { copyToClipboard } = useClipboard();
  const { notifyError, notifySuccess } = useNotifications();

  const handleClipboardWriteSuccess = () => {
    notifySuccess(
      t(
        'referral.sharing.clipboardSuccess',
        'Referral code successfully copied!',
      ),
    );
  };

  const handleClipboardWriteError = () => {
    notifyError(
      t('referral.sharing.clipboardError', 'Error when copying referral code!'),
    );
  };

  return (
    <Container>
      <Typography variant="label-sm-light">{code}</Typography>
      <Link
        variant="label-sm-bold"
        onClick={() =>
          copyToClipboard(
            code,
            handleClipboardWriteSuccess,
            handleClipboardWriteError,
          )
        }
      >
        {t('referral.sharing.copy', 'Copy')}
      </Link>
    </Container>
  );
};

export default ReferralCopyCode;
