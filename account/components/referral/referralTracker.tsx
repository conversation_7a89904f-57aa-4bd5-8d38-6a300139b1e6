import styled from '@emotion/styled';
import { usePaginatedSubscriptions } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useEffect, useMemo, useState } from 'react';
import { theme, Typography } from 'shared-components';
import { ProductFamilyId, ProductType } from 'types';
import { findSubscriptionItemByType } from 'utils';

import { useReferral } from '@/hooks/subscription/useSubscription';

const Container = styled.div`
  ${theme.breakpoints.only('md')} {
    display: flex;
    flex-direction: column;
    max-width: 272px;
  }
`;

const RefereeText = styled(Typography)`
  padding: 0 0 8px;
  text-align: center;

  ${theme.breakpoints.only('md')} {
    order: 2;
  }
`;

const DiscountPercentage = styled(Typography)`
  margin-bottom: 16px;
  padding: 16px;
  background: ${theme.palette.primary.main};
  border-radius: 4px;
  color: ${theme.palette.white.main};
  text-align: center;

  ${theme.breakpoints.only('md')} {
    order: 1;
  }
`;

const ReferralTracker = () => {
  const { t } = useTranslation();
  const referralQuery = useReferral();
  const subscriptionsQuery = usePaginatedSubscriptions();
  const [subscriptionPrice, setSubscriptionPrice] = useState<number>(0);
  const [activeReferrals, setActiveReferrals] = useState<number>(0);
  const [totalDiscount, setTotalDiscount] = useState<number>(0);
  const [discountPerReferral, setDiscountPerReferral] = useState<number>(0);

  const referralsForFreeShadow = useMemo(getReferralsForFreeShadow, [
    subscriptionPrice,
    activeReferrals,
    discountPerReferral,
  ]);

  useEffect(() => {
    if (referralQuery.isSuccess) {
      const activeReferral = referralQuery?.data?.referral_used_total as number;
      const discount = referralQuery?.data?.referral_discount_total as number;
      const amountPerReferral = referralQuery?.data
        ?.amount_per_referral as number;

      setDiscountPerReferral(amountPerReferral);

      if (activeReferral > 0) {
        setActiveReferrals(activeReferral);
        setTotalDiscount(discount);
      }
    }
  }, [
    referralQuery?.data?.amount_per_referral,
    referralQuery?.data?.referral_discount_total,
    referralQuery?.data?.referral_used_total,
    referralQuery.isSuccess,
  ]);

  useEffect(() => {
    if (subscriptionsQuery.isSuccess) {
      const price = subscriptionsQuery.data?.items
        ?.filter(subscriptionData =>
          subscriptionData.items.some(
            item =>
              item.item_type === ProductType.PLAN &&
              item.id.includes(ProductFamilyId.CLOUDPC),
          ),
        )
        .reduce((acc, subscription) => {
          const plan = findSubscriptionItemByType(
            subscription,
            ProductType.PLAN,
          );

          return acc + (plan?.price || 0);
        }, 0);

      if (price && price > 0) {
        setSubscriptionPrice(price / 100);
      }
    }
  }, [subscriptionsQuery.data, subscriptionsQuery.isSuccess]);

  return (
    <Container>
      {referralsForFreeShadow > 1 && (
        <RefereeText variant="body-xs">
          {t('referral.tracking.shadowForFree.value', {
            defaultValue: '{{count}} referees left until free subscription.',
            count: referralsForFreeShadow || 0,
          })}
        </RefereeText>
      )}

      <DiscountPercentage variant="label-sm">
        {referralsForFreeShadow > 1
          ? t('referral.tracking.discountPerMonth', {
              defaultValue: '{{discountPerMonth, currency}} monthly off',
              discountPerMonth: totalDiscount,
            })
          : t('referral.tracking.shadowFree', 'Free Shadow!')}
      </DiscountPercentage>
    </Container>
  );

  function getReferralsForFreeShadow() {
    return Math.ceil(subscriptionPrice / discountPerReferral) - activeReferrals;
  }
};

export default ReferralTracker;
