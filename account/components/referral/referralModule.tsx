import styled from '@emotion/styled';
import { usePaginatedSubscriptions } from 'hooks';
import { Trans, useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { theme, Typography } from 'shared-components';
import {
  SubscriptionStatus,
  ProductFamilyId,
  ProductType,
  B2cProductTarget,
} from 'types';

import ReferralSharing from '@/components/referral/referralSharing';
import ReferralTracker from '@/components/referral/referralTracker';
import ApiError from '@/components/ui/errors/apiError';
import PaperLoader from '@/components/ui/loader/paperLoader';
import Paper from '@/components/ui/paper';
import { useConfig } from '@/hooks/store/useConfig';
import { useCatalog } from '@/hooks/useCatalog';

const Container = styled(Paper)`
  margin-bottom: 0;

  ${theme.breakpoints.only('md')} {
    margin-left: -40px;
    margin-right: -40px;
    text-align: center;
    margin-top: 24px;
  }
  ${theme.breakpoints.up('md')} {
    padding: 24px;
  }
`;

const Wrapper = styled.div`
  ${theme.breakpoints.only('md')} {
    display: flex;
    column-gap: 24px;
    justify-content: center;
  }
`;

const Subtitle = styled(Typography)`
  padding: 16px 0 64px;
`;

const ReferralModule = () => {
  const { t } = useTranslation();
  const { currency } = useConfig();
  const catalogQuery = useCatalog();
  const subscriptionsQuery = usePaginatedSubscriptions();
  const [_hasActiveVmSubscriptions, setHasActiveVmSubscriptions] =
    useState(false);
  const [_hasForMakersSubscriptions, setHasForMakersSubscriptions] =
    useState(false);

  useEffect(() => {
    if (subscriptionsQuery.isSuccess && catalogQuery.isSuccess) {
      const vmSubscriptions = subscriptionsQuery.data?.items?.filter(
        subscriptionData =>
          subscriptionData.items.some(
            item =>
              item.item_type === ProductType.PLAN &&
              item.id.includes(ProductFamilyId.CLOUDPC),
          ),
      );

      if (vmSubscriptions && vmSubscriptions.length > 0) {
        const hasActiveSub = vmSubscriptions.some(
          vmSubscription => vmSubscription.status === SubscriptionStatus.ACTIVE,
        );

        setHasActiveVmSubscriptions(hasActiveSub);

        const hasForMakersSub = vmSubscriptions.some(vmSubscription => {
          const vmSubscriptionProductId =
            catalogQuery.data?.offers.byId[vmSubscription.plan_id]?.product_id;
          return (
            catalogQuery.data?.products.byId[vmSubscriptionProductId ?? '']
              ?.meta_data?.target === B2cProductTarget.MAKERS
          );
        });

        setHasForMakersSubscriptions(hasForMakersSub);
      }
    }
  }, [
    subscriptionsQuery.data,
    subscriptionsQuery.isSuccess,
    catalogQuery.data,
    catalogQuery.isSuccess,
  ]);

  const disableReferralModule = true;
  // Old condition
  // const disableReferralModule =
  //   !hasActiveVmSubscriptions ||
  //   isEmpty(billingDetailsQuery.data) ||
  //   hasForMakersSubscriptions;

  if (disableReferralModule) {
    return <></>;
  }

  if (subscriptionsQuery.isLoading) {
    return <PaperLoader />;
  }

  if (subscriptionsQuery.isError) {
    return <ApiError />;
  }

  return (
    <Container>
      <Typography variant="body-lg-regular">
        {t('referral.sharing.title', 'Save on your Shadow subscription!')}
      </Typography>
      <Subtitle variant="body-sm">
        <Trans
          i18nKey="referral.sharing.subtitle"
          defaults="Offer 5{{ currency }}, and save 1{{ currency }} for each referee on your monthly price (excluding Shadow Drive)."
          values={{ currency }}
        />
      </Subtitle>
      <Wrapper>
        <ReferralTracker />
        <ReferralSharing />
      </Wrapper>
    </Container>
  );
};

export default ReferralModule;
