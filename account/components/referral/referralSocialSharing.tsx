import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import {
  BrowserView as DesktopView,
  MobileView,
  isMobile,
  isTablet,
} from 'react-device-detect';
import { Icon } from 'shared-components';
import { Icons } from 'types';

import { SHOP_URL } from '@/utils/constants';

interface IReferralSocialSharing {
  code: string;
}

const List = styled.ul`
  display: flex;
  padding-top: 16px;
  column-gap: 16px;
  justify-content: center;
`;

const IconLink = styled.a`
  display: block;
  width: 24px;
`;

const ReferralSocialSharing = ({ code }: IReferralSocialSharing) => {
  const { t } = useTranslation();
  const referralUrl = `${SHOP_URL}invite/${code}`;
  const whatsAppBaseUrl =
    isMobile || isTablet ? 'whatsapp://' : 'https://web.whatsapp.com/';

  return (
    <List>
      <li>
        <DesktopView>
          <IconLink
            href={`https://www.facebook.com/share.php?u=${referralUrl}`}
            target="_blank"
            title={t('referral.sharing.shareFacebook', 'Share on Facebook')}
          >
            <Icon name={Icons.FACEBOOK} color="#4172B8" />
          </IconLink>
        </DesktopView>
        <MobileView>
          <IconLink
            href={`fb-messenger://share/?link=${referralUrl}`}
            target="_blank"
            title={t('referral.sharing.shareMessenger', 'Share on Messenger')}
          >
            <Icon name={Icons.MESSENGER} color="#0084FF" />
          </IconLink>
        </MobileView>
      </li>
      <li>
        <IconLink
          href={`https://twitter.com/intent/tweet?status=+${referralUrl}`}
          target="_blank"
          title={t('referral.sharing.shareTwitter', 'Share on Twitter')}
        >
          <Icon name={Icons.TWITTER} color="#1DA1F2" />
        </IconLink>
      </li>
      <li>
        <IconLink
          href={`${whatsAppBaseUrl}send?text=${referralUrl}`}
          target={isMobile || isTablet ? undefined : '_blank'}
          title={t('referral.sharing.shareWhatsApp', 'Share on WhatsApp')}
        >
          <Icon name={Icons.WHATSAPP} color="#25D366" />
        </IconLink>
      </li>
      <li>
        <IconLink
          href={`mailto:?subject=${t(
            'referral.sharing.shareEmail.subject',
            'Join%20me%20on%20Shadow',
          )}&body=${t('referral.sharing.shareEmail.body', {
            defaultValue:
              'Join%20me%20on%20Shadow%20and%20save%205€%20on%20your%20first%20month!%20Just%20use%20my%20referral%20code%20{{code}}%20or%20click%20here:%20{{inviteUrl}}',
            code,
            inviteUrl: referralUrl,
          })}`}
          title={t('referral.sharing.shareEmail.title', 'Send an Email')}
        >
          <Icon name={Icons.EMAIL_CIRCLE} color="#F93A13" />
        </IconLink>
      </li>
    </List>
  );
};

export default ReferralSocialSharing;
