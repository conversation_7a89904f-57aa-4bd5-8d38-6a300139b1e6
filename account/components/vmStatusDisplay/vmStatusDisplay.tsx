import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Typography } from 'shared-components';
import { SubscriptionStatus } from 'types';

import { SUBSCRIPTION_STATUS_COLOR } from '@/types/subscriptions';
import { VmStatus, VM_STATUS_COLOR } from '@/types/vm';

export interface VmStatusProps {
  subscriptionStatus: SubscriptionStatus;
  isVmRunning: boolean;
  isVmInMaintenanceMode: boolean;
  isVmOnHold: boolean;
  isInTable?: boolean;
}

const StatusWrapper = styled.div<{ isInTable: boolean }>`
  display: flex;
  ${({ isInTable }) =>
    !isInTable &&
    `flex-wrap: wrap;
      justify-content: flex-end;
      align-items: center;
    `}
  gap: 8px;
`;

export const VmStatusDisplay = ({
  subscriptionStatus,
  isVmRunning,
  isVmInMaintenanceMode,
  isVmOnHold,
  isInTable = true,
}: VmStatusProps) => {
  const { t } = useTranslation();

  const vmStatusRunningOrStopped = isVmRunning
    ? VmStatus.RUNNING
    : VmStatus.STOPPED;

  if (isVmOnHold) {
    return (
      <Typography
        color={VM_STATUS_COLOR[VmStatus.ON_HOLD]?.color}
        variant={'body-sm'}
      >
        {t(`businessManager.managerList.vm.vmStatus.${VmStatus.ON_HOLD}`)}
      </Typography>
    );
  }

  if (
    [
      SubscriptionStatus.ACTIVE,
      SubscriptionStatus.CANCELLED,
      SubscriptionStatus.PAUSED,
    ].includes(subscriptionStatus)
  ) {
    return (
      <StatusWrapper isInTable={isInTable}>
        <Typography
          color={VM_STATUS_COLOR[vmStatusRunningOrStopped]?.color}
          variant={'body-sm'}
        >
          {t(
            `businessManager.managerList.vm.vmStatus.${vmStatusRunningOrStopped}`,
          )}
        </Typography>
        {isVmInMaintenanceMode && (
          <Typography
            color={VM_STATUS_COLOR[VmStatus.MAINTENANCE]?.color}
            variant={'body-sm'}
          >
            {t(
              `businessManager.managerList.vm.vmStatus.${VmStatus.MAINTENANCE}`,
            )}
          </Typography>
        )}
      </StatusWrapper>
    );
  }

  if ([SubscriptionStatus.FUTURE].includes(subscriptionStatus)) {
    return (
      <Typography variant="body-xs">
        {t(
          `businessManager.managerList.vm.subscriptionStatus.${subscriptionStatus}`,
        )}
      </Typography>
    );
  }

  // subscriptionStatus :  IN_TRIAL or NON_RENEWING
  return (
    <Typography
      color={SUBSCRIPTION_STATUS_COLOR[subscriptionStatus]?.color}
      variant="body-sm"
    >
      {t(
        `businessManager.managerList.vm.subscriptionStatus.${subscriptionStatus}`,
      )}
    </Typography>
  );
};
