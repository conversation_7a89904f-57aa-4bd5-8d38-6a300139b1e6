import { useRouter } from 'next/router';
import { Trans, useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { Alert, useThemeMediaQueries } from 'shared-components';
import { NotificationState } from 'types';

import NextLink from '../../ui/nextLink';

import { ROUTES_PATH } from '@/utils/constants';

const SubscriptionCreatedAlert = () => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();
  const router = useRouter();

  const { subscription_created } = router.query;
  const [wasSubscriptionCreated, setWasSubscriptionCreated] = useState(false);

  useEffect(() => {
    setWasSubscriptionCreated(subscription_created !== undefined);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (wasSubscriptionCreated) {
    return (
      <Alert
        type={NotificationState.SUCCESS}
        title={t(
          'businessManager.vmOnDemand.successAlert.title',
          'Your API access has been created',
        )}
      >
        <Trans i18nKey="businessManager.vmOnDemand.successAlert.body">
          Your API credentials has been sent by email. If you didn’t received
          them under one hour,{' '}
          <NextLink
            href={ROUTES_PATH.SUPPORT}
            variant={isLG ? 'body-sm-regular-link' : 'body-xs-regular-link'}
          >
            contact us
          </NextLink>
          .
        </Trans>
      </Alert>
    );
  } else {
    return null;
  }
};

export default SubscriptionCreatedAlert;
