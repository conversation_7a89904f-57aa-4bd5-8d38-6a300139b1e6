import { usePaginatedSubscriptions } from 'hooks';
import { LocalLoader } from 'shared-components';
import { ProductFamilyId, SubscriptionStatus } from 'types';
import { findSubscription } from 'utils';

import SubscriptionCreatedAlert from './subscriptionCreatedAlert';
import SubscribedVmOnDemand from '../subscription/subscribedVmOnDemand';
import UnsubscribedVmOnDemand from '../subscription/unsubscribedVmOnDemand';

import Paper from '@/components/ui/paper';

const VmOnDemandManager = () => {
  const usePaginatedSubscriptionQuery = usePaginatedSubscriptions();

  const isLoading = usePaginatedSubscriptionQuery.isLoading;

  const vmOnDemandSubscription = findSubscription(
    usePaginatedSubscriptionQuery.data?.items,
    ProductFamilyId.VM_ON_DEMAND,
  );
  const hasValidSubscription =
    !!vmOnDemandSubscription &&
    vmOnDemandSubscription.status !== SubscriptionStatus.CANCELLED;

  return (
    <>
      <SubscriptionCreatedAlert />
      <Paper>
        {isLoading && <LocalLoader />}
        {!isLoading && hasValidSubscription && (
          <SubscribedVmOnDemand
            subscriptionStatus={vmOnDemandSubscription.status}
          />
        )}
        {!isLoading && !hasValidSubscription && <UnsubscribedVmOnDemand />}
      </Paper>
    </>
  );
};

export default VmOnDemandManager;
