import styled from '@emotion/styled';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { Button, theme, Typography } from 'shared-components';
import { ProductAddonNecessity, ProductFamilyId } from 'types';
import {
  IMAGE_PATH,
  getAddons,
  getCharges,
  getOffer,
  getOffers,
  getPlans,
  logError,
  VM_ON_DEMAND_AMOUNTS_PER_MARKET,
} from 'utils';

import OfferCardSecondaryButtons from './offerCardSecondaryButtons';

import { useConfig } from '@/hooks/store/useConfig';
import { useCatalog } from '@/hooks/useCatalog';
import { usePrice } from '@/hooks/usePrice';
import { SHOP_URL } from '@/utils/constants';

const Container = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
`;

const Heading = styled(Typography)`
  margin-top: 40px;
  margin-bottom: 16px;
`;

const SubHeading = styled(Typography)`
  margin-bottom: 24px;
`;

const ButtonWrapper = styled.div`
  margin-top: 40px;
`;

const ButtonStyled = styled(Button)`
  text-align: center;
`;

const UnsubscribedVmOnDemand = () => {
  const { t } = useTranslation();
  const { formatPrice } = usePrice();

  const catalogQuery = useCatalog();
  const catalog = catalogQuery.data;
  const isLoading = catalogQuery.isLoading;

  const { market } = useConfig();

  const vmOnDemandPlansFromCatalog = getPlans(
    catalog,
    plan => plan.item_family_id === ProductFamilyId.VM_ON_DEMAND,
  );

  const vmOnDemandPlan = vmOnDemandPlansFromCatalog[0];

  if (!isLoading && vmOnDemandPlansFromCatalog.length > 1) {
    logError(
      `More than one spot computing plan has been found in catalog, this is not expected. Errors could happen on the frontend.`,
    );
  } else if (!isLoading && !vmOnDemandPlan) {
    logError(
      `No spot computing plan has been found in catalog, this is not expected. Errors could happen on the frontend.`,
    );
  }

  let apiAccessCatalogAmount =
    VM_ON_DEMAND_AMOUNTS_PER_MARKET[market]?.apiAccess; // default if calculation is somehow not possible.

  if (vmOnDemandPlan) {
    const vmOnDemandOffer = getOffer(catalog, vmOnDemandPlan.id);

    const addonsForPlan = getAddons(catalog, vmOnDemandPlan.id, parameter => {
      return parameter.type === ProductAddonNecessity.MANDATORY;
    });
    const addonsTotalAmount = addonsForPlan
      ?.map(addon => getOffers(catalog, addon.id))
      .flat()
      .reduce((acc, addonOffer) => acc + addonOffer.price, 0);

    const chargesForPlan = getCharges(catalog, vmOnDemandPlan.id);
    const chargesTotalAmount = chargesForPlan
      ?.map(charge => getOffers(catalog, charge.id))
      .flat()
      .reduce((acc, chargeOffer) => acc + chargeOffer.price, 0);

    apiAccessCatalogAmount =
      (vmOnDemandOffer?.price || 0) +
      (addonsTotalAmount || 0) +
      (chargesTotalAmount || 0);
  }

  const startingCreditsPrice = formatPrice(
    VM_ON_DEMAND_AMOUNTS_PER_MARKET[market]?.credits * 100,
    undefined,
    0,
  );
  const apiAccessPrice = formatPrice(
    apiAccessCatalogAmount * 100,
    undefined,
    0,
  );

  return (
    <Container>
      <Image
        src={`${IMAGE_PATH}spot-computing-gpu.svg`}
        width="225px"
        height="160px"
      />
      <Heading variant="heading-h5" color={theme.palette.black.main75}>
        {t(
          'businessManager.vmOnDemand.unsubscribed.title',
          'Start now from €0.185/hr*',
        )}
      </Heading>
      <SubHeading variant="body-sm">
        {t(
          'businessManager.vmOnDemand.unsubscribed.subtitle',
          'Shadow Spot Computing is the cost-efficient solution for all your VM needs. Perfect for generative AI, 3D & video rendering, machine learning, CI/CD or complex physics simulations.',
        )}
      </SubHeading>
      <Typography variant="body-xs">
        {t(
          'businessManager.vmOnDemand.unsubscribed.asterisk',
          '*Depending on the currency conversion in your market. Pricing example for NVIDIA P5000 16GB, Xeon 2678V3 8 threads@3.1Ghz, 12GB RAM, 1Gbps/100Mbps.',
        )}
      </Typography>
      <OfferCardSecondaryButtons />
      <ButtonWrapper>
        <ButtonStyled
          component="a"
          color="black"
          href={`${SHOP_URL}business/spot-computing`}
        >
          {t('businessManager.vmOnDemand.buttons.getApiKey.label', {
            defaultValue:
              'Get your API access and {{startingCreditsPrice}} starting credits for only {{apiAccessPrice}}',
            startingCreditsPrice: startingCreditsPrice,
            apiAccessPrice: apiAccessPrice,
          })}
        </ButtonStyled>
      </ButtonWrapper>
    </Container>
  );
};

export default UnsubscribedVmOnDemand;
