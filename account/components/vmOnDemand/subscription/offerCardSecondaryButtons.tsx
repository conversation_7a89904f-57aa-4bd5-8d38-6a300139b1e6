import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Button, Icon } from 'shared-components';
import { Icons } from 'types';
import { VM_ON_DEMAND_API_DOC_URL, VM_ON_DEMAND_PRICE_TABLE_URL } from 'utils';

const ButtonsWrapper = styled.div`
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 40px;
`;

const OfferCardSecondaryButtons = () => {
  const { t } = useTranslation();

  return (
    <ButtonsWrapper>
      <Button
        component="a"
        target="blank"
        color="secondary"
        size="medium"
        href={VM_ON_DEMAND_API_DOC_URL}
        startIcon={<Icon height={20} name={Icons.FILE_TEXT} width={20} />}
      >
        {t(
          'businessManager.vmOnDemand.buttons.apiDocumentation.label',
          'API Documentation',
        )}
      </Button>
      <Button
        component="a"
        target="blank"
        color="secondary"
        size="medium"
        href={VM_ON_DEMAND_PRICE_TABLE_URL}
        startIcon={<Icon height={20} name={Icons.LIST} width={20} />}
      >
        {t(
          'businessManager.vmOnDemand.buttons.priceTable.label',
          'Price table',
        )}
      </Button>
    </ButtonsWrapper>
  );
};

export default OfferCardSecondaryButtons;
