import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { FC } from 'react';
import { Typography, theme } from 'shared-components';
import { SubscriptionStatus } from 'types';

interface IStatusDotProps {
  statusColor: string;
}

const StatusDot = styled('span', {
  shouldForwardProp: prop => prop !== 'statusColor',
})<IStatusDotProps>`
  border-radius: 50%;
  display: inline-block;
  height: 8px;
  width: 8px;
  ${({ statusColor }) => `background-color: ${statusColor};`}
`;

const StatusWrapper = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: center;
`;

interface IVmOnDemandSubscriptionStatusProps {
  status?: SubscriptionStatus;
}

const VmOnDemandSubscriptionStatus: FC<IVmOnDemandSubscriptionStatusProps> = ({
  status,
}) => {
  const { t } = useTranslation();

  const getStatusColorAndLabel = (statusValue?: SubscriptionStatus) => {
    let statusColor: string | undefined;
    let statusLabel: string | undefined;

    switch (statusValue) {
      case SubscriptionStatus.ACTIVE:
        statusColor = theme.palette.success.main;
        statusLabel = t('businessManager.vmOnDemand.status.active', 'Active');
        break;
      case SubscriptionStatus.CANCELLED:
        statusColor = theme.palette.error.main;
        statusLabel = t(
          'businessManager.vmOnDemand.status.cancelled',
          'Cancelled',
        );
        break;
      case SubscriptionStatus.FUTURE:
        statusColor = theme.palette.info.main;
        statusLabel = t('businessManager.vmOnDemand.status.future', 'Future');
        break;
      case SubscriptionStatus.IN_TRIAL:
        statusColor = theme.palette.info.main;
        statusLabel = t(
          'businessManager.vmOnDemand.status.inTrial',
          'In trial',
        );
        break;
      case SubscriptionStatus.NON_RENEWING:
        statusColor = theme.palette.info.main;
        statusLabel = t(
          'businessManager.vmOnDemand.status.nonRenewing',
          'Non renewing',
        );
        break;
      case SubscriptionStatus.PAUSED:
        statusColor = theme.palette.info.main;
        statusLabel = t('businessManager.vmOnDemand.status.paused', 'Paused');
        break;
    }

    return { statusColor, statusLabel };
  };

  const { statusColor, statusLabel } = getStatusColorAndLabel(status);

  if (!statusColor || !statusLabel) {
    return null;
  }

  return (
    <StatusWrapper>
      <StatusDot statusColor={statusColor} />
      <Typography variant="body-sm">{statusLabel}</Typography>
    </StatusWrapper>
  );
};

export default VmOnDemandSubscriptionStatus;
