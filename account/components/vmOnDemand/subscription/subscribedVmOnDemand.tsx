import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import { FC } from 'react';
import { Typography } from 'shared-components';
import { SubscriptionStatus } from 'types';

import OfferCardSecondaryButtons from './offerCardSecondaryButtons';
import VmOnDemandSubscriptionStatus from './vmOnDemandSubscriptionStatus';

import NextLink from '@/components/ui/nextLink';
import { ROUTES_PATH } from '@/utils/constants';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const HeadingWrapper = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  gap: 24px;
`;

interface ISubscribedVmOnDemandProps {
  subscriptionStatus: SubscriptionStatus;
}

const SubscribedVmOnDemand: FC<ISubscribedVmOnDemandProps> = ({
  subscriptionStatus,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Container>
        <HeadingWrapper>
          <Typography variant="label-lg-regular" color="primary">
            {t(
              'businessManager.vmOnDemand.subscribed.title',
              'Spot Computing API access',
            )}
          </Typography>
          <VmOnDemandSubscriptionStatus status={subscriptionStatus} />
        </HeadingWrapper>
        <Typography variant="body-sm">
          {t(
            'businessManager.vmOnDemand.subscribed.subtitle',
            'Shadow Spot Computing is the cost-efficient solution for all your VM needs. Perfect for generative AI, 3D & video rendering, machine learning, CI/CD or complex physics simulations.',
          )}
        </Typography>
        <Typography variant="body-sm-regular">
          <Trans i18nKey="businessManager.vmOnDemand.subscribed.contactUs">
            For any concerns about your API access, billing or account,{' '}
            <NextLink href={ROUTES_PATH.SUPPORT} variant="body-sm-regular-link">
              please contact us
            </NextLink>
            .
          </Trans>
        </Typography>
      </Container>
      <OfferCardSecondaryButtons />
    </>
  );
};

export default SubscribedVmOnDemand;
