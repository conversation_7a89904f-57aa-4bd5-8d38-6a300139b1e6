import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { useAuth } from 'react-oidc-context';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';

import Link from '@/components/ui/link';

const Container = styled(Link)`
  text-transform: capitalize;
  gap: 0;
  ${theme.breakpoints.down('lg')} {
    justify-content: flex-end;
    margin: 24px;
  }

  :hover {
    color: ${theme.palette.primary.main};
  }
`;

const Label = styled(Typography)`
  :hover {
    color: ${theme.palette.primary.main};
  }
`;

const Logout = () => {
  const { t } = useTranslation();
  const auth = useAuth();
  const { isLG } = useThemeMediaQueries();

  return (
    <Container
      onClick={() => {
        void auth.signoutRedirect();
      }}
      variant={isLG ? 'label-md-regular' : 'label-sm-regular'}
    >
      {isLG && '('}
      <Label
        variant={isLG ? 'label-md-regular-link' : 'label-sm-regular-underline'}
      >
        {t('header.link.logout', 'Log out')}
      </Label>
      {isLG && ')'}
    </Container>
  );
};

export default Logout;
