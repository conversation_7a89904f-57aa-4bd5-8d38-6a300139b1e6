import styled from '@emotion/styled';
import { useState } from 'react';
import { theme } from 'shared-components';

import BurgerButton from './burgerButton';
import Drawer from '../ui/drawer/drawer';

import DownloadButton from '@/components/header/downloadButton';
import Logout from '@/components/header/logout';
import Navigation from '@/components/navigation/navigation';

const DrawerStyled = styled(Drawer)`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: right;
  height: calc(100vh - 64px);

  ${theme.breakpoints.up('md')} {
    width: 50%;
  }
`;

const LogoutStyled = styled(Logout)`
  padding: 24px;
  justify-content: flex-end;
  min-height: inherit;
`;

const DownloadButtonContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-end;
`;

interface IBurgerMenu {
  isB2b?: boolean;
}

const BurgerMenu = ({ isB2b = false }: IBurgerMenu) => {
  const [openMobileDrawer, setOpenMobileDrawer] = useState(false);
  const handleClick = () => setOpenMobileDrawer(!openMobileDrawer);

  const closeDrawer = () => setOpenMobileDrawer(false);

  return (
    <>
      <BurgerButton onClick={handleClick} isOpen={openMobileDrawer} />
      <DrawerStyled
        anchor={'right'}
        handleClose={closeDrawer}
        open={openMobileDrawer}
        keepHeaderVisible={true}
      >
        <div>
          <Navigation closeDrawer={closeDrawer} />
          <LogoutStyled />
        </div>
        <DownloadButtonContainer>
          <DownloadButton isB2b={isB2b} />
        </DownloadButtonContainer>
      </DrawerStyled>
    </>
  );
};

export default BurgerMenu;
