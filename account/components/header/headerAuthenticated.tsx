import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import React from 'react';
import { theme, Typography, useThemeMediaQueries } from 'shared-components';
import { IMember, UserRole } from 'types';

import Logout from '@/components/header/logout';
import { useCurrentMember } from '@/hooks/member/useMember';

const Container = styled.div`
  display: flex;
  align-items: center;
`;

const WelcomeText = styled(Typography)`
  display: none;
  white-space: pre-wrap;
  ${theme.breakpoints.up('md')} {
    display: block;
  }
`;

interface IHeaderAuthenticatedProps {
  isB2b?: boolean;
  role: UserRole;
}

function getUserFullname(member: IMember | undefined) {
  if (!member) {
    return '';
  }

  if (!member.user?.last_name) {
    return member.user?.first_name ?? '';
  }

  if (!member.user?.first_name) {
    return member.user?.last_name ?? '';
  }

  return `${member.user?.first_name} ${member.user?.last_name}`;
}

const HeaderAuthenticated = ({ isB2b, role }: IHeaderAuthenticatedProps) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const userFullName = getUserFullname(currentMemberQuery.data);
  const { isLG } = useThemeMediaQueries();

  return (
    <Container>
      <WelcomeText variant="label-md-regular">
        {t('header.login.welcome.label', 'Welcome')} {userFullName}{' '}
        {isB2b && role && `- ${t(`businessManager.user.role.${role}`)} `}
      </WelcomeText>
      {isLG && <Logout />}
    </Container>
  );
};

export default HeaderAuthenticated;
