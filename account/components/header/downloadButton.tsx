import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Button } from 'shared-components';

import { APPS_URL } from '@/utils/constants';

const Container = styled(Button)<{ target: string }>`
  margin-top: 40px;
  padding: 24px;
  border-radius: 0;
  width: 100%;
`;

interface IDownloadButtonProps {
  isB2b?: boolean;
}

const DownloadButton = ({ isB2b = false }: IDownloadButtonProps) => {
  const { t } = useTranslation();

  return (
    <Container
      color={isB2b ? 'black' : 'primary'}
      component="a"
      size="medium"
      href={APPS_URL}
      target="_blank"
    >
      {t('header.download.label', 'Download')}
    </Container>
  );
};

export default DownloadButton;
