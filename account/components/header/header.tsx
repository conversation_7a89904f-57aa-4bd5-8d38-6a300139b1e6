import styled from '@emotion/styled';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { theme, useThemeMediaQueries } from 'shared-components';
import { UserRole } from 'types';
import { SVG_PATH } from 'utils';

import BurgerMenu from '@/components/header/burgerMenu';
import HeaderAuthenticated from '@/components/header/headerAuthenticated';
import { useCurrentMember } from '@/hooks/member/useMember';
import { LANDING_URL } from '@/utils/constants';

const Container = styled.header<{ isB2b: boolean }>`
  display: flex;
  padding: 0 24px;
  align-items: center;
  background: ${theme.palette.white.main};
  height: ${theme.header.height.md};
  border-bottom: 1px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(
    to right,
    ${theme.palette.white.main},
    ${theme.palette.primary.main} 50%,
    ${theme.palette.white.main}
  );
  flex-direction: row;
  position: sticky;
  top: 0;
  z-index: 100;

  ${({ isB2b }) =>
    isB2b &&
    `
      border-image-source: linear-gradient(
        to right,
        ${theme.palette.white.main},
        ${theme.palette.black.main} 50%,
        ${theme.palette.white.main}
      );
  `}

  ${theme.breakpoints.only('md')} {
    padding: 0 40px;
  }

  ${theme.breakpoints.up('lg')} {
    position: static;
    padding: 0 64px;
    height: ${theme.header.height.lg};
  }
`;

const Wrapper = styled.div`
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  max-width: ${theme.container.maxWidth.lg};
  width: 100%;
  align-items: center;
  gap: 8px;
`;

const Links = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
`;

interface IHeaderProps {
  isB2b?: boolean;
}

const Header = ({ isB2b = false }: IHeaderProps) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const role = currentMemberQuery.data?.role as UserRole;
  const { isSM, isLG } = useThemeMediaQueries();

  return (
    <Container isB2b={isB2b}>
      <Wrapper>
        <a href={LANDING_URL} target="_blank" data-test-id="header-logo-link">
          {isB2b ? (
            <Image
              layout="fixed"
              src={`${SVG_PATH}logos/shadow-manager.svg`}
              alt={t('header.logoBusinessAlt', 'Shadow PC Enterprise')}
              // todo integration: after merge shopB2B, use shared components to set sizes
              width={isSM ? '120px' : '160px'}
              height={isSM ? '41px' : '65px'}
            />
          ) : (
            <Image
              layout="fixed"
              src={`${SVG_PATH}logos/shadow-gradient.svg`}
              alt={t('header.logoAlt', 'Shadow')}
              // todo integration: after merge shopB2B, use shared components to set sizes
              width={isSM ? '65px' : '80px'}
              height={isSM ? '15px' : '18px'}
            />
          )}
        </a>
        <Links>
          {isLG ? (
            <HeaderAuthenticated isB2b={isB2b} role={role} />
          ) : (
            <BurgerMenu isB2b={isB2b} />
          )}
        </Links>
      </Wrapper>
    </Container>
  );
};

export default Header;
