import styled from '@emotion/styled';

interface IBurgerIconProps {
  isOpen: boolean;
  onClick: () => void;
}

const Container = styled.button`
  position: relative;
  width: 24px;
  height: 18px;
`;

const Line = styled.div<Pick<IBurgerIconProps, 'isOpen'>>`
  position: absolute;
  top: 0;
  width: 100%;
  height: 2px;
  background: black;
  border-radius: 8px;

  ${({ isOpen }) =>
    !isOpen &&
    `
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  `}

  :last-child {
    top: inherit;
    bottom: 0;
  }

  ${({ isOpen }) => isOpen && `transform: scale(0);`}
`;

const LineHidden = styled(Line)<Pick<IBurgerIconProps, 'isOpen'>>`
  ${({ isOpen }) => isOpen && `transform: scale(0);`}
`;

const LineAnimation = styled(Line)<
  Pick<IBurgerIconProps, 'isOpen'> & { rotate: number }
>`
  top: calc(50% - 1px);

  ${({ isOpen, rotate }) =>
    isOpen
      ? `
        transform: rotate(${rotate}deg);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      `
      : `transform: rotate(0deg);`}
`;

const BurgerButton = ({ isOpen = false, onClick }: IBurgerIconProps) => {
  return (
    <Container onClick={onClick}>
      <LineHidden isOpen={isOpen} />
      <LineAnimation isOpen={isOpen} rotate={-45} />
      <LineAnimation isOpen={isOpen} rotate={45} />
      <LineHidden isOpen={isOpen} />
    </Container>
  );
};

export default BurgerButton;
