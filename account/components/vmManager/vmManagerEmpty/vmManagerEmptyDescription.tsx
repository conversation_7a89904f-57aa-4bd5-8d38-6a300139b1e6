import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Typography } from 'shared-components';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  text-align: center;
`;

const VmManagerEmptyDescription = () => {
  const { t } = useTranslation();

  return (
    <Container>
      <Typography variant="heading-h5">
        {t(
          'businessManager.managerList.vm.empty.title',
          'Welcome on Shadow Business Manager!',
        )}
      </Typography>
      <Typography variant="body-sm">
        {t(
          'businessManager.managerList.vm.empty.description',
          'From here, you’ll be able to manage all your Shadow PCs Pro and invite new users to join your team. Create your first Shadow PC Pro and use its power to fulfill your tasks.',
        )}
      </Typography>
    </Container>
  );
};

export default VmManagerEmptyDescription;
