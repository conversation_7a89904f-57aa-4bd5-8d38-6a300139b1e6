import styled from '@emotion/styled';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { IMAGE_PATH } from 'utils';

import Paper from '@/components/ui/paper';
import CreateVmButton from '@/components/vmManager/createVmButton';
import VmManagerEmptyDescription from '@/components/vmManager/vmManagerEmpty/vmManagerEmptyDescription';

const Container = styled(Paper)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  gap: 40px;
`;
const ImageWrapper = styled.div`
  display: flex;
  justify-content: center;
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: center;
`;

export interface IVmManagerEmpty {
  isB2b: boolean;
}

const VmManagerEmpty = ({ isB2b }: IVmManagerEmpty) => {
  const { t } = useTranslation();

  return (
    <Container>
      <ImageWrapper>
        <Image
          src={`${IMAGE_PATH}shadow-vm.png`}
          alt={t(
            'businessManager.managerList.vm.empty.illustrationAlt',
            'Shadow PC Manager',
          )}
          width={272}
          height={120}
        />
      </ImageWrapper>
      <VmManagerEmptyDescription />
      <ButtonWrapper>
        <CreateVmButton
          isB2b={isB2b}
          label={t(
            'businessManager.managerList.vm.empty.createVmButtonLabel',
            'Create your first Shadow PC Pro',
          )}
        />
      </ButtonWrapper>
    </Container>
  );
};

export default VmManagerEmpty;
