import styled from '@emotion/styled';
import {
  GridColDef,
  GridEventListener,
  GridRenderCellParams,
} from '@mui/x-data-grid';
import { usePaginatedSubscriptions } from 'hooks';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import {
  Alert,
  LocalLoader,
  Typography,
  theme,
  useThemeMediaQueries,
} from 'shared-components';
import {
  IMember,
  ITag,
  NotificationState,
  ProductFamilyId,
  SubscriptionStatus,
} from 'types';
import { DATE_FORMAT_WITH_SLASH_BY_MARKET, formatDate, logError } from 'utils';

import { VmStatusDisplay } from '../vmStatusDisplay/vmStatusDisplay';

import { fetchMember } from '@/api/member';
import { fetchVmStatus } from '@/api/vdi';
import DataGrid from '@/components/ui/dataGrid/dataGrid';
import UserCard from '@/components/userCard/userCard';
import VmDrawer from '@/components/vmDrawer/vmDrawer';
import VmManagerEmpty from '@/components/vmManager/vmManagerEmpty/vmManagerEmpty';
import VmManagerOptions from '@/components/vmManager/vmManagerOptions';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import { IVmManager } from '@/types/adminManagers';
import {
  LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
  ManagerDataType,
} from '@/utils/constants';

const Container = styled.div`
  ${theme.breakpoints.down('md')} {
    .MuiDataGrid-columnHeader[data-field='createdAt'],
    .MuiDataGrid-cell[data-field='createdAt'] {
      display: none;
    }
  }
`;

const UserCardStyled = styled(UserCard)`
  position: absolute;
  z-index: 2;
`;

const VmManager = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { market } = useConfig();
  const { user } = useAuth();

  const [selectedTagsOptions, setSelectedTagsOptions] = useState<string[]>([]);

  const [openDrawer, setOpenDrawer] = useState(false);
  const [selectedVmId, setSelectedVmId] = useState<IVmManager['id']>();

  const [vmRows, setVmRows] = useState<IVmManager[]>([]);
  const [isVmRowsQueryLoading, setIsVmRowsQueryLoading] = useState(true);

  const selectedVmData = vmRows.find(vm => vm.id === selectedVmId);

  const [pageSize, setPageSize] = useState<number>(
    LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE,
  );
  const [pageNumber, setPageNumber] = useState(0);
  const usePaginatedSubscriptionQuery = usePaginatedSubscriptions(
    false,
    pageNumber,
    pageSize,
    selectedTagsOptions,
    ProductFamilyId.CLOUDPC,
  );

  const [rowCount, setRowCount] = useState(
    usePaginatedSubscriptionQuery.data?.total_items,
  );

  const { isMD } = useThemeMediaQueries();
  const currentMemberQuery = useCurrentMember();

  const availableTagsList = currentMemberQuery.data?.available_tags as ITag[];
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  // This useEffect is recommended by Mui to handle cases where total_items is undefined with paginationMode="server" props
  useEffect(() => {
    setRowCount((previousRowCount: number | undefined) =>
      usePaginatedSubscriptionQuery.data?.total_items !== undefined
        ? usePaginatedSubscriptionQuery.data?.total_items
        : previousRowCount,
    );
  }, [usePaginatedSubscriptionQuery.data]);

  useEffect(() => {
    const asyncFunc = async () => {
      if (
        usePaginatedSubscriptionQuery.isSuccess &&
        usePaginatedSubscriptionQuery.data &&
        Array.isArray(usePaginatedSubscriptionQuery.data.items)
      ) {
        setVmRows(
          await Promise.all(
            usePaginatedSubscriptionQuery.data.items
              .filter(
                subscription =>
                  subscription.product_family_id === ProductFamilyId.CLOUDPC,
              )
              .map(async subscription => {
                let fetchedUser: IMember | undefined = undefined;
                const { id, created_at, status, user_id, items, name, tags } =
                  subscription;

                if (user_id) {
                  fetchedUser = await fetchMember(
                    user?.access_token as string,
                    user_id,
                  );
                }

                const getVmStatus = async () => {
                  try {
                    return await fetchVmStatus(
                      user?.access_token as string,
                      id,
                    );
                  } catch (error) {
                    logError('fetchVmStatus in vmManager', error);
                    return undefined;
                  }
                };

                const vmStatus =
                  status === SubscriptionStatus.FUTURE
                    ? undefined
                    : await getVmStatus();

                const foundPlan = items.find(
                  ({ item_type }) => item_type === 'plan',
                );

                return {
                  id,
                  createdAt: created_at,
                  subscriptionStatus: status,
                  isVmRunning: vmStatus?.running ? vmStatus.running : false,
                  isVmInMaintenanceMode: vmStatus?.maintenance
                    ? vmStatus.maintenance
                    : false,
                  isVmOnHold: subscription.on_hold,
                  datacenter: vmStatus?.datacenter,
                  type: foundPlan?.name || '',
                  user: fetchedUser?.user || undefined,
                  name,
                  subscription,
                  tags,
                } as IVmManager;
              }),
          ),
        );
        setIsVmRowsQueryLoading(false);
      }
    };
    asyncFunc();
  }, [
    usePaginatedSubscriptionQuery.data,
    usePaginatedSubscriptionQuery.isSuccess,
    user?.access_token,
  ]);

  const closeDrawer = () => setOpenDrawer(false);

  const handleRowClick: GridEventListener<'rowClick'> = params => {
    setOpenDrawer(true);
    setSelectedVmId(params.row.id);
  };

  const renderVmName = (params: GridRenderCellParams) => (
    <Typography variant="body-xs" noWrap>
      {params.row.subscription.name || `VM #${params.row.subscription.id}`}
    </Typography>
  );

  const renderVmUser = (params: GridRenderCellParams) => {
    return params.row.user ? (
      <UserCardStyled
        onlyThumbnailDisplay
        data={{
          email: params.row.user.email,
          firstName: params.row.user.first_name,
          lastName: params.row.user.last_name,
        }}
      />
    ) : (
      <Typography variant="body-xs" noWrap>
        {t(
          'businessManager.managerList.vm.noUserAssigned',
          'No user is assigned',
        )}
      </Typography>
    );
  };

  const renderVmTags = (params: GridRenderCellParams) => (
    <Typography variant="body-sm" noWrap>
      {params.row.tags.map(
        (tag: ITag, key: number) =>
          `${tag.name}${key < params.row.tags.length - 1 ? ', ' : ''}`,
      )}
    </Typography>
  );

  const vmColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: t('businessManager.managerList.vm.heading.name', 'Name'),
      flex: isMD ? 0.12 : 0.18,
      headerAlign: 'center',
      align: 'center',
      renderCell: renderVmName,
    },
    {
      field: 'createdAt',
      type: 'date',
      headerName: t(
        'businessManager.managerList.vm.heading.created',
        'Created',
      ),
      flex: 0.12,
      headerAlign: 'center',
      align: 'center',
      renderCell: (cellValues: GridRenderCellParams) =>
        formatDate(
          cellValues.row.createdAt * 1000,
          DATE_FORMAT_WITH_SLASH_BY_MARKET[market],
        ),
    },
    {
      field: 'type',
      headerName: t('businessManager.managerList.vm.heading.type', 'Config'),
      flex: isMD ? 0.13 : 0.19,
      headerAlign: 'center',
      align: 'center',
      sortable: false,
      valueGetter: ({ value }) => t(`subscription.details.name.${value}`),
    },
    {
      field: 'user',
      headerName: t('businessManager.managerList.vm.heading.user', 'User'),
      width: 96,
      headerAlign: 'left',
      align: 'left',
      // valueGetter: params => params.row.email, // Kept for reference : valueGetter and renderCell are mutually exclusives
      // valueGetter: params => params.row.user?.email to check to have good sortering behavior
      renderCell: renderVmUser,
    },
    {
      field: 'tags',
      headerName: t('businessManager.managerList.vm.heading.tags', 'Tags'),
      flex: 0.25,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: renderVmTags,
    },
    {
      field: 'subscriptionStatus',
      headerName: t('businessManager.managerList.vm.heading.status', 'Status'),
      flex: 0.27,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: cellValues => (
        <VmStatusDisplay
          subscriptionStatus={cellValues.row.subscriptionStatus}
          isVmRunning={cellValues.row.isVmRunning}
          isVmInMaintenanceMode={cellValues.row.isVmInMaintenanceMode}
          isVmOnHold={cellValues.row.isVmOnHold}
        />
      ),
    },
  ];

  if (isVmRowsQueryLoading || currentMemberQuery.isLoading) {
    return <LocalLoader />;
  }

  const displayVmManagerEmpty =
    vmRows.length === 0 && selectedTagsOptions.length === 0;

  return (
    <>
      {displayVmManagerEmpty ? (
        <VmManagerEmpty isB2b={isB2b} />
      ) : (
        <>
          {router.query?.vm_created && (
            <Alert
              type={NotificationState.SUCCESS}
              title={t(
                'businessManager.vmDetails.created.notification.success.title',
                'Your Shadow PC Pro creation has started',
              )}
            >
              {t(
                'businessManager.vmDetails.created.notification.success.message',
                'The Shadow PC Pro is being setup, it can last up to 1 hour. You will receive an email as soon as your Shadow PC Pro will be available.',
              )}
            </Alert>
          )}
          <VmManagerOptions
            isB2b={isB2b}
            availableTagsList={availableTagsList}
            selectedTagsOptions={selectedTagsOptions}
            setSelectedTagsOptions={setSelectedTagsOptions}
          />
          <Container>
            <DataGrid
              onRowClick={handleRowClick}
              disableSelectionOnClick={false}
              columns={vmColumns}
              rows={vmRows}
              type={ManagerDataType.VMS}
              onPageNumberChange={setPageNumber}
              onPageSizeChange={setPageSize}
              paginationMode="server"
              rowCount={rowCount}
              loading={usePaginatedSubscriptionQuery.isLoading}
            />
          </Container>
          <VmDrawer
            vmData={selectedVmData}
            open={openDrawer}
            handleClose={closeDrawer}
          />
        </>
      )}
    </>
  );
};

export default VmManager;
