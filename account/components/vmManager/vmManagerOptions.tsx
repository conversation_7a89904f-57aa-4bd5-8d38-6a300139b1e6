import styled from '@emotion/styled';
import { useTranslation } from 'next-i18next';
import { Dispatch } from 'react';
import { theme } from 'shared-components';
import { ITag } from 'types';

import TagFilterSelect from '@/components/tagFilter/tagFilterSelect';
import CreateVmButton from '@/components/vmManager/createVmButton';

const Container = styled.div`
  gap: 24px;
  margin: 24px;

  ${theme.breakpoints.up('md')} {
    margin: 0 0 24px;
  }
`;

const Row = styled.div`
  display: flex;
  gap: 24px;
  flex-direction: column-reverse;
  justify-content: space-between;

  ${theme.breakpoints.up('md')} {
    margin: 0 0 24px;
    align-items: center;
    flex-direction: row;
  }
`;

const Filters = styled.div`
  display: flex;
  width: 100%;
  flex: 1;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;

  ${theme.breakpoints.up('md')} {
    width: auto;
    justify-content: start;
  }
`;

export interface IVmManagerOptions {
  isB2b: boolean;
  availableTagsList: ITag[];
  selectedTagsOptions: string[];
  setSelectedTagsOptions: Dispatch<string[]>;
}

const VmManagerOptions = ({
  isB2b,
  availableTagsList,
  selectedTagsOptions,
  setSelectedTagsOptions,
}: IVmManagerOptions) => {
  const { t } = useTranslation();

  return (
    <Container>
      <Row>
        <Filters>
          <TagFilterSelect
            filterName="tags"
            options={availableTagsList}
            selectedOptions={selectedTagsOptions}
            setSelectedOptions={setSelectedTagsOptions}
          />
        </Filters>
        <CreateVmButton
          isB2b={isB2b}
          label={t(
            'businessManager.managerList.vm.options.createVmButtonLabel',
            'New Shadow PC',
          )}
        />
      </Row>
    </Container>
  );
};

export default VmManagerOptions;
