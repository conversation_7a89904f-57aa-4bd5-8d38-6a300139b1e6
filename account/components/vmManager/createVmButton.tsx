import { useRouter } from 'next/router';
import { Button, ClickTracker, Icon } from 'shared-components';
import { Icons, ProductFamilyId } from 'types';

import { SHOP_URL } from '@/utils/constants';

interface ICreateVmButtonProps {
  isB2b: boolean;
  label: string;
}

const CreateVmButton = ({ label, isB2b }: ICreateVmButtonProps) => {
  const router = useRouter();
  const getNewSubscriptionUrl = `${SHOP_URL}b2c?familyId=${ProductFamilyId.CLOUDPC}`;

  const handleRedirect = () => {
    isB2b
      ? router.push('/redirect/shop/business/catalog')
      : (window.location.href = getNewSubscriptionUrl);
  };

  return (
    <ClickTracker
      eventPayload={{
        action: 'click',
        parameters: {
          event_category: 'my_account',
          event_label: 'buy_new_shadow',
        },
      }}
    >
      <Button
        data-test-id="create-vm-button"
        onClick={handleRedirect}
        color={isB2b ? 'black' : 'primary'}
        size="medium"
        startIcon={<Icon name={Icons.PLUS} width={16} height={16} />}
      >
        {label}
      </Button>
    </ClickTracker>
  );
};

export default CreateVmButton;
