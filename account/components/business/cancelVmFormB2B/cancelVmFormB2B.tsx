import styled from '@emotion/styled';
import {
  Accordion as Mu<PERSON><PERSON><PERSON>rdion,
  AccordionDetails as MuiAccordionDetails,
  AccordionSummary as MuiAccordionSummary,
} from '@mui/material';
import throttle from 'lodash/throttle';
import { useTranslation } from 'next-i18next';
import {
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { Controller } from 'react-hook-form';
import {
  Icon,
  Textarea,
  theme,
  Typography,
  useThemeMediaQueries,
} from 'shared-components';
import { Icons } from 'types';

import SubmitButton from '@/components/forms/submitButton/submitButton';
import InputCheckbox from '@/components/ui/form/inputCheckbox';
import useCancelVmForm from '@/hooks/form/useCancelVmForm';
import useCancellationReasons from '@/hooks/useCancellationReasons';
import type { IFormProps } from '@/types/forms';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;

  ${theme.breakpoints.up('md')} {
    margin-top: 40px;
  }
`;

const Accordion = styled(MuiAccordion)`
  box-shadow: inherit;
  &::before {
    background: none;
  }
`;

const AccordionDetails = styled(MuiAccordionDetails)`
  padding: 16px;
`;

const AccordionSummary = styled(MuiAccordionSummary, {
  shouldForwardProp: prop => prop !== 'isActive',
})<{ isActive?: boolean }>`
  padding: 8px 23px 8px 16px;
  min-height: 42px;
  border-radius: 8px;
  border: 1px solid transparent;
  background: ${theme.palette.secondary.main10};
  color: ${theme.palette.black.main75};

  .MuiAccordionSummary-content {
    margin: 0;
  }

  &.Mui-expanded {
    color: ${theme.palette.black.main};
    background-color: ${theme.palette.secondary.main25};
    border-color: ${theme.palette.secondary.main25};
    .expand-more {
      color: ${theme.palette.black.main};
    }
  }
`;

const CancelVmForm = ({
  onSelection = () => null,
  onSuccess = () => null,
  onError = () => null,
  hideSubmitButton = false,
  submitRef,
}: IFormProps) => {
  const { t } = useTranslation();
  const { isLG } = useThemeMediaQueries();

  const reasons = useCancellationReasons({ isB2b: true });
  const { onSubmit, watch, control, isSubmitting } = useCancelVmForm(
    onSuccess,
    onError,
  );
  const categoryKeys = useMemo(
    () =>
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      reasons.map(([key, values]) => key),
    [reasons],
  );

  const [expanded, setExpanded] = useState<string | false>(false);

  const watchReasons = watch(categoryKeys as any); // this also force rerender when a checkbox has been clicked
  const watchComment = watch('comment');

  const handlePanelChange =
    (panel: string) => (event: SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  const handleSubmit = useCallback(
    async (e: SyntheticEvent) => {
      e.preventDefault();
      await onSubmit();
    },
    [onSubmit],
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(
    throttle(
      () => onSelection({ reasons: watchReasons, comment: watchComment }),
      200,
    ),
    [watchReasons, watchComment],
  );

  return (
    <Form onSubmit={handleSubmit}>
      {reasons.map(([reasonKey, reasonData]) => (
        <InputCheckbox
          id={reasonKey}
          dataTestId={`b2b-cancelSubscription-reason-${reasonKey}`}
          key={reasonKey}
          hasBackground
          checkboxSize={isLG ? 'large' : 'medium'}
          control={control}
          label={reasonData.title}
          name={reasonKey}
        />
      ))}
      <Accordion
        disableGutters
        expanded={expanded === 'panel-comment'}
        onChange={handlePanelChange('panel-comment')}
      >
        <AccordionSummary
          aria-controls={`panel-comment-content`}
          id={`panel-comment-header`}
          isActive={!!watchComment}
          expandIcon={<Icon name={Icons.CHEVRON_DOWN} width={12} />}
        >
          <Typography variant={isLG ? 'body-sm-regular' : 'body-xs-regular'}>
            {t('subscription.cancelVm.otherReason.title', 'Other')}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Controller
            name="comment"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Textarea
                {...field}
                aria-label="comment"
                id="b2b-cancelSubscription-comment-input"
                data-test-id="b2b-cancelSubscription-comment-input"
                rows={7}
                placeholder={t(
                  'subscription.cancelVm.otherReason.placeholder',
                  'Please, describe the issue you had',
                )}
                error={error?.message}
              />
            )}
          />
        </AccordionDetails>
      </Accordion>
      <SubmitButton
        ref={submitRef}
        loading={isSubmitting}
        hideSubmitButton={hideSubmitButton}
      >
        {t('global.continue', 'Continue', { ns: 'common' })}
      </SubmitButton>
    </Form>
  );
};

export default CancelVmForm;
