import styled from '@emotion/styled';
import { Trans, useTranslation } from 'next-i18next';
import { theme, Typography } from 'shared-components';
import { Locale } from 'types';

import SupportForm from '@/components/forms/supportForm/supportForm';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import { useConfig } from '@/hooks/store/useConfig';

const Description = styled(Typography)`
  margin-bottom: 24px;
  ${theme.breakpoints.up('lg')} {
    margin-bottom: 40px;
  }
`;

const Contact = () => {
  const { t } = useTranslation();
  const { locale } = useConfig();

  return (
    <Paper>
      <PaperTitle>
        {t('support.contactUs.title', 'Contact the support team')}
      </PaperTitle>
      {locale === Locale.FR_CA && (
        <Description variant="body-sm">
          {t(
            'support.contactUs.frenchCanadaDisclaimer',
            "Notre support francophone est actuellement basé en France. Les horaires d'ouvertures sont de 9h à minuit (heure de Paris). <PERSON><PERSON>, le temps de réponse pourrait être plus élevé dû au décalage horaire.",
          )}
        </Description>
      )}
      <Description variant="body-sm">
        <Trans i18nKey={'support.contactUs.description'}>
          You have a technical issue? A technical question? From the form below,
          you can submit a ticket to our technical support who will answer you
          as soon as possible.
          <br />
          Select a topic to begin:
        </Trans>
      </Description>
      <SupportForm />
    </Paper>
  );
};

export default Contact;
