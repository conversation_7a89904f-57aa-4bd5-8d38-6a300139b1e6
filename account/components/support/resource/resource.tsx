import { Trans, useTranslation } from 'next-i18next';
import { GlobalLoader, Typography } from 'shared-components';

import ResourcesList from '@/components/support/resource/resourceList';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import { useCurrentMember } from '@/hooks/member/useMember';
import { IResourceItem } from '@/types/support';

interface IResourceProps {
  items: IResourceItem[];
}

const Resource = ({ items }: IResourceProps) => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();

  if (currentMemberQuery.isLoading) {
    return <GlobalLoader />;
  }

  return (
    <Paper>
      <PaperTitle>{t('support.ressources.title', 'Get Started')}</PaperTitle>
      <Typography variant="body-sm">
        {t(
          'support.ressources.description',
          'Everything you need to know when getting started with <PERSON>. Check out these guides first to get the most from your Shadow experience.',
        )}
      </Typography>
      <Typography variant="body-sm">
        <Trans
          i18nKey="support.ressources.userId"
          defaults="For information, your id is: {{uid}}"
          values={{ uid: currentMemberQuery.data?.user?.id }}
        />
      </Typography>
      <ResourcesList items={items} />
    </Paper>
  );
};

export default Resource;
