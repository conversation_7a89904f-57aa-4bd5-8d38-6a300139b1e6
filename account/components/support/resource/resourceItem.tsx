import styled from '@emotion/styled';
import { Icon, theme, Typography } from 'shared-components';

import { IResourceItem } from '@/types/support';

interface IResourceItemProps {
  item: IResourceItem;
}

const Link = styled.a`
  display: block;
  padding: 16px;
  text-decoration: none;
  flex: 0.25;
  border: 1px solid ${theme.palette.primary.main10};
  border-radius: ${theme.shape.borderRadius}px;

  :hover {
    background: ${theme.palette.primary.main10};
  }
`;

const Wrapper = styled.div`
  display: flex;
  padding-bottom: 16px;
  align-items: center;
`;

const IconStyled = styled(Icon)`
  margin-right: 16px;
`;

const ResourceItem = ({ item }: IResourceItemProps) => {
  return (
    <Link href={item.href} title={item.title} target="_blank">
      <Wrapper>
        <IconStyled name={item.icon.name} width={24} color={item.icon.color} />
        <Typography variant="body-sm-regular" color="primary">
          {item.title}
        </Typography>
      </Wrapper>
      <Typography variant="body-xs">{item.description}</Typography>
    </Link>
  );
};

export default ResourceItem;
