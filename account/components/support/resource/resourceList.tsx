import styled from '@emotion/styled';
import { theme } from 'shared-components';
import { IMemberDetails } from 'types';

import ResourceItem from '@/components/support/resource/resourceItem';
import { useCurrentMember } from '@/hooks/member/useMember';
import { IResourceItem } from '@/types/support';

const Container = styled.nav<{ isB2b: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;

  ${theme.breakpoints.up('md')} {
    flex-direction: row;
    gap: 10px;
    margin-top: 40px;
  }

  ${({ isB2b }) =>
    isB2b &&
    `
      ${theme.breakpoints.up('md')} {
        gap: 24px;
      }
    `}
`;

interface IResourceListProps {
  items: IResourceItem[];
}
const ResourceList = ({ items }: IResourceListProps) => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];

  return (
    <Container isB2b={isB2b}>
      {items.map((item, index) => (
        <ResourceItem item={item} key={index} />
      ))}
    </Container>
  );
};

export default ResourceList;
