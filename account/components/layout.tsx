import styled from '@emotion/styled';
import type { ReactNode } from 'react';
import { theme } from 'shared-components';

import Footer from '@/components/footer/footer';
import Header from '@/components/header/header';
import InfoBanner from '@/components/infoBanner/infoBanner';
import Sidebar from '@/components/sidebar';
import { useCurrentMember } from '@/hooks/member/useMember';

interface ILayoutProps {
  children: ReactNode;
  isB2b?: boolean;
}

const LayoutStyled = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  justify-content: space-between;
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  justify-content: center;
  width: 100%;

  ${theme.breakpoints.only('md')} {
    padding: 64px 40px 0;
  }
  ${theme.breakpoints.up('lg')} {
    padding: 64px;
  }
`;

const Wrapper = styled.div`
  display: flex;
  flex: 1;
  box-sizing: content-box;
  max-width: ${theme.container.maxWidth.lg};

  ${theme.breakpoints.down('lg')} {
    flex-direction: column;
    width: 100%;
  }

  ${theme.breakpoints.up('lg')} {
    column-gap: 80px;
    flex-direction: row;
    align-items: start;
  }
`;

const Content = styled.div`
  flex: 1 1;
  ${theme.breakpoints.down('md')} {
    order: 1;
  }
`;

const Layout = ({ children }: ILayoutProps) => {
  const currentMemberQuery = useCurrentMember();
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  return (
    <LayoutStyled>
      <Header isB2b={isB2b} />
      <Container>
        <Wrapper>
          <Sidebar isB2b={isB2b} />
          <Content>
            <InfoBanner isB2b={isB2b} />
            {children}
          </Content>
        </Wrapper>
      </Container>
      <Footer isB2b={isB2b} />
    </LayoutStyled>
  );
};

export default Layout;
