import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { trackEvent } from 'utils';

import { useCurrentMember } from '@/hooks/member/useMember';
import { ROUTES_PATH_TO_PAGENAME_MAP } from '@/utils/constants';

const PageViewTracking: React.FunctionComponent = () => {
  const router = useRouter();

  const currentMemberQuery = useCurrentMember();
  const user = currentMemberQuery.data?.user;

  // Handle trackEvent 'page_view' on completed page route change
  useEffect(() => {
    const handleRouteChange = () => {
      trackEvent('page_view', {
        page_location: window.location.href,
        page_title: ROUTES_PATH_TO_PAGENAME_MAP[router.route] ?? document.title,
        pageName: ROUTES_PATH_TO_PAGENAME_MAP[router.route] ?? document.title,
        uri: window.location.pathname,
        userId: user?.id,
      });
    };

    router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router, user?.id]);

  return null;
};

export default PageViewTracking;
