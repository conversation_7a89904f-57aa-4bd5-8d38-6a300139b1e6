import styled from '@emotion/styled';
import { useTranslation, Trans } from 'next-i18next';
import { useState } from 'react';
import { Icon, LocalLoader, theme, Typography } from 'shared-components';
import { Icons } from 'types';
import { logError } from 'utils';

import Link from '@/components/ui/link';
import NextLink from '@/components/ui/nextLink';
import { useIsSubscriptionOnHold } from '@/hooks/subscription/useSubscription';
import { HasUnpaidInvoiceResult, useRetryPayment } from '@/hooks/user/useUser';
import { ROUTES_PATH } from '@/utils/constants';

const ContentContainer = styled.div`
  padding-left: 30px;
`;

interface IRetryPaymentProps {
  paymentDueInfo: HasUnpaidInvoiceResult;
  onLoading: (loading: boolean) => void;
  onShowRetryPaymentSuccess: () => void;
  isB2b: boolean;
}

const RetryPayment = ({
  paymentDueInfo,
  onLoading,
  onShowRetryPaymentSuccess,
  isB2b,
}: IRetryPaymentProps) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [showRetryPaymentSuccess, setShowRetryPaymentSuccess] = useState(false);
  const [showRetryPaymentError, setShowRetryPaymentError] = useState(false);
  const retryPayment = useRetryPayment();
  const { isCloudPcSubscriptionOnHold, isDriveSubscriptionOnHold } =
    useIsSubscriptionOnHold();

  const getProductName = () => {
    if (isCloudPcSubscriptionOnHold) {
      return t('infoBanner.unpaid.infos.productName.cloudpc', 'Shadow PC');
    } else if (isDriveSubscriptionOnHold) {
      return t(
        'infoBanner.unpaid.infos.productName.shadow-drive',
        'Shadow Drive',
      );
    }
  };

  const setIsRetryingPayment = (isRetryingPayment: boolean) => {
    setIsLoading(isRetryingPayment);
    onLoading(isRetryingPayment);
  };

  const onRetryPayment = () => {
    setIsRetryingPayment(true);

    retryPayment.mutate(
      {},
      {
        onSuccess: () => {
          setShowRetryPaymentSuccess(true);
          onShowRetryPaymentSuccess();
          setIsRetryingPayment(false);
        },
        onError: error => {
          logError('Error retrying payment for unpaid invoices', error);
          setShowRetryPaymentError(true);
          setIsRetryingPayment(false);
        },
      },
    );
  };

  const getRetryPaymentIconName = () => {
    if (showRetryPaymentSuccess) {
      return Icons.CHECK_CIRCLE;
    } else if (paymentDueInfo.hasUnpaidInvoice && !showRetryPaymentError) {
      return Icons.CROSS_CIRCLE;
    }

    return Icons.CROSS_TRIANGLE;
  };

  const getRetryPaymentIconColor = () => {
    if (showRetryPaymentSuccess) {
      return theme.palette.success.main;
    }

    return theme.palette.error.main;
  };

  if (isLoading) {
    return <LocalLoader />;
  }

  return (
    <>
      <div>
        <Icon
          name={getRetryPaymentIconName()}
          color={getRetryPaymentIconColor()}
          width={24}
          height={24}
        />
      </div>
      <ContentContainer>
        {(showRetryPaymentError || showRetryPaymentSuccess) && (
          <div>
            <Typography variant="body-md-regular">
              {showRetryPaymentError && t('infoBanner.unpaid.error.title')}
              {showRetryPaymentSuccess && t('infoBanner.unpaid.success.title')}
            </Typography>
          </div>
        )}
        <Typography variant="body-sm-regular">
          {paymentDueInfo.hasUnpaidInvoice && !showRetryPaymentSuccess && (
            <>
              {showRetryPaymentError ? (
                <Trans
                  i18nKey="infoBanner.unpaid.error.content"
                  components={[
                    <NextLink
                      href={`${ROUTES_PATH.BILLING}#billing`}
                      variant={'body-sm-link'}
                    />,
                  ]}
                />
              ) : (
                <Trans
                  i18nKey={
                    isB2b
                      ? 'infoBanner.unpaid.infos.contentBusiness'
                      : 'infoBanner.unpaid.infos.content'
                  }
                  values={{
                    productName: getProductName(),
                    daysBeforeTermination: paymentDueInfo.daysBeforeCancel,
                  }}
                  components={[
                    // @ts-expect-error children are injected by Trans
                    <Link
                      type="button"
                      onClick={onRetryPayment}
                      variant="label-sm-regular-underline"
                    />,
                    <NextLink
                      href={`${ROUTES_PATH.BILLING}#billing`}
                      variant={'body-sm-link'}
                    />,
                  ]}
                />
              )}
            </>
          )}
          {showRetryPaymentSuccess && t('infoBanner.unpaid.success.content')}
        </Typography>
      </ContentContainer>
    </>
  );
};

export default RetryPayment;
