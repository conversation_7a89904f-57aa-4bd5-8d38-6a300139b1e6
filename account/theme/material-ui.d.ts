import React from 'react';

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xs: true;
    sm: false;
    md: true;
    lg: true;
    xl: false;
  }
}

declare module '@mui/material/styles/createPalette' {
  interface SimplePaletteColorOptions {
    main10?: string;
    main25?: string;
    main60?: string;
    main75?: string;
    main90?: string;
    light50?: string;
    gradient?: string;
    gradientLinear25?: string;
  }

  interface PaletteColor {
    main10?: string;
    main25?: string;
    main60?: string;
    main75?: string;
    main90?: string;
    light50?: string;
    gradient?: string;
    gradientLinear25?: string;
  }
  interface PaletteOptions {
    black: PaletteColorOptions;
    white: PaletteColorOptions;
    drive: PaletteColorOptions;
  }

  interface Palette {
    black: PaletteColor;
    white: PaletteColor;
    drive: PaletteColor;
  }
}

declare module '@mui/material/styles' {
  interface TypographyVariants {
    'body-lg-regular': React.CSSProperties;
    'body-lg': React.CSSProperties;
    'body-md-light-italic': React.CSSProperties;
    'body-md-regular': React.CSSProperties;
    'body-md-regular-link': React.CSSProperties;
    'body-md': React.CSSProperties;
    'body-md-link': React.CSSProperties;
    'body-sm-link': React.CSSProperties;
    'body-sm-regular': React.CSSProperties;
    'body-sm-regular-link': React.CSSProperties;
    'body-sm': React.CSSProperties;
    'body-xl': React.CSSProperties;
    'body-xs-regular': React.CSSProperties;
    'body-xs': React.CSSProperties;
    'body-xs-link': React.CSSProperties;
    'heading-h1': React.CSSProperties;
    'heading-h2': React.CSSProperties;
    'heading-h3': React.CSSProperties;
    'heading-h4': React.CSSProperties;
    'heading-h5': React.CSSProperties;
    'heading-h6': React.CSSProperties;
    'label-lg-light': React.CSSProperties;
    'label-lg-link': React.CSSProperties;
    'label-lg-regular': React.CSSProperties;
    'label-lg': React.CSSProperties;
    'label-md-light': React.CSSProperties;
    'label-md-regular-link': React.CSSProperties;
    'label-md-regular': React.CSSProperties;
    'label-md': React.CSSProperties;
    'label-md-bold': React.CSSProperties;
    'label-md-caps': React.CSSProperties;
    'label-sm-bold': React.CSSProperties;
    'label-sm-light': React.CSSProperties;
    'label-sm-regular-underline': React.CSSProperties;
    'label-sm-regular': React.CSSProperties;
    'label-sm': React.CSSProperties;
    'label-sm-link': React.CSSProperties;
    'label-sm-caps': React.CSSProperties;
    'label-xl-light': React.CSSProperties;
    'label-xl-link': React.CSSProperties;
    'label-xl-regular': React.CSSProperties;
    'label-xl-caps': React.CSSProperties;
    'label-xl': React.CSSProperties;
    'label-xs-light': React.CSSProperties;
    'label-xs-link': React.CSSProperties;
    'label-xs': React.CSSProperties;
    'label-xs-regular': React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    'body-lg-regular'?: React.CSSProperties;
    'body-lg'?: React.CSSProperties;
    'body-md-light-italic'?: React.CSSProperties;
    'body-md-regular'?: React.CSSProperties;
    'body-md-regular-link'?: React.CSSProperties;
    'body-md'?: React.CSSProperties;
    'body-md-link'?: React.CSSProperties;
    'body-sm-link'?: React.CSSProperties;
    'body-sm-regular'?: React.CSSProperties;
    'body-sm-regular-link'?: React.CSSProperties;
    'body-sm'?: React.CSSProperties;
    'body-xl'?: React.CSSProperties;
    'body-xs-regular'?: React.CSSProperties;
    'body-xs'?: React.CSSProperties;
    'body-xs-link'?: React.CSSProperties;
    'heading-h1'?: React.CSSProperties;
    'heading-h2'?: React.CSSProperties;
    'heading-h3'?: React.CSSProperties;
    'heading-h4'?: React.CSSProperties;
    'heading-h5'?: React.CSSProperties;
    'heading-h6'?: React.CSSProperties;
    'label-lg-light'?: React.CSSProperties;
    'label-lg-link'?: React.CSSProperties;
    'label-lg'?: React.CSSProperties;
    'label-lg-regular'?: React.CSSProperties;
    'label-md-light'?: React.CSSProperties;
    'label-md-regular-link'?: React.CSSProperties;
    'label-md-regular'?: React.CSSProperties;
    'label-md'?: React.CSSProperties;
    'label-md-bold'?: React.CSSProperties;
    'label-md-caps'?: React.CSSProperties;
    'label-sm-bold'?: React.CSSProperties;
    'label-sm-light'?: React.CSSProperties;
    'label-sm-regular-underline'?: React.CSSProperties;
    'label-sm-regular'?: React.CSSProperties;
    'label-sm'?: React.CSSProperties;
    'label-sm-link'?: React.CSSProperties;
    'label-sm-caps'?: React.CSSProperties;
    'label-xl-light'?: React.CSSProperties;
    'label-xl-link'?: React.CSSProperties;
    'label-xl-regular'?: React.CSSProperties;
    'label-xl-caps'?: React.CSSProperties;
    'label-xl'?: React.CSSProperties;
    'label-xs-light'?: React.CSSProperties;
    'label-xs-link'?: React.CSSProperties;
    'label-xs'?: React.CSSProperties;
    'label-xs-regular'?: React.CSSProperties;
  }

  interface Theme {
    header: {
      height: {
        md: string;
        lg: string;
      };
    };
    footer: {
      height: {
        lg: string;
      };
    };
    animation: {
      timeout: number;
    };
    container: {
      maxWidth: {
        md: string;
        lg: string;
      };
    };
    logo: {
      xs: {
        width: string;
        height: string;
      };
      md: {
        width: string;
        height: string;
      };
    };
    businessLogo: {
      xs: {
        width: string;
        height: string;
      };
      md: {
        width: string;
        height: string;
      };
    };
  }

  interface ThemeOptions {
    header: {
      height: {
        md: string;
        lg: string;
      };
    };
    footer: {
      height: {
        lg: string;
      };
    };
    animation: {
      timeout: number;
    };
    container: {
      maxWidth: {
        md: string;
        lg: string;
      };
    };
    logo: {
      xs: {
        width: string;
        height: string;
      };
      md: {
        width: string;
        height: string;
      };
    };
    businessLogo: {
      xs: {
        width: string;
        height: string;
      };
      md: {
        width: string;
        height: string;
      };
    };
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    'body-lg-regular': true;
    'body-lg': true;
    'body-md-light-italic': true;
    'body-md-regular': true;
    'body-md-regular-link': true;
    'body-md': true;
    'body-md-link': true;
    'body-sm-link': true;
    'body-sm-regular': true;
    'body-sm-regular-link': true;
    'body-sm': true;
    'body-xl': true;
    'body-xs-regular': true;
    'body-xs': true;
    'body-xs-link': true;
    'heading-h1': true;
    'heading-h2': true;
    'heading-h3': true;
    'heading-h4': true;
    'heading-h5': true;
    'heading-h6': true;
    'label-lg-light': true;
    'label-lg-link': true;
    'label-lg-regular': true;
    'label-lg': true;
    'label-md-light': true;
    'label-md-regular-link': true;
    'label-md-regular': true;
    'label-md': true;
    'label-md-bold': true;
    'label-md-caps': true;
    'label-sm-bold': true;
    'label-sm-light': true;
    'label-sm-regular-underline': true;
    'label-sm-regular': true;
    'label-sm-caps': true;
    'label-sm': true;
    'label-sm-link': true;
    'label-xl-light': true;
    'label-xl-link': true;
    'label-xl-regular': true;
    'label-xl-caps': true;
    'label-xl': true;
    'label-xs-light': true;
    'label-xs-link': true;
    'label-xs': true;
    'label-xs-regular': true;
  }
}
