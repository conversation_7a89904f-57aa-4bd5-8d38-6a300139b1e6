const isProd = process.env.NODE_ENV === 'production';

// @todo find a clean way to import these constants/functions from utils/
const DEFAULT_LOCALE = 'en';

const currencyPerLocale = {
  en: 'EUR',
  'de-CH': 'CHF',
  'de-DE': 'EUR',
  'en-AT': 'EUR',
  'en-BE': 'EUR',
  'en-CA': 'CAD',
  'en-CH': 'CHF',
  'en-DE': 'EUR',
  'en-DK': 'DKK',
  'en-ES': 'EUR',
  'en-FR': 'EUR',
  'en-IT': 'EUR',
  'en-GB': 'GBP',
  'en-LU': 'EUR',
  'en-NL': 'EUR',
  'en-SE': 'SEK',
  'en-US': 'USD',
  'fr-FR': 'EUR',
  'fr-BE': 'EUR',
  'fr-LU': 'EUR',
  'fr-CH': 'CHF',
  'fr-CA': 'CAD',
  'de-AT': 'EUR',
  'es-ES': 'EUR',
  'it-IT': 'EUR',
  'sv-SE': 'SEK',
  'da-DK': 'DKK',
  'en-PL': 'EUR',
  'en-CZ': 'EUR',
  'en-HU': 'EUR',
  'en-IE': 'EUR',
  'en-SK': 'EUR',
  'en-HR': 'EUR',
  'en-SI': 'EUR',
  'en-PT': 'EUR',
};

function formatCurrency(value, lang = DEFAULT_LOCALE) {
  lang = lang === 'en' ? 'fr-FR' : lang;

  return new Intl.NumberFormat(lang, {
    style: 'currency',
    currency: currencyPerLocale[lang],
  }).format(value);
}

function formatStorageValue(value, lang = DEFAULT_LOCALE) {
  const baseUnit = lang === 'fr-FR' ? 'o' : 'B';

  if (value >= 1024) {
    return `${(value / 1024).toFixed(2)} T${baseUnit}`;
  }

  return `${value} G${baseUnit}`;
}

function formatMonth(month) {
  if (!month) {
    return '';
  }

  return month < 10 ? `0${month}` : month.toString();
}

module.exports = {
  i18n: {
    locales: [
      'da-DK',
      'de-AT',
      'de-CH',
      'de-DE',
      'en',
      'en-AT',
      'en-BE',
      'en-CA',
      'en-CH',
      'en-DK',
      'en-DE',
      'en-ES',
      'en-FR',
      'en-GB',
      'en-IT',
      'en-LU',
      'en-NL',
      'en-SE',
      'en-US',
      'es-ES',
      'fr-BE',
      'fr-CA',
      'fr-CH',
      'fr-FR',
      'fr-LU',
      'it-IT',
      'sv-SE',
      'en-PL',
      'en-CZ',
      'en-HU',
      'en-IE',
      'en-SK',
      'en-HR',
      'en-SI',
      'en-PT',
    ],
    defaultLocale: DEFAULT_LOCALE,
  },
  localeStructure: '{{ns}}-{{lng}}',
  ns: ['translation', 'common'],
  defaultNS: 'translation',
  fallbackLng: {
    'da-DK': ['en'],
    'de-CH': ['de-DE', 'en'],
    'de-AT': ['de-DE', 'en'],
    'en-BE': ['en'],
    'en-CA': ['en'],
    'en-CH': ['en'],
    'en-DA': ['en'],
    'en-DE': ['en'],
    'en-ES': ['en'],
    'en-FR': ['en'],
    'en-IT': ['en'],
    'en-LU': ['en'],
    'en-NL': ['en'],
    'en-SE': ['en'],
    'es-ES': ['en'],
    'fr-BE': ['fr-FR', 'en'],
    'fr-CA': ['fr-FR', 'en'],
    'fr-CH': ['fr-FR', 'en'],
    'fr-LU': ['fr-FR', 'en'],
    'it-IT': ['en'],
    'sv-SE': ['en'],
    'en-PL': ['en'],
    'en-CZ': ['en'],
    'en-HU': ['en'],
    'en-IE': ['en'],
    'en-SK': ['en'],
    'en-HR': ['en'],
    'en-SI': ['en'],
    'en-PT': ['en'],
    default: [DEFAULT_LOCALE],
  },
  // lowerCaseLng: true,
  interpolation: {
    escapeValue: false,
    skipOnVariables: false,
    format: function (value, format, lang) {
      if (value === undefined) {
        return '';
      }

      switch (format) {
        case 'currency':
          return formatCurrency(value, lang);
        case 'storageUnit':
          return formatStorageValue(value, lang);
        case 'month':
          return formatMonth(value);
        default:
          return value;
      }
    },
  },
  keySeparator: '.',
  nsSeparator: '/',
  react: {
    useSuspense: false,
  },
  redirect: true,
  returnEmptyString: false,
  reloadOnPrerender: !isProd,
  serializeConfig: false,
  debug: !isProd,
};
