import { useTranslation } from 'next-i18next';
import { Icons, Market } from 'types';

import { useConfig } from '@/hooks/store/useConfig';
import { INavigationArray } from '@/types/navigation';
import { RouteId } from '@/types/routes';
import {
  APPS_URL,
  BASE_URL,
  BLACKLISTED_COUNTRY_FOR_DRIVE,
  MANAGER_URL,
  ROUTES_PATH,
} from '@/utils/constants';

export type useNavigationParams = {
  hasAdminRights: boolean;
  isB2b: boolean;
  userCountry: Market;
};

export const useNavigation = ({
  hasAdminRights,
  isB2b,
  userCountry,
}: useNavigationParams) => {
  const { t } = useTranslation();
  const { locale } = useConfig();

  const userCountryIsNotBlacklisted =
    !BLACKLISTED_COUNTRY_FOR_DRIVE.includes(userCountry);

  const navigationArray: Array<INavigationArray> = [
    {
      id: 'pc',
      title: t('navigation.title.pc', 'Shadow PC'),
      icon: 'product-cloudpc-icon.svg',
      enabled: true,
      menuItem: [
        {
          id: RouteId.VM_MANAGER,
          route: `${MANAGER_URL}vm/list`,
          label: t('navigation.route.vms', 'All my PCs'),
          enabled: hasAdminRights,
        },
        {
          id: RouteId.MY_PC,
          route: `${MANAGER_URL}vm/list`,
          label: t('navigation.route.shadow', 'My Shadow PC'),
          enabled: !hasAdminRights,
        },
        {
          id: RouteId.USER_MANAGER,
          route: `${MANAGER_URL}user/list`,
          label: t('navigation.route.users', 'Users'),
          enabled: hasAdminRights,
        },
        {
          id: RouteId.GAMES,
          route: `${BASE_URL}${locale}/${ROUTES_PATH.GAMES}`,
          label: t('navigation.route.games', 'Game Store'),
          enabled: !isB2b && hasAdminRights,
        },
      ],
    },
    {
      id: 'shadow drive',
      title: t('navigation.title.drive', 'Shadow Drive'),
      icon: 'product-shadow-drive-icon.png',
      enabled: userCountryIsNotBlacklisted && hasAdminRights,
      menuItem: [
        {
          id: RouteId.DRIVE,
          route: `${BASE_URL}${locale}/${ROUTES_PATH.DRIVE}`,
          label: t('navigation.route.drive', 'Shadow Drive'),
          enabled: true,
        },
        {
          id: RouteId.DRIVE_GROUPS,
          route: `${BASE_URL}${locale}/${ROUTES_PATH.DRIVE_GROUPS}`,
          label: t('navigation.route.driveGroups', 'Shadow Drive Family'),
          enabled: true,
        },
      ],
    },
    {
      id: 'account',
      title: t('navigation.title.account', 'Account'),
      icon: 'account-icon.png',
      enabled: true,
      menuItem: [
        {
          id: RouteId.ACCOUNT,
          route: `${MANAGER_URL}account/personal`,
          label: t('navigation.route.account', 'Account'),
          enabled: true,
        },
        {
          id: RouteId.BILLING,
          route: `${MANAGER_URL}billing/details`,
          label: t('navigation.route.billing', 'Billing'),
          enabled: hasAdminRights,
        },
        {
          id: RouteId.SECURITY,
          route: `${MANAGER_URL}account/security`,
          label: t('navigation.route.Security', 'Security'),
          enabled: true,
        },
        {
          id: RouteId.SUPPORT,
          route: `${MANAGER_URL}support`,
          label: t('navigation.route.support', 'Support'),
          enabled: true,
        },
        {
          id: 'DOWNLOAD',
          href: APPS_URL,
          label: t('navigation.route.download', 'Download'),
          enabled: true,
          icon: Icons.EXTERNAL,
          external: true,
        },
      ],
    },
  ];

  return navigationArray;
};
