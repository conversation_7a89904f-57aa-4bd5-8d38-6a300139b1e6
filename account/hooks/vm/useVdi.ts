import { useMutation, useQuery } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';

import {
  fetchVmStatus,
  setVmDiskReset,
  setVmStop,
  updateVmReadyToPlay,
  deleteVmReadyToPlay,
} from '@/api/vdi';
import { IReadyToPlayVm, IVdiStatus } from '@/types/api';
import type { ApiError } from '@/types/api';

const useVmStatus = (subscriptionId: string) => {
  const { user } = useAuth();

  return useQuery<IVdiStatus | undefined, ApiError>(
    ['vmStatus'],
    () => fetchVmStatus(user?.access_token as string, subscriptionId),
    {
      enabled: false,
    },
  );
};

const useVmStop = () => {
  const { user } = useAuth();

  return useMutation<string | undefined, ApiError, string>(
    (subscriptionId: string) =>
      setVmStop(user?.access_token as string, subscriptionId),
  );
};

const useUpdateVmReadyToPlay = () => {
  const { user } = useAuth();

  return useMutation<string | undefined, ApiError, IReadyToPlayVm>(
    ({ readyToPlayConfig, timezone, subscriptionId }: IReadyToPlayVm) =>
      updateVmReadyToPlay(
        user?.access_token as string,
        subscriptionId,
        readyToPlayConfig,
        timezone,
      ),
  );
};

const useDeleteVmReadyToPlay = () => {
  const { user } = useAuth();

  return useMutation<null, ApiError, string>((subscriptionId: string) =>
    deleteVmReadyToPlay(user?.access_token as string, subscriptionId),
  );
};

const useVmDiskReset = () => {
  const { user } = useAuth();

  return useMutation<string | undefined, ApiError, string>(
    (subscriptionId: string) =>
      setVmDiskReset(user?.access_token as string, subscriptionId),
  );
};

export {
  useVmStatus,
  useVmDiskReset,
  useVmStop,
  useUpdateVmReadyToPlay,
  useDeleteVmReadyToPlay,
};
