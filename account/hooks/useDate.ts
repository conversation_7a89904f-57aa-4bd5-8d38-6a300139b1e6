import type { DateFormat, Market, ISubscription } from 'types';
import { SubscriptionStatus } from 'types';
import { formatDate, DATE_FORMAT_WITH_SLASH_BY_MARKET } from 'utils';

import { useConfig } from '@/hooks/store/useConfig';

export const useDate = () => {
  const { market } = useConfig();

  const getSubscriptionNextBillingDate = (
    subscription: ISubscription,
    format: Record<Market, DateFormat> = DATE_FORMAT_WITH_SLASH_BY_MARKET,
  ): string | null => {
    const timestampInSeconds = subscription?.next_billing_at;

    if (!timestampInSeconds) {
      return null;
    }

    return formatDate(timestampInSeconds * 1000, format[market]);
  };

  const getSubscriptionStartDate = (
    subscription: ISubscription,
    format: Record<Market, DateFormat> = DATE_FORMAT_WITH_SLASH_BY_MARKET,
  ): string | null => {
    let timestampInSeconds;

    switch (subscription.status) {
      case SubscriptionStatus.ACTIVE:
        timestampInSeconds = subscription.activated_at;
        break;
      case SubscriptionStatus.CANCELLED:
        timestampInSeconds = subscription.cancelled_at;
        break;
      default:
        return null;
    }

    if (!timestampInSeconds) {
      return null;
    }

    return formatDate(timestampInSeconds * 1000, format[market]);
  };

  const getSubscriptionScheduledChangesApplicationDate = (
    subscription: ISubscription,
    format: Record<Market, DateFormat> = DATE_FORMAT_WITH_SLASH_BY_MARKET,
  ): string | null => {
    const timestampInSeconds = subscription?.changes_scheduled_at;

    if (!timestampInSeconds) {
      return null;
    }

    return formatDate(timestampInSeconds * 1000, format[market]);
  };

  return {
    getSubscriptionNextBillingDate,
    getSubscriptionStartDate,
    getSubscriptionScheduledChangesApplicationDate,
  };
};
