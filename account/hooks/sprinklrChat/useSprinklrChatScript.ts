import { useEffect } from 'react';
import { IS_CLIENT_SIDE } from 'utils';

import { useCurrentMember } from '@/hooks/member/useMember';
import { useAuthentication } from '@/hooks/user/useAuthentication';

export const useSprinklrChatScript = () => {
  const { isAuthenticated } = useAuthentication();
  const currentMemberQuery = useCurrentMember();

  useEffect(() => {
    if (
      !isAuthenticated ||
      currentMemberQuery.isLoading ||
      currentMemberQuery.isError ||
      !currentMemberQuery.data ||
      !IS_CLIENT_SIDE
    ) {
      return;
    }

    const script = document.createElement('script');

    script.async = true;
    script.innerHTML = `
      window.sprChatSettings = window.sprChatSettings || {};
      window.sprChatSettings = {
        "appId":"${process.env.NEXT_PUBLIC_SPRINKLR_APP_ID}",
        "skin":"MODERN",
        "user": {
          "id": "${currentMemberQuery.data.user?.id}",
          "firstName": "${currentMemberQuery.data.user?.first_name}",
          "lastName": "${currentMemberQuery.data.user?.last_name}",
          "phoneNo": "${currentMemberQuery.data.user?.phone}",
          "email": "${currentMemberQuery.data.user?.email}"
        }
      };

      (function(){var t=window,e=t.sprChat,a=e&&!!e.loaded,n=document,r=function(){r.m(arguments)};r.q=[],r.m=function(t){r.q.push(t)},t.sprChat=a?e:r;var e2=t.sprTeamChat,r2=function(){r2.m(arguments)};r2.q=[],r2.m=function(t){r2.q.push(t)},t.sprTeamChat=e2?e2:r2;var o=function(){var e=n.createElement("script");
      e.type="text/javascript",e.async=!0,e.src="https://prod3-live-chat.sprinklr.com/api/livechat/handshake/widget/"+t.sprChatSettings.appId;
      
      e.onerror=function(){t.sprChat.loaded=!1},e.onload=function(){t.sprChat.loaded=!0};var a=n.getElementsByTagName("script")[0];a.parentNode.insertBefore(e,a)};"function"==typeof e?a?e("update",t.sprChatSettings):o():"loading"!==n.readyState?o():n.addEventListener("DOMContentLoaded",o)})()
    `;

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, [
    isAuthenticated,
    currentMemberQuery.data,
    currentMemberQuery.isLoading,
    currentMemberQuery.isError,
  ]);
};
