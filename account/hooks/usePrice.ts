import type { Currency } from 'types';

import { useConfig } from '@/hooks/store/useConfig';

export const usePrice = () => {
  const { currency, locale } = useConfig();

  const formatPrice = (
    amount: number,
    forcedCurrency?: Currency,
    minimumFractionDigits = 2,
  ): string => {
    return new Intl.NumberFormat(locale as unknown as string, {
      style: 'currency',
      currency: forcedCurrency ?? currency,
      minimumFractionDigits,
    }).format(amount / 100);
  };

  return {
    formatPrice,
  };
};
