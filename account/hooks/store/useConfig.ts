import type { Locale } from 'types';

import { ConfigState } from '@/types/stores';
import {
  CURRENCY_PER_LOCALE,
  DEFAULT_CURRENCY,
  DEFAULT_LANGUAGE,
  DEFAULT_LOCALE,
  DEFAULT_MARKET,
} from '@/utils/constants';
import { createPersistentStore } from '@/utils/stores';

export const useConfig: () => ConfigState = createPersistentStore<ConfigState>(
  set => ({
    market: DEFAULT_MARKET,
    language: DEFAULT_LANGUAGE,
    currency: DEFAULT_CURRENCY,
    locale: DEFAULT_LOCALE,
    didomiLanguage: null,
    inviteToken: undefined,
    inviteDriveGroup: undefined,

    setLocaleData: i18nLang => {
      if (i18nLang === 'en' || !i18nLang) {
        return;
      }

      const [lang, region] = i18nLang.split('-');

      return set(() => ({
        market: region?.toLowerCase(),
        language: lang,
        currency: CURRENCY_PER_LOCALE[i18nLang as Locale],
        locale: i18nLang,
        didomiLanguage: lang,
      }));
    },
    setInviteToken: inviteToken => set(() => ({ inviteToken })),
    setInviteDriveGroup: inviteDriveGroup => set(() => ({ inviteDriveGroup })),
  }),
);
