import { act, renderHook } from '@testing-library/react';
import { ISubscription } from 'types';
import { DATE_FORMAT_BY_MARKET } from 'utils';

import { useDate } from './useDate';

import mockedSubscriptions from '@/mocks/fixtures/getSubscriptions.json';
import { createWrapper } from '@/utils/tests/testQueryClient';

const activeSubscription = mockedSubscriptions.items[0] as ISubscription;
const cancelledSubscription = mockedSubscriptions.items[1] as ISubscription;

describe('getSubscriptionNextBillingDate', () => {
  it('returns next billing date', async () => {
    const { result } = renderHook(() => useDate(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const { getSubscriptionNextBillingDate } = result.current;

      const value = getSubscriptionNextBillingDate(
        activeSubscription,
        DATE_FORMAT_BY_MARKET,
      );

      expect(value).toEqual('November 24th, 2022');
    });
  });
});

describe('getSubscriptionStartDate', () => {
  it('returns active subscription start date', async () => {
    const { result } = renderHook(() => useDate(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const { getSubscriptionStartDate } = result.current;

      const value = getSubscriptionStartDate(
        activeSubscription,
        DATE_FORMAT_BY_MARKET,
      );

      expect(value).toEqual('October 24th, 2022');
    });
  });

  it('returns cancelled subscription start date', async () => {
    const { result } = renderHook(() => useDate(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const { getSubscriptionStartDate } = result.current;

      const value = getSubscriptionStartDate(
        cancelledSubscription,
        DATE_FORMAT_BY_MARKET,
      );

      expect(value).toEqual('November 24th, 2022');
    });
  });
});
