import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';

import { useSupportFormOptions } from './useSupportFormOptions';

import { Subject, SupportCase, SupportSelectOption } from '@/types/support';

interface ISupportFormChoicesList {
  supportChoicesList: any[];
}

export const useSupportFormChoicesList = (): ISupportFormChoicesList => {
  const { t } = useTranslation();
  const { subjectOptions, deviceOptions, driveDeviceOptions, accountOptions } =
    useSupportFormOptions();

  const supportChoicesList = useMemo(
    () => [
      {
        id: SupportCase.SubjectCase,
        label: t(
          'support.form.placeholder.selectSubject',
          'What can Shadow Support assist you with?',
        ),
        options: subjectOptions,
        nextToDisplay: {
          [Subject.CloudPc]: SupportCase.DeviceCase,
          [Subject.Drive]: SupportCase.DriveDeviceCase,
          [Subject.Account]: SupportCase.AccountCase,
          [SupportCase.OthersCase]: SupportSelectOption.MessageInput,
        },
        reset: [
          SupportSelectOption.Device,
          SupportSelectOption.DriveDevice,
          SupportSelectOption.Account,
          SupportSelectOption.MessageInput,
        ],
      },
      {
        id: SupportCase.DeviceCase,
        label: t(
          'support.form.placeholder.selectSystem',
          'Select an operating system',
        ),
        options: deviceOptions,
        nextToDisplay: {
          [SupportCase.AllCase]: SupportSelectOption.MessageInput,
        },
        reset: [SupportSelectOption.Account, SupportSelectOption.MessageInput],
      },
      {
        id: SupportCase.DriveDeviceCase,
        label: t(
          'support.form.placeholder.selectSystem',
          'Select an operating system',
        ),
        options: driveDeviceOptions,
        nextToDisplay: {
          [SupportCase.AllCase]: SupportSelectOption.MessageInput,
        },
        reset: [SupportSelectOption.Account, SupportSelectOption.MessageInput],
      },
      {
        id: SupportCase.AccountCase,
        label: t('support.form.placeholder.selectTopic', 'Select a topic'),
        options: accountOptions,
        nextToDisplay: {
          [SupportCase.AllCase]: SupportSelectOption.MessageInput,
        },
        reset: [],
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return { supportChoicesList };
};
