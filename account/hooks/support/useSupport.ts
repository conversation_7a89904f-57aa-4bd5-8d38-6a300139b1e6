import { useMutation } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';

import { sendSupportMessage, uploadMessageAttachment } from '@/api/support';
import { ISupportFormPayload, IFile } from '@/types/api';
import type { ApiError } from '@/types/api';

const useSendSupportMessage = () => {
  const { user } = useAuth();

  return useMutation<string | undefined, ApiError, ISupportFormPayload>(
    (payload: ISupportFormPayload) =>
      sendSupportMessage(user?.access_token as string, payload),
  );
};

const useUploadMessageAttachment = () => {
  const { user } = useAuth();

  return useMutation<Array<string> | undefined, ApiError, IFile[]>(
    (filesPayload: IFile[]) =>
      uploadMessageAttachment(user?.access_token as string, filesPayload),
  );
};

export { useSendSupportMessage, useUploadMessageAttachment };
