import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';

import { ISelectOption, Device, Account, Subject } from '@/types/support';

interface UseSupportFormOptions {
  subjectOptions: ISelectOption[];
  deviceOptions: ISelectOption[];
  driveDeviceOptions: ISelectOption[];
  accountOptions: ISelectOption[];
}

export const useSupportFormOptions = (): UseSupportFormOptions => {
  const { t } = useTranslation();

  const subjectOptions: ISelectOption[] = useMemo(
    () => [
      {
        value: Subject.VmIssue,
        displayedValue: t(
          'support.form.subjects.vmIssue',
          'My Shadow PC is not starting',
        ),
      },
      {
        value: Subject.CloudPc,
        displayedValue: t('support.form.subjects.cloudpc', 'Shadow PC'),
      },
      {
        value: Subject.Drive,
        displayedValue: t('support.form.subjects.drive', 'Shadow Drive'),
      },
      {
        value: Subject.Account,
        displayedValue: t(
          'support.form.subjects.account',
          'Account & Subscription',
        ),
      },
    ],
    [t],
  );

  const driveDeviceOptions: ISelectOption[] = useMemo(
    () => [
      {
        value: Device.Windows,
        displayedValue: t('support.form.devices.windows', 'Windows'),
      },
      {
        value: Device.Mac,
        displayedValue: t('support.form.devices.mac', 'MacOS'),
      },
      {
        value: Device.IOs,
        displayedValue: t('support.form.devices.ios', 'iOS or iPadOS'),
      },
      {
        value: Device.Android,
        displayedValue: t('support.form.devices.android', 'Android'),
      },
    ],
    [t],
  );

  const deviceOptions: ISelectOption[] = useMemo(
    () => [
      {
        value: Device.Windows,
        displayedValue: t('support.form.devices.windows', 'Windows'),
      },
      {
        value: Device.Mac,
        displayedValue: t('support.form.devices.mac', 'MacOS'),
      },
      {
        value: Device.IOs,
        displayedValue: t('support.form.devices.ios', 'iOS or iPadOS'),
      },
      {
        value: Device.Android,
        displayedValue: t('support.form.devices.android', 'Android'),
      },
      {
        value: Device.Linux,
        displayedValue: t('support.form.devices.linux', 'Linux'),
      },
      {
        value: Device.Ghost,
        displayedValue: t('support.form.devices.ghost', 'Shadow Ghost'),
      },
      {
        value: Device.Box,
        displayedValue: t('support.form.devices.box', 'Shadow Box'),
      },
      {
        value: Device.WebRTC,
        displayedValue: t(
          'support.form.devices.webrtc',
          'Shadow PC in Browser',
        ),
      },
    ],
    [t],
  );

  const accountOptions: ISelectOption[] = useMemo(
    () => [
      {
        value: Account.PaymentIssues,
        displayedValue: t(
          'support.form.account.paymentIssues',
          'Payment issues',
        ),
      },
      {
        value: Account.UpdateInfo,
        displayedValue: t(
          'support.form.account.updateInfo',
          'Update account info',
        ),
      },
      {
        value: Account.ChangeSubscription,
        displayedValue: t(
          'support.form.account.changeSubscription',
          'Change subscription',
        ),
      },
      {
        value: Account.Referral,
        displayedValue: t('support.form.account.referral', 'Referral program'),
      },
      {
        value: Account.Unsubscribe,
        displayedValue: t(
          'support.form.account.unsubscribe',
          'Cancel subscription',
        ),
      },
    ],
    [t],
  );

  return {
    subjectOptions,
    deviceOptions,
    driveDeviceOptions,
    accountOptions,
  };
};
