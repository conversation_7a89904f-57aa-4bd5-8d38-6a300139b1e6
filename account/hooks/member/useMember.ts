import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { IMember, ITag, ProductFamilyId } from 'types';
import { APIError } from 'utils';

import {
  fetchCurrentMember,
  fetchMembers,
  inviteMember,
  resendInvitation,
  activateMember,
  deactivateMember,
  deleteMember,
  confirmMemberInvite,
  createTag,
  updateMemberTags,
} from '@/api/member';
import { IDeletePayload, IUpdateMemberTagsPayload } from '@/types/api';

const useMembers = (
  subscriptionFamilyId?: ProductFamilyId,
  tags?: string[],
) => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const membersParams = {
    ...(subscriptionFamilyId && {
      assignable_product_family_id: subscriptionFamilyId,
    }),
    ...(tags && tags.length > 0 && { tags: tags.join() }),
  };

  return useQuery<IMember[] | undefined, APIError>(
    ['getMembers', membersParams],
    () => fetchMembers(authToken as string, membersParams),
    {
      enabled: !!authToken,
      retry: false,
    },
  );
};

const useCurrentMember = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<IMember | undefined, APIError>(
    ['getCurrentMember'],
    () => fetchCurrentMember(authToken as string),
    {
      enabled: !!authToken,
    },
  );
};

const useInviteMember = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    (email: string) => inviteMember(authToken as string, email),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

const useConfirmMemberInvite = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    (inviteToken: string) =>
      confirmMemberInvite(inviteToken, authToken as string),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

const useResendInvitation = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    (email: string) => resendInvitation(authToken as string, email),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

const useActivateMember = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    (memberId: string) => activateMember(authToken as string, memberId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

const useDeactivateMember = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, string>(
    (memberId: string) => deactivateMember(authToken as string, memberId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

const useDeleteMember = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<string | undefined, APIError, IDeletePayload>(
    (deletePayload: IDeletePayload) =>
      deleteMember(authToken as string, deletePayload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

const useCreateTag = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<ITag[] | undefined, APIError, string>(
    (newTag: string) => createTag(authToken as string, newTag),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getCurrentMember']);
      },
    },
  );
};

const useUpdateMemberTags = () => {
  const { user } = useAuth();
  const authToken = user?.access_token;
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, IUpdateMemberTagsPayload>(
    ({ memberId, tags }: IUpdateMemberTagsPayload) =>
      updateMemberTags(authToken as string, memberId, tags),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['getMembers']);
      },
    },
  );
};

export {
  useMembers,
  useInviteMember,
  useResendInvitation,
  useActivateMember,
  useDeactivateMember,
  useCurrentMember,
  useDeleteMember,
  useConfirmMemberInvite,
  useCreateTag,
  useUpdateMemberTags,
};
