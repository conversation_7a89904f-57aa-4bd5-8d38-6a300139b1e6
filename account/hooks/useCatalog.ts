import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { REGION_PER_MARKET } from 'utils';

import { useCurrentMember } from './member/useMember';
import { useConfig } from './store/useConfig';

import { fetchCatalog } from '@/api/catalog';
import { MAX_RETRY } from '@/utils/constants';

export function useCatalog(subscriptionId?: string) {
  const authQuery = useAuth();
  const { currency, market } = useConfig();
  const currentMemberQuery = useCurrentMember();
  const authToken = authQuery.user?.access_token;
  const region = REGION_PER_MARKET[market];

  return useQuery(
    ['getCatalog', { subscriptionId }],
    () =>
      fetchCatalog(authToken, {
        currency: currency.toUpperCase(),
        market,
        region,
        subscriptionId,
        withStock: false,
        enabledForCheckout: false,
      }),
    {
      enabled: currentMemberQuery.isSuccess && !authQuery.isLoading,
      staleTime: 600 * 1000,
      retry: MAX_RETRY,
    },
  );
}
