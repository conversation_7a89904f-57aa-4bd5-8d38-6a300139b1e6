import { act, waitFor, renderHook } from '@testing-library/react';
import { HttpResponse, http } from 'msw';

import { useUpdateUserDetails } from './useUser';

import { server } from '@/jest.setup';
import mockedPutUser from '@/mocks/fixtures/putUser.json';
import { createWrapper } from '@/utils/tests/testQueryClient';

describe('Updating user information', () => {
  it('return data on success', async () => {
    const { result } = renderHook(() => useUpdateUserDetails(), {
      wrapper: createWrapper(),
    });

    const { mutateAsync } = result.current;

    await act(async () => {
      await mutateAsync(mockedPutUser);
    });

    await waitFor(() => result.current.isSuccess);

    expect(result.current.data).toBeDefined();
  });

  it('return an error on failure', async () => {
    server.use(http.post('*', () => HttpResponse.error()));

    const { result } = renderHook(() => useUpdateUserDetails(), {
      wrapper: createWrapper(),
    });

    const { mutateAsync } = result.current;
    const mockOnError = jest.fn();

    await act(async () => {
      await mutateAsync(mockedPutUser).catch(error => {
        mockOnError(error);
      });
    });

    await waitFor(() => result.current.isError);

    expect(result.current.error).toBeDefined();
  });
});
