import { useEffect } from 'react';
import { useAuth } from 'react-oidc-context';

export const useAuthentication = () => {
  const auth = useAuth();

  useEffect(() => {
    window.__shadow_auth__ = {
      removeUser: () => auth.removeUser(),
    };
  }, [auth]);

  useEffect(() => {
    return auth.events.addAccessTokenExpired(() => {
      void auth.signoutRedirect();
    });
  });

  return auth;
};
