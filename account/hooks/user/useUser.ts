import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { Currency, IPaymentMethod } from 'types';
import { logError } from 'utils';

import {
  updateUserDetails,
  fetchInvoices,
  fetchBillingDetails,
  updateBillingDetails,
  retryPayment,
  fetchPaymentDetails,
  fetchPersonalData,
  fetchPaymentDue,
  downloadCsvInvoice,
  fetchInvoiceDownload,
} from '@/api/user';
import {
  IUserDetails,
  IBillingDetails,
  IRetryPaymentPayload,
  IInvoice,
  IInvoiceDownload,
  IPersonalData,
} from '@/types/api';
import type { ApiError, IPaymentDue } from '@/types/api';

const useUpdateUserDetails = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, Partial<IUserDetails>>(
    (userDetails: Partial<IUserDetails>) =>
      updateUserDetails(user?.access_token as string, userDetails),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['companyDetails']);
      },
    },
  );
};

const useUpdateCompanyDetails = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, Partial<IBillingDetails>>(
    (companyDetails: Partial<IBillingDetails>) =>
      updateBillingDetails(user?.access_token as string, companyDetails),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['userDetails']);
      },
    },
  );
};

const useBillingDetails = () => {
  const { user } = useAuth();

  return useQuery<IBillingDetails | undefined, ApiError>(
    ['billingDetails'],
    () => fetchBillingDetails(user?.access_token as string),
  );
};

const useUpdateBillingDetails = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, IBillingDetails>(
    (billingDetails: IBillingDetails) =>
      updateBillingDetails(user?.access_token as string, billingDetails),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['billingDetails']);
      },
    },
  );
};

const usePaymentDetails = () => {
  const { user } = useAuth();

  return useQuery<IPaymentMethod[] | undefined, ApiError>(
    ['paymentDetails'],
    () => fetchPaymentDetails(user?.access_token as string),
  );
};

const useRetryPayment = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, IRetryPaymentPayload>(
    (retryPaymentPayload: IRetryPaymentPayload) =>
      retryPayment(user?.access_token as string, retryPaymentPayload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['billingDetails']);
      },
    },
  );
};

const useInvoices = () => {
  const { user } = useAuth();

  return useQuery<IInvoice[] | undefined, ApiError>(
    ['invoices'],
    () => {
      return fetchInvoices(user?.access_token as string);
    },
    {
      staleTime: 300 * 1000,
    },
  );
};

const useInvoiceDownloadLink = () => {
  const { user } = useAuth();

  return useMutation<IInvoiceDownload | undefined, ApiError, string>(
    (invoiceId: string) =>
      fetchInvoiceDownload(user?.access_token as string, invoiceId),
  );
};

const usePaymentDue = () => {
  const { user } = useAuth();

  return useQuery<IPaymentDue | undefined, ApiError>(
    ['paymentDue'],
    () => {
      return fetchPaymentDue(user?.access_token as string);
    },
    {
      staleTime: 300 * 1000,
    },
  );
};

export type HasUnpaidInvoiceResult =
  | {
      unpaidAmount: 0;
      hasUnpaidInvoice: false;
    }
  | {
      unpaidAmount: number;
      hasUnpaidInvoice: true;
      unpaidCurrency: Currency;
      daysBeforeCancel: number;
    };

const useHasUnpaidInvoice = (): HasUnpaidInvoiceResult => {
  const paymentDueQuery = usePaymentDue();

  if (!paymentDueQuery.data || paymentDueQuery.data.amount_due === 0) {
    return { unpaidAmount: 0, hasUnpaidInvoice: false };
  }

  if (paymentDueQuery.data.currency_code === '') {
    logError('Payment due services misses currency code');
    return { unpaidAmount: 0, hasUnpaidInvoice: false };
  }

  return {
    unpaidAmount: paymentDueQuery.data.amount_due,
    unpaidCurrency: paymentDueQuery.data.currency_code,
    daysBeforeCancel: paymentDueQuery.data.remaining_days_before_cancel,
    hasUnpaidInvoice: true,
  };
};

const useDownloadCsvInvoice = () => {
  const { user } = useAuth();

  return useMutation<Blob | undefined, ApiError, string>((invoiceId: string) =>
    downloadCsvInvoice(user?.access_token as string, invoiceId),
  );
};

const usePersonalData = () => {
  const { user } = useAuth();

  return useQuery<IPersonalData | undefined, ApiError>(
    ['personalData'],
    () => fetchPersonalData(user?.access_token as string),
    {
      enabled: false,
    },
  );
};

export {
  useUpdateUserDetails,
  useUpdateCompanyDetails,
  useBillingDetails,
  useUpdateBillingDetails,
  usePaymentDetails,
  useRetryPayment,
  useInvoices,
  useInvoiceDownloadLink,
  useDownloadCsvInvoice,
  useHasUnpaidInvoice,
  usePersonalData,
};
