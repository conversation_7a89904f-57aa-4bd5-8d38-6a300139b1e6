import { identity, pickBy } from 'lodash';
import { useForm } from 'react-hook-form';
import { logError } from 'utils';

import { ICancelVmForm } from '@/types/api';

function useCancelVmForm(
  onSuccess: (formValues: Partial<ICancelVmForm>) => void,
  onError: () => void,
) {
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    watch,
    reset,
    setValue,
    getValues,
    handleSubmit,
  } = useForm<ICancelVmForm>({
    mode: 'onSubmit',
  });

  const onSubmit = async (formValues: ICancelVmForm) => {
    try {
      // Do not call any api here, just send the value for later use
      onSuccess(pickBy(formValues, identity));
    } catch (e) {
      logError('useCancelVmForm onSubmit', e);
      onError();
    }
  };

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    watch,
    setValue,
    getValues,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useCancelVmForm;
