import { yupResolver } from '@hookform/resolvers/yup';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { getTimezoneName, logError } from 'utils';
import * as yup from 'yup';

import {
  useUpdateVmReadyToPlay,
  useDeleteVmReadyToPlay,
  useVmDiskReset,
  useVmStatus,
  useVmStop,
} from '@/hooks/vm/useVdi';
import { IResetVmForm } from '@/types/api';
import { VmConfiguration } from '@/types/vm';
import { LOCALE_KEYBOARD_PER_LANGUAGE } from '@/utils/constants';

function useResetVmForm(
  onSuccess: () => void,
  onError: () => void,
  subscriptionId: string,
  defaultValues?: IResetVmForm,
) {
  const { refetch: refetchVmStatus } = useVmStatus(subscriptionId);
  const vmStop = useVmStop();
  const updateReadyToPlay = useUpdateVmReadyToPlay();
  const deleteReadyToPlay = useDeleteVmReadyToPlay();
  const vmDiskReset = useVmDiskReset();
  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        configuration: yup.string(),
        language: yup.string(),
        keyboard: yup.string(),
      }),
    [],
  );
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    watch,
    reset,
    setValue,
    handleSubmit,
  } = useForm<IResetVmForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: IResetVmForm) => {
    try {
      if (!subscriptionId) {
        throw new Error('No subscription ID found');
      }

      const { configuration, ...readyToPlayConfig } = formValues;
      const { data } = await refetchVmStatus();

      if (data) {
        if (data.running) {
          await vmStop.mutateAsync(subscriptionId);
        }

        if (configuration === VmConfiguration.AUTOMATIC) {
          await updateReadyToPlay.mutateAsync({
            readyToPlayConfig: {
              ...readyToPlayConfig,
              language:
                LOCALE_KEYBOARD_PER_LANGUAGE[readyToPlayConfig.language],
            },
            timezone: getTimezoneName(),
            subscriptionId: subscriptionId,
          });
        } else if (configuration === VmConfiguration.MANUAL) {
          await deleteReadyToPlay.mutateAsync(subscriptionId);
        }

        await vmDiskReset.mutateAsync(subscriptionId);

        onSuccess();
      } else {
        throw new Error('Error fetching VM status');
      }
    } catch (e) {
      onError();
      logError('useResetVmForm onSubmit', e);
    }
  };

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    watch,
    setValue,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useResetVmForm;
