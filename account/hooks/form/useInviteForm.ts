import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { log, APIError } from 'utils';
import * as yup from 'yup';

import { useInviteMember } from '@/hooks/member/useMember';
import type { IInviteForm } from '@/types/api';

function useInviteForm(
  onSuccess?: (() => void) | null,
  onError?: ((error: string) => void) | null,
  defaultValues?: IInviteForm,
) {
  const { t } = useTranslation('common');
  const inviteMember = useInviteMember();

  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        invite_email: yup
          .string()
          .email(t('form.email.error.email', 'Field must be a valid email'))
          .required(t('form.email.error.required', 'Email is required')),
      }),
    [],
  );
  const {
    control,
    formState: { errors, isDirty, isSubmitting },
    reset,
    getValues,
    handleSubmit,
  } = useForm<IInviteForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: IInviteForm) => {
    const email = encodeURIComponent(formValues.invite_email);

    try {
      await inviteMember.mutateAsync(email, {
        onSuccess: () => {
          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: APIError) => {
          throw error.message;
        },
      });
    } catch (error) {
      log('Error on api request:', 'error', error);

      if (onError) {
        onError(error as string);
      }
    }
  };

  return {
    inviteControl: control,
    inviteErrors: errors,
    inviteIsDirty: isDirty,
    inviteIsSubmitting: isSubmitting,
    getInviteValues: getValues,
    inviteReset: reset,
    onInviteSubmit: handleSubmit(onSubmit),
  };
}

export default useInviteForm;
