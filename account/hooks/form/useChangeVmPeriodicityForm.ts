import { yupResolver } from '@hookform/resolvers/yup';
import { useModifySubscription } from 'hooks';
import { isEmpty } from 'lodash';
import { useForm } from 'react-hook-form';
import { ISubscription, OfferPeriodUnit } from 'types';
import {
  computeSubscriptionItemPricesWithTargetPeriodicity,
  logError,
} from 'utils';
import * as yup from 'yup';

import { useCatalog } from '../useCatalog';

import { IChangeVmPeriodicityForm } from '@/types/api';

function useChangeVmPeriodicityForm(
  onSuccess: () => void,
  onError: () => void,
  subscription: ISubscription,
  defaultValues?: IChangeVmPeriodicityForm,
) {
  const validationSchema = yup.object().shape({
    offerId: yup.string(),
  });

  const {
    control,
    formState: { errors, isSubmitting },
    reset,
    handleSubmit,
    watch,
  } = useForm<IChangeVmPeriodicityForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const catalogQuery = useCatalog(subscription.id);
  const catalog = catalogQuery.data;
  const modifySubscription = useModifySubscription(subscription.id);
  const selectedPeriodicity = watch('periodicity');
  const splitedSelectedPeriodicity = selectedPeriodicity.split('-');
  const selectedPeriod = Number(splitedSelectedPeriodicity[0]);
  const selectedPeriodUnit = splitedSelectedPeriodicity[1] as OfferPeriodUnit;

  const onSubmit = async (formValues: IChangeVmPeriodicityForm) => {
    try {
      const itemPricesPayload =
        computeSubscriptionItemPricesWithTargetPeriodicity(
          catalog,
          subscription,
          formValues.periodicity,
        );

      if (isEmpty(itemPricesPayload)) {
        throw new Error(
          `Empty item_prices payload return for subscription with id '${subscription.id}'`,
        );
      }

      await modifySubscription.mutateAsync({
        item_prices: itemPricesPayload,
        zipcode: null,
      });

      onSuccess();
    } catch (e) {
      logError('Update subscription error :', e);
      onError();
    }
  };

  return {
    control,
    errors,
    isSubmitting,
    selectedPeriodicity,
    selectedPeriod,
    selectedPeriodUnit,
    reset,
    watch,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useChangeVmPeriodicityForm;
