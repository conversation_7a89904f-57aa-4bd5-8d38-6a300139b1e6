import { yupResolver } from '@hookform/resolvers/yup';
import { useGetAccessCodeInformation, usePaginatedSubscriptions } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  ApplicationList,
  IMemberDetails,
  OldProduct,
  PowerProduct,
  ProductFamilyId,
  ProductId,
} from 'types';
import {
  FORM_VALIDATION_ACCESS_CODE_MIN_LENGTH,
  findSubscription,
  logError,
} from 'utils';
import * as yup from 'yup';

import { useCurrentMember } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import { useCatalog } from '@/hooks/useCatalog';
import { ICouponForm } from '@/types/api';
import { SHOP_URL } from '@/utils/constants';

function useCouponForm(
  onSuccess: () => void,
  onError: () => void,
  defaultValues?: ICouponForm,
  subscriptionId?: string,
) {
  const { t } = useTranslation();
  const getAccessCodeInformation = useGetAccessCodeInformation();
  const currentMemberQuery = useCurrentMember();
  const isB2b = currentMemberQuery.data?.user?.b2b as IMemberDetails['b2b'];
  const subscriptionsQuery = usePaginatedSubscriptions(isB2b);
  const catalogQuery = useCatalog(subscriptionId);
  const { locale } = useConfig();

  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        coupon: yup
          .string()
          .required(
            t('form.coupon.error.required', 'Coupon is required', {
              ns: 'common',
            }),
          )
          .min(
            FORM_VALIDATION_ACCESS_CODE_MIN_LENGTH,
            t(
              'form.coupon.error.mismatch',
              'Sorry the coupon you entered must be at least 3 characters long.',
              {
                ns: 'common',
              },
            ),
          ),
      }),
    [],
  );
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    reset,
    setValue,
    setError,
    handleSubmit,
  } = useForm<ICouponForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: ICouponForm) => {
    try {
      // We get the coupon information && check if the coupon is still valid
      await getAccessCodeInformation.mutateAsync(formValues?.coupon, {
        onSuccess: data => {
          // Invalid coupon answer from api
          if (!data?.exist) {
            setError('coupon', {
              message: t(
                'form.coupon.error.invalid',
                'This coupon code is invalid or expired.',
                {
                  ns: 'common',
                },
              ),
            });
          } else {
            const currentSubscription = findSubscription(
              subscriptionsQuery.data?.items,
              ProductFamilyId.CLOUDPC,
            );

            const currentSubscribedOffer =
              catalogQuery.data?.offers.byId[
                currentSubscription?.plan_id as string
              ];

            // If the user entered an access_code for an offer that he already has, we show an error
            if (currentSubscribedOffer?.product_id === data.item_id) {
              setError('coupon', {
                message: t(
                  'form.coupon.error.alreadySubscribed',
                  'You already have a subscription to an offer corresponding to this type of code.',
                  {
                    ns: 'common',
                  },
                ),
              });
              return;
            }

            const partialUrl = `${SHOP_URL}b2c?access_code=${formValues?.coupon}&subscription_id=${subscriptionId}&upgrade_from=${ApplicationList.ACCOUNT}&locale=${locale}&productType=`;
            switch (data.item_id) {
              // User submitted an Infinite coupon :
              case ProductId.CLOUDPC_OLD_B2C_INFINITE2021_C1:
                window.location.href = `${partialUrl}${OldProduct.INFINITE}`;
                return;

              // User submitted an Ultra coupon :
              case ProductId.CLOUDPC_OLD_B2C_ULTRA2021_C1:
                window.location.href = `${partialUrl}${OldProduct.ULTRA}`;
                return;

              // User submitted a Power Early Access coupon :
              case ProductId.CLOUDPC_B2C_POWER_A4000:
                window.location.href = `${partialUrl}${PowerProduct.POWER_EARLY_ACCESS}`;
                return;

              // User submitted a Power 2023 coupon :
              case ProductId.CLOUDPC_B2C_POWER2023:
                window.location.href = `${partialUrl}${PowerProduct.POWER_PLUS}`;
                return;

              default:
                return;
            }
          }
        },
        onError: error => {
          throw error;
        },
      });
    } catch (error: any) {
      logError(error.message, error);
      onError();
    }
    return;
  };

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useCouponForm;
