import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { IWordConfirmationForm } from '@/types/api';

function useWordConfirmationForm(
  onSuccess: () => void,
  wordToCompare: string,
  defaultValues?: IWordConfirmationForm,
) {
  const { t } = useTranslation();
  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        word: yup
          .string()
          .required(
            t('form.word.error.required', 'Word is required', {
              ns: 'common',
            }),
          )
          .matches(
            new RegExp(`\\b(${wordToCompare})\\b`, 'g'),
            t(
              'form.word.error.mismatch',
              'Sorry the word you entered is different from what we ask you to type. \nPlease verify your input and correct it.',
            ),
          ),
      }),
    [],
  );
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    reset,
    setValue,
    handleSubmit,
  } = useForm<IWordConfirmationForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSuccess),
  };
}

export default useWordConfirmationForm;
