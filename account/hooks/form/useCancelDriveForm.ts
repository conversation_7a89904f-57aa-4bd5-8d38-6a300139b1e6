import { useForm } from 'react-hook-form';
import { logError } from 'utils';

import { ICancelDriveForm } from '@/types/api';

function useCancelDriveForm(
  onSuccess: (formValues: ICancelDriveForm) => void,
  onError: () => void,
) {
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    watch,
    reset,
    setValue,
    getValues,
    handleSubmit,
  } = useForm<ICancelDriveForm>({
    mode: 'onSubmit',
  });

  const onSubmit = async (formValues: ICancelDriveForm) => {
    try {
      // Do not call any api here, just send the value for later use
      onSuccess(formValues);
    } catch (e) {
      logError('useCancelDriveForm onSubmit', e);
      onError();
    }
  };

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    watch,
    setValue,
    getValues,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useCancelDriveForm;
