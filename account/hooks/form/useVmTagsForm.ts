import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  FORM_VALIDATION_SPECIAL_CHAR,
  FORM_VALIDATION_ALPHANUMERIC_CHARACTER,
  FORM_VALIDATION_VM_TAG_MIN_LENGTH,
  FORM_VALIDATION_VM_TAG_MAX_LENGTH,
} from 'utils';
import * as yup from 'yup';

function useVmTagsForm(onSuccess: (formValues: any) => Promise<void>) {
  const { t } = useTranslation('common');

  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        tag: yup
          .string()
          .required(t('form.vmTags.error.required'))
          .min(
            FORM_VALIDATION_VM_TAG_MIN_LENGTH,
            t('form.vmTags.error.minLength', {
              minLength: FORM_VALIDATION_VM_TAG_MIN_LENGTH,
            }),
          )
          .max(
            FORM_VALIDATION_VM_TAG_MAX_LENGTH,
            t('form.vmTags.error.maxLength', {
              maxLength: FORM_VALIDATION_VM_TAG_MAX_LENGTH,
            }),
          )
          .test(
            'is-special-character',
            t('form.vmTags.error.specialChar'),
            str => !FORM_VALIDATION_SPECIAL_CHAR.test(str || ''),
          )
          .test(
            'contains-at-least-one-alphanumeric-character',
            t('form.vmTags.error.noAlphanumericChar'),
            str => FORM_VALIDATION_ALPHANUMERIC_CHARACTER.test(str || ''),
          ),
      }),
    [],
  );
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    reset,
    setValue,
    watch,
    handleSubmit,
  } = useForm({
    mode: 'onSubmit',
    resolver: yupResolver(validationSchema),
  });

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    setValue,
    watch,
    reset,
    onSubmit: handleSubmit(onSuccess),
  };
}

export default useVmTagsForm;
