import { yupResolver } from '@hookform/resolvers/yup';
import { useCreateDriveGroup } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { ICreateDriveGroupPayload } from 'types';
import {
  log,
  APIError,
  FORM_VALIDATION_DRIVE_GROUP_NAME_MAX_LENGTH,
  FORM_VALIDATION_DRIVE_GROUP_NAME_REGEX,
} from 'utils';
import * as yup from 'yup';

export const useCreateDriveGroupForm = (
  defaultValues?: ICreateDriveGroupPayload,
  onSuccess?: (() => void) | null,
  onError?: ((error: string) => void) | null,
) => {
  const { t } = useTranslation('common');
  const createDriveGroup = useCreateDriveGroup();

  const validationSchema = yup.object().shape({
    name: yup
      .string()
      .required(t('form.groupName.error.required', 'A group name is required'))
      .max(
        FORM_VALIDATION_DRIVE_GROUP_NAME_MAX_LENGTH,
        t(
          'form.groupName.error.maxLength',
          'Group name must be at most {{ maxLength }} characters',
          {
            maxLength: FORM_VALIDATION_DRIVE_GROUP_NAME_MAX_LENGTH,
          },
        ),
      )
      .matches(
        new RegExp(FORM_VALIDATION_DRIVE_GROUP_NAME_REGEX),
        t(
          'form.groupName.error.invalid',
          'Group name can only contain letters, numbers, spaces, underscores, and dashes.',
        ),
      ),
  });

  const {
    control,
    formState: { errors, isDirty, isSubmitting },
    reset,
    getValues,
    handleSubmit,
  } = useForm<ICreateDriveGroupPayload>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: ICreateDriveGroupPayload) => {
    try {
      await createDriveGroup.mutateAsync(formValues, {
        onSuccess: () => {
          onSuccess?.();
        },
        onError: (error: APIError) => {
          throw error.message;
        },
      });
    } catch (error) {
      log('Error on api request:', 'error', error);
      onError?.(error as string);
    }
  };

  return {
    control,
    errors,
    isDirty,
    isSubmitting,
    getValues,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
};
