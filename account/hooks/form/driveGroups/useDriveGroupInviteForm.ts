import { yupResolver } from '@hookform/resolvers/yup';
import { useCreateDriveGroupInvite } from 'hooks';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { log, APIError } from 'utils';
import * as yup from 'yup';

import type { IInviteForm } from '@/types/api';

export const useDriveGroupInviteForm = (
  driveGroupId: string,
  onSuccess?: (() => void) | null,
  onError?: ((error: string) => void) | null,
  defaultValues?: IInviteForm,
) => {
  const { t } = useTranslation();
  const createDriveGroupInvite = useCreateDriveGroupInvite(driveGroupId);

  const validationSchema = yup.object().shape({
    invite_email: yup
      .string()
      .email(
        t('form.email.error.email', 'Field must be a valid email', {
          ns: 'common',
        }),
      )
      .required(
        t('form.email.error.required', 'Email is required', { ns: 'common' }),
      ),
  });

  const {
    control,
    formState: { errors, isDirty, isSubmitting },
    reset,
    getValues,
    handleSubmit,
  } = useForm<IInviteForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: IInviteForm) => {
    const payload = {
      email: formValues.invite_email,
    };

    try {
      await createDriveGroupInvite.mutateAsync(payload, {
        onSuccess: () => {
          onSuccess?.();
        },
        onError: (error: APIError) => {
          if (error.code === 409) {
            throw t(
              'subscription.driveGroups.form.inviteMember.error.alreadyInvited',
              'This user is already invited to this group',
              { ns: 'translation' },
            );
          }

          throw error.message;
        },
      });
    } catch (error) {
      log('Error on sending drive group invite:', 'error', error);
      onError?.(error as string);
    }
  };

  return {
    control,
    errors,
    isDirty,
    isSubmitting,
    getValues,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
};
