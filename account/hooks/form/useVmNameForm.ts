import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  FORM_VALIDATION_SPECIAL_CHAR,
  FORM_VALIDATION_VM_NAME_MIN_LENGTH,
  FORM_VALIDATION_VM_NAME_MAX_LENGTH,
} from 'utils';
import * as yup from 'yup';

import { IVmNameForm } from '@/types/api';

function useVmNameForm(onSuccess: () => void, defaultValues?: IVmNameForm) {
  const { t } = useTranslation('common');

  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        name: yup
          .string()
          .required(t('form.name.error.required'))
          .min(
            FORM_VALIDATION_VM_NAME_MIN_LENGTH,
            t(
              'form.vmName.error.minLength',
              'PC name must be at least {{ minLength }} characters',
              {
                minLength: FORM_VALIDATION_VM_NAME_MIN_LENGTH,
              },
            ),
          )
          .max(
            FORM_VALIDATION_VM_NAME_MAX_LENGTH,
            t(
              'form.vmName.error.maxLength',
              'PC name must be at most {{ maxLength }} characters',
              {
                maxLength: FORM_VALIDATION_VM_NAME_MAX_LENGTH,
              },
            ),
          )
          .test(
            'is-special-character',
            t(
              'form.vmName.error.specialChar',
              'PC name must not use special characters',
            ),
            str => !FORM_VALIDATION_SPECIAL_CHAR.test(str || ''),
          ),
      }),
    [],
  );
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    reset,
    setValue,
    handleSubmit,
  } = useForm<IVmNameForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  return {
    control,
    errors,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSuccess),
  };
}

export default useVmNameForm;
