import { yupResolver } from '@hookform/resolvers/yup';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import {
  log,
  FORM_VALIDATION_ZIPCODE_MAX_LENGTH,
  FORM_VALIDATION_ZIPCODE_MIN_LENGTH,
} from 'utils';
import * as yup from 'yup';

import { useUpdateBillingDetails } from '../user/useUser';

import { IBillingForm, ApiError } from '@/types/api';

function useBillingForm(
  onSuccess?: (() => void) | null,
  onError?: ((error: string) => void) | null,
  defaultValues?: IBillingForm,
) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const updateBillingDetailsQuery = useUpdateBillingDetails();

  const validationSchema = yup.object().shape({
    first_name: yup.string().required(
      t('form.firstname.error.required', 'Firstname is required', {
        ns: 'common',
      }),
    ),
    last_name: yup.string().required(
      t('form.lastname.error.required', 'Lastname is required', {
        ns: 'common',
      }),
    ),
    address1: yup.string().required(
      t('form.address1.error.required', 'Address is required', {
        ns: 'common',
      }),
    ),
    city: yup
      .string()
      .required(
        t('form.city.error.required', 'City is required', { ns: 'common' }),
      ),
    zipcode: yup
      .string()
      .min(FORM_VALIDATION_ZIPCODE_MIN_LENGTH)
      .max(FORM_VALIDATION_ZIPCODE_MAX_LENGTH)
      .required(
        t('form.zipcode.error.required', 'Zipcode is required', {
          ns: 'common',
        }),
      ),
  });

  const {
    control,
    formState: { errors, isDirty, isSubmitting, isValid },
    reset,
    setValue,
    handleSubmit,
  } = useForm<IBillingForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: IBillingForm) => {
    try {
      await updateBillingDetailsQuery.mutateAsync(formValues, {
        onSuccess: () => {
          queryClient.invalidateQueries(['userDetails']);
          queryClient.refetchQueries(['getCurrentMember']);

          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: ApiError) => {
          throw error.message;
        },
      });
    } catch (error) {
      log('Error on api request:', 'error', error);

      if (onError) {
        onError(error as string);
      }
    }
  };

  return {
    control,
    errors,
    isDirty,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useBillingForm;
