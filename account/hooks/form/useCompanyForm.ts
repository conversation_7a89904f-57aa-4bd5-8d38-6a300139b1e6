import { yupResolver } from '@hookform/resolvers/yup';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  log,
  vatNumberValidationSchema,
  companyNameValidationSchema,
} from 'utils';
import * as yup from 'yup';

import { useConfig } from '../store/useConfig';

import { useUpdateCompanyDetails } from '@/hooks/user/useUser';
import { ICompanyForm } from '@/types/api';
import type { ApiError } from '@/types/api';

function useCompanyForm(
  onSuccess?: (() => void) | null,
  onError?: ((error: string) => void) | null,
  defaultValues?: ICompanyForm,
) {
  const { t } = useTranslation('common');
  const updateCompanyDetails = useUpdateCompanyDetails();
  const { market } = useConfig();
  const queryClient = useQueryClient();

  const validationSchema = useMemo(() => {
    return yup.object().shape({
      company: companyNameValidationSchema(t),
      vat_number: vatNumberValidationSchema(t, market),
    });
  }, [t, market]);

  const {
    control,
    formState: { errors, isDirty, isSubmitting, isValid },
    reset,
    setError,
    setValue,
    handleSubmit,
  } = useForm<ICompanyForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: ICompanyForm) => {
    try {
      await updateCompanyDetails.mutateAsync(formValues, {
        onSuccess: () => {
          queryClient.invalidateQueries(['companyDetails']);

          if (onSuccess) {
            onSuccess();
          }
        },
        onError: async (error: ApiError) => {
          const errorResponse = await error.response?.json();

          if (
            errorResponse?.code === 400 &&
            errorResponse.description?.error_param === 'vat_number'
          ) {
            setError('vat_number', {
              message: t(
                'form.vatNumber.error.invalid',
                'Your VAT number is invalid',
                {
                  ns: 'common',
                },
              ),
            });
          }

          throw error.message;
        },
      });
    } catch (error) {
      log('Error on api request:', 'error', error);

      if (onError) {
        onError(error as string);
      }
    }
  };

  return {
    control,
    errors,
    isDirty,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useCompanyForm;
