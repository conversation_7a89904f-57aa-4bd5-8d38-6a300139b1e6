import { yupResolver } from '@hookform/resolvers/yup';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { DateFormat } from 'types';
import {
  log,
  formatDate,
  parseDateString,
  subYears,
  FORM_VALIDATION_MIN_AGE,
} from 'utils';
import * as yup from 'yup';

import { useUpdateUserDetails } from '@/hooks/user/useUser';
import { IUserForm } from '@/types/api';
import type { ApiError } from '@/types/api';

function useUserForm(
  onSuccess?: (() => void) | null,
  onError?: ((error: string) => void) | null,
  defaultValues?: IUserForm,
) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const updateUserDetails = useUpdateUserDetails();

  const validationSchema = yup.object().shape({
    first_name: yup.string().required(
      t('form.firstName.error.required', 'First name is required', {
        ns: 'common',
      }),
    ),
    last_name: yup.string().required(
      t('form.lastName.error.required', 'Last name is required', {
        ns: 'common',
      }),
    ),
    phone: yup.string().nullable(),
    birthdate: yup
      .date()
      .nullable()
      .transform(parseDateString)
      .required(
        t('form.birthdate.error.required', 'Birthdate is required', {
          ns: 'common',
        }),
      )
      .max(
        subYears(FORM_VALIDATION_MIN_AGE),
        t('form.birthdate.error.invalid', 'You must be 15 or older'),
      ),
  });

  const {
    control,
    formState: { errors, isDirty, isSubmitting, isValid },
    reset,
    setValue,
    handleSubmit,
  } = useForm<IUserForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (formValues: IUserForm) => {
    formValues.birthdate = formatDate(
      formValues.birthdate,
      DateFormat.HYPHEN_US,
    );

    try {
      await updateUserDetails.mutateAsync(formValues, {
        onSuccess: () => {
          queryClient.invalidateQueries(['userDetails']);
          queryClient.refetchQueries(['getCurrentMember']);

          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: ApiError) => {
          throw error.message;
        },
      });
    } catch (error) {
      log('Error on api request:', 'error', error);

      if (onError) {
        onError(error as string);
      }
    }
  };

  return {
    control,
    errors,
    isDirty,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}

export default useUserForm;
