import { yupResolver } from '@hookform/resolvers/yup';
import compact from 'lodash/compact';
import find from 'lodash/find';
import { useTranslation } from 'next-i18next';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { logError } from 'utils';
import * as yup from 'yup';

import { useCurrentMember } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import {
  useSendSupportMessage,
  useUploadMessageAttachment,
} from '@/hooks/support/useSupport';
import { useSupportFormOptions } from '@/hooks/support/useSupportFormOptions';
import { ISupportForm } from '@/types/api';
import type { ISupportFormPayload, IFile } from '@/types/api';
import { ISelectOption } from '@/types/support';
import { convertBase64 } from '@/utils/file';

export function useSupportForm(
  gatherInformationForm: () => any, // @todo we have to rework these components so any here
  onSuccess: () => void,
  onError: () => void,
  defaultValues?: ISupportForm,
) {
  const { market } = useConfig();
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const sendSupportMessage = useSendSupportMessage();
  const uploadMessageAttachment = useUploadMessageAttachment();

  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        message: yup.string(),
      }),
    [],
  );

  const { subjectOptions, accountOptions, deviceOptions, driveDeviceOptions } =
    useSupportFormOptions();

  const {
    control,
    formState: { errors, isDirty, isValid, isSubmitting },
    reset,
    setValue,
    handleSubmit,
  } = useForm<ISupportForm>({
    mode: 'onSubmit',
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const getOptionDisplayedValue = (
    options: ISelectOption[],
    value: string,
  ): string => {
    return find(options, { value })?.displayedValue as string;
  };

  const createFilesPayload = async (files: File[] | []) => {
    if (files.length === 0) {
      return null;
    }

    const payload: IFile[] = [];

    for (const file of files) {
      const base64File = await convertBase64(file);

      payload.push({
        file: base64File as string,
        fileName: encodeURIComponent(file.name),
        market,
      });
    }

    return payload;
  };

  const onSubmit = async (formValues: ISupportForm) => {
    const { subject, account, device, driveDevice, files } =
      gatherInformationForm();
    // Generate the final message using the tags translations and the typed message
    const subjectLabel = getOptionDisplayedValue(subjectOptions, subject);
    const translatedTags = compact([
      getOptionDisplayedValue(accountOptions, account),
      getOptionDisplayedValue(deviceOptions, device),
      getOptionDisplayedValue(driveDeviceOptions, driveDevice),
    ]);
    const comment = translatedTags
      .join(' | ')
      .concat('\n')
      .concat(formValues.message);

    const payload: ISupportFormPayload = {
      requester: {
        name: `${currentMemberQuery.data?.user?.first_name} ${currentMemberQuery.data?.user?.last_name}`,
        email:
          currentMemberQuery.data?.user?.email ??
          t('support.unknownEmail', 'unknown email'),
      },
      subject: subjectLabel ?? subject,
      comment: {
        body: comment,
        uploads: null,
      },
      tags: compact([subject, account, device, driveDevice]),
      market,
      sendNotification: true,
    };
    const filesPayload = await createFilesPayload(files);

    try {
      if (filesPayload) {
        const uploadIds = await uploadMessageAttachment.mutateAsync(
          filesPayload,
        );

        payload.comment.uploads = uploadIds;
      }

      await sendSupportMessage.mutateAsync(payload);

      onSuccess();
    } catch (error) {
      logError('useSupportForm onSubmit function failed', error);

      onError();
    }
  };

  return {
    control,
    errors,
    isDirty,
    isSubmitting,
    isValid,
    setValue,
    reset,
    onSubmit: handleSubmit(onSubmit),
  };
}
