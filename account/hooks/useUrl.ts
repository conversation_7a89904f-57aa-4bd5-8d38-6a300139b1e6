import { useConfig } from '@/hooks/store/useConfig';
import { DISCORD_URLS } from '@/utils/constants';

export const useUrl = () => {
  const { language, market } = useConfig();

  const getDiscordUrl = (): string => DISCORD_URLS[language];

  const getCguUrl = (isB2b = false) => {
    const fileSuffix = isB2b ? 'b2b' : 'b2c';
    return `https://statics.shadow.tech/terms-of-use/tc-${market.toUpperCase()}-${fileSuffix}.pdf`;
  };

  return {
    getDiscordUrl,
    getCguUrl,
  };
};
