import { renderHook } from '@testing-library/react';

import { usePrice } from './usePrice';

import { useConfig } from '@/hooks/store/useConfig';

jest.mock('@/hooks/store/useConfig', () => ({
  useConfig: jest.fn(),
}));

describe('usePrice', () => {
  it('formats a price correctly in USD', () => {
    (useConfig as jest.Mock).mockImplementation(() => ({
      currency: 'USD',
      locale: 'en-US',
    }));

    const { result } = renderHook(() => usePrice());
    const formattedPrice = result.current.formatPrice(12345);
    expect(formattedPrice).toBe('$123.45');
  });

  it('formats a price correctly in EUR', () => {
    (useConfig as jest.Mock).mockImplementation(() => ({
      currency: 'EUR',
      locale: 'fr-FR',
    }));

    const { result } = renderHook(() => usePrice());
    const formattedPrice = result.current.formatPrice(12345);
    // https://stackoverflow.com/questions/66409161/how-to-tell-jest-that-spaces-are-in-fact-spaces
    // Intl.NumberFormat use small non-breaking space (\u202f) for thousand separator and normal non-breaking space before currency (\xa0).
    expect(formattedPrice).toBe('123,45\xa0€');
  });
});
