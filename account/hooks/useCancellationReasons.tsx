import shuffle from 'lodash/shuffle';
import { Trans, useTranslation } from 'next-i18next';
import { useMemo } from 'react';

import Link from '@/components/ui/link';
import NextLink from '@/components/ui/nextLink';
import {
  CancellationCategory,
  CancellationCategoryData,
  CancellationReasonData,
  CancellationReasonKey,
} from '@/types/forms';
import { APPS_URL, ROUTES_PATH, WEB_SUPPORT_URL } from '@/utils/constants';

interface useCancellationReasonsParams {
  isB2b?: boolean;
}

const useCancellationReasons = ({
  isB2b,
}: useCancellationReasonsParams = {}): Array<
  [CancellationCategory, CancellationCategoryData]
> => {
  const { t } = useTranslation();

  const linkVariant = 'body-sm-link';

  const getHelpCenterHelperContent = () => (
    <Trans i18nKey="subscription.cancellationReasons.helper.helpCenter">
      Did you visit our{' '}
      <Link
        href={WEB_SUPPORT_URL}
        target="_blank"
        component="a"
        variant={linkVariant}
      >
        Help Center
      </Link>
      ?
    </Trans>
  );

  return useMemo(() => {
    const subscriptionCancelReasons: CancellationReasonData = {
      [CancellationCategory.FinancialReasons]: {
        title: isB2b
          ? t(
              'subscription.cancellationReasonsB2b.category.financialReason',
              'Financial reasons',
            )
          : t(
              'subscription.cancellationReasons.category.financialReason',
              'Financial reasons',
            ),
        reasons: [
          [
            CancellationReasonKey.PriceTooHigh,
            t(
              'subscription.cancellationReasons.reason.priceTooHigh',
              'The price is too high for what I do with my Shadow',
            ),
            <Trans i18nKey="subscription.cancellationReasons.helper.priceTooHigh">
              Did you know that we have TV apps, mobile apps, … ? (
              <Link
                href={APPS_URL}
                target="_blank"
                component="a"
                variant={linkVariant}
              >
                Download
              </Link>
              )
            </Trans>,
          ],
          [
            CancellationReasonKey.PersonalFinancialIssue,
            t(
              'subscription.cancellationReasons.reason.personalFinancialIssue',
              'I would like to keep my Shadow but I prefer to stop my subscription for financial issues',
            ),
            null,
          ],
        ],
      },
      [CancellationCategory.TechnicalIssue]: {
        title: isB2b
          ? t(
              'subscription.cancellationReasonsB2b.category.technicalIssue',
              'Technical or compatibility issue',
            )
          : t(
              'subscription.cancellationReasons.category.technicalIssue',
              'Technical Issue',
            ),
        reasons: [
          [
            CancellationReasonKey.LatencyIssue,
            t(
              'subscription.cancellationReasons.reason.latencyIssue',
              'I felt latency and/or delay in my actions',
            ),
            <>
              <Trans
                i18nKey="subscription.cancellationReasons.helper.latencyIssue"
                components={[
                  <NextLink href={ROUTES_PATH.SUPPORT} variant={linkVariant} />,
                ]}
              />
              <br />
              {getHelpCenterHelperContent()}
            </>,
          ],
          [
            CancellationReasonKey.WeakInternetConnection,
            t(
              'subscription.cancellationReasons.reason.weakInternetConnection',
              'My internet connection is too weak',
            ),
            null,
          ],
          [
            CancellationReasonKey.StartIssue,
            t(
              'subscription.cancellationReasons.reason.startIssue',
              "I'm having trouble launching Shadow",
            ),
            <>
              <Trans
                i18nKey="subscription.cancellationReasons.helper.startIssue"
                components={[
                  <NextLink href={ROUTES_PATH.SUPPORT} variant={linkVariant} />,
                ]}
              />
              <br />
              {getHelpCenterHelperContent()}
            </>,
          ],
          [
            CancellationReasonKey.StabilityIssue,
            t(
              'subscription.cancellationReasons.reason.stabilityIssue',
              'I felt instability (bugs, updates, …)',
            ),
            <>
              <Trans
                i18nKey="subscription.cancellationReasons.helper.stabilityIssue"
                components={[
                  <NextLink href={ROUTES_PATH.SUPPORT} variant={linkVariant} />,
                ]}
              />
              <br />
              {getHelpCenterHelperContent()}
            </>,
          ],
        ],
      },
      [CancellationCategory.ProductAndUsage]: {
        title: isB2b
          ? t(
              'subscription.cancellationReasonsB2b.category.productAndUsage',
              'I needed Shadow for a short period of time',
            )
          : t(
              'subscription.cancellationReasons.category.productAndUsage',
              'Product and usage',
            ),
        reasons: [
          [
            CancellationReasonKey.IncompatibilitySoftwareOrGame,
            t(
              'subscription.cancellationReasons.reason.incompatibilitySoftwareOrGame',
              'Incompatibility of the software or the game for which I chose Shadow',
            ),
            null,
          ],
          [
            CancellationReasonKey.NoNeedAnymore,
            t(
              'subscription.cancellationReasons.reason.noNeedAnymore',
              "I don't need it anymore",
            ),
            null,
          ],
          [
            CancellationReasonKey.ItWasJustATest,
            t(
              'subscription.cancellationReasons.reason.itWasJustATest',
              "I don't plan to keep my Shadow (it was just a test)",
            ),
            null,
          ],
          [
            CancellationReasonKey.ShadowSpecTooWeak,
            t(
              'subscription.cancellationReasons.reason.shadowSpecTooWeak',
              'The technical specifications are too weak for what I want to do',
            ),
            null,
          ],
          [
            CancellationReasonKey.StorageNotBigEnough,
            t(
              'subscription.cancellationReasons.reason.storageNotBigEnough',
              'The storage is not big enough for my use',
            ),
            <Trans
              i18nKey="subscription.cancellationReasons.helper.storageNotBigEnough"
              components={[
                <NextLink
                  href={ROUTES_PATH.VM_MANAGER}
                  variant={linkVariant}
                />,
              ]}
            />,
          ],
          [
            CancellationReasonKey.TooManyConstraints,
            t(
              'subscription.cancellationReasons.reason.tooManyConstraints',
              'Too many constraints? (autoshutdown…)',
            ),
            null,
          ],
        ],
      },
    };

    return shuffle(Object.entries(subscriptionCancelReasons)) as Array<
      [CancellationCategory, CancellationCategoryData]
    >;
  }, [t, isB2b]);
};

export default useCancellationReasons;
