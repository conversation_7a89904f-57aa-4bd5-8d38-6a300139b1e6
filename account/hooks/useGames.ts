import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { fetchGamesList } from 'utils';

import { useCurrentMember } from './member/useMember';
import { useConfig } from './store/useConfig';

import { MAX_RETRY } from '@/utils/constants';

export function useGames() {
  const authQuery = useAuth();
  const { currency, market } = useConfig();
  const currentMemberQuery = useCurrentMember();
  const authToken = authQuery.user?.access_token;

  return useQuery(
    ['getGamesList'],
    () =>
      fetchGamesList(authToken, {
        currency: currency.toUpperCase(),
        market,
      }),
    {
      enabled: currentMemberQuery.isSuccess && !authQuery.isLoading,
      staleTime: 600 * 1000,
      retry: MAX_RETRY,
    },
  );
}
