import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  useDatacenterListForPlan,
  useScheduledChangesSubscription,
} from 'hooks';
import { isEmpty } from 'lodash';
import { useAuth } from 'react-oidc-context';
import {
  IAnyOffer,
  IEstimation,
  IEstimationPayload,
  ISubscription,
  ISubscriptionPayload,
  ISubscriptionRetention,
  Market,
  OfferPeriodicity,
  ProductFamilyId,
  ProductType,
  SubscriptionStatus,
} from 'types';
import {
  APIError,
  getSamePlanDifferentPeriodicityOfferId,
  getOffers,
  findSubscriptionItemByType,
  isOfferEnabledForSale,
  checkStockAvailability,
} from 'utils';

import { useCurrentMember } from '../member/useMember';

import {
  applySubscriptionRetention,
  fetchProductFamilySubscriptionStatuses,
  getSubscriptionModificationEstimation,
  getSubscriptionRetention,
  setVmName,
  setVmTags,
} from '@/api/subscription';
import {
  addSubscriptions,
  assignMemberToSubscription,
  cancelSubscription,
  fetchReferrals,
  reactivateSubscription,
  unassignMemberFromSubscription,
  cancelScheduledChanges,
} from '@/api/subscription';
import { useCatalog } from '@/hooks/useCatalog';
import type {
  ApiError,
  IProductFamilySubscriptionStatus,
  IUpdateVmNamePayload,
  IUpdateVmTagsPayload,
} from '@/types/api';
import {
  IAssignMemberToSubscriptionPayload,
  ICancelSubscriptionPayload,
  IReferrals,
  IUnassignMemberFromSubscriptionPayload,
} from '@/types/api';
import { DEFAULT_MARKET } from '@/utils/constants';

const useAddSubscription = () => {
  const { user } = useAuth();

  return useMutation<string | undefined, ApiError, ISubscriptionPayload>(
    ['addSubscriptions'],
    (subscriptionPayload: ISubscriptionPayload) =>
      addSubscriptions(user?.access_token as string, subscriptionPayload),
  );
};

const useCancelSubscription = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, ICancelSubscriptionPayload>(
    ({ cancellationReasons, subscriptionId }: ICancelSubscriptionPayload) =>
      cancelSubscription(
        user?.access_token as string,
        subscriptionId,
        cancellationReasons,
      ),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useReactivateSubscription = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, string>(
    (subscriptionId: string) =>
      reactivateSubscription(user?.access_token as string, subscriptionId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useReferral = () => {
  const { user } = useAuth();

  return useQuery<IReferrals | undefined, Error>(['referrals'], () =>
    fetchReferrals(user?.access_token as string),
  );
};

const useProductFamilySubscriptionStatuses = (
  productFamilyIds: ProductFamilyId[],
) => {
  const authQuery = useAuth();
  const authToken = authQuery.user?.access_token;

  return useQuery(
    ['subscriptionStatus', { productFamilyIds }],
    () => fetchProductFamilySubscriptionStatuses(authToken, productFamilyIds),
    {
      enabled: !authQuery.isLoading,
    },
  );
};

const useIsSubscriptionOnHold = () => {
  const subscriptionStatuses = useProductFamilySubscriptionStatuses([
    ProductFamilyId.CLOUDPC,
    ProductFamilyId.SHADOWDRIVE,
  ]);

  const getSubscriptionStatus = (
    productFamilyId: ProductFamilyId,
    statusesData?: IProductFamilySubscriptionStatus[],
  ) => {
    const subStatusData = statusesData?.find(
      subStatus => subStatus.product_family === productFamilyId,
    );
    return (
      !!subStatusData && 'on_hold' in subStatusData && subStatusData.on_hold
    );
  };

  const isCloudPcSubscriptionOnHold = getSubscriptionStatus(
    ProductFamilyId.CLOUDPC,
    subscriptionStatuses?.data,
  );
  const isDriveSubscriptionOnHold = getSubscriptionStatus(
    ProductFamilyId.SHADOWDRIVE,
    subscriptionStatuses?.data,
  );

  return { isCloudPcSubscriptionOnHold, isDriveSubscriptionOnHold };
};

const useAssignMemberToSubscription = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<
    string | undefined,
    ApiError,
    IAssignMemberToSubscriptionPayload
  >(
    ({ memberId, subscriptionId }: IAssignMemberToSubscriptionPayload) =>
      assignMemberToSubscription(
        user?.access_token as string,
        memberId,
        subscriptionId,
      ),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useRevokeMemberFromSubscription = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<
    string | undefined,
    ApiError,
    IUnassignMemberFromSubscriptionPayload
  >(
    ({ memberId, subscriptionId }: IUnassignMemberFromSubscriptionPayload) =>
      unassignMemberFromSubscription(
        user?.access_token as string,
        memberId,
        subscriptionId,
      ),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useCanUpdateBillingPeriodicity = (
  subscription: ISubscription | undefined,
  targetPeriodicity?: OfferPeriodicity,
) => {
  const currentMemberQuery = useCurrentMember();
  const { country } = currentMemberQuery.data?.user ?? {};
  const catalogQuery = useCatalog(subscription?.id);
  const catalog = catalogQuery.data;
  const scheduledChangesSubscriptionQuery = useScheduledChangesSubscription(
    subscription?.id as string,
    subscription?.has_scheduled_changes,
  );
  const scheduledChangesItems = subscription?.has_scheduled_changes
    ? scheduledChangesSubscriptionQuery?.data?.items
    : [];

  const currentPlan = findSubscriptionItemByType(
    subscription,
    ProductType.PLAN,
  );

  const currentOffer = currentPlan
    ? catalog?.offers.byId[currentPlan?.id]
    : undefined;

  const { data: datacentersList } = useDatacenterListForPlan({
    zipcode: subscription?.meta_data?.zipcode ?? null,
    country: (
      subscription?.meta_data?.country ??
      country ??
      DEFAULT_MARKET
    ).toLowerCase() as Market,
    planId: subscription?.plan_id,
  });

  const isOfferInStock = (offer: IAnyOffer) =>
    checkStockAvailability({
      datacentersList,
      datacenterName: subscription?.meta_data?.datacenter ?? null,
      planOrAddonId: offer.product_id,
      productType: ProductType.ADDON,
      productFamilyId: offer.itemFamilyId,
    });

  // We only want to update billing periodicity for "active" subscriptions
  if (
    !catalog ||
    !subscription ||
    subscription?.status !== SubscriptionStatus.ACTIVE ||
    !currentPlan ||
    !currentOffer ||
    (targetPeriodicity && currentOffer.periodicity === targetPeriodicity)
  ) {
    return false;
  }

  if (targetPeriodicity) {
    return !!getSamePlanDifferentPeriodicityOfferId(
      currentPlan?.id,
      targetPeriodicity,
      catalog,
    );
  } else {
    const offersWithDifferentPeriodicityThanCurrentOfferAndScheduledChange =
      getOffers(
        catalog,
        currentOffer.product_id,
        offer =>
          offer.periodicity !== currentOffer.periodicity &&
          isOfferEnabledForSale({ offer }) &&
          isOfferInStock(offer) &&
          !scheduledChangesItems?.find(item => item.id === offer.id),
      );

    return !isEmpty(
      offersWithDifferentPeriodicityThanCurrentOfferAndScheduledChange,
    );
  }
};

const useVmName = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, IUpdateVmNamePayload>(
    ({ subscriptionId, name }: IUpdateVmNamePayload) =>
      setVmName(user?.access_token as string, subscriptionId, name),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useVmTags = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<undefined, ApiError, IUpdateVmTagsPayload>(
    ({ subscriptionId, tags }: IUpdateVmTagsPayload) =>
      setVmTags(user?.access_token as string, subscriptionId, tags),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useCancelScheduledChanges = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<string | undefined, ApiError, string>(
    (subscriptionId: string) =>
      cancelScheduledChanges(user?.access_token as string, subscriptionId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subscriptions']);
      },
    },
  );
};

const useSubscriptionModificationEstimation = (
  subscriptionIdToModify: string,
) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useMutation<IEstimation | undefined, APIError, IEstimationPayload>(
    ['getSubscriptionModificationEstimation'],
    (estimatePayload: IEstimationPayload) =>
      getSubscriptionModificationEstimation(
        authToken,
        estimatePayload,
        subscriptionIdToModify,
      ),
  );
};

const useGetSubscriptionRetention = (subscriptionId: string) => {
  const { user } = useAuth();
  const authToken = user?.access_token;

  return useQuery<ISubscriptionRetention, APIError>(
    ['subscriptionRetention'],
    () => getSubscriptionRetention(authToken, subscriptionId),
    {
      enabled: !!authToken,
      retry: false,
    },
  );
};

const useApplySubscriptionRetention = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation<boolean, ApiError, string>(
    ['applySubscriptionRetention'],
    (subscriptionId: string) =>
      applySubscriptionRetention(user?.access_token as string, subscriptionId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries([
          'subscriptions',
          'subscriptionRetention',
        ]);
      },
    },
  );
};

export {
  useAddSubscription,
  useCancelSubscription,
  useReactivateSubscription,
  useReferral,
  useProductFamilySubscriptionStatuses,
  useIsSubscriptionOnHold,
  useAssignMemberToSubscription,
  useRevokeMemberFromSubscription,
  useCanUpdateBillingPeriodicity,
  useVmName,
  useVmTags,
  useCancelScheduledChanges,
  useSubscriptionModificationEstimation,
  useGetSubscriptionRetention,
  useApplySubscriptionRetention,
};
