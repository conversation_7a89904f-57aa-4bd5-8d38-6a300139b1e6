import { logError } from 'utils';

import useClipboard from './useClipboard';

jest.mock('utils', () => ({
  logError: jest.fn(),
}));

describe('useClipboard', () => {
  it('should copy the value to the clipboard and call the success callback', async () => {
    const successCb = jest.fn();
    const errorCb = jest.fn();
    const value = 'hello';

    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn(() => Promise.resolve()),
      },
    });

    jest.spyOn(navigator.clipboard, 'writeText');

    const { copyToClipboard } = useClipboard();
    await copyToClipboard(value, successCb, errorCb);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(value);
    expect(successCb).toHaveBeenCalled();
    expect(errorCb).not.toHaveBeenCalled();
    expect(logError).not.toHaveBeenCalled();
  });
});
