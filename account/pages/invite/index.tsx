import type { NextPage } from 'next';
import { useRouter } from 'next/router';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useEffect } from 'react';
import { GlobalLoader } from 'shared-components';
import { appendQueryParamsToUrl } from 'utils';

import { useConfig } from '@/hooks/store/useConfig';
import { useAuthentication } from '@/hooks/user/useAuthentication';
import i18nextConfig from '@/next-i18next.config';
import { MANAGER_URL, ROUTES_PATH } from '@/utils/constants';

const Invite: NextPage = () => {
  const router = useRouter();
  const { signinRedirect } = useAuthentication();
  const { setInviteToken, setInviteDriveGroup } = useConfig();

  useEffect(() => {
    const inviteToken = router.query.token?.toString() || '';
    const driveGroup = router.query.drive_group?.toString() || undefined;

    if (!driveGroup) {
      // Redirect to manager to handle invitation confirmation (not for drive group)
      const redirectUrl = new URL(MANAGER_URL + ROUTES_PATH.INVITE.slice(1));
      const urlWithQueryParams = appendQueryParamsToUrl(
        redirectUrl,
        router.query,
      );
      window.location.href = urlWithQueryParams.toString();
      return;
    }

    setInviteToken(inviteToken);
    setInviteDriveGroup(driveGroup);

    void signinRedirect({
      extraQueryParams: {
        login_hint: router.query.login_hint?.toString() || '',
        register: true,
        provider: router.query.provider?.toString() || '',
        invite_token: inviteToken,
      },
      state: {
        invite: true,
        drive_group: driveGroup,
        token: inviteToken,
      },
    });
  }, [router.query, setInviteToken, setInviteDriveGroup, signinRedirect]);

  return <GlobalLoader fullHeight />;
};

export default Invite;

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'], i18nextConfig)),
    },
  };
}
