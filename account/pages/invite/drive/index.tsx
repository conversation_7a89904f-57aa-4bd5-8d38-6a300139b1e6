import { usePaginatedSubscriptions } from 'hooks';
import { isEmpty } from 'lodash';
import type { NextPage } from 'next';
import { useRouter } from 'next/router';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useEffect } from 'react';
import { GlobalLoader } from 'shared-components';
import { ProductFamilyId, ProductId } from 'types';
import { findSubscription } from 'utils';

import { useConfig } from '@/hooks/store/useConfig';
import { useAuthentication } from '@/hooks/user/useAuthentication';
import { useBillingDetails } from '@/hooks/user/useUser';
import nextI18nextConfig from '@/next-i18next.config';
import { SHOP_URL } from '@/utils/constants';
import { ROUTES_PATH } from '@/utils/constants';

const DriveInvite: NextPage = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuthentication();
  const billingDetailsQuery = useBillingDetails();
  const { inviteToken, inviteDriveGroup, setInviteToken, setInviteDriveGroup } =
    useConfig();

  useEffect(() => {
    const inviteTokenFromQuery = router.query.token?.toString() || '';
    const driveGroupFromQuery =
      router.query.drive_group?.toString() || undefined;

    if (inviteTokenFromQuery) {
      setInviteToken(inviteTokenFromQuery);
    }

    if (driveGroupFromQuery) {
      setInviteDriveGroup(driveGroupFromQuery);
    }
  }, [
    router.query.drive_group,
    router.query.token,
    setInviteDriveGroup,
    setInviteToken,
  ]);

  const subscriptionsQuery = usePaginatedSubscriptions(false);
  const currentDriveSubscription = findSubscription(
    subscriptionsQuery.data?.items,
    ProductFamilyId.SHADOWDRIVE,
  );

  useEffect(() => {
    if (subscriptionsQuery.isLoading) {
      return;
    }

    if (!isAuthenticated) {
      router.push(ROUTES_PATH.VM_MANAGER);
      return;
    }

    if (currentDriveSubscription || !isEmpty(billingDetailsQuery.data)) {
      router.push(ROUTES_PATH.DRIVE);
      return;
    }

    if (!currentDriveSubscription && inviteToken && inviteDriveGroup) {
      window.location.href = `${SHOP_URL}b2c/product/${ProductFamilyId.SHADOWDRIVE}/${ProductId.DRIVE_B2C_FREE}?invite_token=${inviteToken}&drive_group=${inviteDriveGroup}`;
      return;
    }
  }, [
    billingDetailsQuery.data,
    currentDriveSubscription,
    inviteDriveGroup,
    inviteToken,
    isAuthenticated,
    router,
    subscriptionsQuery.isLoading,
  ]);

  return <GlobalLoader fullHeight />;
};

export default DriveInvite;

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'], nextI18nextConfig)),
    },
  };
}
