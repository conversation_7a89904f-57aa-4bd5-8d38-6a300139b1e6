import type { NextPage } from 'next';
import { useRouter } from 'next/router';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useEffect } from 'react';
import { GlobalLoader } from 'shared-components';
import { logError } from 'utils';

import { useConfirmMemberInvite } from '@/hooks/member/useMember';
import { useConfig } from '@/hooks/store/useConfig';
import { useAuthentication } from '@/hooks/user/useAuthentication';
import nextI18nextConfig from '@/next-i18next.config';
import { ROUTES_PATH } from '@/utils/constants';

const RegistrationSuccess: NextPage = () => {
  const { inviteToken } = useConfig();
  const router = useRouter();
  const confirmMemberInvite = useConfirmMemberInvite();

  const { isAuthenticated } = useAuthentication();

  useEffect(() => {
    if (isAuthenticated) {
      const asyncFunc = async () => {
        try {
          if (inviteToken) {
            await confirmMemberInvite.mutateAsync(inviteToken);
            router.push(ROUTES_PATH.ACCOUNT);
          } else {
            router.push(ROUTES_PATH.VM_MANAGER);
          }
        } catch (e: any) {
          logError('confirmMemberInvite failed', e.message);
        }
      };
      asyncFunc();
    }
  }, [isAuthenticated]); // eslint-disable-line react-hooks/exhaustive-deps

  return <GlobalLoader />;
};

export default RegistrationSuccess;

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'], nextI18nextConfig)),
    },
  };
}
