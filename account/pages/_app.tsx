import { CacheProvider, EmotionCache } from '@emotion/react';
import { createTheme, CssBaseline, ThemeProvider } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Script from 'next/script';
import { appWithTranslation } from 'next-i18next';
import { SnackbarProvider } from 'notistack';
import { useEffect, useState } from 'react';
import { AuthProvider } from 'react-oidc-context';
import { theme, SnackbarCloseButton, FeatureOverlay } from 'shared-components';
import { ApplicationList } from 'types';
import { GTM_ID, GTM_URL, IS_DEV, SnackbarUtilsConfigurator } from 'utils';

import i18nextConfig from '../next-i18next.config';

import ConsentBanner from '@/components/consentBanner/consentBanner';
import Main from '@/components/main';
import PageViewTracking from '@/components/tracking/PageViewTracking';
import { oidcConfig } from '@/config/oauth2-config';
import { FlagshipContext } from '@/contexts/FlagshipContext';
import createEmotionCache from '@/utils/createEmotionCache';
import 'shared-components/public/styles/globals.css';

const FEATURE_MESSAGE = process.env.NEXT_PUBLIC_FEATURE_MESSAGE;

interface IAppProps extends AppProps {
  emotionCache?: EmotionCache;
}

const shouldMock =
  process.env.NODE_ENV === 'development' &&
  process.env.NEXT_PUBLIC_API_MOCKING === 'enabled';

const clientSideEmotionCache = createEmotionCache();
const shadowTheme = createTheme(theme);
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // At least 30 seconds of default stale time
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
});

// This is a workaround wrapper only used to make MSW work
// This should only be used when mocks are enabled
const AppWrapper = (props: IAppProps) => {
  const [shouldStart, setShouldStart] = useState(typeof window === 'undefined');

  useEffect(() => {
    if (!shouldStart) {
      const { initMocks } = require('../mocks');
      initMocks().then(() => {
        setShouldStart(true);
      });
    }
  }, [shouldStart]);

  return shouldStart ? <InnerApp {...props} /> : null;
};

const InnerApp = (props: IAppProps) => {
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;
  const router = useRouter();

  return (
    <>
      <AuthProvider {...oidcConfig}>
        <CacheProvider value={emotionCache}>
          <QueryClientProvider client={queryClient}>
            <FlagshipContext>
              <ThemeProvider theme={shadowTheme}>
                <SnackbarProvider
                  action={key => (
                    <SnackbarCloseButton
                      data-test-id={'snackbar-close-button'}
                      snackbarKey={key}
                    />
                  )}
                  autoHideDuration={3000} // default is 5000ms
                  maxSnack={1}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                  }}
                >
                  <SnackbarUtilsConfigurator />
                  <Head>
                    <link
                      rel="icon"
                      href={`${router.basePath}/favicon.svg`}
                      type="image/svg+xml"
                    />
                    <link
                      rel="apple-touch-icon"
                      href={`${router.basePath}/apple-touch-icon.png`}
                    />
                    <link
                      rel="manifest"
                      href={`${router.basePath}/site.webmanifest`}
                    />
                  </Head>
                  <CssBaseline />
                  <Main>
                    <Component {...pageProps} />
                  </Main>
                  {FEATURE_MESSAGE && (
                    <FeatureOverlay
                      app={ApplicationList.ACCOUNT}
                      message={FEATURE_MESSAGE}
                    />
                  )}

                  {/* Didomi consent banner */}
                  <ConsentBanner />

                  {/* Sends page view events in datalayer */}
                  <PageViewTracking />

                  {/* React Query devtool */}
                  {IS_DEV && <ReactQueryDevtools initialIsOpen={true} />}
                </SnackbarProvider>
              </ThemeProvider>
            </FlagshipContext>
          </QueryClientProvider>
        </CacheProvider>
      </AuthProvider>

      {/* GTM script */}
      <Script
        id="gtag-start"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            '${GTM_URL}/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `,
        }}
      />
    </>
  );
};

const App = shouldMock ? AppWrapper : InnerApp;

export default appWithTranslation(App, i18nextConfig);
