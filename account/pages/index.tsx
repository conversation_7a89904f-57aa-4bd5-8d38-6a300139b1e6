import type { NextPage } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { Alert } from 'shared-components';
import { NotificationState } from 'types';

import Layout from '@/components/layout';
import VmManagerList from '@/components/vmManager/vmManager';
import i18nextConfig from '@/next-i18next.config';

const VmManager: NextPage = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { subscription_updated, game_purchased } = router.query;

  const maybeRenderAlerts = () => {
    return (
      <>
        {subscription_updated !== undefined && (
          <Alert
            type={NotificationState.SUCCESS}
            title={t(
              'subscription.update.success.title',
              'Your subscription has been updated',
            )}
          >
            {t(
              'subscription.update.success.content',
              'It may take a few moments before your Shadow PC is fully ready. Please reboot your Shadow PC the next time you access it.',
            )}
          </Alert>
        )}
        {game_purchased !== undefined && (
          <Alert
            type={NotificationState.SUCCESS}
            title={t(
              'subscription.gamePurchased.success.title',
              'You just bought a game !',
            )}
          >
            {t(
              'subscription.gamePurchased.success.content',
              'Check your emails to see the next steps.',
            )}
          </Alert>
        )}
      </>
    );
  };

  return (
    <>
      <Head>
        <title>
          {t('seo.businessManager.vm.title', 'Shadow - PC Manager')}
        </title>
      </Head>
      <Layout>
        {maybeRenderAlerts()}
        <VmManagerList />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default VmManager;
