import type { NextPage } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { ReactElement, useState } from 'react';
import { Alert } from 'shared-components';
import { NotificationState } from 'types';

import Layout from '@/components/layout';
import StorageManager from '@/components/subscription/storage/storageManager';
import VmManager from '@/components/subscription/vm/vmManager';
import i18nextConfig from '@/next-i18next.config';

const Home: NextPage = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { subscription_updated, game_purchased } = router.query;

  const [
    vmPeriodicityChangedAlertContent,
    setVmPeriodicityChangedAlertContent,
  ] = useState<ReactElement>();

  const onVmPeriodicityChangeSuccess = (element: ReactElement) => {
    setVmPeriodicityChangedAlertContent(element);
  };

  const onScheduledChangeCancelSuccess = () => {
    setVmPeriodicityChangedAlertContent(undefined);
  };

  const maybeRenderAlerts = () => {
    return (
      <>
        {subscription_updated !== undefined && (
          <Alert
            type={NotificationState.SUCCESS}
            title={t(
              'subscription.update.success.title',
              'Your subscription has been updated',
            )}
          >
            {t(
              'subscription.update.success.content',
              'It may take a few moments before your Shadow PC is fully ready. Please reboot your Shadow PC the next time you access it.',
            )}
          </Alert>
        )}
        {game_purchased !== undefined && (
          <Alert
            type={NotificationState.SUCCESS}
            title={t(
              'subscription.gamePurchased.success.title',
              'You just bought a game !',
            )}
          >
            {t(
              'subscription.gamePurchased.success.content',
              'Check your emails to see the next steps.',
            )}
          </Alert>
        )}
        {!!vmPeriodicityChangedAlertContent && (
          <Alert
            type={NotificationState.SUCCESS}
            title={t(
              'subscription.changeVmPeriodicity.success.title',
              'Your subscription has been updated',
            )}
          >
            {vmPeriodicityChangedAlertContent}
          </Alert>
        )}
      </>
    );
  };

  return (
    <>
      <Head>
        <title>{t('seo.vm.title', 'Shadow - Manage my Shadow')}</title>
      </Head>
      <Layout>
        {maybeRenderAlerts()}
        <VmManager
          onVmPeriodicityChangeSuccess={onVmPeriodicityChangeSuccess}
          onScheduledChangeCancelSuccess={onScheduledChangeCancelSuccess}
        />
        <StorageManager />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Home;
