import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import Layout from '@/components/layout';
import UserManagerContent from '@/components/userManager/userManagerContent';
import i18nextConfig from '@/next-i18next.config';

const UserManager: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>
          {t('seo.businessManager.user.title', 'Shadow - User Manager')}
        </title>
      </Head>
      <Layout>
        <UserManagerContent />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default UserManager;
