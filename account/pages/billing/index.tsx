import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import BillingManager from '@/components/account/billing/billingManager';
import InvoicesManager from '@/components/account/invoices/invoicesManager';
import Layout from '@/components/layout';
import i18nextConfig from '@/next-i18next.config';

const Billing: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>
          {t('seo.billing.title', 'Shadow - Manage my billing information')}
        </title>
      </Head>
      <Layout>
        <div id="billing">
          <BillingManager />
        </div>
        <div id="invoices">
          <InvoicesManager />
        </div>
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Billing;
