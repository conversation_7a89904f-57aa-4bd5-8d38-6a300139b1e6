import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import Layout from '@/components/layout';
import VmOnDemandManager from '@/components/vmOnDemand/vmOnDemandManager/vmOnDemandManager';
import i18nextConfig from '@/next-i18next.config';

const Home: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>
          {t('seo.businessManager.vmOnDemand.title', 'Shadow - Spot computing')}
        </title>
      </Head>
      <Layout>
        <VmOnDemandManager />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Home;
