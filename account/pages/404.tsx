import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { Typography } from 'shared-components';

import Layout from '@/components/layout';
import Paper from '@/components/ui/paper';
import PaperTitle from '@/components/ui/paperTitle';
import i18nextConfig from '@/next-i18next.config';

const NotFound: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>
          {t(
            'notFound.pageTitle',
            '404 - Shadow Account - The page your are looking for does not exist.',
          )}
        </title>
      </Head>
      <Layout>
        <Paper>
          <PaperTitle>{t('notFound.title', '404 not found')}</PaperTitle>
          <Typography variant="body-md-regular">
            {t(
              'notFound.description',
              'The page your are looking for does not exist.',
            )}
          </Typography>
        </Paper>
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default NotFound;
