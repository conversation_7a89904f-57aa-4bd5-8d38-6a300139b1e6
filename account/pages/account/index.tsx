import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import PrivacyManager from '@/components/account/privacyManager';
import UserManager from '@/components/account/user/userManager';
import Layout from '@/components/layout';
import i18nextConfig from '@/next-i18next.config';

const Account: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>{t('seo.account.title', 'Shadow - Manage my account')}</title>
      </Head>
      <Layout>
        <div id="userManager">
          <UserManager />
        </div>
        <div id="privacyManager">
          <PrivacyManager />
        </div>
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Account;
