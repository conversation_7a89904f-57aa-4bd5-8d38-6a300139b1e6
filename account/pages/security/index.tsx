import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import SecurityManager from '@/components/account/security/securityManager';
import Layout from '@/components/layout';
import i18nextConfig from '@/next-i18next.config';

const Security: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>{t('seo.security.title', 'Shadow - Manage my security')}</title>
      </Head>
      <Layout>
        <div id="security">
          <SecurityManager />
        </div>
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Security;
