import type { NextPage } from 'next';
import Head from 'next/head';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { IMAGE_PATH } from 'utils';

import Layout from '@/components/layout';
import SubscriptionAddon from '@/components/subscription/addon/subscriptionAddon';
import i18nextConfig from '@/next-i18next.config';
import { GAME_STORE_URL } from '@/utils/constants';

const Games: NextPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>{t('seo.games.title', 'Shadow - Game Store')}</title>
      </Head>
      <Layout>
        <Image
          src={`${IMAGE_PATH}game_bundle.png`}
          alt={t('subscription.plan.vm.illustrationAlt', 'Games')}
          width={872}
          height={240}
        />
        <SubscriptionAddon
          id="bundle"
          label={t(
            'subscription.plan.vm.bundle.get.link',
            'Take advantage of exceptional prices on video games',
          )}
          href={GAME_STORE_URL}
          showPrice={false}
          hasSecondaryBackground
          description={t(
            'subscription.plan.vm.bundle.get.description',
            'We would like to thank you for your loyalty by offering you PC video games at exceptional prices.',
          )}
        />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Games;
