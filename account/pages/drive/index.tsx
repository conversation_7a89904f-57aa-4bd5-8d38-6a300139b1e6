import type { NextPage } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { Alert } from 'shared-components';
import { Market, NotificationState } from 'types';

import Layout from '@/components/layout';
import DriveManager from '@/components/subscription/drive/driveManager';
import PaperLoader from '@/components/ui/loader/paperLoader';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useBillingDetails } from '@/hooks/user/useUser';
import i18nextConfig from '@/next-i18next.config';
import {
  BLACKLISTED_COUNTRY_FOR_DRIVE,
  DEFAULT_MARKET,
  ROUTES_PATH,
} from '@/utils/constants';

const Drive: NextPage = () => {
  const { t } = useTranslation();
  const currentMemberQuery = useCurrentMember();
  const { country } = currentMemberQuery.data?.user ?? {};
  const userCountry = (
    country ? country.toLowerCase() : DEFAULT_MARKET
  ) as Market;
  const billingDetailsQuery = useBillingDetails();
  const router = useRouter();

  const { subscription_updated } = router.query;

  if (billingDetailsQuery.isLoading || currentMemberQuery.isLoading) {
    return <PaperLoader />;
  }

  if (BLACKLISTED_COUNTRY_FOR_DRIVE.includes(userCountry)) {
    router.push(ROUTES_PATH.VM_MANAGER);

    return <></>;
  }

  return (
    <>
      <Head>
        <title>{t('seo.drive.title', 'Shadow - Manage my Shadow Drive')}</title>
      </Head>
      <Layout>
        {subscription_updated !== undefined && (
          <Alert
            type={NotificationState.SUCCESS}
            title={t(
              'subscription.updateDrive.success.title',
              'Your subscription has been updated',
            )}
          />
        )}
        <DriveManager />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Drive;
