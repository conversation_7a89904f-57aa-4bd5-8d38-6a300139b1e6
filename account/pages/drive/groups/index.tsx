import { useGetDriveGroups } from 'hooks';
import { isEmpty } from 'lodash';
import type { NextPage } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { Alert } from 'shared-components';
import { Market, NotificationState } from 'types';

import Layout from '@/components/layout';
import { DriveGroupsManager } from '@/components/subscription/driveGroups/driveGroupsManager';
import PaperLoader from '@/components/ui/loader/paperLoader';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useBillingDetails } from '@/hooks/user/useUser';
import i18nextConfig from '@/next-i18next.config';
import {
  BLACKLISTED_COUNTRY_FOR_DRIVE,
  DEFAULT_MARKET,
  ROUTES_PATH,
} from '@/utils/constants';

const DriveGroupsPage: NextPage = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const currentMemberQuery = useCurrentMember();
  const driveGroupsQuery = useGetDriveGroups();
  const { group_upgraded, group_upgrade_failed } = router.query;

  const { country } = currentMemberQuery.data?.user ?? {};
  const userCountry = (
    country ? country.toLowerCase() : DEFAULT_MARKET
  ) as Market;

  const billingDetailsQuery = useBillingDetails();

  if (billingDetailsQuery.isLoading || currentMemberQuery.isLoading) {
    return <PaperLoader />;
  }

  if (isEmpty(billingDetailsQuery.data)) {
    router.push(ROUTES_PATH.ACCOUNT);
    return <></>;
  }

  if (BLACKLISTED_COUNTRY_FOR_DRIVE.includes(userCountry)) {
    router.push(ROUTES_PATH.VM_MANAGER);
    return <></>;
  }

  const maybeRenderAlerts = () => {
    if (group_upgraded !== undefined) {
      return (
        <Alert
          type={NotificationState.SUCCESS}
          title={t(
            'subscription.upgradeDriveGroup.success.title',
            "Your group's storage has been upgraded",
          )}
        >
          {t(
            'subscription.upgradeDriveGroup.success.description',
            'It may take a few moments before the changes take effect for your group {{groupName}}',
            {
              groupName: driveGroupsQuery.data?.items.find(
                driveGroup => driveGroup.id === group_upgraded,
              )?.name,
            },
          )}
        </Alert>
      );
    }

    if (group_upgrade_failed !== undefined) {
      return (
        <Alert
          type={NotificationState.ERROR}
          title={t(
            'subscription.upgradeDriveGroup.error.title',
            'An error occurred while upgrading your group',
          )}
        >
          {t(
            'subscription.upgradeDriveGroup.error.description',
            "We're sorry, but we couldn't upgrade your storage for your group {{groupName}}. Please contact the support team.",

            {
              groupName: driveGroupsQuery.data?.items.find(
                driveGroup => driveGroup.id === group_upgrade_failed,
              )?.name,
            },
          )}
        </Alert>
      );
    }
  };

  return (
    <>
      <Head>
        <title>
          {t(
            'seo.driveGroups.title',
            'Shadow - Manage my Shadow Drive Family Groups',
          )}
        </title>
      </Head>
      <Layout>
        {maybeRenderAlerts()}
        <DriveGroupsManager />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default DriveGroupsPage;
