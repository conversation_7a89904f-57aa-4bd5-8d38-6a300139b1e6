import type { NextPage } from 'next';
import Head from 'next/head';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { theme } from 'shared-components';
import { Icons } from 'types';

import Layout from '@/components/layout';
import Contact from '@/components/support/contact/contact';
import Resource from '@/components/support/resource/resource';
import { useCurrentMember } from '@/hooks/member/useMember';
import { useUrl } from '@/hooks/useUrl';
import i18nextConfig from '@/next-i18next.config';
import { IResourceItem } from '@/types/support';
import { FAQ_URL, WEB_SUPPORT_URL } from '@/utils/constants';

const Support: NextPage = () => {
  const { t } = useTranslation();
  const { getDiscordUrl } = useUrl();

  const currentMemberQuery = useCurrentMember();
  const isB2b = !!currentMemberQuery.data?.user?.b2b;

  const resourceItem: IResourceItem[] = [
    {
      href: WEB_SUPPORT_URL,
      icon: {
        name: Icons.QUESTION_CIRCLE,
        color: theme.palette.primary.main,
      },
      title: t('support.ressources.helpcenter.title', 'Help center'),
      description: t(
        'support.ressources.helpcenter.description',
        'Learn how to use Shadow, fix a problem, and get answers to your questions.',
      ),
    },
    {
      href: FAQ_URL,
      icon: {
        name: Icons.LIST,
        color: theme.palette.primary.main,
      },
      title: t('support.ressources.faq.title', 'FAQ'),
      description: t(
        'support.ressources.faq.description',
        'This page provides answers to frequently asked questions.',
      ),
    },
    ...(!isB2b
      ? [
          {
            href: getDiscordUrl(),
            icon: {
              name: Icons.DISCORD,
              color: '#5865F2',
            },
            title: t('support.ressources.discord.title', 'Discord'),
            description: t(
              'support.ressources.discord.description',
              'Join us on Discord and connect with the whole community of #TeamShadow!',
            ),
          },
        ]
      : []),
  ];

  return (
    <>
      <Head>
        <title>{t('seo.support.title', 'Shadow - Support')}</title>
      </Head>
      <Layout>
        <Resource items={resourceItem} />
        <Contact />
      </Layout>
    </>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ['translation', 'common'],
        i18nextConfig,
      )),
    },
  };
}

export default Support;
