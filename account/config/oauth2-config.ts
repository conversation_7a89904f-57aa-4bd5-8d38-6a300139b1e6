import { WebStorageStateStore } from 'oidc-client-ts';
import { isInIframe, IS_CLIENT_SIDE } from 'utils';

import { BASE_URL, CLIENT_ID, OAUTH2_AUTHORITY } from '../utils/constants';

const selectUserStore = () => {
  if (IS_CLIENT_SIDE && !isInIframe()) {
    return new WebStorageStateStore({ store: localStorage });
  }
};

const onSigninCallback = (user: any): void => {
  // Hack because iam cannot set multiple redirect_uri
  if (user?.state?.token && user?.state?.drive_group) {
    window.location.href = `${BASE_URL}invite/drive?token=${user.state.token}&drive_group=${user.state.drive_group}`;
  } else if (user?.state?.invite) {
    window.location.href = `${BASE_URL}invite/registration-success`;
  }
  window.history.replaceState({}, document.title, window.location.pathname);
};

export const oidcConfig = {
  authority: OAUTH2_AUTHORITY,
  client_id: CLIENT_ID,
  redirect_uri: BASE_URL,
  post_logout_redirect_uri: BASE_URL,
  response_type: 'code',
  scope: 'openid profile email',
  onSigninCallback: onSigninCallback,
  userStore: selectUserStore(),
};
