import { ReactNode, RefObject } from 'react';
import { Control } from 'react-hook-form';

import { ISupportFormValues } from './support';

export interface SelectOption {
  value: string;
  label: string | null;
  flag?: string;
}

/*eslint-disable @typescript-eslint/no-explicit-any */

export interface IFormInputProps {
  control: any;
  name: string;
  showError?: boolean;
  onChange?: (value: string) => void;
}

export interface ICheckboxProps {
  control: any;
  name: string;
  onChange?: (value: string) => void;
  required?: boolean;
  justifyContent: string;
  size?: 'normal' | 'small';
  color?: 'primary' | 'secondary';
  isBackground?: boolean;
}

export interface IFormSelectProps {
  name: string;
  control: Control<any>;
  label: string;
  items: SelectOption[];
  required?: boolean;
  disabled?: boolean;
  defaultValue?: string;
  onChange?: (value: string) => void;
  isValue?: boolean;
  placeholder?: string;
}

export interface IFormProps {
  onSelection?: (data: any) => void;
  onSubmitting?: (onSubmitting: boolean) => void;
  onError?: () => void;
  onSuccess?: (data?: any) => void;
  hideSubmitButton?: boolean;
  submitRef?: RefObject<HTMLButtonElement> | null;
}

export interface ISupportFormProps {
  onSupportSubmit?: (
    formValues: ISupportFormValues,
    resetForm: () => void,
  ) => Promise<void>;
}

// Cancel VM
export enum CancellationCategory {
  FinancialReasons = 'financial_reasons',
  TechnicalIssue = 'technical_issues',
  ProductAndUsage = 'product_and_usage',
}

export enum CancellationReasonKey {
  PriceTooHigh = 'price_too_high',
  PersonalFinancialIssue = 'personal_financial_issue',
  LatencyIssue = 'latency_issue',
  WeakInternetConnection = 'weak_internet_connection',
  StartIssue = 'start_issue',
  StabilityIssue = 'stability_issue',
  IncompatibilitySoftwareOrGame = 'incompatibility_software_or_game',
  NoNeedAnymore = 'no_need_anymore',
  ItWasJustATest = 'it_was_just_a_test',
  ShadowSpecTooWeak = 'shadow_spec_too_weak',
  StorageNotBigEnough = 'storage_not_big_enough',
  TooManyConstraints = 'too_many_constraints',
  Other = 'other',
  NoAnswer = 'no_answer',
}

export type CancellationReasonData = {
  [key in CancellationCategory]?: CancellationCategoryData;
};

export interface CancellationCategoryData {
  title: string;
  reasons: Array<[CancellationReasonKey, string, string | ReactNode | null]>;
}
