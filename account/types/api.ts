import type {
  Currency,
  Language,
  Market,
  CardBrand,
  PaymentDetailsStatus,
  PaymentType,
  ITag,
  OfferPeriodicity,
  KeyboardISO,
  LastPaymentStatus,
  ProductFamilyId,
} from 'types';

import type { CancellationCategory } from './forms';
import type { CancellationReasons } from './subscriptions';
import type { VmConfiguration } from './vm';

interface ApiError extends Error {
  response: Response;
  error_code: string | undefined;
  code: number | undefined;
}

type AuthToken = string | undefined;

interface IIndexable {
  [key: string]: any;
}

interface IUserForm extends IIndexable {
  first_name: string;
  last_name: string;
  phone: string;
  birthdate: string;
}

interface ICompanyForm extends IIndexable {
  company: string;
  vat_number: string;
}

interface IBillingForm extends IIndexable {
  address1: string;
  zipcode: string;
  city: string;
  country: string;
}

interface IInviteForm extends IIndexable {
  invite_email: string;
}

interface IUserDetails {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  country: string;
  language: string;
  b2b: boolean;
}

interface IInvoice {
  id: string;
  date: number;
  status: string;
  total: number;
  currency_code: Currency;
  link: null;
}

interface IInvoiceDownload {
  link: string;
}

type IPaymentDue =
  | {
      amount_due: 0;
      currency_code: '';
    }
  | {
      amount_due: number;
      remaining_days_before_cancel: number;
      currency_code: Currency;
    };

interface ICancelSubscriptionPayload extends IIndexable {
  subscriptionId: string;
  cancellationReasons: CancellationReasons;
}

interface IReferrals {
  public_coupon: string;
  referral_discount_total: number;
  referral_used_total: number;
  amount_per_referral: number;
}

type IProductFamilySubscriptionStatus =
  | {
      id: number;
      last_payment_date: number;
      last_payment_status: LastPaymentStatus | null;
      on_hold_reason: string | null;
      on_hold: boolean;
      plan_id: string;
      product_family: ProductFamilyId;
      started_at: number;
      status: string;
    }
  | {
      product_family: ProductFamilyId;
      status: 'notfound' | string;
    };

interface IBillingDetails extends IIndexable {
  first_name?: string;
  last_name?: string;
  country?: string;
  phone?: string;
  birthdate?: string;
  address1?: string;
  address2?: string;
  city?: string;
  zipcode?: string;
  state?: string;
  consent_newsletter?: boolean;
}

interface CardDetails {
  brand: CardBrand;
  masked_number: string;
  expiry_year: number;
  expiry_month: number;
  first_name: string;
  last_name: string;
}
interface IVdiStatus extends IIndexable {
  datacenter: string;
  maintenance: boolean;
  running: boolean;
}

interface IReadyToPlayConfig {
  language: string | undefined;
  keyboard: string | undefined;
}

interface IReadyToPlayVm extends IIndexable {
  subscriptionId: string;
  readyToPlayConfig: IReadyToPlayConfig;
  timezone: string; // legacy param
}

interface IPaymentDetails extends IIndexable {
  id: string;
  status: PaymentDetailsStatus;
  type: PaymentType;
  card?: CardDetails;
}

interface IRetryPaymentPayload {
  invoice_ids?: string[];
  payment_source_id?: string;
}

// interface INewsletterOptins {
//   shadowPc: boolean,
//   shadowDrive: boolean,
//   shadowB2b: boolean,
// }

// interface IBills {
//   name: string;
//   date: Date;
//   amount: number;
//   link: string;
// }

interface IUpdateVmNamePayload {
  subscriptionId: string;
  name: string;
}

interface IUpdateVmTagsPayload {
  subscriptionId: string;
  tags: ITag[];
}

interface IWordConfirmationForm extends IIndexable {
  word: string;
}

interface IVmNameForm extends IIndexable {
  name: string;
}

interface IVmTagsForm extends IIndexable {
  tag: string;
}

interface ICouponForm extends IIndexable {
  coupon: string;
}

type CancelReason = {
  [key in CancellationCategory]: boolean;
};

interface ICancelVmForm {
  reasons: CancelReason[];
  comment: string;
}

interface ICancelDriveForm {
  reason: string;
}

interface IResetVmForm extends IIndexable {
  configuration: VmConfiguration;
  language: Language;
  keyboard: KeyboardISO;
}

interface IChangeVmPeriodicityForm extends IIndexable {
  periodicity: OfferPeriodicity;
}

interface ISupportForm extends IIndexable {
  message: string;
}

interface IPersonalData extends IIndexable {
  result: Record<string, unknown>; // We don't care typing that result, its the downloaded JSON
}

interface ILoginPayload {
  email: string;
  password: string;
}

type LoginResponse =
  | { success: false; error: string }
  | {
      success: true;
      result?: {
        token: string;
        role: string[];
        badges: string[];
      };
    };

interface ISupportFormPayload {
  requester: {
    email: string;
    name: string;
  };
  subject: string;
  comment: {
    body: string;
    uploads: string[] | null | undefined;
  };
  tags: string[];
  market: Market;
  sendNotification: boolean;
}

interface IFile {
  file: string;
  fileName: string;
  market: Market;
}

interface IAssignMemberToSubscriptionPayload {
  subscriptionId: string;
  memberId: string;
}

interface IUnassignMemberFromSubscriptionPayload {
  subscriptionId?: string;
  memberId: string;
}

interface IDeletePayload {
  member_id: string | null;
  invite_email: string;
}

interface IUpdateMemberTagsPayload {
  memberId: string;
  tags: ITag[];
}

export type {
  ApiError,
  AuthToken,
  IUserForm,
  ICompanyForm,
  IBillingForm,
  IInviteForm,
  IUserDetails,
  IBillingDetails,
  IPaymentDetails,
  IRetryPaymentPayload,
  IVdiStatus,
  IReadyToPlayConfig,
  IReadyToPlayVm,
  IInvoice,
  IInvoiceDownload,
  ICancelSubscriptionPayload,
  IReferrals,
  IProductFamilySubscriptionStatus,
  IWordConfirmationForm,
  IVmNameForm,
  IUpdateVmNamePayload,
  IVmTagsForm,
  IUpdateVmTagsPayload,
  ICouponForm,
  ICancelVmForm,
  ICancelDriveForm,
  IResetVmForm,
  IChangeVmPeriodicityForm,
  ISupportForm,
  IPersonalData,
  ILoginPayload,
  LoginResponse,
  ISupportFormPayload,
  IFile,
  IAssignMemberToSubscriptionPayload,
  IUnassignMemberFromSubscriptionPayload,
  IDeletePayload,
  IUpdateMemberTagsPayload,
  IPaymentDue,
};
