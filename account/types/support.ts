import { Icons } from 'types';

export interface ISelectOption {
  value: string;
  displayedValue: string;
}

export interface ISupportFormValues {
  subject: string;
  message: string;
  tags: string[];
  uploadedFile?: any;
}

export interface ISupportFormChoice {
  id: SupportCase;
  label: string;
  options: ISelectOption[];
  nextToDisplay: any;
  reset: string[];
}

export enum SupportSelectOption {
  Subject = 'subject',
  Account = 'account',
  Device = 'device',
  DriveDevice = 'driveDevice',
  MessageInput = 'messageInput',
}

export interface ISupportSelectDisplay {
  subject: boolean;
  account: boolean;
  device: boolean;
  driveDevice: boolean;
  messageInput: boolean;
}

export interface ISupportSelectValues {
  subject: string;
  account: string;
  device: string;
  driveDevice: string;
  messageInput: string;
}

export interface IResourceItem {
  href: string;
  icon: {
    name: Icons;
    color: string;
  };
  title: string;
  description: string;
}

export enum Subject {
  VmIssue = 'vm_issue',
  CloudPc = 'shadow_pc',
  Drive = 'shadow_drive',
  Account = 'account',
}

export enum Account {
  PaymentIssues = 'payment_issues',
  UpdateInfo = 'update_info',
  ChangeSubscription = 'change_subscription',
  Referral = 'referral',
  Unsubscribe = 'unsubscribe',
}

export enum SupportCase {
  SubjectCase = 'subject',
  AccountCase = 'account',
  DeviceCase = 'device',
  DriveDeviceCase = 'driveDevice',
  AllCase = 'all',
  OthersCase = 'others',
}

export enum Device {
  Windows = 'windows',
  Mac = 'mac',
  Android = 'android',
  Ghost = 'ghost',
  Box = 'box',
  Linux = 'linux',
  IOs = 'ios',
  WebRTC = 'shadow_in_browser',
}
