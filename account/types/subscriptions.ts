import { theme } from 'shared-components';
import { OfferPeriodUnit, SubscriptionStatus } from 'types';

export type SubscriptionColor = {
  [index: string]: {
    color?: string;
    background?: string;
  };
};

export const SUBSCRIPTION_STATUS_COLOR: SubscriptionColor = {
  [SubscriptionStatus.FUTURE]: {
    color: theme.palette.black.main75,
    background: 'transparent',
  },
  [SubscriptionStatus.IN_TRIAL]: {
    color: theme.palette.warning.main,
    background: theme.palette.warning.light,
  },
  [SubscriptionStatus.ACTIVE]: {
    color: theme.palette.success.main,
    background: theme.palette.success.light,
  },
  [SubscriptionStatus.CANCELLED]: {
    color: theme.palette.primary.main,
    background: theme.palette.primary.main10,
  },
  [SubscriptionStatus.NON_RENEWING]: {
    color: theme.palette.error.main,
    background: theme.palette.error.light,
  },
  [SubscriptionStatus.PAUSED]: {
    color: theme.palette.primary.main,
    background: theme.palette.primary.main10,
  },
};

export interface IPrice {
  id?: string;
  amount: number;
  period: number;
  periodUnit: OfferPeriodUnit;
  isAddon?: boolean;
  hasStartingPrice?: boolean;
}

export interface CancellationReasons {
  reasons?: string[];
  comment?: string;
}
