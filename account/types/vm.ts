import { theme } from 'shared-components';

export enum VmConfiguration {
  AUTOMATIC = 'automatic',
  MANUAL = 'manual',
}

export enum VmStatus {
  MAINTENANCE = 'maintenance',
  RUNNING = 'running',
  STOPPED = 'stopped',
  ON_HOLD = 'on_hold',
}

export type VmColor = {
  [index: string]: {
    color?: string;
    background?: string;
  };
};

export const VM_STATUS_COLOR: VmColor = {
  [VmStatus.MAINTENANCE]: {
    color: theme.palette.warning.main,
    background: theme.palette.warning.light,
  },
  [VmStatus.RUNNING]: {
    color: theme.palette.success.main,
    background: theme.palette.success.light,
  },
  [VmStatus.STOPPED]: {
    color: theme.palette.primary.main,
    background: theme.palette.primary.main10,
  },
  [VmStatus.ON_HOLD]: {
    color: theme.palette.error.main,
    background: theme.palette.error.main10,
  },
};
