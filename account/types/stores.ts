import type { Currency, Language, Locale, Market } from 'types';
import { State } from 'zustand';

export interface ConfigState extends State {
  market: Market;
  language: Language;
  currency: Currency;
  locale: Locale;
  didomiLanguage: Language | null;
  inviteToken?: string;
  inviteDriveGroup?: string;
  setLocaleData: (i18nLang: string | undefined) => void;
  setInviteToken: (inviteToken: string) => void;
  setInviteDriveGroup: (inviteDriveGroup: string | undefined) => void;
}
