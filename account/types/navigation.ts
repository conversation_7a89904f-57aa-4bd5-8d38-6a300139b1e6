import { Icons } from 'types';

export interface INavigationArray {
  id: 'pc' | 'shadow drive' | 'account';
  title?: string;
  icon?: string;
  enabled?: boolean;
  menuItem: {
    enabled?: boolean;
    id: string;
    label: string;
    external?: boolean;
    href?: string;
    icon?: Icons;
    route?: string;
  }[];
}

export type NavigationColor = {
  [key in 'black' | 'primary']: {
    background: { main: string; hover?: string; selected?: string };
    textColor: { main: string; hover: string; selected: string };
  };
};

export enum NavigationType {
  NAVIGATION_ADMIN = 'admin',
  NAVIGATION_MEMBER = 'member',
  NAVIGATION_USER = 'user',
}
