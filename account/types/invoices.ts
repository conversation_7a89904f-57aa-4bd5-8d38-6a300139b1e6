import type { ReactNode } from 'react';

export interface IInvoicesColumns {
  id: 'date' | 'status' | 'total' | 'link';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right';
  format?: (value: number | string, ...args: any[]) => string | ReactNode;
}

export enum InvoicesStatus {
  NOT_PAID = 'not_paid',
  PAID = 'paid',
  PAYMENT_DUE = 'payment_due',
  PENDING = 'pending',
  POSTED = 'posted',
  VOIDED = 'voided',
}
