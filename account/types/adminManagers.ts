import type {
  IMemberDetails,
  ProductId,
  UserRole,
  ISubscription,
  ITag,
  IDriveGroup,
} from 'types';
import { SubscriptionStatus } from 'types';

import { UserStatus } from '@/utils/constants';

export interface IUserManager {
  id: string;
  createdAt: number;
  status: UserStatus;
  role: UserRole;
  hasUser: boolean;
  firstName?: string;
  lastName?: string;
  email: string;
  tags: ITag[];
}

export type IDriveGroupsManager = IDriveGroup;

export interface IVmManager {
  id: string;
  createdAt: number;
  datacenter: string;
  type: ProductId;
  subscriptionStatus: SubscriptionStatus;
  isVmRunning: boolean;
  isVmInMaintenanceMode: boolean;
  isVmOnHold: boolean;
  subscription: ISubscription;
  name?: string;
  user?: IMemberDetails;
  member?: IMemberDetails;
}

export interface IUser {
  id: string;
  createdAt: number;
  status: UserStatus;
  firstName: string;
  lastName: string;
  email: string;
}
