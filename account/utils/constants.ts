import {
  CountryCode,
  Currency,
  CurrencyPerLocale,
  Language,
  Locale,
  Market,
  NotificationState,
  ProductId,
  SubscriptionStatus,
  CouponType,
  KeyboardISO,
} from 'types';

import { SelectOption } from '@/types/forms';
import { RouteId } from '@/types/routes';
import { VmConfiguration } from '@/types/vm';

export const REDIRECT_KEY = 'shadowNavigationRedirect';
export const MAX_RETRY = 1;

// Have to make this complex array because Next doesn't allow
// destructuring env variables so process.env[VAR_NAME] returns undefined
const requiredEnvVars = [
  {
    key: 'NEXT_PUBLIC_API_URL',
    value: process.env.NEXT_PUBLIC_API_URL,
  },
  {
    key: 'NEXT_PUBLIC_BASE_URL',
    value: process.env.NEXT_PUBLIC_BASE_URL,
  },
  {
    key: 'NEXT_PUBLIC_CLIENT_ID',
    value: process.env.NEXT_PUBLIC_CLIENT_ID,
  },
  {
    key: 'NEXT_PUBLIC_OAUTH2_AUTHORITY',
    value: process.env.NEXT_PUBLIC_OAUTH2_AUTHORITY,
  },
  {
    key: 'NEXT_PUBLIC_SHOP_URL',
    value: process.env.NEXT_PUBLIC_SHOP_URL,
  },
  {
    key: 'NEXT_PUBLIC_DRIVE_URL',
    value: process.env.NEXT_PUBLIC_DRIVE_URL,
  },
  {
    key: 'NEXT_PUBLIC_LANDING_URL',
    value: process.env.NEXT_PUBLIC_LANDING_URL,
  },
  {
    key: 'NEXT_PUBLIC_AUTH_URL',
    value: process.env.NEXT_PUBLIC_AUTH_URL,
  },
  {
    key: 'NEXT_PUBLIC_MANAGER_URL',
    value: process.env.NEXT_PUBLIC_MANAGER_URL,
  },
  {
    key: 'NEXT_PUBLIC_SPRINKLR_APP_ID',
    value: process.env.NEXT_PUBLIC_SPRINKLR_APP_ID,
  },
  {
    key: 'NEXT_PUBLIC_FLAGSHIP_API_KEY',
    value: process.env.NEXT_PUBLIC_FLAGSHIP_API_KEY,
  },
  {
    key: 'NEXT_PUBLIC_FLAGSHIP_ENV_ID',
    value: process.env.NEXT_PUBLIC_FLAGSHIP_ENV_ID,
  },
  {
    key: 'NEXT_PUBLIC_GTM_URL',
    value: process.env.NEXT_PUBLIC_GTM_URL,
  },
];

const computeErrorText = (name: string) =>
  `Required env variable ${name} is undefined`;

requiredEnvVars.map(requiredEnvVar => {
  if (!requiredEnvVar.value) {
    throw new Error(computeErrorText(requiredEnvVar.key));
  }
});

// Base urls
export const API_URL = process.env.NEXT_PUBLIC_API_URL as string;
export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL as string;
export const AUTH_URL = process.env.NEXT_PUBLIC_AUTH_URL as string;

export const OAUTH2_AUTHORITY = process.env
  .NEXT_PUBLIC_OAUTH2_AUTHORITY as string;
export const CLIENT_ID = process.env.NEXT_PUBLIC_CLIENT_ID as string;

export const SHOP_URL = process.env.NEXT_PUBLIC_SHOP_URL;
export const LANDING_URL = process.env.NEXT_PUBLIC_LANDING_URL;
export const DRIVE_URL = process.env.NEXT_PUBLIC_DRIVE_URL;
export const GAME_STORE_URL = `${LANDING_URL}game-store`;
export const MANAGER_URL = process.env.NEXT_PUBLIC_MANAGER_URL;

export const DIDOMI_PUBLIC_API_KEY =
  process.env.NEXT_PUBLIC_DIDOMI_PUBLIC_API_KEY;
export const DIDOMI_DEFAULT_NOTICE_ID =
  process.env.NEXT_PUBLIC_DIDOMI_DEFAULT_NOTICE_ID;

export const FLAGSHIP_API_KEY = process.env
  .NEXT_PUBLIC_FLAGSHIP_API_KEY as string;
export const FLAGSHIP_ENV_ID = process.env
  .NEXT_PUBLIC_FLAGSHIP_ENV_ID as string;

// Apis responses code
export const STATUS_CODE_INVALID_DRIVE_COUPON = 412;

// AUTH Paths urls

export const AUTH_UI = `${AUTH_URL}/ui`;
export const AUTH_SETTINGS = `${AUTH_UI}/settings/profile`;
export const AUTH_SECURITY_HUB = `${AUTH_UI}/settings/security`;
export const AUTH_SOCIALS_LOGIN = `${AUTH_UI}/settings/socials`;
export const AUTH_RESEND_VERIFICATION_EMAIL = `${AUTH_UI}/kratos/verification`;

//export const DEFAULT_LOCALE = Locale.EN_US;
export const DEFAULT_MARKET = Market.US;
export const DEFAULT_LANGUAGE = Language.EN;
export const DEFAULT_CURRENCY = Currency.USD;
export const DEFAULT_LOCALE = Locale.EN_US;
export const FALLBACK_LOCALE = 'en';

export const COUNTRY_CODE: Record<Market, CountryCode> = {
  [Market.US]: 'US',
  [Market.GB]: 'GB',
  [Market.FR]: 'FR',
  [Market.BE]: 'BE',
  [Market.CH]: 'CH',
  [Market.LU]: 'LU',
  [Market.DE]: 'DE',
  [Market.NL]: 'NL',
  [Market.CA]: 'CA',
  [Market.AT]: 'AT',
  [Market.ES]: 'ES',
  [Market.IT]: 'IT',
  [Market.SE]: 'SE',
  [Market.DK]: 'DK',
  [Market.PL]: 'PL',
  [Market.CZ]: 'CZ',
  [Market.HU]: 'HU',
  [Market.IE]: 'IE',
  [Market.SK]: 'SK',
  [Market.HR]: 'HR',
  [Market.SI]: 'SI',
  [Market.PT]: 'PT',
};

export const CURRENCY_PER_LOCALE: CurrencyPerLocale = {
  en: Currency.EUR,
  'en-AT': Currency.EUR,
  'en-CA': Currency.CAD,
  'en-CH': Currency.CHF,
  'en-BE': Currency.EUR,
  'en-DK': Currency.DKK,
  'en-DE': Currency.EUR,
  'en-ES': Currency.EUR,
  'en-GB': Currency.GBP,
  'en-FR': Currency.EUR,
  'en-IT': Currency.EUR,
  'en-LU': Currency.EUR,
  'en-NL': Currency.EUR,
  'en-SE': Currency.SEK,
  'en-US': Currency.USD,
  'fr-BE': Currency.EUR,
  'fr-CA': Currency.CAD,
  'fr-CH': Currency.CHF,
  'fr-FR': Currency.EUR,
  'fr-LU': Currency.EUR,
  'de-AT': Currency.EUR,
  'de-DE': Currency.EUR,
  'de-CH': Currency.CHF,
  'es-ES': Currency.EUR,
  'it-IT': Currency.EUR,
  'da-DK': Currency.DKK,
  'sv-SE': Currency.SEK,
  'en-PL': Currency.EUR,
  'en-CZ': Currency.EUR,
  'en-HU': Currency.EUR,
  'en-IE': Currency.EUR,
  'en-SK': Currency.EUR,
  'en-HR': Currency.EUR,
  'en-SI': Currency.EUR,
  'en-PT': Currency.EUR,
};

export const LANGUAGES_PER_MARKET: Record<Market, Language[]> = {
  [Market.US]: [Language.EN],
  [Market.GB]: [Language.EN],
  [Market.FR]: [Language.FR, Language.EN],
  [Market.BE]: [Language.FR, Language.EN],
  [Market.CH]: [Language.FR, Language.DE, Language.EN],
  [Market.LU]: [Language.FR, Language.EN],
  [Market.DE]: [Language.DE, Language.EN],
  [Market.NL]: [Language.EN],
  [Market.CA]: [Language.FR, Language.EN],
  [Market.AT]: [Language.DE, Language.EN],
  [Market.ES]: [Language.ES, Language.EN],
  [Market.IT]: [Language.IT, Language.EN],
  [Market.SE]: [Language.EN],
  [Market.DK]: [Language.EN],
  [Market.PL]: [Language.EN],
  [Market.CZ]: [Language.EN],
  [Market.HU]: [Language.EN],
  [Market.IE]: [Language.EN],
  [Market.SK]: [Language.EN],
  [Market.HR]: [Language.EN],
  [Market.SI]: [Language.EN],
  [Market.PT]: [Language.EN],
};

export const LANGUAGES_OPTIONS: SelectOption[] = [
  { value: Language.EN, label: 'English' },
  { value: Language.FR, label: 'Français' },
  { value: Language.DE, label: 'Deutsche' },
  { value: Language.NL, label: 'Nederlandse' },
  { value: Language.ES, label: 'Español' },
  { value: Language.IT, label: 'Italiano' },
];

export const LANGUAGES_WITH_FLAG_OPTIONS: SelectOption[] = [
  { value: Language.EN, label: 'English', flag: 'us' },
  { value: Language.FR, label: 'Français', flag: 'fr' },
  { value: Language.DE, label: 'Deutsche', flag: 'de' },
  { value: Language.NL, label: 'Nederlandse', flag: 'nl' },
  { value: Language.ES, label: 'Español', flag: 'es' },
  { value: Language.IT, label: 'Italiano', flag: 'it' },
];

export const VM_LANGUAGES_WITH_FLAG_OPTIONS: SelectOption[] = [
  { value: Language.EN, label: 'English', flag: 'us' },
  { value: Language.FR, label: 'Français', flag: 'fr' },
  { value: Language.DE, label: 'Deutsche', flag: 'de' },
  { value: Language.NL, label: 'Nederlandse', flag: 'nl' },
];

export const MARKETS_OPTIONS: SelectOption[] = [
  { value: Market.US, label: 'United States' },
  { value: Market.GB, label: 'United Kingdom' },
  { value: Market.FR, label: 'France' },
  { value: Market.BE, label: 'Belgique' },
  { value: Market.CH, label: 'Switzerland' },
  { value: Market.LU, label: 'Luxembourg' },
  { value: Market.DE, label: 'Deutschland' },
  { value: Market.NL, label: 'Nederland' },
  { value: Market.CA, label: 'Canada' },
  { value: Market.ES, label: 'España' },
  { value: Market.IT, label: 'Italia' },
  { value: Market.SE, label: 'Sweden' },
  { value: Market.DK, label: 'Denmark' },
  { value: Market.PL, label: 'Poland' },
  { value: Market.CZ, label: 'Czech Republic' },
  { value: Market.HU, label: 'Hungary' },
  { value: Market.IE, label: 'Ireland' },
  { value: Market.SK, label: 'Slovakia' },
  { value: Market.HR, label: 'Croatia' },
  { value: Market.SI, label: 'Slovenia' },
  { value: Market.PT, label: 'Portugal' },
];

export const CONFIGURATION_OPTIONS: SelectOption[] = [
  { value: VmConfiguration.AUTOMATIC, label: null },
  { value: VmConfiguration.MANUAL, label: null },
];

export const KEYBOARD_OPTIONS: SelectOption[] = [
  { value: KeyboardISO.FR_FR, label: 'Français - AZERTY', flag: 'fr' },
  { value: KeyboardISO.EN_GB, label: 'English - QWERTY', flag: 'gb' },
  { value: KeyboardISO.EN_US, label: 'English - QWERTY', flag: 'us' },
  { value: KeyboardISO.DE_DE, label: 'Deutch - QWERTZ', flag: 'de' },
  { value: KeyboardISO.ES_ES, label: 'Español - QWERTY', flag: 'es' },
  { value: KeyboardISO.IT_IT, label: 'Italiano - QWERTY', flag: 'it' },
];

export const KEYBOARD_PER_LANGUAGE: Record<Language, KeyboardISO> = {
  [Language.FR]: KeyboardISO.FR_FR,
  [Language.EN]: KeyboardISO.EN_US,
  [Language.NL]: KeyboardISO.EN_GB,
  [Language.DE]: KeyboardISO.DE_DE,
  [Language.ES]: KeyboardISO.ES_ES,
  [Language.IT]: KeyboardISO.IT_IT,
  [Language.DA]: KeyboardISO.EN_GB,
  [Language.SV]: KeyboardISO.EN_GB,
};

export const LOCALE_KEYBOARD_PER_LANGUAGE: Record<Language, Locale> = {
  [Language.FR]: Locale.FR_FR,
  [Language.EN]: Locale.EN_US,
  [Language.NL]: Locale.EN_NL,
  [Language.DE]: Locale.DE_DE,
  [Language.ES]: Locale.ES_ES,
  [Language.IT]: Locale.IT_IT,
  [Language.DA]: Locale.DA_DK,
  [Language.SV]: Locale.SV_SE,
};

export const ROUTES_PATH: Record<RouteId, string> = {
  [RouteId.VM_MANAGER]: '/',
  [RouteId.MY_PC]: '/my-pc',
  [RouteId.GAMES]: '/games',
  [RouteId.DRIVE]: '/drive',
  [RouteId.DRIVE_GROUPS]: '/drive/groups',
  [RouteId.ACCOUNT]: '/account',
  [RouteId.BILLING]: '/billing',
  [RouteId.SUPPORT]: '/support',
  [RouteId.USER_MANAGER]: '/user-manager',
  [RouteId.VM_ON_DEMAND]: '/spot-computing',
  [RouteId.SECURITY]: '/security',
  [RouteId.INVITE]: '/invite',
  [RouteId.INVITE_SUCCESS]: '/invite/registration-success',
  [RouteId.INVITE_DRIVE]: '/invite/drive',
};

export const ROUTES_PATH_TO_PAGENAME_MAP: Record<string, string> = {
  [ROUTES_PATH.VM_MANAGER]: 'CS - VM Management',
  [ROUTES_PATH.MY_PC]: 'CS - Shadow PC',
  [ROUTES_PATH.GAMES]: 'CS - Game Store',
  [ROUTES_PATH.DRIVE]: 'CS - Drive',
  [ROUTES_PATH.DRIVE_GROUPS]: 'CS - Drive Family',
  [ROUTES_PATH.ACCOUNT]: 'CS - Account',
  [ROUTES_PATH.BILLING]: 'CS - Billing',
  [ROUTES_PATH.SUPPORT]: 'CS - Support',
  [ROUTES_PATH.USER_MANAGER]: 'CS - Users Management',
  [ROUTES_PATH.VM_ON_DEMAND]: 'CS - Spot computing',
  [ROUTES_PATH.SECURITY]: 'CS - Security',
  [ROUTES_PATH.INVITE]: 'CS - Invite',
  [ROUTES_PATH.INVITE_SUCCESS]: 'CS - Invite Success',
  [ROUTES_PATH.INVITE_DRIVE]: 'CS - Invite Drive',
};

/* Form validation */
export const COUPON_INPUT_PLACEHOLDER: Record<CouponType, string> = {
  [CouponType.DRIVE_FREE]: 'sdrive_XXXXXXXXXXX',
  [CouponType.DRIVE_PREMIUM]: 'sdrive_XXXXXXXXXXX',
  [CouponType.POWER]: 'paccess_XXXXXXXXXXX',
};

export const FORM_VALIDATION_WORD_COMPARE_RESET_VM: Partial<
  Record<Language, string>
> = {
  [Language.FR]: 'REINITIALISER',
  [Language.DE]: 'ZURÜCKSETZEN',
  [Language.NL]: 'RESET',
  [Language.EN]: 'RESET',
  [Language.ES]: 'REINICIADOR',
  [Language.IT]: 'REINIZIALIZZATORE',
};
export const FORM_VALIDATION_WORD_COMPARE_DOWNLOAD_DATA: Partial<
  Record<Language, string>
> = {
  [Language.FR]: 'TELECHARGER',
  [Language.DE]: 'HERUNTERLADEN',
  [Language.NL]: 'DOWNLOAD',
  [Language.EN]: 'DOWNLOAD',
  [Language.ES]: 'DESCARGAR',
  [Language.IT]: 'SCARICARE',
};

export const CUSTOMER_API_REQUEST_MINIMUM_DELAY_IN_MS = 1200;

export const FAQ_URL = `${LANDING_URL}faq`;
export const TECH_SPECS_URL = `${LANDING_URL}specs`;
export const APPS_URL = `${LANDING_URL}download`;
export const PRIVACY_POLICY_URL = `${LANDING_URL}privacy-policy`;
export const LEGAL_URL = `${LANDING_URL}legal-notice`;
export const COOKIES_URL = `${LANDING_URL}cookies`;
export const WEB_SUPPORT_URL = 'https://shdw.me/HC-B2C';

export const DISCORD_URLS: Record<Language, string> = {
  [Language.FR]: 'https://discord.com/invite/shadowfr',
  [Language.DE]: 'https://discord.com/invite/shadowde',
  [Language.NL]: 'https://discord.com/invite/shadow',
  [Language.EN]: 'https://discord.com/invite/shadow',
  [Language.ES]: 'https://discord.com/invite/shadow',
  [Language.IT]: 'https://discord.com/invite/shadow',
  [Language.SV]: 'https://discord.com/invite/shadow',
  [Language.DA]: 'https://discord.com/invite/shadow',
};

export const STATUS_COLORS: Record<SubscriptionStatus, string> = {
  [SubscriptionStatus.ACTIVE]: '#3653CC',
  [SubscriptionStatus.CANCELLED]: '#F22424',
  [SubscriptionStatus.FUTURE]: '#FFC119',
  [SubscriptionStatus.IN_TRIAL]: '#FFFFFF',
  [SubscriptionStatus.NON_RENEWING]: '#FFC119',
  [SubscriptionStatus.PAUSED]: '#FFFFFF',
};

export const INFO_BANNER_BACKGROUND_COLORS: Record<NotificationState, string> =
  {
    [NotificationState.SUCCESS]: '#E4F4E6',
    [NotificationState.ERROR]: '#F7E3E3',
    [NotificationState.WARNING]: '#FFF9E8',
    [NotificationState.INFO]: '#E6E9F9',
  };

export const INFO_BANNER_BORDER_COLORS: Record<NotificationState, string> = {
  [NotificationState.SUCCESS]: '#D2F0D6',
  [NotificationState.ERROR]: '#F6D0D0',
  [NotificationState.WARNING]: '#FFF2CF',
  [NotificationState.INFO]: '#D2D8F4',
};

export const CHECKBOX_SIZES = {
  small: 16,
  normal: 24,
};

export const DAYS_BETWEEN_DUE_INVOICE_AND_TERMINATION = 8;

export const INVOICES_PER_PAGE = 4;

export const TOOLTIP_LEAVE_TOUCH_DELAY = 3;
export const TOOLTIP_ENTER_TOUCH_DELAY = 0;

export const LIST_MANAGER_DEFAULT_ITEMS_PER_PAGE = 5;
export const LIST_MANAGER_PAGE_OPTIONS: SelectOption[] = [
  { value: '5', label: '5' },
  { value: '10', label: '10' },
  { value: '15', label: '15' },
];

export const USER_ROLES = [
  { value: 'admin', label: 'admin' },
  { value: 'member', label: 'member' },
];

export const UNPROTECTED_ROUTES = [
  ROUTES_PATH.INVITE,
  ROUTES_PATH.INVITE_SUCCESS,
];

export enum ManagerDataType {
  VMS = 'vms',
  USERS = 'users',
  DRIVE_GROUPS = 'driveGroups',
}

export enum UserStatus {
  DISABLED = 'disabled',
  PENDING = 'pending',
  ACTIVE = 'active',
  EXPIRED = 'expired',
}

export const EXTRA_STORAGE_FOR_CLOUDPC_ADDON_REGEX =
  /^cloudpc-extrastorage-256go2019-c1/;

export const POWER_OFFERS = [
  ProductId.CLOUDPC_B2C_POWER_C1,
  ProductId.CLOUDPC_B2C_POWER_A4000,
];

export const BLACKLISTED_COUNTRY_FOR_DRIVE = [Market.US, Market.CA];
