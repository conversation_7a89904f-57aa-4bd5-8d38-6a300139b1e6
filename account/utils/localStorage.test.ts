import { log } from 'utils';

import {
  saveToLocalStorage,
  removeFromLocalStorage,
  getFromLocalStorage,
} from './localStorage';

jest.mock('utils', () => ({
  log: jest.fn(),
}));

describe('LocalStorage utilities', () => {
  const mockKey = 'testKey';
  const mockValue = { a: 1, b: 2 };
  let mockLocalStorage: Record<string, string>;

  const setupMockLocalStorage = () => {
    mockLocalStorage = {};

    Storage.prototype.setItem = jest.fn((key, value) => {
      mockLocalStorage[key] = value;
    });

    Storage.prototype.getItem = jest.fn(key => {
      return mockLocalStorage[key] || null;
    });

    Storage.prototype.removeItem = jest.fn(key => {
      delete mockLocalStorage[key];
    });
  };

  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    setupMockLocalStorage();
  });

  describe('saveToLocalStorage', () => {
    it('should save an item to localStorage', () => {
      saveToLocalStorage(mockKey, mockValue);
      expect(Storage.prototype.setItem).toHaveBeenCalledWith(
        mockKey,
        JSON.stringify(mockValue),
      );
    });

    it('should log error if saving to localStorage fails', () => {
      const setError = jest
        .spyOn(Storage.prototype, 'setItem')
        .mockImplementation(() => {
          throw new Error('Mocked error');
        });

      saveToLocalStorage(mockKey, mockValue);

      expect(log).toHaveBeenCalledWith(
        "Couldn't save to localStorage",
        'error',
        expect.any(Error),
      );

      setError.mockRestore();
    });
  });

  describe('removeFromLocalStorage', () => {
    it('should remove an item from localStorage', () => {
      localStorage.setItem(mockKey, JSON.stringify(mockValue));
      removeFromLocalStorage(mockKey);
      expect(localStorage.removeItem).toHaveBeenCalledWith(mockKey);
    });

    it('should log error if removing from localStorage fails', () => {
      jest.spyOn(Storage.prototype, 'removeItem').mockImplementation(() => {
        throw new Error();
      });

      removeFromLocalStorage(mockKey);
      expect(log).toHaveBeenCalledWith(
        "Couldn't remove from localStorage",
        'error',
        expect.any(Error),
      );
    });
  });

  describe('getFromLocalStorage', () => {
    it('should get an item from localStorage', () => {
      saveToLocalStorage(mockKey, mockValue);
      const result = getFromLocalStorage(mockKey);

      expect(result).toEqual(mockValue);
    });

    it('should return null for non-existent key', () => {
      const result = getFromLocalStorage('nonExistentKey');
      expect(result).toBeNull();
    });

    it('should log error if getting from localStorage fails', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockImplementation(() => {
        throw new Error();
      });

      const result = getFromLocalStorage(mockKey);
      expect(log).toHaveBeenCalledWith(
        "Couldn't get from localStorage",
        'error',
        expect.any(Error),
      );
      expect(result).toBeNull();
    });
  });
});
