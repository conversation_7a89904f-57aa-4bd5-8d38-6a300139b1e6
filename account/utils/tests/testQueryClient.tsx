import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import * as React from 'react';
import { AuthProvider } from 'react-oidc-context';

import { oidcConfig } from '@/config/oauth2-config';

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: Infinity,
      },
    },
  });

// export function renderWithClient(ui: React.ReactElement) {
//   const testQueryClient = createTestQueryClient();
//   const { rerender, ...result } = render(
//     <QueryClientProvider client={testQueryClient}>{ui}</QueryClientProvider>,
//   );

//   return {
//     ...result,
//     rerender: (rerenderUi: React.ReactElement) =>
//       rerender(
//         <QueryClientProvider client={testQueryClient}>
//           {rerenderUi}
//         </QueryClientProvider>,
//       ),
//   };
// }

export function createWrapper() {
  const testQueryClient = createTestQueryClient();

  /* eslint-disable react/display-name */
  return ({ children }: { children: React.ReactNode }) => (
    <AuthProvider {...oidcConfig}>
      <QueryClientProvider client={testQueryClient}>
        {children}
      </QueryClientProvider>
    </AuthProvider>
  );
}
