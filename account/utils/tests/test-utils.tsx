import { CacheProvider } from '@emotion/react';
import { createTheme, ThemeProvider } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, RenderOptions } from '@testing-library/react';
import { ReactNode, ReactElement } from 'react';
import { AuthProvider } from 'react-oidc-context';
import { theme } from 'shared-components';

import { oidcConfig } from '@/config/oauth2-config';
import createEmotionCache from '@/utils/createEmotionCache';

const clientSideEmotionCache = createEmotionCache();
const shadowTheme = createTheme(theme);
const queryClient = new QueryClient();

interface IAllTheProvidersProps {
  children: ReactNode;
}

const AllTheProviders = ({ children }: IAllTheProvidersProps) => {
  return (
    <AuthProvider {...oidcConfig}>
      <CacheProvider value={clientSideEmotionCache}>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider theme={shadowTheme}>{children}</ThemeProvider>
        </QueryClientProvider>
      </CacheProvider>
    </AuthProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
