import jwt from 'jsonwebtoken';
import { User } from 'oidc-client-ts';
import { logError } from 'utils';

import { CLIENT_ID, OAUTH2_AUTHORITY } from '@/utils/constants';

export function getUser() {
  try {
    const persistedStateJSON = localStorage.getItem(
      `oidc.user:${OAUTH2_AUTHORITY}:${CLIENT_ID}`,
    );
    return persistedStateJSON
      ? User.fromStorageString(persistedStateJSON)
      : null;
  } catch (e) {
    logError('getUser', e);
    return null;
  }
}

export function isKratosFederationProvider() {
  const idToken = getUser()?.id_token;
  if (!idToken) {
    return null;
  }
  const decodedToken = <jwt.JwtPayloadFederation>jwt.decode(idToken);
  return decodedToken.federation.provider === 'kratos';
}

export function getToken() {
  return getUser()?.access_token;
}

export function hasToken() {
  return !!getUser()?.access_token;
}

export function doesntHaveToken() {
  return !getUser()?.access_token;
}
