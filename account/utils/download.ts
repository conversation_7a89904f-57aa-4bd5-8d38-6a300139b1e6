export function downloadObjectAsJson(
  exportObj: Record<string, unknown>,
  exportName: string,
): void {
  const obj = JSON.stringify(exportObj);

  if (window?.navigator && (window?.navigator as any)?.msSaveOrOpenBlob) {
    // IE
    const blob = new Blob([obj], { type: 'text/json;charset=utf-8' });

    (window?.navigator as any)?.msSaveOrOpenBlob(blob, exportName);
  } else {
    // Chrome/FF/Safari
    const dataStr = `data:text/json;charset=utf-8,${encodeURIComponent(obj)}`;
    const a = document.createElement('a');

    a.setAttribute('href', dataStr);
    a.setAttribute('download', `${exportName}.json`);
    document.body.appendChild(a); // required for firefox
    a.click();
    a.remove();
  }
}
