import theme from 'shared-components/theme/theme';

import { NavigationColor } from '@/types/navigation';

export const NAVIGATION_COLORS: NavigationColor = {
  primary: {
    background: {
      main: theme.palette.white.main,
      hover: theme.palette.primary.main10,
      selected: theme.palette.primary.main10,
    },
    textColor: {
      main: theme.palette.black.main,
      hover: theme.palette.primary.main,
      selected: theme.palette.primary.main,
    },
  },
  black: {
    background: {
      main: theme.palette.white.main,
      hover: theme.palette.black.main,
      selected: theme.palette.black.main,
    },
    textColor: {
      main: theme.palette.black.main,
      hover: theme.palette.white.main,
      selected: theme.palette.white.main,
    },
  },
};
