import { log } from 'utils';

export const saveToLocalStorage = (key: string, nextState: unknown) => {
  try {
    localStorage.setItem(key, JSON.stringify(nextState));
  } catch (e) {
    log("Couldn't save to localStorage", 'error', e);
  }
};

export const removeFromLocalStorage = (key: string) => {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    log("Couldn't remove from localStorage", 'error', e);
  }
};

export const getFromLocalStorage = (key: string) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (e) {
    log("Couldn't get from localStorage", 'error', e);
    return null;
  }
};
