import { IDidomiObject } from '@didomi/react';

import { IVendorStatus, IPurposeStatus } from '@/types/tracking';

export const getPurposeStatus = (Didomi: IDidomiObject): IPurposeStatus => {
  return {
    preferences:
      Didomi.getUserConsentStatusForPurpose('create_content_profile') &&
      Didomi.getUserConsentStatusForPurpose('select_personalized_content'),
  };
};

export const getVendorStatus = (Didomi: IDidomiObject): IVendorStatus => {
  return {
    google: Didomi.getUserConsentStatusForVendor('c:googleana-4TXnJigR'),
  };
};
