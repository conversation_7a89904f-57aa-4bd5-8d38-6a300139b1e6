import { getToken, hasToken, doesntHaveToken } from './persistedUser';

import fixture from '@/mocks/fixtures/persistedUser.json';

jest.mock('./constants', () => ({
  __esModule: true,
  CLIENT_ID: 'dcf7df7a-0acb-41a1-a103-2d2198cf69ad',
  OAUTH2_AUTHORITY: 'https://auth.spectr.be/hydra',
}));

describe('test persistedUser functions', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('getToken should return undefined with no persisted user', () => {
    expect(getToken()).toBeUndefined;
  });

  it('hasToken should return false with no persisted user', () => {
    expect(hasToken()).toEqual(false);
  });

  it('doesntHaveToken should return true with no persisted user', () => {
    expect(doesntHaveToken()).toEqual(true);
  });

  it('getToken should return the access_token with a persisted user', () => {
    window.localStorage.setItem(fixture.key, JSON.stringify(fixture.value));
    expect(getToken()).toEqual(fixture.value.access_token);
    window.localStorage.removeItem(fixture.key);
  });

  it('hasToken should return true with a persisted user', () => {
    window.localStorage.setItem(fixture.key, JSON.stringify(fixture.value));
    expect(hasToken()).toEqual(true);
    window.localStorage.removeItem(fixture.key);
  });

  it('doesntHaveToken should return false with a persisted user', () => {
    window.localStorage.setItem(fixture.key, JSON.stringify(fixture.value));
    expect(doesntHaveToken()).toEqual(false);
    window.localStorage.removeItem(fixture.key);
  });
});
