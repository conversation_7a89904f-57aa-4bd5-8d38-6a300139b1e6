import { getDatacenterByName } from './datacenter';

const dataCentersListMock = [
  {
    id: 'TestId1',
    name: 'TestName1',
    country: 'TestCountry1',
    city: 'TestCity1',
  },
  {
    id: 'TestId2',
    name: 'TestName2',
    country: 'TestCountry2',
    city: 'TestCity2',
  },
];

describe('test getDatacenterByName function', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it('should return nothing if datacenters list is empty', () => {
    expect(getDatacenterByName(undefined, 'Test')).toEqual(undefined);
  });

  it('should return correct datacenter from name', () => {
    expect(getDatacenterByName(dataCentersListMock, 'TestName1')).toEqual(
      dataCentersListMock[0],
    );
  });
});
