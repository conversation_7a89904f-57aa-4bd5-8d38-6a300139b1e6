import {
  Currency,
  EstimationDiscountEntityType,
  PriceTypeEstimation,
  TaxObjectType,
} from 'types';

// Mock of the renewal-estimate endpoint :
// UPDATED 13,SEPT 2023

export const mockedRenewalEstimate = {
  created_at: 1694007002,
  object: 'estimate',
  subscription_estimate: {
    id: 'BTTzKOToz1begJXn',
    status: 'active',
    next_billing_at: 1696508739,
    object: 'subscription_estimate',
    currency_code: 'EUR' as Uppercase<Currency>,
  },
  invoice_estimate: {
    recurring: true,
    price_type: 'tax_inclusive' as PriceTypeEstimation,
    sub_total: 3299,
    total: 1299,
    credits_applied: 0,
    amount_paid: 0,
    amount_due: 1299,
    object: 'invoice_estimate',
    customer_id: '7266b973-a9a5-46cd-b8f8-5f81bdee938d',
    line_items: [
      {
        id: 'li_BTcd00Tp5EkHrC6i',
        date_from: 1696508739,
        date_to: 1699190739,
        unit_amount: 3299,
        quantity: 1,
        amount: 3299,
        pricing_model: 'flat_fee',
        is_taxed: true,
        tax_amount: 217,
        tax_rate: 20.0,
        object: 'line_item',
        subscription_id: 'BTTzKOToz1begJXn',
        customer_id: '7266b973-a9a5-46cd-b8f8-5f81bdee938d',
        description: 'Shadow PC +',
        entity_type: 'plan_item_price' as EstimationDiscountEntityType,
        entity_id: 'cloudpc-b2c-standard2023-EUR-Monthly',
        metered: false,
        discount_amount: 2000,
        item_level_discount_amount: 0,
      },
      {
        id: 'li_BTcd00Tp5EkI1C6j',
        date_from: 1696508739,
        date_to: 1699190739,
        unit_amount: 0,
        quantity: 1,
        amount: 0,
        pricing_model: 'flat_fee',
        is_taxed: true,
        tax_amount: 0,
        tax_rate: 20.0,
        object: 'line_item',
        subscription_id: 'BTTzKOToz1begJXn',
        customer_id: '7266b973-a9a5-46cd-b8f8-5f81bdee938d',
        description: 'Browser access',
        entity_type: 'addon_item_price',
        entity_id: 'cloudpc-webrtc-2023-EUR-Monthly',
        metered: false,
        discount_amount: 0,
        item_level_discount_amount: 0,
      },
      {
        id: 'li_BTcd00Tp5EkICC6k',
        date_from: 1696508739,
        date_to: 1699190739,
        unit_amount: 0,
        quantity: 1,
        amount: 0,
        pricing_model: 'per_unit',
        is_taxed: true,
        tax_amount: 0,
        tax_rate: 20.0,
        object: 'line_item',
        subscription_id: 'BTTzKOToz1begJXn',
        customer_id: '7266b973-a9a5-46cd-b8f8-5f81bdee938d',
        description: 'Primary Storage - included',
        entity_type: 'addon_item_price',
        entity_id: 'cloudpc-primarystorage-2022-free-EUR-Monthly',
        metered: false,
        discount_amount: 0,
        item_level_discount_amount: 0,
      },
    ],
    discounts: [
      {
        object: 'discount',
        entity_type: 'document_level_discount' as EstimationDiscountEntityType,
        description: '20,00 € off',
        amount: 2000,
        entity_id: '19AN2ETp5EfPG1Iy',
        discount_type: 'fixed_amount',
      },
    ],
    taxes: [
      {
        object: 'tax' as TaxObjectType,
        name: 'VAT',
        description: 'VAT @ 20 %',
        amount: 217,
      },
    ],
    line_item_taxes: [
      {
        tax_name: 'VAT',
        tax_rate: 20.0,
        tax_juris_type: 'country',
        tax_juris_name: 'France',
        tax_juris_code: 'FR',
        object: 'line_item_tax',
        line_item_id: 'li_BTcd00Tp5EkHrC6i',
        tax_amount: 217,
        is_partial_tax_applied: false,
        taxable_amount: 1082,
        is_non_compliance_tax: false,
      },
      {
        tax_name: 'VAT',
        tax_rate: 20.0,
        tax_juris_type: 'country',
        tax_juris_name: 'France',
        tax_juris_code: 'FR',
        object: 'line_item_tax',
        line_item_id: 'li_BTcd00Tp5EkI1C6j',
        tax_amount: 0,
        is_partial_tax_applied: false,
        taxable_amount: 0,
        is_non_compliance_tax: false,
      },
      {
        tax_name: 'VAT',
        tax_rate: 20.0,
        tax_juris_type: 'country',
        tax_juris_name: 'France',
        tax_juris_code: 'FR',
        object: 'line_item_tax',
        line_item_id: 'li_BTcd00Tp5EkICC6k',
        tax_amount: 0,
        is_partial_tax_applied: false,
        taxable_amount: 0,
        is_non_compliance_tax: false,
      },
    ],
    currency_code: Currency.EUR,
    round_off_amount: 0,
    line_item_discounts: [
      {
        object: 'line_item_discount',
        line_item_id: 'li_BTcd00Tp5EkHrC6i',
        discount_type: 'document_level_discount',
        discount_amount: 2000,
        coupon_id: '19AN2ETp5EfPG1Iy',
        entity_id: '19AN2ETp5EfPG1Iy',
      },
    ],
  },
};
