import { HttpResponse, http } from 'msw';
import { API_URL } from 'utils';

import mockedGetBilling from './fixtures/getBilling.json';
import mockedGetClonableSubscriptions from './fixtures/getClonableSubscription.json';
import mockedGetInvoices from './fixtures/getInvoices.json';
import mockedGetPayment from './fixtures/getPayment.json';
import mockedGetPersonalData from './fixtures/getPersonalData.json';
import mockedGetReferrals from './fixtures/getReferrals.json';
import mockedGetSubscriptions from './fixtures/getSubscriptions.json';
import mockedGetUser from './fixtures/getUser.json';
import mockedGetVmStatus from './fixtures/getVmStatus.json';
import mockedPutBilling from './fixtures/putBilling.json';
import mockedPutUser from './fixtures/putUser.json';

export const testHandlers = [
  http.get(`${API_URL}/account`, () =>
    HttpResponse.json(mockedGetUser, {
      status: 200,
    }),
  ),

  http.put(`${API_URL}/account`, () =>
    HttpResponse.json(mockedPutUser, {
      status: 200,
    }),
  ),

  http.post(`${API_URL}/account`, () =>
    HttpResponse.json(mockedPutUser, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/subscription/`, () =>
    HttpResponse.json(mockedGetSubscriptions, {
      status: 200,
    }),
  ),

  http.delete(`${API_URL}/subscription/:subscriptionId`, () =>
    HttpResponse.json(
      {},
      {
        status: 200,
      },
    ),
  ),

  http.post(`${API_URL}/subscription/:subscriptionId/reactivate`, () =>
    HttpResponse.json(
      {},
      {
        status: 200,
      },
    ),
  ),

  http.get(`${API_URL}/subscription/referral`, () =>
    HttpResponse.json(mockedGetReferrals, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/subscription/clonable/:accountUuid`, () =>
    HttpResponse.json(mockedGetClonableSubscriptions, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/account/billing`, () =>
    HttpResponse.json(mockedGetBilling, {
      status: 200,
    }),
  ),

  http.post(`${API_URL}/account/billing`, () =>
    HttpResponse.json(mockedPutBilling, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/account/payment`, () =>
    HttpResponse.json(mockedGetPayment, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/account/invoices`, () =>
    HttpResponse.json(mockedGetInvoices, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/account/personal/data`, () =>
    HttpResponse.json(mockedGetPersonalData, {
      status: 200,
    }),
  ),

  http.get(`${API_URL}/pu/shadow-vdi/vm/:subscriptionId/status`, () =>
    HttpResponse.json(mockedGetVmStatus, {
      status: 200,
    }),
  ),

  http.post(`${API_URL}/pu/shadow-vdi/vm/:subscriptionId/stop`, () =>
    HttpResponse.json(
      {},
      {
        status: 200,
      },
    ),
  ),

  http.post(`${API_URL}/pu/shadow-vdi/vm/:subscriptionId/disk-reset`, () =>
    HttpResponse.json(
      {},
      {
        status: 200,
      },
    ),
  ),

  http.post(`${API_URL}/pu/shadow-vdi/vm/:subscriptionId/readytoplay`, () =>
    HttpResponse.json(
      {},
      {
        status: 200,
      },
    ),
  ),

  http.post(`${API_URL}/account/zendesk/tickets`, () =>
    HttpResponse.json(
      {},
      {
        status: 200,
      },
    ),
  ),

  http.post(`${API_URL}/account/zendesk/tickets/upload`, () =>
    HttpResponse.json(
      { token: 'dxZgn7vtPGZ6PF2vGJ4j9XRsN' },
      {
        status: 200,
      },
    ),
  ),
];
