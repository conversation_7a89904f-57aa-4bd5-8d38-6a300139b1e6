const withFonts = require('next-fonts');
const withTM = require('next-transpile-modules')([
  'hooks',
  'mocks',
  'common-components',
  'shared-components',
  'types',
  'utils',
]);
const path = require('path');

const { i18n } = require('./next-i18next.config');

/** @type {import("next").NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  basePath: '/account',
  i18n,
  experimental: {
    externalDir: true,
    outputFileTracingRoot: path.join(__dirname, '../'),
  },

  images: {
    domains: ['statics.shadow.tech'],
  },
  async headers() {
    return [
      {
        // Append the "Service-Worker-Allowed" header
        // to each response, overriding the default worker's scope.
        source: '/(.*)',
        headers: [
          {
            key: 'Service-Worker-Allowed',
            value: '/',
          },
        ],
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/vm-manager',
        destination: `${process.env.NEXT_PUBLIC_BASE_URL}`,
        permanent: true,
      },
      {
        source: '/redirect/shop/business/catalog',
        destination: `${process.env.NEXT_PUBLIC_SHOP_URL}?funnel=b2b_purchase`,
        permanent: true,
      },
      {
        source: '/redirect/shop/business/catalog/:clonableSubscriptionId',
        destination: `${process.env.NEXT_PUBLIC_SHOP_URL}?funnel=b2b_purchase&clonableSubscriptionId=:clonableSubscriptionId`,
        permanent: true,
      },
    ];
  },
  output: 'standalone',
};

module.exports = withTM(withFonts(nextConfig));
