import * as jwt from 'jsonwebtoken';

declare module 'jsonwebtoken' {
  export interface JwtPayloadFederation extends jwt.JwtPayload {
    federation: {
      provider: string;
      subject: string;
      type: string;
    };
  }
}

declare global {
  interface Window {
    LC_API: any;
    LiveChatWidget: any;
    dataLayer: any;
    opera: any;
    __localeId__: DatesLocale;
    Didomi: any;
    didomiOnReady: any;
    __shadow_auth__?: {
      removeUser: () => void;
    };
  }
}

declare module 'react-hook-clipboard';
