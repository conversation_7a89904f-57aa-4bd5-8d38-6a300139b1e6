{"name": "account", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "prebuild": "node ../scripts/copy-i18n-files-to-public.js", "build": "next build", "start": "next start", "lint": "next lint", "i18n": "node ../scripts/prepare-i18next.js && i18next --silent --config ./config/i18next-parser-config.js && node ../scripts/i18next-parser-postprocess.js", "test": "cp ./babelconfig.js ./babel.config.js && jest && rm ./babel.config.js", "test:e2e": "playwright test", "tsc": "tsc --noEmit"}, "dependencies": {"@didomi/react": "^1.8.1", "@emotion/cache": "11.7.1", "@emotion/react": "^11.8.1", "@emotion/server": "^11.4.0", "@emotion/styled": "^11.8.1", "@flagship.io/react-sdk": "^3.3.0", "@hookform/resolvers": "^2.8.8", "@mui/lab": "^5.0.0-alpha.79", "@mui/material": "5.10.2", "@mui/styles": "5.10.2", "@mui/system": "5.10.2", "@mui/x-data-grid": "^5.17.1", "@tanstack/react-query": "^4.0.10", "@tanstack/react-query-devtools": "^4.0.10", "common-components": "*", "date-fns": "3.3.1", "hooks": "1.0.0", "i18next": "^21.8.14", "i18next-parser": "^8.0.0", "immer": "^9.0.12", "jest-canvas-mock": "^2.4.0", "lodash": "^4.17.21", "msw": "2.1.2", "next": "12.3.0", "next-fonts": "^1.5.1", "next-i18next": "^10.5.0", "next-transpile-modules": "^9.0.0", "notistack": "^2.0.4", "oidc-client-ts": "^2.0.1", "react": "18.2.0", "react-device-detect": "^2.2.2", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.28.0", "react-oidc-context": "^2.1.0", "shared-components": "1.0.0", "sharp": "^0.31.2", "store-js": "^2.0.4", "styled-jsx": "^5.0.2", "types": "1.0.0", "utils": "1.0.0", "yup": "^0.32.11", "zustand": "^3.7.1"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-syntax-flow": "^7.16.7", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/plugin-transform-runtime": "^7.16.10", "@emotion/babel-plugin": "^11.9.2", "@next/env": "^13.0.5", "@percy/cli": "^1.0.0-beta.76", "@playwright/test": "^1.28.1", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^13.4.0", "@types/jest": "^28.1.8", "@types/jsonwebtoken": "^8.5.9", "@types/lodash": "4.14.182", "@types/node": "17.0.21", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "@types/storejs": "^2.0.3", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "babel-jest": "^27.5.1", "babel-plugin-explicit-exports-references": "^1.0.2", "clsx": "^1.2.1", "eslint": "8.21.0", "eslint-config-next": "12.1.0", "eslint-config-prettier": "^8.4.0", "eslint-config-react-app": "^7.0.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.3", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-test-id": "^2.1.0", "eslint-webpack-plugin": "^3.2.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "jsonwebtoken": "^8.5.1", "mocks": "*", "prettier": "^2.5.1", "prop-types": "^15.8.1", "typescript": "^5.3.3", "undici": "5.28.2", "webpack": "^5.74.0", "whatwg-fetch": "^3.6.2"}, "msw": {"workerDirectory": "public"}}