import { test, expect } from '@playwright/test';

import { login } from './flows/login';
import {
  mockB2cBilling,
  mockPaymenIntent,
  mockB2cMembersMe,
} from './mocks/account.routes';
import { mockToken } from './mocks/authentication.routes';
import {
  mockCreateUsingTempToken,
  mockStripe,
  mockTrackInfoError,
} from './mocks/chargebee.routes';
import {
  mockB2cSubscription,
  mockReferral,
  mockUpdatesForSubscription,
} from './mocks/subscription.routes';

test.beforeEach(async ({ page }) => {
  await mockB2cSubscription(page);
  await mockB2cBilling(page);
  await mockToken(page);
  await mockCreateUsingTempToken(page);
  await mockTrackInfoError(page);
  await mockStripe(page);
  await mockPaymenIntent(page);
  await mockB2cMembersMe(page);
  await mockReferral(page);
  await mockUpdatesForSubscription(page);
});

test('should update account basic informations', async ({ page }) => {
  // Go to account default page
  await page.goto('http://localhost:3000');
  await login(page);

  await expect(page).toHaveURL('http://localhost:3000/account/fr-FR');
  await page.getByTestId('subscription-vm').waitFor();
  await page.locator('#didomi-notice-agree-button').click();

  await expect(page.locator('#logo-shadow-dark')).toBeVisible();

  // Go to account information page
  await page.getByTestId('navigation_ACCOUNT_button').click();
  await page.getByTestId('first_name-input').waitFor();
  await expect(page.getByTestId('submit-user_form-button')).toBeDisabled();

  // Enter new user values
  await page
    .getByTestId('first_name-input')
    .locator('input')
    .fill('New FirstName');
  await page
    .getByTestId('last_name-input')
    .locator('input')
    .fill('New LastName');
  await page.getByTestId('phone-input').locator('input').fill('***********');
  await page.getByTestId('birthdate-input').locator('input').fill('02/01/1970');

  // Click on save button
  await expect(page.getByTestId('submit-user_form-button')).toBeEnabled();
  await page.getByTestId('submit-user_form-button').click();
});
