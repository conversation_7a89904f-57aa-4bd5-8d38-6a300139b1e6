import { Page } from '@playwright/test';
import {
  b2cAccountData,
  b2cBillingData,
  b2cMemberData,
  paymentData,
  paymentIntentData,
} from 'mocks';

export async function mockB2cBilling(page: Page) {
  await page.route('https://api.spectr.be/v1/account/billing', route => {
    route.fulfill({
      status: 200,
      body: JSON.stringify(b2cBillingData),
    });
  });
}

export async function mockEmptyPayment(page: Page) {
  await page.route('https://api.spectr.be/v1/account/payment', route => {
    route.fulfill({
      body: JSON.stringify([]),
    });
  });
}

export async function mockPayment(page: Page) {
  await page.route('https://api.spectr.be/v1/account/payment', route => {
    route.fulfill({
      body: JSON.stringify(paymentData),
    });
  });
}

export async function mockB2cAccount(page: Page) {
  await page.route('https://api.spectr.be/v1/account/', route => {
    route.fulfill({
      body: JSON.stringify(b2cAccountData),
    });
  });
}

export async function mockPaymenIntent(page: Page) {
  await page.route('https://api.spectr.be/v1/account/payment/intent', route => {
    route.fulfill({
      body: JSON.stringify(paymentIntentData),
    });
  });
}

export async function mockB2cMembersMe(page: Page) {
  await page.route('https://api.spectr.be/v1/account/members/me', route => {
    route.fulfill({
      body: JSON.stringify(b2cMemberData),
    });
  });
}
