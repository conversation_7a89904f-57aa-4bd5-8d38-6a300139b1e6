import { Page } from '@playwright/test';
import {
  createUsingTempTokenData,
  publicGatewayData,
  paymentIntentConfirmData,
  paymentIntentRetrieveData,
  retrieveConfigData,
  stripeData,
} from 'mocks';

export async function mockCreateUsingTempToken(page: Page) {
  await page.route(
    `https://eu-shadow-test.chargebee.com/api/js/v2/tokens/create_using_temp_token`,
    route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify(createUsingTempTokenData),
      });
    },
  );
}

export async function mockTrackInfoError(page: Page) {
  await page.route(
    `https://eu-shadow-test.chargebee.com/api/internal/track_info_error`,
    route => {
      route.fulfill({
        status: 200,
      });
    },
  );
}

export async function mockPublicGatewayChargebee(page: Page) {
  await page.route(
    `https://eu-shadow-test.chargebee.com/api/internal/payment_intents/fetch_gateway_public_credential`,
    route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify(publicGatewayData),
      });
    },
  );
}

export async function mockRetrieveConfigChargebee(page: Page) {
  await page.route(
    `https://eu-shadow-test.chargebee.com/api/internal/component/retrieve_config`,
    route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify(retrieveConfigData),
      });
    },
  );
}

export async function mockPaymentIntentConfirmChargebee(page: Page) {
  await page.route(
    `https://eu-shadow-test.chargebee.com/api/internal/payment_intents/confirm`,
    route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify(paymentIntentConfirmData),
      });
    },
  );
}

export async function mockPaymentIntentRetrieveChargebee(page: Page) {
  await page.route(
    `https://eu-shadow-test.chargebee.com/api/internal/payment_intents/retrieve`,
    route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify(paymentIntentRetrieveData),
      });
    },
  );
}

export async function mockStripe(page: Page) {
  await page.route(`https://m.stripe.com/**`, route => {
    route.fulfill({
      status: 200,
      body: JSON.stringify(stripeData),
    });
  });
}
