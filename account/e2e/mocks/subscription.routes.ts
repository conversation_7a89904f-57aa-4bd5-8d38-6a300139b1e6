import { Page } from '@playwright/test';
import {
  estimateData,
  referralData,
  b2cSubscriptionData,
  updatesData,
} from 'mocks';

export async function mockEmptySubscription(page: Page) {
  await page.route('https://api.spectr.be/v1/subscription', route => {
    route.fulfill({
      body: JSON.stringify({ items: [] }),
    });
  });
}

export async function mockB2cSubscription(page: Page) {
  await page.route('https://api.spectr.be/v1/subscription?**', route => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        items: b2cSubscriptionData,
      }),
    });
  });
}

export async function mockEstimateSubscription(page: Page) {
  await page.route('https://api.spectr.be/v1/subscription/estimate', route => {
    route.fulfill({
      body: JSON.stringify(estimateData),
    });
  });
}

export async function mockReferral(page: Page) {
  await page.route('https://api.spectr.be/v1/subscription/referral', route => {
    route.fulfill({
      status: 200,
      body: JSON.stringify(referralData),
    });
  });
}

export async function mockUpdatesForSubscription(page: Page) {
  await page.route(
    'https://api.spectr.be/v1/subscription/**/updates',
    route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify(updatesData),
      });
    },
  );
}
