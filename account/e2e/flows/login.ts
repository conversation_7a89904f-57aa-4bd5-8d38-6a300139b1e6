import { Page, expect } from '@playwright/test';
import jwt from 'jsonwebtoken';

export async function login(page: Page) {
  const token = jwt.sign(
    {
      exp: Math.floor(Date.now() / 1000) + 60 * 60,
      at_hash: 'Eife7zPsR1inXBHbAQzbEA',
      aud: ['f278c30e-4439-41e9-a019-8fcdec943b2e'],
      auth_time: Date.now() / 1000,
      family_name: '<PERSON><PERSON><PERSON>',
      federation: {
        provider: 'kratos',
        subject: 'a1a96685-2ada-405e-a779-f21a855d8ec6',
        type: 'kratos',
      },
      given_name: '<PERSON>',
      iat: **********,
      iss: 'https://auth.spectr.be/hydra/',
      jti: '57254eae-675f-448d-9716-1982e048089c',
      locale: 'FR',
      name: '<PERSON>',
      rat: **********,
      sid: 'eecd6489-7f1b-4f9a-9f59-bf0a2015d085',
      sub: 'd1012ad9-370d-4d73-92d1-1b47b176e766',
    },
    'secret',
  );
  await page.evaluate(jwtToken => {
    window.localStorage.setItem(
      'oidc.user:https://auth.spectr.be/hydra:f278c30e-4439-41e9-a019-8fcdec943b2e',
      `{"id_token":"${jwtToken}","session_state":null,"access_token":"G-yYv30RvOATXHPf-61TIbsR4OsT48dek7Nr0HNIF4k.xHI4d8XfbjPcdxp7Iq3L9odxBG3knAFTARnkREKApWA","token_type":"bearer","scope":"openid profile","profile":{"family_name":"Debove","federation":{"provider":"kratos","subject":"a1a96685-2ada-405e-a779-f21a855d8ec6","type":"kratos"},"given_name":"Christopher","locale":"FR","name":"Christopher Debove","rat":**********,"sid":"eecd6489-7f1b-4f9a-9f59-bf0a2015d085","sub":"d1012ad9-370d-4d73-92d1-1b47b176e766"},"expires_at":${
        Math.floor(Date.now() / 1000) + 60 * 60
      }}`,
    );
  }, token);
  await expect(page).toHaveURL(
    new RegExp('^https://auth.spectr.be/ui/hydra/landing'),
  );
}
