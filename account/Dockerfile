FROM node:20-alpine3.18 AS runtime-image

FROM runtime-image as deps

ARG TOKEN

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk update && apk add --no-cache libc6-compat python3 make g++

WORKDIR /app

# Set the NPM token using npm config set
ENV NPM_TOKEN=${TOKEN}
COPY .npmrc .
RUN sed -i '$ s/#//' .npmrc

COPY package.json yarn.lock ./
COPY ./account ./account
COPY ./common-components ./common-components
COPY ./shared-components ./shared-components
COPY ./utils ./utils
COPY ./types ./types
COPY ./hooks ./hooks
COPY ./mocks ./mocks
RUN yarn install --frozen-lockfile --non-interactive

# Rebuild the source code only when needed
FROM runtime-image AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/account/node_modules ./account/node_modules
COPY --from=deps /app/common-components/node_modules ./commont-components/node_modules
COPY --from=deps /app/shared-components/node_modules ./shared-components/node_modules
COPY --from=deps /app/utils/node_modules ./utils/node_modules
COPY --from=deps /app/types/node_modules ./types/node_modules
COPY --from=deps /app/hooks/node_modules ./hooks/node_modules
COPY --from=deps /app/mocks/node_modules ./mocks/node_modules
COPY . .
ENV NEXT_TELEMETRY_DISABLED 1
RUN yarn workspace account build

# Production image, copy all the files and run next
FROM runtime-image AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/account/public ./account/public
COPY --from=builder /app/account/package.json ./package.json

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/account/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/account/.next/static ./account/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/account/next.config.js ./
COPY --from=builder --chown=nextjs:nodejs /app/account/next-i18next.config.js ./



USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "./account/server.js"]
