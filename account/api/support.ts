import { fetchSpectrApi } from 'utils';

import { ISupportFormPayload, IFile } from '@/types/api';
import { API_URL } from '@/utils/constants';

const sendSupportMessage = async (
  authToken: string,
  payload: ISupportFormPayload,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/zendesk/tickets`,
    {
      method: 'POST',
      body: payload,
    },
  );
};

const uploadMessageAttachment = async (
  authToken: string,
  filesPayload: IFile[],
): Promise<Array<string> | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/zendesk/tickets/upload`,
    {
      method: 'POST',
      body: filesPayload,
    },
  );
};

export { sendSupportMessage, uploadMessageAttachment };
