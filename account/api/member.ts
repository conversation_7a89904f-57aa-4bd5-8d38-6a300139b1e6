import { IM<PERSON>ber, ITag } from 'types';
import { fetchSpectrApi, fetchText } from 'utils';

import { IDeletePayload } from '@/types/api';
import { API_URL } from '@/utils/constants';

const fetchMembers = async (
  authToken: string,
  membersParams?: { [key: string]: string },
): Promise<IMember[] | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/members`, {
    method: 'GET',
    body: membersParams,
  });
};

const fetchMember = async (
  authToken: string,
  memberId: string,
): Promise<IMember | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members/${memberId}`,
  );
};

const fetchCurrentMember = async (
  authToken: string,
): Promise<IMember | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/account/members/me`);
};

const inviteMember = async (
  authToken: string,
  email: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members?member_email=${email}&send_invite=true`,
    {
      method: 'POST',
    },
  );
};

const resendInvitation = async (
  authToken: string,
  email: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members?member_email=${email}&renew_expiration=true`,
    {
      method: 'PUT',
    },
  );
};

const activateMember = async (
  authToken: string,
  memberId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members/activate/${memberId}`,
    {
      method: 'PUT',
    },
  );
};

const deactivateMember = async (
  authToken: string,
  memberId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members/deactivate/${memberId}`,
    {
      method: 'PUT',
    },
  );
};

const deleteMember = async (
  authToken: string,
  deletePayload: IDeletePayload,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members/delete`,
    {
      method: 'POST',
      body: {
        ...deletePayload,
      },
    },
  );
};

const confirmMemberInvite = async (
  inviteToken: string,
  authToken: string,
): Promise<string> => {
  return fetchText(`${API_URL}/account/members/join/${inviteToken}`, {
    method: 'POST',
    headers: { Authorization: `Bearer ${authToken}` },
  });
};

const createTag = async (
  authToken: string,
  tag: string,
): Promise<ITag[] | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/members/me/tags`,
    {
      method: 'POST',
      body: {
        tags: [{ name: tag }],
      },
    },
  );
};

const updateMemberTags = async (
  authToken: string,
  memberId: string,
  tags: ITag[],
): Promise<undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/account/member/${memberId}/tags`,
    {
      method: 'PUT',
      body: {
        tags,
      },
    },
  );
};

export {
  fetchMembers,
  fetchMember,
  fetchCurrentMember,
  inviteMember,
  resendInvitation,
  activateMember,
  deactivateMember,
  deleteMember,
  confirmMemberInvite,
  createTag,
  updateMemberTags,
};
