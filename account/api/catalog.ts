import {
  IAnyProduct,
  ICatalog,
  IApiProduct,
  MaybeAuthToken,
  Market,
  ProductFamilyId,
  ProductType,
  IAnyOffer,
} from 'types';
import { fetchSpectrApi } from 'utils';

import { API_URL } from '@/utils/constants';

const familyIds = Object.values(ProductFamilyId);
const productTypes = Object.values(ProductType);

interface FetchCatalogPayload {
  currency: string;
  region: string;
  market: Market;
  subscriptionId: string | undefined;
  accessCode?: string;
  withStock?: boolean;
  enabledForCheckout?: boolean;
}

const fetchCatalog = async (
  authToken: MaybeAuthToken,
  payload: FetchCatalogPayload,
): Promise<ICatalog> => {
  const apiResult: IApiProduct[] = await fetchSpectrApi(
    authToken ? `Bearer ${authToken}` : '', // Used for server fetch and non co user fetch
    `${API_URL}/catalog/`,
    {
      method: 'GET',
      body: {
        currency_code: payload.currency.toUpperCase(),
        region: payload.region.toUpperCase(),
        country: payload.market,
        ...(payload.withStock === false && { with_stock: 'false' }),
        ...(payload.enabledForCheckout === false && {
          enabled_for_checkout: 'false',
        }),
        ...(payload.subscriptionId && {
          subscription_id: payload.subscriptionId,
        }),
        ...(payload.accessCode && { access_code: payload.accessCode }),
      },
    },
  );

  return {
    products: {
      byId: apiResult.reduce<Record<string, IAnyProduct>>((acc, apiProduct) => {
        const hasValidFamilyId = (familyIds as string[]).includes(
          apiProduct.item_family_id,
        );
        const hasValidProductType = (productTypes as string[]).includes(
          apiProduct.type,
        );

        if (!hasValidFamilyId || !hasValidProductType) {
          return acc;
        }

        acc[apiProduct.id] = {
          ...apiProduct,
          offers: apiProduct.offers?.map(offer => offer.id),
        } as IAnyProduct;

        return acc;
      }, {}),

      allIds: apiResult.map(product => product.id),
    },
    offers: {
      byId: apiResult.reduce<Record<string, IAnyOffer>>((acc, productRaw) => {
        productRaw.offers?.forEach(offer => {
          acc[offer.id] = {
            ...offer,
            itemFamilyId: productRaw.item_family_id as ProductFamilyId,
            periodicity: `${offer.period}-${offer.period_unit}`,
          };
        });
        return acc;
      }, {}),

      allIds: apiResult.reduce<Array<string>>((acc, product) => {
        product.offers?.forEach(offer => {
          acc.push(offer.id);
        });
        return acc;
      }, []),
    },
  };
};

export { fetchCatalog };
