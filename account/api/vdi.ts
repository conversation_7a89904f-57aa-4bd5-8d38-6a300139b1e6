import { fetchSpectrApi } from 'utils';

import { IReadyToPlayConfig, IVdiStatus } from '@/types/api';
import { API_URL } from '@/utils/constants';

const fetchVmStatus = async (
  authToken: string,
  subscriptionId: string,
): Promise<IVdiStatus | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/status`,
  );
};

const setVmStop = async (
  authToken: string,
  subscriptionId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/stop`,
    {
      method: 'POST',
    },
  );
};

const updateVmReadyToPlay = async (
  authToken: string,
  subscriptionId: string,
  readyToPlayConfig: IReadyToPlayConfig,
  timezone: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/readytoplay`,
    {
      method: 'POST',
      body: {
        ...readyToPlayConfig,
        timezone,
      },
    },
  );
};

const deleteVmReadyToPlay = async (
  authToken: string,
  subscriptionId: string,
): Promise<null> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/readytoplay`,
    {
      method: 'DELETE',
    },
  );
};

const setVmDiskReset = async (
  authToken: string,
  subscriptionId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/pu/shadow-vdi/vm/${subscriptionId}/disk-reset`,
    {
      method: 'POST',
    },
  );
};

export {
  fetchVmStatus,
  setVmDiskReset,
  setVmStop,
  updateVmReadyToPlay,
  deleteVmReadyToPlay,
};
