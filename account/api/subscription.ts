import {
  ISubscriptionPayload,
  ITag,
  MaybeAuthToken,
  IEstimationPayload,
  IEstimation,
  ISubscriptionRetention,
  ProductFamilyId,
} from 'types';
import { fetchSpectrApi } from 'utils';

import { IProductFamilySubscriptionStatus, IReferrals } from '@/types/api';
import type { CancellationReasons } from '@/types/subscriptions';
import { API_URL } from '@/utils/constants';

const addSubscriptions = async (
  authToken: string,
  subscriptionPayload: ISubscriptionPayload,
): Promise<string | undefined> => {
  return fetchSpectrApi(`Bearer ${authToken}`, `${API_URL}/subscription`, {
    method: 'POST',
    body: {
      ...subscriptionPayload,
    },
  });
};

const cancelSubscription = async (
  authToken: string,
  subscriptionId: string,
  cancellationReasons: CancellationReasons,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/cancel`,
    {
      method: 'PUT',
      body: {
        ...cancellationReasons,
      },
    },
  );
};

const reactivateSubscription = async (
  authToken: string,
  subscriptionId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/cancellation`,
    {
      method: 'DELETE',
    },
  );
};

const fetchReferrals = async (
  authToken: string,
): Promise<IReferrals | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/referral`,
  );
};

const fetchProductFamilySubscriptionStatuses = async (
  authToken: MaybeAuthToken,
  productFamilyIds: ProductFamilyId[],
): Promise<IProductFamilySubscriptionStatus[]> => {
  const queryParams = productFamilyIds
    .map(id => `product_family=${id}`)
    .join('&');

  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/statuses?${queryParams}`,
  );
};

const unassignMemberFromSubscription = async (
  authToken: string,
  userId: string,
  subscriptionId?: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/unassign/${userId}`,
    {
      method: 'PUT',
    },
  );
};

const assignMemberToSubscription = async (
  authToken: string,
  userId: string,
  subscriptionId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/assign/${userId}`,
    {
      method: 'PUT',
    },
  );
};

const setVmName = async (
  authToken: string,
  subscriptionId: string,
  name: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/name`,
    {
      method: 'PUT',
      body: {
        name,
      },
    },
  );
};

const setVmTags = async (
  authToken: string,
  subscriptionId: string,
  tags: ITag[],
): Promise<undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/tags`,
    {
      method: 'PUT',
      body: {
        tags,
      },
    },
  );
};

const cancelScheduledChanges = async (
  authToken: string,
  subscriptionId: string,
): Promise<string | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/scheduled-changes/${subscriptionId}`,
    {
      method: 'DELETE',
    },
  );
};

const getSubscriptionModificationEstimation = async (
  authToken: MaybeAuthToken,
  estimatePayload: IEstimationPayload,
  subscriptionIdToModify: string,
): Promise<IEstimation | undefined> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/estimate/${subscriptionIdToModify}`,
    {
      method: 'POST',
      body: {
        ...estimatePayload,
      },
    },
  );
};

const getSubscriptionRetention = async (
  authToken: MaybeAuthToken,
  subscriptionId: string,
): Promise<ISubscriptionRetention> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/retention`,
    {
      method: 'GET',
    },
  );
};

const applySubscriptionRetention = async (
  authToken: MaybeAuthToken,
  subscriptionId: string,
): Promise<boolean> => {
  return fetchSpectrApi(
    `Bearer ${authToken}`,
    `${API_URL}/subscription/${subscriptionId}/retention`,
    {
      method: 'PUT',
    },
  );
};

export {
  addSubscriptions,
  cancelSubscription,
  fetchReferrals,
  fetchProductFamilySubscriptionStatuses,
  reactivateSubscription,
  unassignMemberFromSubscription,
  assignMemberToSubscription,
  setVmName,
  setVmTags,
  cancelScheduledChanges,
  getSubscriptionModificationEstimation,
  getSubscriptionRetention,
  applySubscriptionRetention,
};
