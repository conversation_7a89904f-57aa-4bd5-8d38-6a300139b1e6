<svg width="48" height="48" fill="none" version="1.1" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
 <g transform="scale(.08 .079734)" filter="url(#filter0_i_2_17)">
  <circle cx="300" cy="300" r="300" fill="url(#paint0_radial_2_17)"/>
 </g>
 <mask id="path-2-inside-1_2_17" fill="white">
  <path d="m413.56 215.17c14.19 19.189 28.585 47.756 28.585 84.68 0 84.188-71.145 142.29-142.29 142.29s-142.29-58.576-142.29-142.29c0-64.43 52.41-142.29 141.82-142.29 89.405 0 97.706-73.516 97.706-73.516s-34.861-21.344-105.53-21.344c-97.469 0-228.85 84.188-228.85 237.15 0 113.6 90.354 237.15 237.15 237.15s237.15-123.08 237.15-237.15c0-75.416-34.997-128.8-60.115-157.4z" clip-rule="evenodd" fill-rule="evenodd"/>
 </mask>
 <g transform="scale(.08 .079734)" filter="url(#filter1_i_2_17)">
  <path d="m413.56 215.17c14.19 19.189 28.585 47.756 28.585 84.68 0 84.188-71.145 142.29-142.29 142.29s-142.29-58.576-142.29-142.29c0-64.43 52.41-142.29 141.82-142.29 89.405 0 97.706-73.516 97.706-73.516s-34.861-21.344-105.53-21.344c-97.469 0-228.85 84.188-228.85 237.15 0 113.6 90.354 237.15 237.15 237.15s237.15-123.08 237.15-237.15c0-75.416-34.997-128.8-60.115-157.4z" clip-rule="evenodd" fill="#fff" fill-rule="evenodd"/>
 </g>
 <path transform="scale(.08 .079734)" d="m413.56 215.17-1.508-1.314-1.055 1.211 0.955 1.292zm-16.473-131.13 1.987 0.2243 0.143-1.2652-1.086-0.6648zm79.803 58.404 1.503-1.319-1.509-1.719-1.502 1.725zm-64.938 73.912c14.014 18.952 28.193 47.112 28.193 83.491h4c0-37.468-14.612-66.443-28.976-85.87zm28.193 83.491c0 82.967-70.129 140.29-140.29 140.29v4c72.129 0 144.29-58.88 144.29-144.29zm-140.29 140.29c-70.156 0-140.29-57.793-140.29-140.29h-4c0 84.931 72.157 144.29 144.29 144.29zm-140.29-140.29c0-63.568 51.743-140.29 139.82-140.29v-4c-90.738 0-143.82 78.999-143.82 144.29zm139.82-140.29c45.356 0 70.382-18.697 84.002-37.589 6.779-9.402 10.695-18.797 12.917-25.839 1.112-3.5235 1.803-6.4662 2.217-8.5381 0.207-1.0363 0.345-1.8556 0.431-2.4218 0.044-0.2831 0.075-0.5031 0.095-0.6553 0.01-0.0761 0.017-0.1353 0.022-0.1771 3e-3 -0.0208 5e-3 -0.0373 6e-3 -0.0494 1e-3 -6e-3 2e-3 -0.0109 2e-3 -0.0147 0-0.0019 1e-3 -0.0039 1e-3 -0.0049 0-0.0017 0-0.0032-1.987-0.2275-1.988-0.2244-1.988-0.2253-1.988-0.2259v2e-4 0.0053c-1e-3 0.0057-2e-3 0.0159-4e-3 0.0304-3e-3 0.0289-9e-3 0.0751-0.018 0.1381-0.016 0.126-0.043 0.3188-0.082 0.5741-0.079 0.5107-0.206 1.2709-0.401 2.2449-0.389 1.9485-1.045 4.7493-2.108 8.1179-2.128 6.7425-5.876 15.726-12.347 24.704-12.881 17.866-36.709 35.928-80.758 35.928zm97.706-75.516c1.044-1.7057 1.043-1.7063 1.042-1.7069 0-3e-4 -2e-3 -1e-3 -3e-3 -0.0017-2e-3 -0.0012-4e-3 -0.0028-7e-3 -0.0047-7e-3 -0.0038-0.015-0.0089-0.026-0.0153-0.021-0.0127-0.05-0.0304-0.089-0.0531-0.076-0.0452-0.186-0.11-0.331-0.193-0.29-0.166-0.716-0.4048-1.279-0.7061-1.127-0.6025-2.802-1.4546-5.026-2.4728-4.45-2.0363-11.1-4.7365-19.964-7.4317-17.73-5.391-44.309-10.758-79.849-10.758v4c35.131 0 61.318 5.3045 78.686 10.585 8.685 2.6407 15.167 5.2764 19.462 7.242 2.148 0.9828 3.749 1.7981 4.805 2.3628 0.527 0.2823 0.919 0.502 1.175 0.6487 0.128 0.0733 0.222 0.1284 0.282 0.1639 0.03 0.0178 0.051 0.0306 0.064 0.0384 6e-3 0.0039 0.011 0.0066 0.013 8e-3 1e-3 6e-4 2e-3 1e-3 2e-3 1e-3l-1e-3 -2e-4c0-3e-4 -1e-3 -6e-4 1.044-1.7063zm-105.53-23.344c-98.352 0-230.85 84.875-230.85 239.15h4c0-151.65 130.26-235.15 226.85-235.15zm-230.85 239.15c0 114.51 91.072 239.15 239.15 239.15v-4c-145.51 0-235.15-122.47-235.15-235.15zm239.15 239.15c148.08 0 239.15-124.16 239.15-239.15h-4c0 113.15-89.634 235.15-235.15 235.15zm239.15-239.15c0-76.067-35.303-129.9-60.612-158.72l-3.006 2.639c24.927 28.385 59.618 81.318 59.618 156.08zm-63.623-158.72-63.33 72.722 3.017 2.627 63.329-72.722z" fill="#fff" mask="url(#path-2-inside-1_2_17)"/>
 <defs>
  <filter id="filter0_i_2_17" x="0" y="0" width="600" height="602" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dy="4"/>
   <feGaussianBlur stdDeviation="1"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_2_17"/>
  </filter>
  <filter id="filter1_i_2_17" x="62.7" y="62.7" width="474.3" height="489.3" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dy="30"/>
   <feGaussianBlur stdDeviation="7.5"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0.211765 0 0 0 0 0.32549 0 0 0 0 0.8 0 0 0 0.25 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_2_17"/>
  </filter>
  <radialGradient id="paint0_radial_2_17" cx="0" cy="0" r="1" gradientTransform="matrix(600 -600 868.24 868.24 -.00014726 600)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#34BAF4" offset="0"/>
   <stop stop-color="#3D46CC" offset=".48438"/>
   <stop stop-color="#5334D6" offset=".87941"/>
  </radialGradient>
 </defs>
</svg>
