{"account": {"billing": {"contactSupport": "In order to change your country, <0>contact the support</0>.", "title": "Billing information", "update": {"error": {"message": "An error occurred when updating your billing information:"}, "submit": {"label": "Save billing information"}, "success": {"message": "Your billing information has been updated.", "title": "Billing information updated"}}}, "company": {"update": {"error": {"message": "An error occurred when updating your company information:"}, "success": {"message": "Your company information has been updated.", "title": "Company infos updated"}}}, "details": {"creditsMessage": "Good news! We will deduct <bold>{{ amount }}</bold> from your next bill."}, "payment": {"add": {"link": "Add a new payment method"}, "edit": {"link": "Edit"}, "type": {"none": "No payment method", "card": "Credit card", "bancontact": "Bancontact", "ideal": "Ideal", "paypal_express_checkout": "PayPal", "sepa": "SEPA", "sofort": "Sofort", "direct_debit": "SEPA"}}, "privacy": {"cta": {"label": "Change my privacy preferences"}, "title": "Manage your privacy preferences"}, "user": {"downloadData": {"label": "Download my data"}, "editEmail": {"label": "Edit my email"}, "editPassword": {"label": "Security settings"}, "form": {"submit": {"label": "Save account information"}}, "resendVerificationEmail": {"label": "Resend the verification email"}, "title": "Account information", "update": {"error": {"message": "An error occurred when updating your personal information:"}, "success": {"message": "Your personal information has been updated.", "title": "User infos updated"}}, "security": {"buttons": {"hub": "Manage my security settings", "password": "Change my password"}, "text": "Strengthen the security of your account by activating two-factor authentication (2FA). You can use an authentication app or security keys. Feel free to generate backup codes as an alternative to 2FA authentication.<2>Learn more about two-factor authentication.</2>", "title": "Security settings"}, "socials": {"buttons": {"text": "Manage my login settings"}, "text": "To log in to your Shadow account, you can set up your Shadow password, or link an external account. Email communications from Shadow will continue to be sent to the email address of your Shadow account.", "title": "Alternative login methods"}}}, "businessManager": {"managerList": {"footer": {"rowPerPage": "Row per page", "total": "Total:", "users": "users", "vms": "PCs", "driveGroups": "groups"}, "user": {"actions": {"activateMember": {"buttonLabel": "Activate this account", "notification": {"error": "Error reactivating member {{email}}: {{error}}", "success": "Successfully reactivated member {{email}}"}}, "deactivateMember": {"buttonLabel": "Disable user", "notification": {"error": "Error deactivating member {{email}}: {{error}}", "success": "Successfully deactivated member {{email}}"}}, "deleteMember": {"buttonLabel": "Delete user", "modal": {"content": "This operation cannot be undone.", "title": "Delete user"}, "notification": {"alreadyAssigned": "Cannot delete, member is currently assigned to a PC.", "error": "Error deleting member {{email}}: {{error}}", "success": "Successfully deleted member {{email}}"}}, "resendInvitation": {"buttonLabel": "Resend email confirmation", "notification": {"error": "Error resending invitation to {{email}}: {{error}}", "success": "Successfully resend invitation to {{email}}"}}, "updateTags": {"notification": {"error": "Error updating user's tags : {{error}}", "success": "Successfully updating user's tags"}}}, "heading": {"created": "Created", "role": "Role", "user": "User"}, "invite": {"error": {"message": "You have already sent an invitation to {{ email }}", "title": "An error has occured when sending an invitation"}, "success": {"message": "{{ email }} will receive an email shortly inviting him to create an account in your team and get access to his Shadow PC Pro.", "title": "Invitation sent"}}, "options": {"createUser": "Add new user"}}, "vm": {"empty": {"createVmButtonLabel": "Create your first Shadow PC Pro", "description": "From here, you’ll be able to manage all your Shadow PCs Pro and invite new users to join your team. Create your first Shadow PC Pro and use its power to fulfill your tasks.", "illustrationAlt": "Shadow PC Manager", "title": "Welcome on Shadow Business Manager!"}, "heading": {"created": "Created", "name": "Name", "status": "Status", "tags": "Tags", "type": "Config", "user": "User"}, "noUserAssigned": "No user is assigned", "options": {"createVmButtonLabel": "New Shadow PC"}, "subscriptionStatus": {"active": "Active", "cancelled": "Cancelled", "future": "Creating...", "in_trial": "In trial", "non_renewing": "Cancellation in progress", "not_paid": "Unpaid", "paused": "Paused"}, "vmStatus": {"maintenance": "maintenance", "running": "running", "stopped": "stopped", "on_hold": "unpaid"}}}, "subscription": {"plan": {"vm": {"logoAlt": "Shadow"}}}, "vmDetails": {"created": {"notification": {"success": {"message": "The Shadow PC Pro is being setup, it can last up to 1 hour. You will receive an email as soon as your Shadow PC Pro will be available.", "title": "Your Shadow PC Pro creation has started"}}}, "user": {"actions": {"assignMember": {"notification": {"error": "Error assigning member {{email}}: {{error}}", "success": "Successfully assigned {{email}}"}, "server": {"error": "User already assigned to another PC."}}, "revokeMember": {"notification": {"error": "Error revoke member {{email}}: {{error}}", "success": "Successfully revoke {{email}}"}}}, "assignUser": "Assign", "assignUserPlaceholder": "Search a user...", "noUser": "No user is assigned to this Shadow PC Pro.", "revokeMember": {"buttonLabel": "Revoke access", "modal": {"content": "The user will not have access to this Shadow PC Pro anymore, but will still remains in your users list.<1></1><2></2>The Shadow PC Pro will not be deleted.", "title": "Revoke access"}}, "title": "User", "vmNotStopped": "Cannot assign or revoke new users until this PC is stopped."}, "vm": {"configuration": "Configuration", "creationDate": "Creation Date", "datacenter": "Datacenter", "deleteVm": "Delete this PC", "id": "ID", "internalStorage": {"label": "Internal Storage", "value": "{{totalStorageQty, storageUnit}}"}, "offer": "Offer", "renameVm": "Rename this PC", "resetVm": "Reset this PC", "stopVm": "Stop this PC", "subscriptionRenewal": "Subscription renewal", "tags": "Tags", "vmTags": "Manage tags", "CannotClone": "Cannot clone this PC", "clone": "Clone this PC", "outOfStock": "Out of Stock", "copy": "<PERSON>pied"}}, "vmOnDemand": {"buttons": {"apiDocumentation": {"label": "API Documentation"}, "getApiKey": {"label": "Get your API access and {{startingCreditsPrice}} starting credits for only {{apiAccessPrice}}"}, "priceTable": {"label": "Price table"}}, "status": {"active": "Active", "cancelled": "Cancelled", "future": "Future", "inTrial": "In trial", "nonRenewing": "Non renewing", "paused": "Paused"}, "subscribed": {"contactUs": "For any concerns about your API access, billing or account, <2>please contact us</2>.", "subtitle": "Shadow Spot Computing is the cost-efficient solution for all your VM needs. Perfect for generative AI, 3D & video rendering, machine learning, CI/CD or complex physics simulations.", "title": "Spot Computing API access"}, "successAlert": {"body": "Your API credentials has been sent by email. If you didn’t received them under one hour, <2>contact us</2>.", "title": "Your API access has been created"}, "unsubscribed": {"asterisk": "*Depending on the currency conversion in your market. Pricing example for NVIDIA P5000 16GB, Xeon 2678V3 8 threads@3.1Ghz, 12GB RAM, 1Gbps/100Mbps.", "subtitle": "Shadow Spot Computing is the cost-efficient solution for all your VM needs. Perfect for generative AI, 3D & video rendering, machine learning, CI/CD or complex physics simulations.", "title": "Start now from €0.185/hr*"}}, "user": {"role": {"admin": "admin", "member": "member"}, "status": {"disabled": "Deactivated", "expired": "Expired", "pending": "Invitation sent"}}, "vmTagManager": {"title": "Tags"}}, "company": {"button": "Save company information", "title": "Your company"}, "consentBanner": {"title": "Shadow consent banner"}, "downloadData": {"modal": {"title": "Download personal information"}}, "emailing": {"title": "Newsletter", "update": {"error": "", "success": "Your preferences have been saved!"}, "shadow": {"title": "Shadow PC", "description": "Sign Up to stay up to date about our last news and exclusive offers. Unsubscribe easily whenever you want."}, "storage": {"title": "Shadow Storage", "description": "Sign Up to stay up to date about our last news and exclusive offers. Unsubscribe easily whenever you want."}, "drive": {"title": "Shadow Drive", "description": "Sign Up to stay up to date about our last news and exclusive offers. Unsubscribe easily whenever you want."}}, "errors": {"api": {"changeVmPeriodicity": "An error occurred while updating your subscription, please try again later.", "changeVmPeriodicityEstimate": "An error occurred while getting an estimation for updating your subscription, please try again later.", "tooManyRequest": "Our website is currently experiencing too many simultaneous connections and cannot access your request at the moment. <PERSON> apologizes for the inconvenience and advises you to try again in a few minutes."}}, "footer": {"cgu": "Terms of Use", "companyName": "© Shadow", "cookies": "Cookies", "legal": "Legal", "privacy": "Privacy"}, "form": {"birthdate": {"error": {"invalid": "You must be 15 or older"}}, "configuration": {"label": {"information": "In manual configuration, you will need to set up the language, keyboard, and your Windows PC yourself."}}, "word": {"error": {"mismatch": "Sorry the word you entered is different from what we ask you to type. \nPlease verify your input and correct it."}}}, "header": {"download": {"label": "Download"}, "link": {"logout": "Log out"}, "login": {"welcome": {"label": "Welcome"}}, "logoAlt": "Shadow", "logoBusinessAlt": "Shadow PC Enterprise"}, "infoBanner": {"unpaid": {"error": {"content": "Your payment has been declined. Please try again later, or update your payment method by clicking <0>here</0>", "title": "Payment failed"}, "infos": {"content": "Your {{ productName }} subscription is unpaid. Your {{ productName }} subscription will be terminated and data erased $t(infoBanner.unpaid.infos.daysBeforeTermination, {\"count\": {{daysBeforeTermination}} }) if your subscription remains unpaid. Please <0>retry your payment</0> or <1>update your payment method</1> to avoid losing your subscription and data.", "daysBeforeTermination_zero": "in less than a day", "daysBeforeTermination_one": "within 1 day", "daysBeforeTermination_other": "within {{count}} days", "contentBusiness": "One or more invoices remain unpaid to this day. Please regularize your subscription as soon as possible. Please <0>retry your payment</0> or <1>update your payment method</1> to avoid losing your subscription and data.", "productName": {"cloudpc": "Shadow PC", "shadow-drive": "Shadow Drive"}}, "success": {"content": "Your payment has been successful. For more information and details, please check your invoice", "title": "Payment succeeded"}}}, "invoices": {"list": {"body": {"link": {"csv": "Download CSV", "pdf": "Download PDF", "title": "Download your invoice"}, "status": {"not_paid": "Unpaid", "paid": "Paid", "payment_due": "Payment due", "pending": "Pending", "posted": "Posted", "voided": "Voided"}, "statusTooltip": {"not_paid": "Your invoice hasn't been paid, please update your payment method or retry a payment.", "payment_due": "Your invoice's payement hasn't been fulfilled, please update your payment method or retry a payment.", "pending": "Your invoice is waiting to be fulfilled.", "posted": "Your invoice has been posted.", "voided": "Your invoice has been voided due to various reasons. Please contact support if you do not know why."}}, "download": {"error": "An error occurred while downloading your invoice, please try again later"}, "heading": {"date": "Date", "link": " ", "status": "Status", "total": "Amount"}}, "noInvoice": "No invoices", "title": "Invoices"}, "navigation": {"route": {"account": "Account", "download": "Download", "drive": "Shadow Drive", "shadow": "My Shadow PC", "shadowB2b": "My Shadow PC Pro", "support": "Support", "users": "Users", "vmOnDemand": "Spot Computing", "vms": "All my PCs", "billing": "Billing", "games": "Game Store", "driveGroups": "Shadow Drive Family", "Security": "Security"}, "title": {"administration": "Your fleet", "settings": "Settings", "account": "Account", "drive": "Shadow Drive", "pc": "Shadow PC"}}, "notFound": {"description": "The page your are looking for does not exist.", "pageTitle": "404 - Shadow Account - The page your are looking for does not exist.", "title": "404 not found"}, "referral": {"sharing": {"clipboardError": "Error when copying referral code!", "clipboardSuccess": "Referral code successfully copied!", "copy": "Copy", "shareEmail": {"body": "Join%20me%20on%20Shadow%20and%20save%205€%20on%20your%20first%20month!%20Just%20use%20my%20referral%20code%20{{code}}%20or%20click%20here:%20{{inviteUrl}}", "subject": "Join%20me%20on%20Shadow", "title": "Send an Email"}, "shareFacebook": "Share on Facebook", "shareMessenger": "Share on Messenger", "shareTwitter": "Share on Twitter", "shareWhatsApp": "Share on WhatsApp", "subtitle": "Offer 5{{ currency }}, and save 1{{ currency }} for each referee on your monthly price (excluding Shadow Drive).", "title": "Save on your Shadow subscription!"}, "tracking": {"discountPerMonth": "{{discountPerMonth, currency}} monthly off", "shadowForFree": {"value_one": "{{count}} referees left until free subscription.", "value_other": "{{count}} referees left until free subscription."}, "shadowFree": "Free Shadow!"}}, "scheduledChange": {"update": {"error": {"title": "An error occurred when updating your scheduled change. Please try again later."}, "success": {"title": "Your change was canceled successfully"}}}, "seo": {"account": {"title": "Shadow - Manage my account"}, "businessManager": {"account": {"title": "Shadow - Manage my account"}, "subscription": {"title": "Shadow - Manage my Shadow"}, "support": {"title": "Shadow - Support"}, "user": {"title": "Shadow - User Manager"}, "vm": {"title": "Shadow - PC Manager"}, "vmOnDemand": {"title": "Shadow - Spot computing"}, "billing": {"title": "Shadow - Manage my billing information"}}, "drive": {"title": "Shadow - Manage my Shadow Drive"}, "support": {"title": "Shadow - Support"}, "vm": {"title": "Shadow - Manage my Shadow"}, "games": {"title": "Shadow - Game Store"}, "billing": {"title": "Shadow - Manage my billing information"}, "driveGroups": {"title": "Shadow - Manage my Shadow Drive Family Groups"}, "security": {"title": "Shadow - Manage my security"}}, "subscription": {"activateFreeDrive": {"description": "By activating your free Shadow Drive, you agree to our ToS.", "submitLabel": "Activate my free Shadow Drive", "title": "Activate my free Shadow Drive"}, "activatePremiumDrive": {"description": "By activating your Premium Shadow Drive, you agree to our ToS.", "submitLabel": "Get my Premium Shadow Drive", "title": "Get my Premium Shadow Drive"}, "cancelDrive": {"confirmation": {"subscription": {"changedYourMind": "We are always improving Shadow Drive so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime.", "item-1": "You will still have access to your Shadow Drive until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>", "item-2": "We will send you a confirmation email to <bold>{{ email }}</bold> shortly.", "item-3": "You won't be charged anymore."}, "subtitle": "We are sad to see you leave.", "title": "Your subscription has been canceled"}, "information": {"subtitle": "If you cancel your subscription, your access to Shadow Drive will end on <bold>{{ lastBillingDate }}</bold>", "title": "Here's some information before you cancel", "whenSubscriptionActive": {"access": "You can still access your Shadow Drive until the end of your subscription.", "backupTip1": "Don't forget to back up your files", "backupTip2": "Make sure to transfer important files from Shadow Drive to your local PC. Your data will be deleted after your subscription ends.", "enjoy": "Enjoy the most out of Shadow Drive", "title": "While your subscription is still active"}, "whenSubscriptionEnds": {"dataAccess": "You'll no longer be able to access your files and they will be permanently deleted.", "description": "If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!", "reSubscribe": {"title": "Restart your subscription anytime!"}, "shadowAccess": "You'll lose access to your Shadow Drive", "title": "When your subscription ends"}}, "notification": {"error": "There was an error when trying to cancel your Drive subscription, please try again later or contact support."}, "reason": {"placeholder": "Please, describe why you want to cancel your Drive subscription"}, "survey": {"title": "We're always improving our service and your feedback matters"}}, "cancellationReasons": {"category": {"financialReason": "Financial reasons", "productAndUsage": "Product and usage", "technicalIssue": "Technical Issue"}, "helper": {"helpCenter": "Did you visit our <2>Help Center</2>?", "latencyIssue": "Did you try to contact our support team? Some users experiencing latency were able to resolve the issue by contacting the support team. (<0>I want to contact support now</0>).", "priceTooHigh": "Did you know that we have TV apps, mobile apps, … ? (<1>Download</1>)", "stabilityIssue": "Did you try to contact our support team? Some users experiencing these issues have been able to resolve the issue by contacting the support team. (<0>I want to contact support now</0>).", "startIssue": "Did you try to contact our support team? Some of the users who were experiencing this problem have been able to resolve the issue by contacting the support team. (<0>I want to contact support now</0>).", "storageNotBigEnough": "Do you want more storage? (<0>Yes</0>)"}, "reason": {"incompatibilitySoftwareOrGame": "Incompatibility of the software or the game for which I chose Shadow", "itWasJustATest": "I don't plan to keep my <PERSON> (it was just a test)", "latencyIssue": "I felt latency and/or delay in my actions", "noNeedAnymore": "I don't need it anymore", "personalFinancialIssue": "I would like to keep my Shadow but I prefer to stop my subscription for financial issues", "priceTooHigh": "The price is too high for what I do with my Shadow", "shadowSpecTooWeak": "The technical specifications are too weak for what I want to do", "stabilityIssue": "I felt instability (bugs, updates, …)", "startIssue": "I'm having trouble launching Shadow", "storageNotBigEnough": "The storage is not big enough for my use", "tooManyConstraints": "Too many constraints? (autoshutdown…)", "weakInternetConnection": "My internet connection is too weak"}}, "cancellationReasonsB2b": {"category": {"financialReason": "Financial reasons", "productAndUsage": "I needed <PERSON> for a short period of time", "technicalIssue": "Technical or compatibility issue"}}, "cancelVm": {"confirmation": {"subscription": {"changedYourMind": "We are always improving <PERSON> so if you've changed your mind, just log in to your user account and click on <bold>Restart my subscription</bold> anytime.", "item-1": "You will still have access to your Shadow until the end of your billing cycle on <bold>{{ lastBillingDate }}</bold>", "item-2": "We will send you a confirmation email to <bold>{{ email }}</bold>", "item-3": "You won't be charged anymore."}, "subtitle": "We are sad to see you leave.", "title": "Your subscription has been canceled"}, "information": {"subtitle": "If you cancel your subscription, your access to Shadow will end on <bold>{{ lastBillingDate }}</bold>", "whenSubscriptionActive": {"access": "You can still access your Shadow until the end of your subscription. You still have time to make it to the top of the leaderboard.", "backupTip1": "Don't forget to back up your files", "backupTip2": "Make sure to transfer important files from <PERSON> to your local PC. Your data will be deleted after your subscription ends.", "enjoy": "Enjoy the most out of your games", "title": "While your subscription is still active"}, "whenSubscriptionEnds": {"dataAccess": "You'll no longer be able to access games, data, or software on your high-performance PC.", "description": "If you restart your subscription before <bold>{{ lastBillingDate }}</bold>, you will be able to keep your data!", "reSubscribe": {"title": "Restart your subscription anytime!"}, "shadowAccess": "You'll lose access to your Shadow", "title": "When your subscription ends"}}, "notification": {"error": "There was an error when trying to cancel your subscription, please try again later or contact support.", "success": "You have successfully cancelled your subscription."}, "otherReason": {"placeholder": "Please, describe the issue you had", "title": "Other"}, "survey": {"subtitle": "Please select your main reason for canceling. It will only take a minute.", "title": "We're always improving our service and your feedback matters"}, "discount": {"acceptDiscountButton": {"label": "Yes, I want to keep Shadow PC for another month at {{ discountedPrice }}"}, "declineDiscountButton": {"label": "No, I definitely want to cancel"}, "description": "That’s why we want to offer you your Shadow PC for <bold>only {{ discountedPrice }}</bold> next month instead of {{ regularPrice }}.", "title": "We are sad to let you go!", "notification": {"error": "There was an error, please try again later or contact support."}}, "discountAccepted": {"confirmationText": "You can now continue to play, create, work, or do anything you want on your Shadow PC", "title": "Delighted to keep you with us!", "confirmationText2": "Good news! We will deduct <bold>{{ amount }}</bold> from your next bill."}}, "changeVmPeriodicity": {"modal": {"bullet-1": {"description": "Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment.", "title": "Your change will be effective at your next billing date"}, "bullet-2": {"description": "If you cancel your change before <bold>{{ nextBillingDate }}</bold>, you'll be able to keep your current subscription!", "title": "Cancel your change anytime"}, "cancelCurrentChangeAlert": {"description": "Any new changes to your Shadow PC subscription will cancel the current change.", "title": "Attention"}, "selectPeriodicity": {"label": "Periodicity"}, "subtitle": "Customize billing cycles for better control over your finances.", "title": "Change your periodicity"}, "success": {"content": "Your new periodicity will be applied after the end of your current one (<bold>{{ nextBillingDate }}</bold>) at the price of <bold>{{ newPrice }} for {{ relativeDuration }}</bold> of commitment.", "title": "Your subscription has been updated"}}, "details": {"cancelChangesModal": {"Text": "A modification to your subscription was scheduled. If you proceed with the cancellation, your subscription will remain unchanged as it is today. Are you sure you want to proceed with this cancellation?", "Title": "Cancel modification"}, "hasStartingPrice": "Starting", "label": "{{planName}} ({{status}} {{startDate}})", "scheduledChanges": "A change ({{reasons}}) is being made to your {{productFamily}} subscription that will take effect on <bold>{{scheduledChangesApplicationDate}}</bold>.", "status": {"active": "active since ", "inactive": "inactive since ", "pending": "end on ", "future": "waiting for activation", "non_renewing": "Cancellation in progress"}, "name": {"unknown": "Shadow PC", "cloudpc-b2b-edu-plan-a-2022": "Spark Edu", "cloudpc-b2b-edu-plan-b-2022": "Aurora Edu", "cloudpc-b2b-edu-plan-c-2022": "Lightning Edu", "cloudpc-b2b-edu-plan-d-2022": "<PERSON><PERSON>", "cloudpc-b2b-nogpu-plan-a-2023": "Shadow PC Enterprise - Essential", "cloudpc-b2b-plan-a-2022": "Shadow PC Enterprise - Standard - GPU P5000/1080", "cloudpc-b2b-plan-b-2022": "Shadow PC Enterprise - Extended - GPU Quadro RTX5000", "cloudpc-b2b-plan-c-2022": "Shadow PC Enterprise - Expert - GPU Quadro RTX6000", "cloudpc-b2b-plan-d-2022": "Shadow PC Enterprise - Advanced - GPU A4500", "cloudpc-b2b-premium2022": "Shadow PC Pro", "cloudpc-b2b-standard2022": "Shadow PC Pro", "cloudpc-b2c-A4000power-2022": "Shadow Power", "cloudpc-b2c-power2022-c1": "Power Upgrade", "cloudpc-b2c-power2022-c12": "Power Upgrade", "cloudpc-b2c-power2023": "Shadow PC - Power", "cloudpc-b2c-standard2021-c1": "Shadow PC", "cloudpc-b2c-standard2021-c12": "Shadow PC", "cloudpc-b2c-standard2023": "Shadow PC - <PERSON><PERSON>", "cloudpc-b2p-nogpu-plan-a-2023": "Shadow PC Pro - Essential", "cloudpc-b2p-plan-a-2022": "Shadow PC Pro - Standard", "cloudpc-b2p-plan-b-2022": "Shadow PC Pro - Extended", "cloudpc-b2p-plan-c-2022": "Shadow PC Pro - Expert", "cloudpc-b2p-plan-d-2022": "Shadow PC Pro - Advanced", "cloudpc-b2p-plan-e-2022": "Shadow PC Pro - Advanced Lite", "cloudpc-old-b2c-boost2019-c1": "Shadow PC - <PERSON><PERSON>", "cloudpc-old-b2c-boost2019-c12": "Shadow PC - <PERSON><PERSON>", "cloudpc-old-b2c-infinite2019-c1": "Shadow Infinite", "cloudpc-old-b2c-infinite2019-c12": "Shadow Infinite", "cloudpc-old-b2c-infinite2021-c1": "Shadow Infinite", "cloudpc-old-b2c-ultra2019-c1": "Shadow Ultra", "cloudpc-old-b2c-ultra2019-c12": "Shadow Ultra", "cloudpc-old-b2c-ultra2021-c1": "Shadow Ultra", "shadow-drive-b2c-free": "Free plan: 20 GB", "shadow-drive-b2c-premium": "Shadow Drive Premium: 2 TB", "cloudpc-b2c-discovery2024": "Shadow PC - Discovery", "shadow-drive-b2c-premium_b": "Shadow Drive Premium: 200 GB"}, "scheduledChangesName": {"change_plan": "Change your plan", "default": "Update in your subscription", "reduce_extra_storage": "Reduce your extra storage quantity", "remove_power_upgrade": "Remove Power Upgrade", "update_ram": "Update your RAM quantity"}, "scheduledChangesReasons": {"offer": "", "storage": "Reduce extra storage (<bold>from {{currentStorageQty, storageUnit}} to {{storageQty, storageUnit}}</bold>)", "periodicity": "<bold>Change periodicity</bold>", "offers": "Switch to offer <bold>{{planOfferName}}</bold>"}, "scheduledChangesProductFamily": {"shadow-drive": "Shadow Drive", "cloudpc": "Shadow PC"}}, "memberTags": {"title": "User's tags"}, "periodicity": {"save": "(save {{percent}}%)", "adjective": {"day_one": "Daily", "day_other": "For {{count}} days", "week_one": "Weekly", "week_other": "For {{count}} weeks", "month_one": "Monthly", "month_other": "For {{count}} months", "year_one": "Yearly", "year_other": "For {{count}} years"}, "relative": {"day_one": "Daily", "day_other": "{{count}} days", "week_one": "Weekly", "week_other": "{{count}} weeks", "month_one": "Monthly", "month_other": "{{count}} months", "year_one": "Yearly", "year_other": "{{count}} years"}}, "plan": {"addon": {"drive": {"get": {"link": "Get your Shadow Drive"}, "upgrade": {"link": "Upgrade to Premium (2 TB)"}}, "technicalSpecs": "Technical Specifications"}, "details": "{{planName}} ({{status}}{{startDate}})", "drive": {"action": {"cancel": "Cancel my subscription", "reactivate": "Reactivate", "resubscribe": "Resubscribe", "upgrade": "Switch to premium plan (2 TB)", "changePlan": "Change plan"}, "application": {"link": {"label": "Access my Shadow Drive", "title": "Go to Shadow Drive application"}}, "description": "A simple cloud storage solution to back up your data and access it anywhere, fully secured via traffic encryption. The perfect companion to Shadow.", "label": "Edit", "logoAlt": "Shadow Drive", "title": "My subscription"}, "storage": {"ctaLabel": "Manage extra storage", "extra": "{{extraStorageQty, storageUnit}} of extra storage", "included": "{{storageQty, storageUnit}} included", "title": "Storage"}, "vm": {"action": {"cancel": "Cancel my subscription", "changePlan": "Change plan", "changeVmPeriodicity": "Change periodicity", "reactivate": "Reactivate", "redeemAccessCode": "Redeem code", "reset": "Reset my Shadow PC", "resubscribe": "Resubscribe"}, "bundle": {"get": {"description": "We would like to thank you for your loyalty by offering you PC video games at exceptional prices.", "link": "Take advantage of exceptional prices on video games"}}, "description": "All you need from a PC, in just a click: Anything you can do on a PC; Works on all your devices; Secured storage for all your files; Ready-to-use", "illustrationAlt": "Shadow PC", "label": "Edit", "logoAlt": "Shadow", "powerPlus": {"get": {"link": "Need more power? Modify your subscription."}}, "shadow": {"get": {"description": "Click here to access our shop and buy your new Shadow PC", "link": "Get your Shadow PC"}, "ask": "Ask your admin for a PC."}, "title": "My subscription", "updatePeriodicity": {"get": {"link": "Save more than 15% by opting for a 6-month subscription."}}, "assign": {"description": "A Shadow PC has not been assigned. Please assign a PC.", "title": "No Shadow PC assigned"}, "titleMember": "My assigned PC"}}, "reactivateVm": {"description": "Your subscription is currently cancelled but can be reactivated right now.", "notification": {"error": "We could not reactivate your subscription, please try again later.", "success": "You have successfully reactivated your subscription."}, "resetLabel": "Reactivate my Shadow PC", "title": "Reactivate my Shadow"}, "redeemAccessCode": {"modal": {"description": "By activating your new plan, you agree to our ToS.", "submitLabel": "Upgrade", "title": "Upgrade to an other Shadow PC"}, "notification": {"error": "We could not activate your private offer, please try again later.", "success": "You have successfully activated your private offer!"}}, "resetVm": {"alert": {"description": "You will lose all your data (except D: Disk) in the process.", "title": "Warning"}, "notification": {"error": "There was an error when trying to reset your PC, please try again later or contact support.", "success": "You have successfully triggered a reset of your PC. This process will take around 1 hour."}, "resetLabel": "Reset my Shadow PC", "selectConfiguration": "Please select your new configuration", "subtitle": "Resetting your Shadow will bring a new clean install of your Windows, and reset all your settings.", "title": "Reset my Shadow"}, "stopVm": {"notification": {"error": "There was an error when trying to stop your <PERSON>, please try again later or contact support.", "success": "You have successfully triggered a stop of your PC. This process will take around a few minutes."}}, "update": {"success": {"content": "It may take a few moments before your Shadow PC is fully ready. Please reboot your Shadow PC the next time you access it.", "title": "Your subscription has been updated"}}, "vmName": {"notification": {"error": "There was an error changing this <PERSON>'s name. Please try again later.", "success": "PC's name changed successfully"}, "title": "Rename this PC"}, "vmTags": {"notification": {"error": "There was an error when trying to set tags, please try again later or contact support.", "success": "You have successfully set tags"}, "title": "PC's tags"}, "updateDrive": {"success": {"title": "Your subscription has been updated"}}, "gamePurchased": {"success": {"content": "Check your emails to see the next steps.", "title": "You just bought a game !"}}, "driveGroups": {"addGroupButton": {"label": "Add a family group"}, "currentGroupsCount": "You created {{currentGroupsCount}} out of {{maxGroupsCount}} groups.", "onboarding": {"firstGroupButton": {"label": "Add your first group"}, "title": "Family Groups", "description": "Family groups help sharing files between you and your loved ones thanks to a special group folder accessible directly from all of your Drive accounts.", "maxGroupsCount": {"free": "As a free user, you can create only {{maxGroupsCount}} Family groups.", "premium": "As a premium user, you can create up to {{maxGroupsCount}} Family groups."}}, "upgradeLink": "Update to Premium for more", "createGroup": {"submitButton": {"label": "Create"}, "subtitle": "Family groups help you share your storage quota with people around you who will be able to see a special volume directly in their Drive account.", "title": "Create a Family group", "description": "This will create a {{defaultGroupStorage, storageUnit}} shared folder accessible to all future group members."}, "deleteGroup": {"description": "<bold>You have currently {{storageSize}} shared across your group members.</bold> Please back up your files manually before you delete the group.", "title": "Delete a Family group", "warning": "Note that all files in the group shared folder will be deleted!", "wordConfirmation": {"description": "To validate this group deletion, please write the name of the group (<bold>{{groupName}}</bold>) in the field below."}, "alert": "An error occurred while deleting your group", "notEmpty": {"description": "In order to delete this group, you must first remove all members and invites.", "manageMembersButton": "Manage members", "title": "Your group still has members."}}, "form": {"createGroupName": {"label": "Group name", "placeholder": "Group name"}, "deleteGroupName": {"label": "Group name", "placeholder": "Enter group name to validate"}, "editGroupName": {"confirmButton": {"label": "Save"}, "placeholder": "Group name"}, "inviteMember": {"confirmButton": {"label": "Send"}, "error": {"title": "An error has occured while sending your invitation", "alreadyInvited": "This user is already invited to this group"}, "success": {"message": "{{ email }} will receive an email shortly inviting him to join your Family group.", "title": "Invitation sent"}, "maxMembersCountReached": {"message": "You have reached the maximum number of members for this group.", "title": "Maximum members count reached"}}, "inviteMemberInput": {"placeholder": "Invite member by email"}, "createGroup": {"alert": "An error occurred while creating your group"}, "editGroup": {"alert": "An error occurred while editing your group"}}, "deleteMember": {"description": "Are you sure you want to remove <bold>{{memberName}}</bold> from the group <bold>{{groupName}}</bold>? This will remove their access to files in this group.", "title": "Delete a member", "alert": "An error occurred while deleting this user"}, "drawer": {"title": "Edit Family Group"}, "editGroupName": {"title": "Group name"}, "manageGroupMembers": {"title": "Group members", "deleteMember": "Delete member", "status": {"pending": "Pending", "active": "Active", "disabled": "Disabled", "expired": "Expired"}}, "manager": {"actions": {"heading": "Actions"}, "deleteGroup": "Delete", "editGroup": "Edit", "groupMembers": {"heading": "Group members"}, "groupName": {"heading": "Group name"}, "storage": {"heading": "Storage"}, "inviteButton": {"label": "Invite"}, "noUserAssigned": "No user is assigned", "deleteGroupButton": {"label": "Delete"}, "editGroupButton": {"label": "Edit"}}, "addStorage": {"description": "Choose how you want to add space to your Family group:", "title": "Add storage to your Family group", "noUpgrades": "There are no upgrades available for your current subscription.", "offer": {"label": "{{storageSize}} for {{price}}/{{periodUnit}}"}}, "inviteMember": {"subtitle": "Family groups help you share your storage quota with people around you who will be able to see a special volume directly in their Drive account.", "title": "Invite a member"}, "invite": {"acceptButton": {"label": "Accept invite"}, "accepted": {"content": "You can now access your shared Family folder on Shadow Drive.", "title": "You successfully joined the Family group"}, "description": "You've been invited to a Shadow Drive Family group.", "error": {"title": "An error occurred"}, "rejectButton": {"label": "Reject invite"}, "title": "Invitation Received"}}, "upgradeDriveGroup": {"success": {"description": "It may take a few moments before the changes take effect for your group {{groupName}}", "title": "Your group's storage has been upgraded"}, "error": {"description": "We're sorry, but we couldn't upgrade your storage for your group {{groupName}}. Please contact the support team.", "title": "An error occurred while upgrading your group"}}}, "support": {"contactUs": {"description": "Need help or have questions about Shadow services? Our support team is here to help, and will get back to you as soon as possible.\nOur Customer Support is available in English and French from Monday to Friday, 9:00 to 19:00 (CET). <1></1>To get started, please select a topic below:", "dropzone": {"label": "<0>Import a file</0> or drag it here (max. 3 Mo)"}, "frenchCanadaDisclaimer": "Notre support francophone est actuellement basé en France. Les horaires d'ouvertures sont de 9h à 19h UTC+1 (heure de Paris). <PERSON>si, le temps de réponse pourrait être plus élevé dû au décalage horaire.", "textarea": {"label": "Please provide additional details to help us efficiently solve your request."}, "title": "Contact the support team", "update": {"error": "An error occurred when sending your message to our support team. Please try again later.", "success": "Your message has been send to our support team!"}}, "form": {"account": {"changeSubscription": "Change subscription", "paymentIssues": "Payment issues", "referral": "Referral program", "unsubscribe": "Cancel subscription", "updateInfo": "Update account info"}, "devices": {"android": "Android", "box": "Shadow Box", "ghost": "Shadow Ghost", "ios": "iOS or iPadOS", "linux": "Linux", "mac": "MacOS", "webrtc": "Shadow PC in Browser", "windows": "Windows"}, "placeholder": {"selectSubject": "What can Shadow Support assist you with?", "selectSystem": "Select an operating system", "selectTopic": "Select a topic"}, "subjects": {"account": "Account & Subscription", "cloudpc": "Shadow PC", "drive": "Shadow Drive", "vmIssue": "My Shadow PC is not starting"}}, "ressources": {"description": "Everything you need to know when getting started with <PERSON>. Check out these guides first to get the most from your Shadow experience.", "discord": {"description": "Join us on Discord and connect with the whole community of #TeamShadow!", "title": "Discord"}, "faq": {"description": "This page provides answers to frequently asked questions.", "title": "FAQ"}, "helpcenter": {"description": "Learn how to use <PERSON>, fix a problem, and get answers to your questions.", "title": "Help center"}, "title": "Get Started", "userId": "For information, your id is: {{uid}}"}, "unknownEmail": "unknown email"}, "catalog": {"game": {"starcraft2-game": {"name": "Starcraft 2"}, "farmingSimulator2022-game": {"name": "Farming Simulator 2022 Platinum Edition"}, "game-b2c-one_time-park_beyond": {"name": "Park Beyond"}, "game-b2c-one_time-dying_light_2": {"name": "Dying Light 2"}, "game-b2c-one_time-lords_of_the_fallen": {"name": "Lords of the Fallen"}, "game-b2c-one_time-helldivers_2": {"name": "Helldivers 2"}, "game-b2c-one_time-dragons_dogma_2": {"name": "Dragon's Dogma 2"}}}}