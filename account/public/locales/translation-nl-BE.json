{"account": {"billing": {"contactSupport": "", "title": "", "update": {"error": {"message": ""}, "submit": {"label": ""}, "success": {"message": "", "title": ""}}}, "company": {"update": {"error": {"message": ""}, "success": {"message": "", "title": ""}}}, "details": {"creditsMessage": ""}, "payment": {"add": {"link": ""}, "edit": {"link": ""}, "type": {"none": "", "card": "", "bancontact": "", "ideal": "", "paypal_express_checkout": "", "sepa": "", "sofort": "", "direct_debit": ""}}, "privacy": {"cta": {"label": ""}, "title": ""}, "user": {"downloadData": {"label": ""}, "editEmail": {"label": ""}, "editPassword": {"label": ""}, "form": {"submit": {"label": ""}}, "resendVerificationEmail": {"label": ""}, "title": "", "update": {"error": {"message": ""}, "success": {"message": "", "title": ""}}, "security": {"buttons": {"hub": "", "password": ""}, "text": "", "title": ""}, "socials": {"buttons": {"text": ""}, "text": "", "title": ""}}}, "businessManager": {"managerList": {"footer": {"rowPerPage": "", "total": "", "users": "", "vms": "", "driveGroups": ""}, "user": {"actions": {"activateMember": {"buttonLabel": "", "notification": {"error": "", "success": ""}}, "deactivateMember": {"buttonLabel": "", "notification": {"error": "", "success": ""}}, "deleteMember": {"buttonLabel": "", "modal": {"content": "", "title": ""}, "notification": {"alreadyAssigned": "", "error": "", "success": ""}}, "resendInvitation": {"buttonLabel": "", "notification": {"error": "", "success": ""}}, "updateTags": {"notification": {"error": "", "success": ""}}}, "heading": {"created": "", "role": "", "user": ""}, "invite": {"error": {"message": "", "title": ""}, "success": {"message": "", "title": ""}}, "options": {"createUser": ""}}, "vm": {"empty": {"createVmButtonLabel": "", "description": "", "illustrationAlt": "", "title": ""}, "heading": {"created": "", "name": "", "status": "", "tags": "", "type": "", "user": ""}, "noUserAssigned": "", "options": {"createVmButtonLabel": ""}, "subscriptionStatus": {"active": "", "cancelled": "", "future": "", "in_trial": "", "non_renewing": "", "not_paid": "", "paused": ""}, "vmStatus": {"maintenance": "", "running": "", "stopped": "", "on_hold": ""}}}, "subscription": {"plan": {"vm": {"logoAlt": ""}}}, "vmDetails": {"created": {"notification": {"success": {"message": "", "title": ""}}}, "user": {"actions": {"assignMember": {"notification": {"error": "", "success": ""}, "server": {"error": ""}}, "revokeMember": {"notification": {"error": "", "success": ""}}}, "assignUser": "", "assignUserPlaceholder": "", "noUser": "", "revokeMember": {"buttonLabel": "", "modal": {"content": "", "title": ""}}, "title": "", "vmNotStopped": ""}, "vm": {"configuration": "", "creationDate": "", "datacenter": "", "deleteVm": "", "id": "", "internalStorage": {"label": "", "value": ""}, "offer": "", "renameVm": "", "resetVm": "", "stopVm": "", "subscriptionRenewal": "", "tags": "", "vmTags": "", "clone": "", "outOfStock": "", "copy": "", "CannotClone": ""}}, "vmOnDemand": {"buttons": {"apiDocumentation": {"label": ""}, "getApiKey": {"label": ""}, "priceTable": {"label": ""}}, "status": {"active": "", "cancelled": "", "future": "", "inTrial": "", "nonRenewing": "", "paused": ""}, "subscribed": {"contactUs": "", "subtitle": "", "title": ""}, "successAlert": {"body": "", "title": ""}, "unsubscribed": {"asterisk": "", "subtitle": "", "title": ""}}, "user": {"role": {"admin": "", "member": ""}, "status": {"disabled": "", "expired": "", "pending": ""}}, "vmTagManager": {"title": ""}}, "company": {"button": "", "title": ""}, "consentBanner": {"title": ""}, "downloadData": {"modal": {"title": ""}}, "emailing": {"title": "", "update": {"error": "", "success": ""}, "shadow": {"title": "", "description": ""}, "storage": {"title": "", "description": ""}, "drive": {"title": "", "description": ""}}, "errors": {"api": {"changeVmPeriodicity": "", "changeVmPeriodicityEstimate": "", "tooManyRequest": ""}}, "footer": {"cgu": "", "companyName": "", "cookies": "", "legal": "", "privacy": ""}, "form": {"birthdate": {"error": {"invalid": ""}}, "configuration": {"label": {"information": ""}}, "word": {"error": {"mismatch": ""}}, "companyName": {"error": {"maxLength": "", "minLength": "", "required": ""}}, "vatNumber": {"error": {"maxLength": "", "minLength": "", "wrongFormat": ""}}}, "header": {"download": {"label": ""}, "link": {"logout": ""}, "login": {"welcome": {"label": ""}}, "logoAlt": "", "logoBusinessAlt": ""}, "infoBanner": {"unpaid": {"error": {"content": "", "title": ""}, "infos": {"content": "", "daysBeforeTermination_zero": "", "daysBeforeTermination_one": "", "daysBeforeTermination_other": "", "contentBusiness": "", "productName": {"cloudpc": "", "shadow-drive": ""}}, "success": {"content": "", "title": ""}}}, "invoices": {"list": {"body": {"link": {"csv": "", "pdf": "", "title": ""}, "status": {"not_paid": "", "paid": "", "payment_due": "", "pending": "", "posted": "", "voided": ""}, "statusTooltip": {"not_paid": "", "payment_due": "", "pending": "", "posted": "", "voided": ""}}, "download": {"error": ""}, "heading": {"date": "", "link": "", "status": "", "total": ""}}, "noInvoice": "", "title": ""}, "navigation": {"route": {"account": "", "download": "", "drive": "", "shadow": "", "shadowB2b": "", "support": "", "users": "", "vmOnDemand": "", "vms": "", "billing": "", "games": "", "driveGroups": "", "Security": ""}, "title": {"administration": "", "settings": "", "account": "", "drive": "", "pc": ""}}, "notFound": {"description": "", "pageTitle": "", "title": ""}, "referral": {"sharing": {"clipboardError": "", "clipboardSuccess": "", "copy": "", "shareEmail": {"body": "", "subject": "", "title": ""}, "shareFacebook": "", "shareMessenger": "", "shareTwitter": "", "shareWhatsApp": "", "subtitle": "", "title": ""}, "tracking": {"discountPerMonth": "", "shadowForFree": {"value_one": "", "value_other": ""}, "shadowFree": ""}}, "scheduledChange": {"update": {"error": {"title": ""}, "success": {"title": ""}}}, "seo": {"account": {"title": ""}, "businessManager": {"account": {"title": ""}, "subscription": {"title": ""}, "support": {"title": ""}, "user": {"title": ""}, "vm": {"title": ""}, "vmOnDemand": {"title": ""}, "billing": {"title": ""}}, "drive": {"title": ""}, "support": {"title": ""}, "vm": {"title": ""}, "games": {"title": ""}, "billing": {"title": ""}, "driveGroups": {"title": ""}, "security": {"title": ""}}, "subscription": {"activateFreeDrive": {"description": "", "submitLabel": "", "title": ""}, "activatePremiumDrive": {"description": "", "submitLabel": "", "title": ""}, "cancelDrive": {"confirmation": {"subscription": {"changedYourMind": "", "item-1": "", "item-2": "", "item-3": ""}, "subtitle": "", "title": ""}, "information": {"subtitle": "", "title": "", "whenSubscriptionActive": {"access": "", "backupTip1": "", "backupTip2": "", "enjoy": "", "title": ""}, "whenSubscriptionEnds": {"dataAccess": "", "description": "", "reSubscribe": {"title": ""}, "shadowAccess": "", "title": ""}}, "notification": {"error": ""}, "reason": {"placeholder": ""}, "survey": {"title": ""}}, "cancellationReasons": {"category": {"financialReason": "", "productAndUsage": "", "technicalIssue": ""}, "helper": {"helpCenter": "", "latencyIssue": "", "priceTooHigh": "", "stabilityIssue": "", "startIssue": "", "storageNotBigEnough": ""}, "reason": {"incompatibilitySoftwareOrGame": "", "itWasJustATest": "", "latencyIssue": "", "noNeedAnymore": "", "personalFinancialIssue": "", "priceTooHigh": "", "shadowSpecTooWeak": "", "stabilityIssue": "", "startIssue": "", "storageNotBigEnough": "", "tooManyConstraints": "", "weakInternetConnection": ""}}, "cancellationReasonsB2b": {"category": {"financialReason": "", "productAndUsage": "", "technicalIssue": ""}}, "cancelVm": {"confirmation": {"subscription": {"changedYourMind": "", "item-1": "", "item-2": "", "item-3": ""}, "subtitle": "", "title": ""}, "information": {"subtitle": "", "whenSubscriptionActive": {"access": "", "backupTip1": "", "backupTip2": "", "enjoy": "", "title": ""}, "whenSubscriptionEnds": {"dataAccess": "", "description": "", "reSubscribe": {"title": ""}, "shadowAccess": "", "title": ""}}, "notification": {"error": "", "success": ""}, "otherReason": {"placeholder": "", "title": ""}, "survey": {"subtitle": "", "title": ""}, "discount": {"acceptDiscountButton": {"label": ""}, "declineDiscountButton": {"label": ""}, "description": "", "title": "", "notification": {"error": ""}}, "discountAccepted": {"confirmationText": "", "title": "", "confirmationText2": ""}}, "changeVmPeriodicity": {"modal": {"bullet-1": {"description": "", "title": ""}, "bullet-2": {"description": "", "title": ""}, "cancelCurrentChangeAlert": {"description": "", "title": ""}, "selectPeriodicity": {"label": ""}, "subtitle": "", "title": ""}, "success": {"content": "", "title": ""}}, "details": {"cancelChangesModal": {"Text": "", "Title": ""}, "hasStartingPrice": "", "label": "", "scheduledChanges": "", "status": {"active": "", "inactive": "", "pending": "", "future": "", "non_renewing": ""}, "name": {"unknown": "", "cloudpc-b2b-edu-plan-a-2022": "", "cloudpc-b2b-edu-plan-b-2022": "", "cloudpc-b2b-edu-plan-c-2022": "", "cloudpc-b2b-edu-plan-d-2022": "", "cloudpc-b2b-nogpu-plan-a-2023": "", "cloudpc-b2b-plan-a-2022": "", "cloudpc-b2b-plan-b-2022": "", "cloudpc-b2b-plan-c-2022": "", "cloudpc-b2b-plan-d-2022": "", "cloudpc-b2b-premium2022": "", "cloudpc-b2b-standard2022": "", "cloudpc-b2c-A4000power-2022": "", "cloudpc-b2c-power2022-c1": "", "cloudpc-b2c-power2022-c12": "", "cloudpc-b2c-power2023": "", "cloudpc-b2c-standard2021-c1": "", "cloudpc-b2c-standard2021-c12": "", "cloudpc-b2c-standard2023": "", "cloudpc-b2p-nogpu-plan-a-2023": "", "cloudpc-b2p-plan-a-2022": "", "cloudpc-b2p-plan-b-2022": "", "cloudpc-b2p-plan-c-2022": "", "cloudpc-b2p-plan-d-2022": "", "cloudpc-b2p-plan-e-2022": "", "cloudpc-old-b2c-boost2019-c1": "", "cloudpc-old-b2c-boost2019-c12": "", "cloudpc-old-b2c-infinite2019-c1": "", "cloudpc-old-b2c-infinite2019-c12": "", "cloudpc-old-b2c-infinite2021-c1": "", "cloudpc-old-b2c-ultra2019-c1": "", "cloudpc-old-b2c-ultra2019-c12": "", "cloudpc-old-b2c-ultra2021-c1": "", "shadow-drive-b2c-free": "", "shadow-drive-b2c-premium": "", "cloudpc-b2c-discovery2024": "", "shadow-drive-b2c-premium_b": ""}, "scheduledChangesName": {"change_plan": "", "default": "", "reduce_extra_storage": "", "remove_power_upgrade": "", "update_ram": ""}, "scheduledChangesReasons": {"offer": "", "storage": "", "periodicity": "", "offers": ""}, "scheduledChangesProductFamily": {"shadow-drive": "", "cloudpc": ""}}, "memberTags": {"title": ""}, "periodicity": {"save": "", "adjective": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}, "relative": {"day_one": "", "day_other": "", "week_one": "", "week_other": "", "month_one": "", "month_other": "", "year_one": "", "year_other": ""}}, "plan": {"addon": {"drive": {"get": {"link": ""}, "upgrade": {"link": ""}}, "technicalSpecs": ""}, "details": "", "drive": {"action": {"cancel": "", "reactivate": "", "resubscribe": "", "upgrade": "", "changePlan": ""}, "application": {"link": {"label": "", "title": ""}}, "description": "", "label": "", "logoAlt": "", "title": ""}, "storage": {"ctaLabel": "", "extra": "", "included": "", "title": ""}, "vm": {"action": {"cancel": "", "changePlan": "", "changeVmPeriodicity": "", "reactivate": "", "redeemAccessCode": "", "reset": "", "resubscribe": ""}, "bundle": {"get": {"description": "", "link": ""}}, "description": "", "illustrationAlt": "", "label": "", "logoAlt": "", "powerPlus": {"get": {"link": ""}}, "shadow": {"get": {"description": "", "link": ""}, "ask": ""}, "title": "", "updatePeriodicity": {"get": {"link": ""}}, "assign": {"description": "", "title": ""}, "titleMember": ""}}, "reactivateVm": {"description": "", "notification": {"error": "", "success": ""}, "resetLabel": "", "title": ""}, "redeemAccessCode": {"modal": {"description": "", "submitLabel": "", "title": ""}, "notification": {"error": "", "success": ""}}, "resetVm": {"alert": {"description": "", "title": ""}, "notification": {"error": "", "success": ""}, "resetLabel": "", "selectConfiguration": "", "subtitle": "", "title": ""}, "stopVm": {"notification": {"error": "", "success": ""}}, "update": {"success": {"content": "", "title": ""}}, "vmName": {"notification": {"error": "", "success": ""}, "title": ""}, "vmTags": {"notification": {"error": "", "success": ""}, "title": ""}, "updateDrive": {"success": {"title": ""}}, "gamePurchased": {"success": {"content": "", "title": ""}}, "driveGroups": {"addGroupButton": {"label": ""}, "currentGroupsCount": "", "onboarding": {"firstGroupButton": {"label": ""}, "title": "", "description": "", "maxGroupsCount": {"free": "", "premium": ""}}, "upgradeLink": "", "createGroup": {"submitButton": {"label": ""}, "subtitle": "", "title": "", "description": ""}, "deleteGroup": {"description": "", "title": "", "warning": "", "wordConfirmation": {"description": ""}, "alert": "", "notEmpty": {"description": "", "manageMembersButton": "", "title": ""}}, "form": {"createGroupName": {"label": "", "placeholder": ""}, "deleteGroupName": {"label": "", "placeholder": ""}, "editGroupName": {"confirmButton": {"label": ""}, "placeholder": ""}, "inviteMember": {"confirmButton": {"label": ""}, "error": {"title": "", "alreadyInvited": ""}, "success": {"message": "", "title": ""}, "maxMembersCountReached": {"message": "", "title": ""}}, "inviteMemberInput": {"placeholder": ""}, "createGroup": {"alert": ""}, "editGroup": {"alert": ""}}, "deleteMember": {"description": "", "title": "", "alert": ""}, "drawer": {"title": ""}, "editGroupName": {"title": ""}, "manageGroupMembers": {"title": "", "deleteMember": "", "status": {"pending": "", "active": "", "disabled": "", "expired": ""}}, "manager": {"actions": {"heading": ""}, "deleteGroup": "", "editGroup": "", "groupMembers": {"heading": ""}, "groupName": {"heading": ""}, "storage": {"heading": ""}, "inviteButton": {"label": ""}, "noUserAssigned": "", "deleteGroupButton": {"label": ""}, "editGroupButton": {"label": ""}}, "addStorage": {"description": "", "title": "", "noUpgrades": "", "offer": {"label": ""}}, "inviteMember": {"subtitle": "", "title": ""}, "invite": {"acceptButton": {"label": ""}, "accepted": {"content": "", "title": ""}, "description": "", "error": {"title": ""}, "rejectButton": {"label": ""}, "title": ""}}, "upgradeDriveGroup": {"success": {"description": "", "title": ""}, "error": {"description": "", "title": ""}}}, "support": {"contactUs": {"description": "", "dropzone": {"label": ""}, "frenchCanadaDisclaimer": "", "textarea": {"label": ""}, "title": "", "update": {"error": "", "success": ""}}, "form": {"account": {"changeSubscription": "", "paymentIssues": "", "referral": "", "unsubscribe": "", "updateInfo": ""}, "devices": {"android": "", "box": "", "ghost": "", "ios": "", "linux": "", "mac": "", "webrtc": "", "windows": ""}, "placeholder": {"selectSubject": "", "selectSystem": "", "selectTopic": ""}, "subjects": {"account": "", "cloudpc": "", "drive": "", "vmIssue": ""}}, "ressources": {"description": "", "discord": {"description": "", "title": ""}, "faq": {"description": "", "title": ""}, "helpcenter": {"description": "", "title": ""}, "title": "", "userId": ""}, "unknownEmail": ""}, "catalog": {"game": {"starcraft2-game": {"name": ""}, "farmingSimulator2022-game": {"name": ""}, "game-b2c-one_time-park_beyond": {"name": ""}, "game-b2c-one_time-dying_light_2": {"name": ""}, "game-b2c-one_time-lords_of_the_fallen": {"name": ""}, "game-b2c-one_time-helldivers_2": {"name": ""}, "game-b2c-one_time-dragons_dogma_2": {"name": ""}}}}