{"api": {"error": {"description": "<PERSON>im Abrufen deiner Daten ist ein Fehler aufgetreten. Lade die Seite neu oder kontaktiere den Support", "title": "Etwas ist schief gelaufen..."}}, "form": {"address1": {"error": {"required": "<PERSON>resse ist erforderlich"}, "label": "<PERSON><PERSON><PERSON>"}, "birthdate": {"error": {"required": "Geburtsdatum ist erforderlich"}, "label": "Geburtsdatum"}, "city": {"error": {"required": "Stadt ist erforderlich"}, "label": "Stadt"}, "companyName": {"error": {"maxLength": "Der Firmenname darf höchstens {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "minLength": "Der Firmenname muss mindestens {{minLength}} <PERSON><PERSON><PERSON> enthalten", "required": "Unternehmensname ist erforderlich"}, "label": "Unternehmensname"}, "configuration": {"label": "Konfiguration"}, "country": {"label": "Land"}, "coupon": {"error": {"alreadySubscribed": "Du hast bereits ein Abonnement für ein Angebot, das dieser Art von Code entspricht.", "invalid": "Dieser Gutschein-Code ist ungültig oder abgelaufen.", "mismatch": "Leider muss der eingegebene Gutschein mindestens 3 Zeichen lang sein.", "required": "Gutschein ist erforderlich"}, "info": "<PERSON>te gib einen gültigen Gutschein ein."}, "email": {"error": {"email": "Feld muss eine gültige E-Mail-Adresse enthalten", "required": "Email ist erforderlich"}}, "firstName": {"error": {"required": "Vorname ist erforderlich"}, "label": "<PERSON><PERSON><PERSON>"}, "keyboard": {"label": "Tastatur"}, "language": {"label": "<PERSON><PERSON><PERSON>"}, "lastName": {"error": {"required": "Nachname ist erforderlich"}, "label": "Nachname"}, "name": {"error": {"required": ""}}, "phone": {"label": "Telefonnummer"}, "tags": {"addButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "label": "Einen Tag hinzufügen"}, "vatNumber": {"error": {"invalid": "Deine Umsatzsteuer-Identifikationsnummer ist ungültig", "maxLength": "Die Mehrwertsteuernummer darf höchstens {{maxLength}} <PERSON><PERSON><PERSON> lang sein", "minLength": "Die Mehrwertsteuernummer muss mindestens {{minLength}} <PERSON><PERSON><PERSON> lang sein", "required": "Mehrwertsteuernummer erforderlich", "wrongFormat": "Mehrwertsteuernummer kann nur alphanumerische Zeichen enthalten"}, "label": "Umsatzsteuer-Identifikationsnummer"}, "vmName": {"error": {"maxLength": "Der PC-Name darf h<PERSON> {{ maxLength }} <PERSON><PERSON><PERSON> lang sein", "minLength": "Der PC-Name muss <PERSON>ns {{ minLength }} <PERSON><PERSON><PERSON> lang sein", "specialChar": "Der PC-Name darf keine Sonderzeichen enthalten"}, "label": "Nachname"}, "vmTags": {"error": {"maxLength": "Der Tag darf höchstens {{ maxLength }} <PERSON><PERSON><PERSON> lang sein", "minLength": "Tag muss mindestens {{ minLength }} <PERSON><PERSON><PERSON> lang sein", "noAlphanumericChar": "Tag muss mindestens ein alphanumerisches Zeichen enthalten", "required": "Tag ist erforderlich", "specialChar": "Tag darf keine Sonderzeichen enthalten"}, "label": "Tag"}, "word": {"error": {"required": "Wort ist erforderlich"}, "info": "<PERSON><PERSON> schreibe <bold>{{ wordToCompare }}</bold> unten, um fortzufahren:", "label": "<PERSON><PERSON><PERSON> ein <PERSON>ort ein"}, "zipcode": {"error": {"required": "Postleitzahl ist erforderlich"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "select": {"automatic": {"label": "Automatisch"}, "manual": {"label": "<PERSON><PERSON>"}}, "firstname": {"error": {"required": "Vorname ist erforderlich"}, "label": "<PERSON><PERSON><PERSON>"}, "lastname": {"error": {"required": "Nachname ist erforderlich"}, "label": "Nachname"}, "groupName": {"error": {"required": "Ein Gruppenname ist erforderlich", "invalid": "Der Gruppenname darf nur Buchstaben, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Unterstriche und Bindestriche enthalten.", "maxLength": "Der Gruppenname darf höchstens {{ maxLength }} <PERSON><PERSON><PERSON> lang sein"}}}, "global": {"cancel": "Abbrechen", "confirm": "Bestätigen", "continue": "<PERSON><PERSON>", "email": "Email", "invite": "Einladung", "next": "<PERSON><PERSON>", "ok": "OK", "revoke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipReason": "Ich möchte nicht antworten", "tags": "Tags", "unknown": "Ein unbekanntes Problem ist aufgetreten", "delete": "Löschen"}, "login": {"error": {"description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, ist ein Fehler aufgetreten. Bitte versuche es später noch einmal oder kontaktiere den Support, wenn das Problem weiterhin besteht.", "title": "Ups, etwas ist nicht in Ordnung"}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktiv", "disabled": "Inaktiv", "expired": "Abgelaufen"}}}}}