{"api": {"error": {"description": "Se ha producido un error al obtener los datos. Vuelve a cargar la página o contacta con el servicio de asistencia", "title": "Algo ha salido mal…"}}, "form": {"address1": {"error": {"required": "La Dirección es un campo obligatorio"}, "label": "Dirección"}, "birthdate": {"error": {"required": "La fecha de nacimiento es un campo obligatorio"}, "label": "Fecha de nacimiento"}, "city": {"error": {"required": "La ciudad es un campo obligatorio"}, "label": "Ciudad"}, "companyName": {"error": {"maxLength": "El nombre de la compañía debe de tener máximo {{maxLength}} caracteres", "minLength": "El nombre de la compañía debe de tener al menos {{minLength}} caracteres", "required": "Se requiere el nombre de la Compañía"}, "label": "Nombre de la Compañía"}, "configuration": {"label": "Configuración"}, "country": {"label": "<PERSON><PERSON>"}, "coupon": {"error": {"alreadySubscribed": "Ya tienes una suscripción a una oferta correspondiente a este tipo de código.", "invalid": "Este código de cupón no es válido o ha caducado.", "mismatch": "<PERSON> sentimo<PERSON>, el cupón que ingresó debe tener al menos 3 caracteres.", "required": "El cupón es un campo obligatorio"}, "info": "Introduzce un cupón válido."}, "email": {"error": {"email": "Ingresa un correo electrónico válido", "required": "Se requiere un Correo Electrónico"}}, "firstName": {"error": {"required": "El nombre es un campo obligatorio"}, "label": "Nombre"}, "keyboard": {"label": "Teclado"}, "language": {"label": "Idioma"}, "lastName": {"error": {"required": "El apellido es un campo obligatorio"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "name": {"error": {"required": ""}}, "phone": {"label": "Número de teléfono"}, "tags": {"addButtonLabel": "<PERSON><PERSON><PERSON>", "label": "Añadir etiqueta"}, "vatNumber": {"error": {"invalid": "Su número de IVA es inválido", "maxLength": "El número de IVA debe de tener máximo {{maxLength}} caracteres", "minLength": "El número de IVA debe de tener al menos {{minLength}} caracteres", "required": "Se requiere un número IVA", "wrongFormat": "El IVA solo contiene caracteres alfanuméricos"}, "label": "IVA"}, "vmName": {"error": {"maxLength": "El nombre de la PC debe tener como máximo {{ maxLength }} caracteres", "minLength": "El nombre de la PC debe tener al menos {{ minLength }} caracteres", "specialChar": "El nombre de la PC no debe usar caracteres especiales"}, "label": "Nombre"}, "vmTags": {"error": {"maxLength": "La etiqueta debe de tener máximo {{ maxLength }} caracteres", "minLength": "La etiqueta debe de tener al menos {{ minLength }} caracteres", "noAlphanumericChar": "La etiqueta debe de incluir al menos un caracter alfanumérico", "required": "La etiqueta es necesaria", "specialChar": "La etiqueta no puede incluir caracteres especiales"}, "label": "Tag"}, "word": {"error": {"required": "Palabra es un campo obligatorio"}, "info": "Escriba <bold>{{ wordToCompare }}</bold>  debajo para continuar:", "label": "Palabra que se debe escribir"}, "zipcode": {"error": {"required": "El código postal es un campo obligatorio"}, "label": "Código postal"}, "select": {"automatic": {"label": "Automático"}, "manual": {"label": "Manual"}}, "firstname": {"error": {"required": "El nombre es un campo obligatorio"}, "label": "Nombre"}, "lastname": {"error": {"required": "El apellido es un campo obligatorio"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "groupName": {"error": {"required": "Debes indicar un nombre de grupo", "invalid": "El nombre del grupo solo puede contener letras, números, espacios, guiones bajos y guiones.", "maxLength": "El nombre del grupo debe tener como máximo {{ maxLength }} caracteres"}}}, "global": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "continue": "<PERSON><PERSON><PERSON><PERSON>", "email": "Correo electrónico", "invite": "Invitación", "next": "Siguient<PERSON>", "ok": "Aceptar", "revoke": "Revocar", "send": "Enviar", "skipReason": "Prefiero no decir", "tags": "Tags", "unknown": "Desconocido", "delete": "Bo<PERSON>r"}, "login": {"error": {"description": "Se ha producido un error al intentar iniciar sesión. Vuelva a intentarlo más tarde o contacte con el servicio de asistencia si el problema continúa.", "title": "Vaya, algo no funciona"}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "Pendiente", "active": "Activo", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>"}}}}}