{"api": {"error": {"description": "An error has occurred fetching your data. Reload the page or contact support", "title": "Si è verificato un problema..."}}, "form": {"address1": {"error": {"required": "L'indirizzo è obbligatorio"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "birthdate": {"error": {"required": "La data di nascita è obbligatoria"}, "label": "Data di nascita"}, "city": {"error": {"required": "La città è obbligatoria"}, "label": "Città"}, "companyName": {"error": {"maxLength": "Il nome della compagnia può essere al massimo {{maxLength}} caratteri", "minLength": "Il nome della compagnia deve essere almeno {{minLength}} caratteri", "required": "Il nome dell'azienda è obbligato"}, "label": "Nome azienda"}, "configuration": {"label": "Configurazione"}, "country": {"label": "<PERSON><PERSON>"}, "coupon": {"error": {"alreadySubscribed": "Sei già abbonato a un'offerta corrispondente a questo tipo di codice.", "invalid": "Questo codice coupon non è valido o è scaduto.", "mismatch": "Il coupon inserito deve essere lungo almeno 3 caratteri.", "required": "Il coupon è obbligatorio"}, "info": "Inserire un coupon valido."}, "email": {"error": {"email": "Il campo deve contenere una e-mail valida", "required": "L'e-mail è obbligatoria"}}, "firstName": {"error": {"required": "Il nome è obbligatorio"}, "label": "Nome"}, "keyboard": {"label": "Tastiera"}, "language": {"label": "<PERSON><PERSON>"}, "lastName": {"error": {"required": "Il cognome è obbligatorio"}, "label": "Cognome"}, "name": {"error": {"required": ""}}, "phone": {"label": "Numero di telefono"}, "tags": {"addButtonLabel": "<PERSON><PERSON>", "label": "Aggiungi un tag"}, "vatNumber": {"error": {"invalid": "La tua partita IVA non è valida", "maxLength": "La partita IVA può essere al massimo {{maxLength}} caratteri", "minLength": "La partita IVA deve essere di almeno {{minLength}} caratteri", "required": "Il numero di registrazione IVA è obbligatorio", "wrongFormat": "Il numero di registrazione IVA deve contenere soltanto caratteri alfanumerici"}, "label": "Numero di registrazione IVA"}, "vmName": {"error": {"maxLength": "Il nome del PC può essere al massimo {{ maxLength }} caratteri", "minLength": "Il nome del PC deve essere lungo almeno {{ minLength }} caratteri", "specialChar": "Il nome del PC non può contenere caratteri speciali"}, "label": "Nome"}, "vmTags": {"error": {"maxLength": "Il tag deve essere al massimo di {{ maxLength }} caratteri", "minLength": "Il tag deve essere di almeno {{ minLength }} caratteri", "noAlphanumericChar": "Il tag deve contenere almeno un carattere alfanumerico", "required": "Il tag è obbligatorio", "specialChar": "Il tag non può contenere caratteri speciali"}, "label": "Tag"}, "word": {"error": {"required": "La parola è obbligatoria"}, "info": "<PERSON>rivere <bold>{{ wordToCompare }}</bold>  qui sotto per continuare:", "label": "Parola da digitare"}, "zipcode": {"error": {"required": "Il codice postale è obbligatorio"}, "label": "Codice postale"}, "select": {"automatic": {"label": "Automatica"}, "manual": {"label": "Manuale"}}, "firstname": {"error": {"required": "Il nome è obbligatorio"}, "label": "Nome"}, "lastname": {"error": {"required": "Il cognome è obbligatorio"}, "label": "Cognome"}, "groupName": {"error": {"required": "Il nome del gruppo è obbligatorio", "invalid": "Il nome del gruppo può contenere solo lettere, numeri, spazi, trattini e trattini bassi.", "maxLength": "Il nome del gruppo deve essere composto al massimo da {{ maxLength }} caratteri"}}}, "global": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "continue": "Continua", "email": "E-mail", "invite": "Invi<PERSON>", "next": "<PERSON><PERSON>", "ok": "OK", "revoke": "Revoca", "send": "Invia", "skipReason": "Non voglio rispondere", "tags": "Tags", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Cancellare"}, "login": {"error": {"description": "Si è verificato un errore durante il tentativo di accesso. Riprova più tardi o contatta il supporto tecnico se il problema persiste.", "title": "Ops, c'è stato un problema"}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "In attesa", "active": "Attivo", "disabled": "Disabilitato", "expired": "Scaduto"}}}}}