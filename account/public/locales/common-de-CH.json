{"api": {"error": {"description": "", "title": ""}}, "form": {"address1": {"error": {"required": ""}, "label": ""}, "birthdate": {"error": {"required": ""}, "label": ""}, "city": {"error": {"required": ""}, "label": ""}, "companyName": {"error": {"maxLength": "", "minLength": "", "required": ""}, "label": ""}, "configuration": {"label": ""}, "country": {"label": ""}, "coupon": {"error": {"alreadySubscribed": "", "invalid": "", "mismatch": "", "required": ""}, "info": ""}, "email": {"error": {"email": "", "required": ""}}, "firstName": {"error": {"required": ""}, "label": ""}, "keyboard": {"label": ""}, "language": {"label": ""}, "lastName": {"error": {"required": ""}, "label": ""}, "name": {"error": {"required": ""}}, "phone": {"label": ""}, "tags": {"addButtonLabel": "", "label": ""}, "vatNumber": {"error": {"invalid": "", "maxLength": "", "minLength": "", "required": "", "wrongFormat": ""}, "label": ""}, "vmName": {"error": {"maxLength": "", "minLength": "", "specialChar": ""}, "label": ""}, "vmTags": {"error": {"maxLength": "", "minLength": "", "noAlphanumericChar": "", "required": "", "specialChar": ""}, "label": ""}, "word": {"error": {"required": ""}, "info": "", "label": ""}, "zipcode": {"error": {"required": ""}, "label": ""}, "select": {"automatic": {"label": ""}, "manual": {"label": ""}}, "firstname": {"error": {"required": ""}, "label": ""}, "lastname": {"error": {"required": ""}, "label": ""}, "groupName": {"error": {"required": "", "invalid": "", "maxLength": ""}}}, "global": {"cancel": "", "confirm": "", "continue": "", "email": "", "invite": "", "next": "", "ok": "", "revoke": "", "send": "", "skipReason": "", "tags": "", "unknown": "", "delete": ""}, "login": {"error": {"description": "", "title": ""}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "", "active": "", "disabled": "", "expired": ""}}}}}