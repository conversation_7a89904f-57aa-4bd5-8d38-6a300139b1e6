{"api": {"error": {"description": "", "title": "Un problème est survenu..."}}, "form": {"address1": {"error": {"required": "Adresse requise"}, "label": ""}, "birthdate": {"error": {"required": "Date de naissance requise"}, "label": "Date de naissance"}, "city": {"error": {"required": "Ville requise"}, "label": ""}, "companyName": {"error": {"maxLength": "", "minLength": "", "required": ""}, "label": ""}, "configuration": {"label": ""}, "country": {"label": ""}, "coupon": {"error": {"alreadySubscribed": "", "invalid": "Le code est incorrect ou expiré.", "mismatch": "", "required": "Code requis"}, "info": "<PERSON><PERSON><PERSON> d'entrer un code valide."}, "email": {"error": {"email": "", "required": ""}}, "firstName": {"error": {"required": "Prénom requis"}, "label": "Prénom"}, "keyboard": {"label": "<PERSON><PERSON><PERSON>"}, "language": {"label": "<PERSON><PERSON>"}, "lastName": {"error": {"required": "Nom de famille requis"}, "label": "Nom de famille"}, "name": {"error": {"required": ""}}, "phone": {"label": "Numéro de téléphone"}, "tags": {"addButtonLabel": "", "label": ""}, "vatNumber": {"error": {"invalid": "", "maxLength": "", "minLength": "", "required": "", "wrongFormat": ""}, "label": ""}, "vmName": {"error": {"maxLength": "", "minLength": "", "specialChar": ""}, "label": ""}, "vmTags": {"error": {"maxLength": "", "minLength": "", "noAlphanumericChar": "", "required": "", "specialChar": ""}, "label": ""}, "word": {"error": {"required": "Mot requis"}, "info": "<PERSON><PERSON><PERSON> d'<PERSON><PERSON> <bold>{{ wordToCompare }}</bold> ci-dessous pour continuer :", "label": "Mot à écrire"}, "zipcode": {"error": {"required": "Code Postal requis"}, "label": ""}, "select": {"automatic": {"label": "Automatique"}, "manual": {"label": "<PERSON>"}}, "firstname": {"error": {"required": ""}, "label": ""}, "lastname": {"error": {"required": ""}, "label": ""}, "groupName": {"error": {"required": "", "invalid": "", "maxLength": ""}}}, "global": {"cancel": "Annuler", "confirm": "Confirmer", "continue": "<PERSON><PERSON><PERSON>", "email": "", "invite": "", "next": "Suivant", "ok": "", "revoke": "", "send": "Envoyer", "skipReason": "Je ne préfère pas répondre", "tags": "", "unknown": "", "delete": ""}, "login": {"error": {"description": "", "title": "<PERSON><PERSON>, une erreur est survenue"}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "", "active": "", "disabled": "", "expired": ""}}}}}