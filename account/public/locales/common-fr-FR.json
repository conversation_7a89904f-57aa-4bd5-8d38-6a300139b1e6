{"api": {"error": {"description": "Une erreur s'est produite lors de la récupération de vos données. Rechargez la page ou contactez le support", "title": "Un problème est survenu..."}}, "form": {"address1": {"error": {"required": "Adresse requise"}, "label": "<PERSON><PERSON><PERSON>"}, "birthdate": {"error": {"required": "Date de naissance requise"}, "label": "Date de naissance"}, "city": {"error": {"required": "Ville requise"}, "label": "Ville"}, "companyName": {"error": {"maxLength": "Le nom de l'entreprise doit être composé d'au maximum {{maxLength}} caractères", "minLength": "Le nom de l'entreprise doit être composé d'au moins {{minLength}} caractères", "required": "Nom de l'entreprise requis"}, "label": "Nom de l'entreprise"}, "configuration": {"label": "Configuration"}, "country": {"label": "Pays"}, "coupon": {"error": {"alreadySubscribed": "Vous avez déjà souscrit à une offre correspondant à ce type de code.", "invalid": "Le code est incorrect ou expiré.", "mismatch": "Désolé, la réduction doit comporter au moins 3 caractères.", "required": "Code requis"}, "info": "<PERSON><PERSON><PERSON> d'entrer un code valide."}, "email": {"error": {"email": "<PERSON><PERSON><PERSON> d'entrer un email valide", "required": "Email requis"}}, "firstName": {"error": {"required": "Prénom requis"}, "label": "Prénom"}, "keyboard": {"label": "<PERSON><PERSON><PERSON>"}, "language": {"label": "<PERSON><PERSON>"}, "lastName": {"error": {"required": "Nom de famille requis"}, "label": "Nom de famille"}, "name": {"error": {"required": ""}}, "phone": {"label": "Numéro de téléphone"}, "tags": {"addButtonLabel": "<PERSON><PERSON><PERSON>", "label": "Ajouter un tag"}, "vatNumber": {"error": {"invalid": "Votre numéro de TVA est incorrect", "maxLength": "Le numéro de TVA doit être composé d'au maximum {{maxLength}} caractères", "minLength": "Le numéro de TVA doit être composé d'au moins {{minLength}} caractères", "required": "Numéro de TVA requis", "wrongFormat": "Le numéro de TVA ne doit contenir que des caractères alphanumériques"}, "label": "Numéro de TVA"}, "vmName": {"error": {"maxLength": "Le nom du PC doit être composé d'au maximum {{ maxLength }} caractères", "minLength": "Le nom du PC doit être composé d'au moins {{ minLength }} caractères", "specialChar": "Le nom du PC ne peut comporter de caractères spéciaux"}, "label": "Nom"}, "vmTags": {"error": {"maxLength": "Le tag doit être composé d'au maximum {{ maxLength }} caractères", "minLength": "Le tag doit être composé d'au moins {{ minLength }} caractères", "noAlphanumericChar": "Le tag doit contenir au moins un caractère alphanumérique", "required": "Tag requis", "specialChar": "Le tag ne peut comporter de caractères spéciaux"}, "label": "Tag"}, "word": {"error": {"required": "Mot requis"}, "info": "<PERSON><PERSON><PERSON> d'<PERSON><PERSON> <bold>{{ wordToCompare }}</bold> ci-dessous pour continuer :", "label": "Mot à écrire"}, "zipcode": {"error": {"required": "Code Postal requis"}, "label": "Code postal"}, "select": {"automatic": {"label": "Automatique"}, "manual": {"label": "<PERSON>"}}, "firstname": {"error": {"required": "Prénom requis"}, "label": "Prénom"}, "lastname": {"error": {"required": "Nom de famille requis"}, "label": "Nom"}, "groupName": {"error": {"required": "Nom de groupe requis", "invalid": "Le nom du groupe ne peut contenir que des lettres, des chiffres, des espaces, des underscores, et des tirets.", "maxLength": "Le nom du groupe doit être composé d'au maximum {{ maxLength }} caractères"}}}, "global": {"cancel": "Annuler", "confirm": "Confirmer", "continue": "<PERSON><PERSON><PERSON>", "email": "Email", "invite": "Inviter", "next": "Suivant", "ok": "OK", "revoke": "Révoquer", "send": "Envoyer", "skipReason": "Je ne préfère pas répondre", "tags": "Tags", "unknown": "Inconnu", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "login": {"error": {"description": "Une erreur est survenue lors de votre tentative de connexion. <PERSON><PERSON><PERSON> r<PERSON> plus tard ou bien contactez le support si le problème persiste.", "title": "Une erreur est survenue"}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "En attente", "active": "Actif", "disabled": "Désactivé", "expired": "Expiré"}}}}}