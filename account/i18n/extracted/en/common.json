{"api": {"error": {"description": "An error has occurred fetching your data. Reload the page or contact support", "title": "Something went wrong..."}}, "form": {"address1": {"error": {"required": "Address is required"}, "label": "Address"}, "birthdate": {"error": {"required": "Birthdate is required"}, "label": "Birthdate"}, "city": {"error": {"required": "City is required"}, "label": "City"}, "companyName": {"error": {"maxLength": "Company name must be at most {{maxLength}} characters", "minLength": "Company name must be at least {{minLength}} characters", "required": "Company name is required"}, "label": "Company name"}, "configuration": {"label": "Configuration"}, "country": {"label": "Country"}, "coupon": {"error": {"alreadySubscribed": "You already have a subscription to an offer corresponding to this type of code.", "invalid": "This coupon code is invalid or expired.", "mismatch": "Sorry the coupon you entered must be at least 3 characters long.", "required": "Coupon is required"}, "info": "Please enter a valid coupon."}, "email": {"error": {"email": "Field must be a valid email", "required": "Email is required"}}, "firstName": {"error": {"required": "First name is required"}, "label": "First name"}, "keyboard": {"label": "Keyboard"}, "language": {"label": "Language"}, "lastName": {"error": {"required": "Last name is required"}, "label": "Last name"}, "name": {"error": {"required": ""}}, "phone": {"label": "Phone number"}, "tags": {"addButtonLabel": "Create", "label": "Add a tag"}, "vatNumber": {"error": {"invalid": "Your VAT number is invalid", "maxLength": "VAT number must be at most {{maxLength}} characters", "minLength": "VAT number must be at least {{minLength}} characters", "required": "VAT number is required", "wrongFormat": "VAT number must contain only alphanumerical characters"}, "label": "VAT number"}, "vmName": {"error": {"maxLength": "PC name must be at most {{ maxLength }} characters", "minLength": "PC name must be at least {{ minLength }} characters", "specialChar": "PC name must not use special characters"}, "label": "Name"}, "vmTags": {"error": {"maxLength": "Tag must be at most {{ maxLength }} characters", "minLength": "Tag must be at least {{ minLength }} characters", "noAlphanumericChar": "Tag must contain at least one alphanumeric character", "required": "Tag is required", "specialChar": "Tag must not use special characters"}, "label": "Tag"}, "word": {"error": {"required": "Word is required"}, "info": "Please write <bold>{{ wordToCompare }}</bold> below in order to continue:", "label": "Word to type"}, "zipcode": {"error": {"required": "Zipcode is required"}, "label": "Zipcode"}, "select": {"automatic": {"label": "Automatic"}, "manual": {"label": "Manual"}}, "firstname": {"error": {"required": "Firstname is required"}, "label": "Firstname"}, "lastname": {"error": {"required": "Lastname is required"}, "label": "Lastname"}, "groupName": {"error": {"required": "A group name is required", "invalid": "Group name can only contain letters, numbers, spaces, underscores, and dashes.", "maxLength": "Group name must be at most {{ maxLength }} characters"}}}, "global": {"cancel": "Cancel", "confirm": "Confirm", "continue": "Continue", "email": "Email", "invite": "Invite", "next": "Next", "ok": "OK", "revoke": "Revoke", "send": "Send", "skipReason": "I don't want to answer", "tags": "Tags", "unknown": "Unknown", "delete": "Delete"}, "login": {"error": {"description": "An error occurred when attempting to logged you in. Please try again later or contact support if the problem persists.", "title": "Oops, something is broken"}}, "subscription": {"driveGroups": {"manageGroupMembers": {"status": {"pending": "Pending", "active": "Active", "disabled": "Disabled", "expired": "Expired"}}}}}